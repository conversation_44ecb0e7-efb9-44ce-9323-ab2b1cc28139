<template>
  <div class="page-customer-detail">
    <div class="info-box card">
      <div class="box-title">
        <div class="title">基本信息</div>
      </div>
      <div class="infos">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">手机号码： </span>
              <span class="info-text">{{ rowData?.phone }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">账号： </span>
              <span class="info-text">{{ rowData?.account }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">用户邮箱： </span>
              <span class="info-text">{{ rowData?.email }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">客户来源： </span>
              <span class="info-text"> {{ useDictLabel(dict_user_source, rowData?.userSource) }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">认证级别： </span>
              <span class="info-text"> {{ useDictLabel(portal_verify_level, rowData?.verifyLevel) }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">状态： </span>
              <span class="info-text">{{ useDictLabel(dict_status, rowData?.status) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">企业名称：</span>
              <span class="info-text">{{ rowData?.company }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">所在城市： </span>
              <span class="info-text">{{ rowData?.areaName }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">通讯地址： </span>
              <span class="info-text">{{ rowData?.city }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">注册时间： </span>
              <span class="info-text">{{ rowData?.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">上一次登录时间： </span>
              <span class="info-text">{{ rowData?.lastLoginTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="info-box card">
      <div class="box-title">
        <div class="title">产品信息</div>
      </div>
      <div class="infos">
        <custProductTable :id="rowData.id"></custProductTable>
      </div>
    </div>
    <div class="info-box card">
      <div class="box-title">
        <div class="title">商机信息</div>
      </div>
      <div class="infos">
        <businessTable :id="rowData.id"></businessTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="customerDetail">
import { queryPortalUserInfo } from "@/api/modules/customerManage";
import businessTable from "./components/businessTable.vue";
import custProductTable from "./components/custProductTable.vue";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { System } from "@/api/interface/system";
const { dict_user_source, portal_verify_level } = useDict("dict_user_source", "portal_verify_level");
const dict_status = [
  {
    itemKey: 1,
    itemValue: "正常"
  },
  {
    itemKey: 0,
    itemValue: "停用"
  }
];
const rowData = ref<System.PortalUserParams>({});
const route = useRoute();
/* 请求 */
onMounted(() => {
  const id = route.query.id;
  queryPortalUserInfo({ id }).then(res => {
    rowData.value = res.data;
  });
});
</script>

<style scoped lang="scss">
.info-box {
  margin-bottom: 15px;
  .box-title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-weight: 600;
    color: #3a3a3d;
    border-bottom: 1px solid #e4e7ed;
    .title {
      font-size: 18px;
      .card-btn {
        float: right;
        margin-top: -5px;
        margin-right: 10px;
      }
    }
    .sub-tips {
      color: red;
    }
  }
  .infos {
    margin-left: 16px;
  }
  .info-item {
    // margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;

      // width: 115px;
      color: #73787f;
      text-align: right;
      &.default {
        width: 80px;
      }
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
}
</style>
