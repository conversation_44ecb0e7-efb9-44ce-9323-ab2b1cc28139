<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-08 14:32:12
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-15 17:03:05
 * @Description: file content
-->
<template>
  <div>
    <el-button type="primary" :icon="CirclePlus" style="margin-bottom: 24px" @click="showAttrDialog" v-if="!disabled">
      添加属性
    </el-button>
    <el-form :model="formField" :rules="rules" label-width="120px" ref="attrFormRef" :validate-on-rule-change="false">
      <el-form-item
        v-for="(item, index) in attrData"
        :key="'attr' + index"
        class="el-form-item"
        :prop="item.attrCode"
        :label="item.attrName"
        :style="item.attrType === attrTypeEnum['系统盘'] || item.attrType === attrTypeEnum['数据盘'] ? 'margin-bottom:0' : ''"
      >
        <el-checkbox-group v-model="(formField as any)[item.attrCode]" v-if="item.attrType === attrTypeEnum['枚举类型']">
          <el-checkbox v-for="(item2, index2) in item.attrFilter.content" :label="item2" :key="index2" :disabled="disabled">{{
            item2.name
          }}</el-checkbox>
        </el-checkbox-group>
        <div class="" v-if="item.attrType === attrTypeEnum['数值类型']">
          <attr-config
            v-model:data="(formField as any)[item.attrCode]"
            :attr-list="item.attrFilter.children"
            :disabled="disabled"
            :rules="(rules as any)[item.attrCode + '_children']"
            ref="NumberTypeRef"
          ></attr-config>
        </div>
        <div class="" v-if="item.attrType === attrTypeEnum['系统盘']">
          <attr-config
            v-model:data="(formField as any)[item.attrCode]"
            :attr-list="item.attrFilter.children"
            :disabled="disabled"
            :rules="(rules as any)[item.attrCode + '_children']"
            ref="systemDiskRef"
          ></attr-config>
        </div>
        <div class="" v-if="item.attrType === attrTypeEnum['数据盘']">
          <attr-config
            v-model:data="(formField as any)[item.attrCode]"
            :attr-list="item.attrFilter.children"
            :disabled="disabled"
            :rules="(rules as any)[item.attrCode + '_children']"
            ref="dataDiskRef"
          ></attr-config>
        </div>
      </el-form-item>
    </el-form>

    <el-dialog v-model="attrDialogVisible" title="选择产品属性" width="40%">
      <porduct-attr-select-dialog
        :prop-list="propList"
        :data="tableData"
        v-model:dialogVisible="attrDialogVisible"
        v-model:select-data="selectData"
        @change-select-data="changeSelectData"
      ></porduct-attr-select-dialog>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { Ref, computed, onMounted, ref, watch } from "vue";
import { set, cloneDeep } from "lodash";
import attrConfig from "./attrConfig.vue";
import { getProductAttrList } from "@/api/modules/product";
import { CirclePlus } from "@element-plus/icons-vue";
import porductAttrSelectDialog from "./porductAttrSelectDialog.vue";
import { attrTypeEnum } from "@/enums/productEnum";
import { filterFields } from "element-plus/es/components/form/src/utils";
import type { FormInstance, FormRules } from "element-plus";
import { enumDataType, numberDataType, sysDiskDataType, dataDiskDataType, buyTimeAttr, buyNumberAttr } from "./productAttr.js";
type attrType = {
  id: string;
  attrName: string;
  attrCode: string;
  attrType: string;
  attrEnumType: string;
  attrValueList: string;
  attrTypeName: string;
  config?: any;
  attrFilter?: any;
};
const attrDialogVisible = ref(false);
const tableData: Ref<any[]> = ref([]);
const selectData: Ref<attrType[]> = ref([]);

const propList = [
  {
    prop: "attrName",
    label: "属性名称"
  },
  {
    prop: "attrTypeName",
    label: "属性类型"
  },
  {
    prop: "attrCode",
    label: "属性编码"
  }
];
const props = defineProps({
  productAttr: {
    type: String,
    default: ""
  },
  disabled: {
    type: Boolean,
    default: false
  }
});
const emits = defineEmits(["update:productAttr"]);
//展示弹窗
const showAttrDialog = () => {
  selectData.value?.forEach((item, index) => {
    const attrItem = attrData.value.find(item2 => item2.id === item.id);
    if (attrItem) {
      selectData.value[index] = { ...attrItem, config: (formField.value as any)[attrItem.attrCode] };
    }
  });
  attrDialogVisible.value = true;
};
//设置完成再显示字段
getProductAttrList().then(res => {
  const defaultData = [
    {
      id: "1",
      attrName: "系统盘",
      attrCode: "SYSTEM_DISK",
      attrType: attrTypeEnum["系统盘"],
      attrEnumType: "",
      attrValueList: "",
      attrTypeName: "系统盘"
    },
    {
      id: "2",
      attrName: "数据盘",
      attrCode: "DATA_DISK",
      attrType: attrTypeEnum["数据盘"],
      attrEnumType: "",
      attrValueList: "",
      attrTypeName: "数据盘"
    }
  ];
  const typeObject = {
    "1": "数字类型",
    "2": "枚举类型",
    "3": "系统盘",
    "4": "数据盘"
  };
  tableData.value = [...(res.data as Array<any>), ...defaultData].map(item => {
    return {
      attrTypeName: typeObject[item.attrType as "1" | "2" | "3" | "4"],
      ...item
    };
  });
});

const systemDiskRef = ref(null);
const dataDiskRef = ref(null);
const attrFormRef = ref<FormInstance>();
const NumberTypeRef = ref(null);
const attrData: Ref<attrType[]> = ref([]);
const rules = ref({});
//值
const formField: Ref<any> = ref({});
//设置值和校验规则
const setFormField = (dataArray: Array<any>, parentString?: string, noChangeData?: boolean) => {
  if (!dataArray || dataArray.length <= 0) return;
  dataArray.forEach((item: any) => {
    const attrCode = parentString ? parentString + "." + item.attrCode : (item.attrCode as string);
    if (!noChangeData) {
      set(formField.value, attrCode, item.config || item.defaultValue || "");
    }
    if (parentString) {
      set(rules.value, parentString + "_children." + item.attrCode, [{ required: true, message: "该配置不能为空" }]);
    } else if (attrCode) {
      set(rules.value, attrCode, [{ required: true, message: "该配置不能为空" }]);
    }
    if (item.attrFilter?.children) {
      setFormField(item.attrFilter.children, attrCode, item.config);
    }
  });
};

//检验数据
const checkFormField = () => {
  const promiseList: Array<any> = [];
  promiseList.push(
    new Promise((resolve, reject) => {
      attrFormRef.value!.validate((valid: any, fields: any) => {
        if (valid) {
          resolve("校验成功");
        } else {
          let obj = Object.keys(fields);
          attrFormRef.value!.scrollToField(obj[0]);
          reject("校验失败");
        }
      });
    })
  );
  if (NumberTypeRef.value) {
    let list = (NumberTypeRef.value as Array<any>).map(item => item.validateData());
    promiseList.push(...list);
  }
  if (systemDiskRef.value) {
    let list = (systemDiskRef.value as Array<any>).map(item => item.validateData());
    promiseList.push(...list);
  }
  if (dataDiskRef.value) {
    let list = (dataDiskRef.value as Array<any>).map(item => item.validateData());
    promiseList.push(...list);
  }
  return new Promise((resolve, reject) => {
    Promise.all(promiseList)
      .then(result => {
        resolve(result);
      })
      .catch(error => {
        reject(error);
      });
  });
};
//需要提交的数据
const submitData = computed(() => {
  const result = cloneDeep(attrData.value);
  result.forEach((item: any) => {
    item.config = formField.value[item.attrCode as string];
  });
  return result;
});

//初始化数据
const initAttrData = () => {
  if (attrData.value.length <= 0) {
    attrData.value.push(buyTimeAttr, buyNumberAttr);
  }
  //同步数据
  attrData.value = setAttrDataConfig(attrData.value);
};
const setAttrDataConfig = (dataList: any) => {
  //关联旧的值
  if (!dataList.find((item: any) => item.attrCode === "BUY_DURATION")) {
    const buyTime: attrType | undefined = attrData.value.find((item2: any) => item2.attrCode === "BUY_DURATION");
    buyTime && dataList.push({ ...buyTimeAttr, config: buyTime.config });
  }
  if (!dataList.find((item: any) => item.attrCode === "BUY_COUNT")) {
    const buyNumber: attrType | undefined = attrData.value.find(item2 => item2.attrCode === "BUY_COUNT");
    buyNumber && dataList.push({ ...buyNumberAttr, config: buyNumber.config });
  }
  const tempAttrData: any[] = [];
  const clearValidateData: any[] = [];
  attrData.value.forEach(item => {
    if (!dataList.find((item2: any) => item2.id === item.id)) {
      //找不到说明被删了
      clearValidateData.push(item.attrCode);
    }
  });
  attrFormRef.value?.clearValidate(clearValidateData);
  dataList.forEach((item: attrType) => {
    let attrDataItem: any = {};
    //枚举类型
    const attrItem = attrData.value.find(item2 => item2.id === item.id);
    if (attrItem) {
      item = { ...item, config: attrItem.config };
    }
    if (item.attrType === attrTypeEnum["枚举类型"]) {
      attrDataItem = { ...cloneDeep(enumDataType), ...item };
      attrDataItem.attrFilter.content = JSON.parse(item.attrValueList);
    } else if (item.attrType === attrTypeEnum["数值类型"]) {
      //数值类型
      attrDataItem = { ...cloneDeep(numberDataType), ...item };
    } else if (item.attrType === attrTypeEnum["系统盘"]) {
      //系统盘
      attrDataItem = { ...cloneDeep(sysDiskDataType), ...item };
    } else if (item.attrType === attrTypeEnum["数据盘"]) {
      //数据盘
      attrDataItem = { ...cloneDeep(dataDiskDataType), ...item };
    }
    tempAttrData.push(attrDataItem);
  });
  return tempAttrData;
};
//选择框变化的时候要重新渲染值
const changeSelectData = () => {
  attrData.value = setAttrDataConfig(selectData.value);
  setFormField(attrData.value);
};
watch(
  () => props.productAttr,
  () => {
    const productAttr = JSON.parse(props.productAttr);
    selectData.value = productAttr || [];
    attrData.value = productAttr || [];
    initAttrData();
    setFormField(attrData.value);
  },
  {
    immediate: true
  }
);
defineExpose({ formField, checkFormField, submitData });
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 24px;
}
</style>
