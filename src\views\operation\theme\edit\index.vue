<template>
  <div class="card content-box">
    <div class="edit-title">{{ route.meta.title }}</div>
    <el-form ref="ruleFormRef" :model="ruleForm" label-position="left" :rules="rules" label-width="83px">
      <el-form-item label="专区名称" placeholder="请输入专题名称" prop="name">
        <el-input :maxlength="20" v-model="ruleForm.name" :disabled="isEdit" />
      </el-form-item>
      <el-form-item label="专区介绍" prop="description" placeholder="请输入内容">
        <el-input :maxlength="100" v-model="ruleForm.description" type="textarea" />
      </el-form-item>
      <el-form-item label="banner图片" prop="iconUrl">
        <UploadImg :file-size="2" v-model:image-url="ruleForm.iconUrl" width="135px" height="135px">
          <template #tip>建议上传图片尺寸为768 px× 960px，图片大小不超过5M，支持图片格式jpg/jpeg/png</template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="banner操作按钮" prop="operationButtons">
        <!-- <el-button type="primary" class="mb-10" :disabled="!isEdit" @click="selectProduct">添加</el-button> -->
        <button-table v-model:buttonData="ruleForm.operationButtons" @update-button="updateButton"></button-table>
      </el-form-item>

      <el-form-item label="模块配置" prop="children">
        <!-- <el-switch v-model="ruleForm.moreEnabled" /> -->
        <model-config
          ref="modelConfigRef"
          v-model:dataList="ruleForm.children"
          :is-edit="isEdit"
          :product-detail="productDetail"
          @add-model="addModel"
          @delete-model="deleteModel"
          @add-card="addCard"
          @delete-card="deleteCard"
          @init-model-item="getInitModelItem"
          @add-large-model="addLargeModel"
          @delete-large-model="deleteLargeModel"
          @update-model-type="updateModelType"
          @update-link="updateLink"
          @update-button-link="updateButtonLink"
          @update-sku-ids="updateSkuIds"
          @delete-list-tags="deleteListTags"
          @update-tags="updateTags"
        >
          <template #nameTips>最多可添加10个，至少配置1个模块</template>
        </model-config>
      </el-form-item>
      <div>
        <el-button type="primary" @click="submitForm(ruleFormRef)">确定</el-button>
        <!-- <el-button type="primary" @click="isPreview = true">预览</el-button> -->
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="productEdit">
import { reactive, ref, onMounted, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import ModelConfig from "./modelConfig.vue";
import buttonTable from "./buttonTable.vue";
// import { ElMessageBox } from "element-plus";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import UploadImg from "@/components/Upload/Img.vue";
import { change, add } from "@/api/modules/business/theme";
import { getThemeDetail } from "@/api/modules/business/theme";
import { buttonItemType, modelChildren, cardItem, modelItem, productItem } from "@/api/interface/business/theme";
import { closeTabItem } from "@/utils/closeTab";
const route = useRoute();
const router = useRouter();
// const drawerRef = ref<InstanceType<typeof ProductDrawer> | null>(null);
const modelConfigRef = ref();
const id = (route.query.id as string) || "";
let isEdit = id.length > 0 ? true : false; //编辑、新增

const productDetail = ref<Array<Array<productItem>>>([]);

const ruleFormRef = ref<FormInstance>();
// const ruleForm = reactive(getInitForm());
const ruleForm = reactive({
  name: "",
  id: "",
  description: "",
  iconUrl: "",
  operationButtons: [] as buttonItemType[],
  children: [
    {
      name: "",
      title: "",
      type: "",
      skuIds: [],
      categoryList: [],
      tags: [],
      richText: "",
      iconUrl: "",
      videoUrl: "",
      noticeContent: "",
      link: "",
      modelId: "",
      description: "",
      children: [
        {
          name: "",
          title: "string",
          uploadType: "",
          videoUrl: "",
          iconUrl: "",
          link: "",
          operationButtons: [],
          additionalTitles: [
            { title: "", context: "" },
            { title: "", context: "" }
          ],
          modelId: "",
          category: "",
          tags: [],
          description: "",
          advantage: "",
          supplier: ""
        }
      ] as modelChildren[]
    }
  ] as modelItem[]
});

const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入专区名称", trigger: "blur" }],
  description: [{ required: true, message: "专区介绍不能为空", trigger: "blur" }],
  iconUrl: [{ required: true, message: "banner图片不能为空", trigger: "blur" }],
  children: [{ required: true, trigger: "blur", message: "最少需添加一个模块配置" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    // const data = { ...ruleForm, productIdList: productStore.productDetailList.map((item: any) => item.id) };
    // data.productIds = JSON.stringify(data.productIdList);
    if (valid) {
      let modelPromise = modelConfigRef.value.ruleValidatePromise();
      Promise.all([...modelPromise])
        .then(result => {
          // saveData(result);
          //判断大小卡片中的校验、关联产品校验
          const flag = ref(true);
          ruleForm.children.forEach((item: any = {}) => {
            if (item.type === "1") {
              if (item.skuIds.length < 2) {
                flag.value = false;
                ElMessage.error("至少需要添加2个关联产品");
              }
            } else if (item.type === "4") {
              item.children.forEach((cardItem: any = {}) => {
                if (cardItem.videoUrl === "" && cardItem.iconUrl === "") {
                  flag.value = false;
                  ElMessage.error("卡片中必须上传一张图片或一段视频");
                } else if (cardItem.title === "" || cardItem.description === "") {
                  flag.value = false;
                  ElMessage.error("卡片标题、描述不能为空");
                }
              });
            } else if (item.type === "5") {
              item.children.forEach((cardItem: any = {}) => {
                if (cardItem.videoUrl === "" && cardItem.iconUrl === "") {
                  flag.value = false;
                  ElMessage.error("卡片中必须上传一张图片或一段视频");
                } else if (cardItem.additionalTitles[0].title === "" || cardItem.additionalTitles[0].context === "") {
                  flag.value = false;
                  ElMessage.error("卡片子标题一、描述一不能为空");
                }
              });
            }
          });

          if (flag.value) {
            if (isEdit) {
              change(ruleForm)
                .then((res: any) => {
                  if (res.code === 200) {
                    ElMessage.success(res.msg);
                    goBack();
                  }
                })
                .catch((err: any) => {
                  console.log(err);
                });
            } else {
              add(ruleForm)
                .then((res: any) => {
                  if (res.code === 200) {
                    ElMessage.success(res.msg);
                    goBack();
                  }
                })
                .catch((err: any) => {
                  console.log(err);
                });
            }
            // goBack();
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  });
};

const updateButton = (data: any) => {
  ruleForm.operationButtons = data;
};

const goBack = () => {
  closeTabItem(route);
  router.push("/operation/theme");
};

const getInitModelItem = (index: any) => {
  productDetail.value[index] = [];
  ruleForm.children[index].categoryList = [];
  ruleForm.children[index].tags = [];
  ruleForm.children[index].skuIds = [];
  ruleForm.children[index].iconUrl = "";
  ruleForm.children[index].videoUrl = "";
  ruleForm.children[index].richText = "";
  ruleForm.children[index].noticeContent = "";
  ruleForm.children[index].link = "";
  ruleForm.children[index].modelId = "";
  ruleForm.children[index].description = "";
  ruleForm.children[index].children = [
    {
      name: "",
      title: "",
      uploadType: "",
      videoUrl: "",
      iconUrl: "",
      link: "",
      operationButtons: [],
      additionalTitles: [
        { title: "", context: "" },
        { title: "", context: "" }
      ],
      modelId: "",
      tags: [],
      category: "",
      description: "",
      advantage: "",
      supplier: ""
    }
  ] as modelChildren[];
}; //改变展示类型时初始化数据

const addModel = () => {
  if (ruleForm.children.length >= 10) {
    ElMessage.error("最多添加十个模块配置");
    return;
  }
  ruleForm.children.push({
    name: "",
    title: "",
    type: "",
    skuIds: [],
    categoryList: [],
    tags: [],
    iconUrl: "",
    videoUrl: "",
    richText: "",
    noticeContent: "",
    modelId: "",
    description: "",
    link: "",
    children: [] as modelChildren[]
  });
};

const deleteModel = (index: number) => {
  if (ruleForm.children.length < 2) {
    ElMessage.error("最少需添加一个模块配置");
    return;
  }
  ruleForm.children.splice(index, 1);
};

const addCard = (index: number) => {
  // if (ruleForm.children.length >= 10) {
  //   ElMessage.error("最多添加十个产品规格");
  //   return;
  // }
  ruleForm.children[index].children.push({
    name: "",
    title: "",
    uploadType: "",
    videoUrl: "",
    iconUrl: "",
    link: "",
    operationButtons: [],
    additionalTitles: [
      { title: "", context: "" },
      { title: "", context: "" }
    ],
    modelId: "",
    category: "",
    tags: [],
    description: "",
    advantage: "",
    supplier: ""
  });
};

const deleteCard = (index: number, cardIndex: number) => {
  if (ruleForm.children[index].children.length < 2) {
    ElMessage.error("最少需添加一个卡片配置");
    return;
  }
  ruleForm.children[index].children.splice(cardIndex, 1);
};

const deleteListTags = (modelIndex: number, largeModelIndex: number, tagIndex: number) => {
  // if (ruleForm.children[index].children.length < 2) {
  //   ElMessage.error("最少需添加一个卡片配置");
  //   return;
  // }
  ruleForm.children[modelIndex].children[largeModelIndex].tags.splice(tagIndex, 1);
};

const updateTags = (data: any, modelIndex: number) => {
  // if (ruleForm.children[index].children.length < 2) {
  //   ElMessage.error("最少需添加一个卡片配置");
  //   return;
  // }
  ruleForm.children[modelIndex].tags = data;
};

const addLargeModel = (index: number) => {
  // if (ruleForm.children.length >= 20) {
  //   ElMessage.error("最多添加二十个大模型");
  //   return;
  // }
  ruleForm.children[index].children.push({
    name: "",
    title: "",
    uploadType: "",
    videoUrl: "",
    iconUrl: "",
    link: "",
    operationButtons: [],
    additionalTitles: [
      { title: "", context: "" },
      { title: "", context: "" }
    ],
    modelId: "",
    category: "",
    tags: [],
    description: "",
    advantage: "",
    supplier: ""
  });
};

const deleteLargeModel = (index: number, cardIndex: number) => {
  if (ruleForm.children[index].children.length < 2) {
    ElMessage.error("最少需添加一个大模型");
    return;
  }
  ruleForm.children[index].children.splice(cardIndex, 1);
};

const updateModelType = (index: number, data: any) => {
  ruleForm.children[index].categoryList = data;
};

//更新大模型列表中带出的跳转链接
const updateLink = (data: any, modelIndex: number, index: number) => {
  ruleForm.children[modelIndex].children[index].link = data;
};

//更新大模型列表中带出的跳转链接
const updateButtonLink = (data: any, index: number) => {
  ruleForm.children[index].link = data;
};

//更新大模型列表中关联产品
const updateSkuIds = (data: any, index: number) => {
  productDetail.value[index] = data;
  ruleForm.children[index].skuIds = data.map((item: any) => item.skuId);
  // ruleForm.children[index].skuIds = data;
};

watch(
  () => ruleForm.children,
  () => {
    // emit(
    //   "updateSkuIds",
    //   tableList.value
    //   // tableList.value.map((item: any) => item.id)
    // );
  },
  { deep: true }
);

onMounted(() => {
  if (id.length > 0) {
    getThemeDetail(id).then(res => {
      res.data.children.forEach((item: any) => {
        productDetail.value.push(item.products);
      });

      ruleForm.name = res.data.name;
      ruleForm.id = res.data.id;
      ruleForm.description = res.data.description;
      ruleForm.iconUrl = res.data.iconUrl;
      ruleForm.operationButtons = res.data.operationButtons;
      ruleForm.children = res.data.children;
      ruleForm.children.forEach((item: any, index: any) => {
        ruleForm.children[index].skuIds = productDetail.value[index].map(product => product.id);
      });
    });
  }
});
</script>

<style scoped lang="scss">
.el-form {
  width: 100%;
  .text-center {
    text-align: center;
  }
}
.content-box {
  align-items: start;
}
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
:deep(.table-main .el-table .el-table__empty-block) {
  min-height: 0 !important;
}
</style>
