<template>
  <div class="source-count">
    <div class="customer-info">
      <div
        class="info-item"
        :style="{ backgroundColor: item.iconBackgroundColor, backgroundImage: 'url(' + item.iconUrl + ')' }"
        v-for="item in sourceCount"
        :key="item.sourceType"
      >
        <div class="item-body">
          <div class="name">
            {{ item.sourceName }}

            <el-popover :width="232" placement="right" trigger="hover" :content="item.desc">
              <template #reference>
                <el-icon class="icon" size="16px"><Warning /></el-icon>
              </template>
            </el-popover>
          </div>
          <div class="count">{{ item.userCount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { querySourceCount } from "@/api/modules/customerManage";

const sourceCount = ref();

onMounted(() => {
  querySourceCount()
    .then(result => {
      sourceCount.value = result.data;
    })
    .catch(err => {});
});
</script>

<style scoped lang="scss">
.customer-info {
  display: flex;
  padding: 24px;
  background: #ffffff;
}

@for $i from 1 through 6 {
  .info-item {
    flex: 1;
    height: 120px;
    margin-left: 16px;
    overflow: hidden;
    background-repeat: no-repeat;
    background-position: 90% center;
    background-size: 72px;
    border-radius: 4px;
    &:first-child {
      margin-left: 0;
    }
    .item-body {
      margin-top: 24px;
      margin-left: 16px;
      .name {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        color: #3a3a3d;
        .icon {
          margin-left: 8px;
          color: #c4c6cc;
          cursor: pointer;
          &:hover {
            color: #3a3a3d;
          }
        }
      }
      .count {
        margin-top: 7px;
        font-size: 32px;
        font-weight: 600;
        color: #3a3a3d;
      }
    }
  }
}
</style>
