import { FormInstance } from "element-plus";
import { Ref, nextTick } from "vue";

export const validApplyEffectTime = (formData: any, index: number) => {
  return (rule: any, value: any, callback: any) => {
    if (value !== "" && value !== null && value !== undefined) {
      if (formData.lifeSpanUnit !== null && formData.lifeSpanUnit !== "" && formData.lifeSpanUnit !== undefined) {
        callback();
      } else {
        callback(new Error("请输入有效期单位"));
      }
    }
  };
};

// 外部对接类产品提交数据处理
export default class ExternalDocking {
  id?: string;
  isSubAccountSharing: boolean;
  subAccountQuantity: number;
  minQuantity: string;
  maxQuantity: string;
  quantityStep: string;
  skuUnit: string;
  minTime: string;
  maxTime: string;
  timeStep: string;
  timeUnit: string;
  probationTime: string;
  lifeSpan: string;
  lifeSpanUnit: string;
  giftSkuId: string[];
  skuGiftList: string;
  skuContent: string;
  renewalRules: string;
  giftFlag: string;
  quantityCounting: boolean;
  timeCounting: boolean;
  skuName: string;
  externalCode: string;

  constructor(itemInfo: any) {
    this.id = itemInfo?.id;
    this.skuName = itemInfo.skuName;
    this.externalCode = itemInfo.externalCode;
    this.isSubAccountSharing = itemInfo.isSubAccountSharing;
    this.subAccountQuantity = itemInfo.subAccountQuantity;
    this.minQuantity = itemInfo.minQuantity;
    this.maxQuantity = itemInfo.maxQuantity;
    this.quantityStep = itemInfo.quantityStep;
    this.skuUnit = itemInfo.skuUnit;
    this.minTime = itemInfo.minTime;
    this.maxTime = itemInfo.maxTime;
    this.timeStep = itemInfo.timeStep;
    this.timeUnit = itemInfo.timeUnit;
    this.probationTime = itemInfo.probationTime;
    this.lifeSpan = itemInfo.lifeSpan;
    this.lifeSpanUnit = itemInfo.lifeSpanUnit;
    this.giftSkuId = itemInfo.giftSkuId;
    this.skuGiftList = itemInfo.skuGiftList;
    this.skuContent = itemInfo.skuContent;
    this.renewalRules = itemInfo.renewalRules;
    this.giftFlag = itemInfo.giftFlag;
    this.quantityCounting = itemInfo.quantityCounting;
    this.giftFlag = itemInfo.giftFlag;
    this.timeCounting = itemInfo.timeCounting;
  }
}
