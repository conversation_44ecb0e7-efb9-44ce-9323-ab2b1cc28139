<template>
  <div id="ratePieEcharts" class="echarts"></div>
</template>

<script setup lang="ts">
import { ECharts, EChartsOption, init } from "echarts";
import { toThousandFilter } from "@/utils/tools";
import { useEcharts } from "@/hooks/useEcharts";

interface DataType {
  num: string;
  numPer: string;
  userSource: string;
  name?: string;
  value?: string;
}
interface ChartProp {
  chartData: DataType[];
  total: string;
}

const initChart = (
  data: ChartProp = {
    chartData: [],
    total: ""
  }
) => {
  const charEle = document.getElementById("ratePieEcharts") as HTMLElement;
  const charEch: ECharts = init(charEle);
  const chartOption: any = data.chartData;

  const colors = ["#3BA0FF", "#36CBCB", "#4DCB73", "#FAD337"];
  const chartTitle = "注册用户占比";
  const title = "总注册数";
  const subtitle = data.total;

  const option: EChartsOption = {
    color: colors,
    tooltip: {
      trigger: "item"
    },
    title: [
      {
        text: chartTitle,
        textStyle: {
          color: "#3A3A3D",
          fontWeight: 400
        },
        top: 16
      }
    ],
    legend: {
      orient: "vertical",
      icon: "circle",
      textStyle: {
        color: "#373737",
        fontSize: "12px",
        rich: {
          name: {
            color: "rgba(0,0,0,0.65)",
            fontSize: 14,
            width: 80,
            fontWeight: 400,
            padding: [0, 0, 0, 2]
          },
          symbol: {
            color: "#D9D9D9",
            fontSize: 14,
            fontWeight: 400,
            padding: [0, 0, 0, 8]
          },
          percent: {
            color: "rgba(0,0,0,0.45)",
            fontSize: 14,
            fontWeight: 400,
            width: 40,
            padding: [0, 0, 0, 4]
          },
          value: {
            color: "rgba(0,0,0,0.65)",
            fontSize: 14,
            fontWeight: 400,
            padding: [0, 0, 0, 24]
          }
        }
      },
      top: "center",
      // right: "5%",
      left: "55%",
      itemGap: 16,
      itemHeight: 8,
      itemWidth: 8,
      data: chartOption.map((item: any) => item.name),
      formatter: name => {
        let obj: any = {};
        if (name) {
          obj = chartOption.find((item: any) => item.name === name);
        }
        return `{name|${name}}{symbol| |}{percent|${obj.numPer}}{value|${toThousandFilter(obj.value) || 0}}`;
      }
    },
    grid: {
      top: "20%",
      right: "2%",
      bottom: "0",
      left: "0",
      containLabel: true
    },
    series: [
      {
        name: "注册用户数累计",
        type: "pie",
        radius: ["50%", "70%"],
        center: ["30%", "50%"],
        avoidLabelOverlap: false,
        selectedMode: false,
        labelLine: {
          show: false
        },
        label: {
          show: true,
          position: "center", // 展示在中间位置
          formatter: () => {
            return `{title|${title}}\n\n{subtitle|${subtitle}}`;
          },
          rich: {
            title: {
              fontSize: 14,
              fontWeight: 400,
              color: "#3A3A3D",
              align: "center",
              padding: [10, 0]
            },
            subtitle: {
              fontSize: 32,
              fontWeight: 600,
              color: "#3A3A3D",
              align: "center"
            }
          }
        },
        emphasis: {
          label: {
            show: true
          }
        },
        data: chartOption
      }
    ]
  };
  useEcharts(charEch, option);
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
</style>
