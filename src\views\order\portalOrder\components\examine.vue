<template>
  <div class="page-order-detail">
    <div class="order-info">
      <div class="page-title">审核信息</div>
      <div class="form">
        <el-form ref="formRef" :model="examineForm" label-width="83px" class="demo-ruleForm">
          <el-form-item label="审核意见" prop="auditRemark" :rules="[{ required: validRequired, message: '请输入驳回意见' }]">
            <el-input
              rows="5"
              v-model="examineForm.auditRemark"
              placeholder="驳回时必须填写审核意见，通过默认审核意见为同意"
              type="textarea"
              maxlength="200"
              show-word-limit
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item>
            <!-- <div class="tips">驳回时必须填写审核意见，通过默认审核意见为同意</div> -->
          </el-form-item>
          <div class="button">
            <el-button plain @click="handleBack">返回</el-button>
            <el-button type="primary" @click="handleTask('3')">通过</el-button>
            <el-button type="primary" @click="handleTask('4')">驳回</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref } from "vue";
import type { Action, FormInstance, FormRules } from "element-plus";
import { orderAuditOrder } from "@/api/modules/order";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";

const router = useRouter();
const formRef = ref<FormInstance>();
const validType = ref<string>("3");

const validRequired = computed(() => {
  return validType.value === "4";
});

const examineForm = reactive({
  auditRemark: ""
});

const emit = defineEmits(["onChange"]);

const handleTask = (index: string) => {
  validType.value = index;
  let tips = "";
  if (index === "3") {
    tips = "是否确认审核通过";
  } else {
    tips = "是否确认驳回审核";
  }

  nextTick(() => {
    formRef.value?.validate().then(valid => {
      ElMessageBox.alert(tips, "操作提示", {
        confirmButtonText: "确定",
        callback: (action: Action) => {
          if (action === "confirm") {
            const auditRemark = examineForm.auditRemark === "" ? "同意" : examineForm.auditRemark;
            orderAuditOrder({
              auditRemark: index === "3" ? auditRemark : examineForm.auditRemark,
              operate: index,
              parentOrderNo: props.orderId || 0
            }).then(res => {
              emit("onChange");
              ElMessage({
                message: index === "3" ? "审核成功" : "审核失败",
                type: "success"
              });
            });
          }
        }
      });
    });
  });
};

const handleBack = () => {
  router.back();
};
const props = defineProps({
  orderId: {
    type: String,
    require: true
  }
});
</script>

<style scoped lang="scss">
.button {
  display: flex;
  justify-content: flex-start;
  margin-top: 30px;
  margin-bottom: 10px;

  // :deep(.el-button) {
  //   height: 36px;
  //   margin-right: 16px;
  // }
}
.order-info {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}
.page-title {
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #3a3a3d;
  border-bottom: 1px solid #e4e7ed;
}
.form {
  margin-top: 20px;
}
.tips {
  color: rgb(156 163 172 / 100%);
}
</style>
