<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="需求管理列表"
      :columns="columns"
      :tool-button="false"
      :request-api="getTableList"
      :request-auto="false"
    >
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <span v-auth="'detail'">
          <el-tooltip content="详情" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="showDetail(row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
    <demand-detail ref="demandDetailRef"></demand-detail>
  </div>
</template>

<script setup lang="tsx" name="portalOrder">
import { ref, onMounted, onActivated } from "vue";
import { Plus, Download } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import DemandDetail from "./components/demandDetail/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { queryDemandList } from "@/api/modules/transactionVolumeFiling";

const { BUTTONS } = useAuthButtons();

const demandDetailRef = ref();

const proTable = ref<ProTableInstance>();

// 表格配置项
const columns: ColumnProps[] = [
  {
    prop: "id",
    label: "OID",
    minWidth: 90
  },
  {
    prop: "enterpriseName",
    label: "企业名称",
    minWidth: 90
  },
  {
    prop: "linkman",
    label: "企业联系人",
    minWidth: 90
  },
  {
    prop: "contact",
    label: "联系方式",
    minWidth: 90
  },
  {
    prop: "providerName",
    label: "供应商名称",
    minWidth: 90,
    search: { el: "input" }
  },
  {
    prop: "productName",
    label: "产品名称",
    width: 90,
    search: { el: "input" }
  },
  {
    prop: "createTime",
    label: "创建时间",
    minWidth: 90
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 100
  }
];

const getTableList = async (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }

  return queryDemandList(params);
};

const showDetail = (row: any) => {
  demandDetailRef.value?.show(row);
};

onMounted(() => {
  proTable.value?.getTableList();
});

// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  proTable.value?.getTableList();
});
</script>
