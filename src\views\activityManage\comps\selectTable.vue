<template>
  <div class="s">
    <el-table :data="cList" border style="width: 100%" :header-cell-style="{ backgroundColor: '#F0F2F5', color: '#333' }">
      <el-table-column prop="productName" label="产品名称" />
      <el-table-column prop="skuName" label="产品规格">
        <!-- <template #default="scope">
          {{ useDictLabel(sku_type, scope.row.skuType) }}
        </template> -->
      </el-table-column>
      <el-table-column prop="skuPrice" label="规格单价" width="150" />
      <el-table-column prop="totalLimit" width="200">
        <template #header> 发放数量 </template>
        <template #default="{ row, $index }">
          <el-form-item :prop="'marketingProductList.' + $index + '.totalLimit'">
            <el-input-number
              :class="isEdit ? 'btn-decrease' : null"
              :min="1"
              v-d-input-int
              :max="999999"
              v-model="row.totalLimit"
              placeholder="发放数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="userLimit" width="200">
        <template #header> 每人限制领取次数 </template>
        <template #default="{ row, $index }">
          <!-- <div class="current-count">当前最小值：{{ row._userLimit }}</div> -->
          <el-form-item :prop="'marketingProductList.' + $index + '.userLimit'">
            <el-input-number
              :class="isEdit ? 'btn-decrease' : null"
              :min="1"
              v-d-input-int
              :max="99"
              v-model="row.userLimit"
              placeholder="每人限制领取次数"
            >
            </el-input-number>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="description" width="300">
        <template #header> 使用说明 </template>
        <template #default="{ row }">
          <!-- <el-form-item
            size="large"
            :prop="'marketingProductList.' + $index + '.description'"
            :rules="[
              {
                required: true,
                message: '请输入活动规则说明',
                trigger: 'blur'
              }
            ]"
          >
           
          </el-form-item> -->
          <!-- <el-input  maxlength="500" v-model="row.description" placeholder="请输入使用说明，500字以内" /> -->
          <el-input
            v-model="row.description"
            type="textarea"
            placeholder="请输入使用说明，500字以内"
            maxlength="500"
            :autosize="{ minRows: 2, maxRows: 2 }"
            :disabled="isEdit"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)" v-if="!isEdit"> 移除 </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { useDict, useDictLabel } from "@/hooks/useDict";
const { sku_type } = useDict("sku_type");

const props = defineProps({
  cList: {
    type: Array,
    default: () => {
      return [];
    }
  },
  pageType: {
    type: String,
    default: ""
  },
  isEdit: {
    type: Boolean,
    default: true
  }
});

const emits = defineEmits(["onDelete"]);

const handleDelete = (index: number) => {
  emits("onDelete", index);
};
</script>

<style scoped lang="scss">
:deep(.btn-decrease) {
  .el-input-number__decrease {
    display: none;
  }
}
</style>
