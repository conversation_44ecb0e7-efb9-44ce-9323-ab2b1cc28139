<template>
  <el-dialog
    v-model="dialogVisible"
    :title="currType === operateTypeConfig.BIND ? '绑定客户账号信息' : '解绑客户账号信息'"
    align-center
    width="600px"
  >
    <div class="dialog-content">
      <el-form
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        :disabled="formDisabled"
        label-width="120px"
        label-position="left"
        status-icon
      >
        <el-form-item label="关联客户账号" prop="userAccount">
          <el-input
            v-model="ruleForm.userAccount"
            placeholder="请填写关联的昊算账号"
            minlength="1"
            maxlength="50"
            :disabled="currType === operateTypeConfig.UNBIND"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="订单号" prop="subOrderNo">
          <el-input v-model="ruleForm.subOrderNo" placeholder="请填写订单号" minlength="1" maxlength="50" clearable> </el-input>
        </el-form-item>
      </el-form>
      <!--      <div class="sync-btn-box">-->
      <!--        <el-button type="primary" @click="syncClick">同步天穹</el-button>-->
      <!--      </div>-->
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="confirmLoading" @click="confirmClick">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import { operateTypeConfig } from "../config";
import { platformAccountBind } from "@/api/modules/platform";
import { ElMessage } from "element-plus";

const emit = defineEmits(["update"]);

let currId;
const currType = ref<string>("");

const formDisabled = ref<boolean>(false);

const dialogVisible = ref(false);
const title = ref("");

const confirmLoading = ref<boolean>(false);

const ruleFormRef = ref();

const ruleForm = reactive({
  userAccount: "",
  subOrderNo: ""
});

const rules = reactive({
  userAccount: [
    {
      required: true,
      message: "请填写关联的昊算账号",
      trigger: "blur"
    }
  ],
  subOrderNo: []
});

const show = ({ type, id, userAccount, subOrderNo, disabled = false }) => {
  currId = id;
  currType.value = type;
  formDisabled.value = disabled;
  confirmLoading.value = false;
  ruleForm.userAccount = userAccount || "";
  ruleForm.subOrderNo = subOrderNo || "";
  dialogVisible.value = true;
};

const close = () => {
  dialogVisible.value = false;
};

const syncClick = () => {
  // TODO
};

const save = () => {
  confirmLoading.value = true;
  const queryData = {
    id: currId,
    isBind: currType.value === operateTypeConfig.BIND ? 1 : 0,
    ...ruleForm
  };
  platformAccountBind(queryData)
    .then(res => {
      confirmLoading.value = false;
      const msg = currType.value === operateTypeConfig.BIND ? "绑定成功" : "解绑成功";
      ElMessage({
        message: msg,
        type: "success"
      });
      emit("update");
      close();
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const confirmClick = () => {
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      save();
    }
  });
};

defineExpose({
  show,
  close
});
</script>

<style lang="scss" scoped>
.dialog-content {
  .sync-btn-box {
    padding-left: 100px;
    margin-top: 30px;
  }
}
</style>
