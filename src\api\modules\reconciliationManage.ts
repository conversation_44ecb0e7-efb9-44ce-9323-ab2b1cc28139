import http from "@/api";

// 对账分页查询
export const getReconciliationList = (params?: any) => {
  return http.get(`/cnud-order/admin/order-bill/reconciliation/page`, params);
};

// 对账列表导出
export const exportReconciliation = (params: Object) => {
  return http.get(`/cnud-order/admin/order-bill/reconciliation/export`, params, { responseType: "blob" });
};

// 账单确认处理
export const confirmHandleStatus = (params: { id: string; handleOpinion: string }) => {
  return http.post(`/cnud-order/admin/order-bill/reconciliation/confirm-handle-status`, params);
};

// 账单取消处理
export const cancelHandleStatus = (params: { id: string }) => {
  return http.post(`/cnud-order/admin/order-bill/reconciliation/cancel-handle-status`, params);
};

// 确认生成账单
export const confirmBill = (params: any) => {
  return http.post(`/cnud-order/admin/order-bill/reconciliation/confirm-bill`, params);
};

// 选择全部生成账单
export const generateBillAll = (params: any) => {
  return http.get(`/cnud-order/admin/order-bill/reconciliation/generate-all`, params);
};

// 生成账单
export const generateBill = (params: { idList: string[] }) => {
  return http.post(`/cnud-order/admin/order-bill/reconciliation/generate-bill`, params);
};
