<template>
  <div class="home">
    <div class="overview">
      <div class="overview-box">
        <div class="overview-title flx-align-center">
          <span>累计生效订单数</span>
          <el-popover :width="232" placement="right" trigger="hover" content="截止至昨天，累计所有订单状态为“订购完成”的订单数据">
            <template #reference>
              <el-icon class="icon" size="16px"><Warning /></el-icon>
            </template>
          </el-popover>
        </div>
        <div class="overview-num">{{ toThousandFilter(topData.orderAccCard?.accumulateCount) }}</div>
        <div class="yesterday-add">
          <span class="add-label">昨日新增数</span>
          <span class="add-num">{{ toThousandFilter(topData.orderAccCard?.netGrowthCount) }}</span>
        </div>
      </div>
      <div class="overview-box">
        <div class="overview-title flx-align-center">
          <span>产品商机数</span>
          <el-popover :width="232" placement="right" trigger="hover" content="截止至昨天，累计所有的产品商机数">
            <template #reference>
              <el-icon class="icon" size="16px"><Warning /></el-icon>
            </template>
          </el-popover>
        </div>
        <div class="overview-num">{{ toThousandFilter(topData.productBusinessCard?.accumulateCount) }}</div>
        <div class="yesterday-add">
          <span class="add-label">昨日新增数</span>
          <span class="add-num">{{ toThousandFilter(topData.productBusinessCard?.netGrowthCount) }}</span>
        </div>
      </div>
      <div class="overview-box">
        <div class="overview-title flx-align-center">
          <span>解决方案商机数</span>
          <el-popover :width="232" placement="right" trigger="hover" content="截止至昨天，累计所有的解决方案商机数">
            <template #reference>
              <el-icon class="icon" size="16px"><Warning /></el-icon>
            </template>
          </el-popover>
        </div>
        <div class="overview-num">{{ toThousandFilter(topData.solutionBusinessCard?.accumulateCount) }}</div>
        <div class="yesterday-add">
          <span class="add-label">昨日新增数</span>
          <span class="add-num">{{ toThousandFilter(topData.solutionBusinessCard?.netGrowthCount) }}</span>
        </div>
      </div>
    </div>
    <div class="second-box">
      <div class="board-box card" v-if="isPortalTrafficAnalysis">
        <div class="content-title">
          <span>门户访问量</span>
        </div>
        <div class="data-box">
          <el-statistic
            group-separator=","
            :precision="0"
            :value="portalInfo.total"
            :value-style="{ fontWeight: 600 }"
            :title="'累计访问量'"
          >
            <template #suffix>
              <span class="rate">
                {{ portalInfo.totalMom }}
                <HBArrow
                  v-if="portalInfo.totalMom"
                  :value="checkNumberRate(portalInfo.totalMom)"
                  :icon-style="{ fontSize: '14px' }"
                />
              </span>
            </template>
          </el-statistic>
          <div class="portal-line-box">
            <portal-line-chart ref="portalLineChartRef" />
          </div>
          <div class="table-box custom-list">
            <pro-table
              ref="proTable"
              title="门户访问量按日期统计分析总列表"
              :request-auto="false"
              :columns="columns"
              :tool-button="false"
              :pagination="false"
              :data="tableData"
              :height="220"
            >
              <template #pvCumMom="{ row }">
                {{ row.pvCumMom }}
                <HBArrow v-if="row.pvCumMom !== '-'" :value="checkNumberRate(row.pvCumMom)" />
              </template>
            </pro-table>
          </div>
        </div>
      </div>
      <div class="board-box card" v-if="isRegisteredUsersAnalysis">
        <div class="content-title">
          <span>注册用户数</span>
        </div>
        <div class="data-box">
          <el-statistic
            group-separator=","
            :precision="0"
            :value="registeredUsersInfo.total"
            :value-style="{ fontWeight: 600 }"
            :title="'总注册用户数'"
          >
            <template #suffix>
              <span class="rate">
                {{ registeredUsersInfo.totalMom }}
                <HBArrow
                  v-if="registeredUsersInfo.totalMom"
                  :value="checkNumberRate(registeredUsersInfo.totalMom)"
                  :icon-style="{ fontSize: '14px' }"
                />
              </span>
            </template>
          </el-statistic>
          <div class="chart-box">
            <pie-chart ref="registeredUsersRef" />
          </div>
        </div>
      </div>
    </div>
    <div class="board-box card" v-if="isOrderAnalysis">
      <div class="content-title">
        <span>产品订单</span>
        <div class="has-choice">
          <span @click="onChangeOrderDate('day')" :class="['choice-option', orderDateChoice === 'day' ? 'active' : 'default']">
            近一周
          </span>
          <span
            @click="onChangeOrderDate('month')"
            :class="['choice-option', orderDateChoice === 'month' ? 'active' : 'default']"
          >
            近一月
          </span>
        </div>
      </div>
      <div class="data-box">
        <div class="product-data">
          <div class="order-main">
            <indicator-button
              :indicator-title="'订单指标'"
              :list="orderIndex"
              :active="activeBtn"
              @item-click="onChangeOrder"
            ></indicator-button>
            <indicator-button
              :indicator-title="'财务指标'"
              :list="financialIndex"
              :active="activeBtn"
              @item-click="onChangeOrder"
            ></indicator-button>
            <div class="chart-box">
              <line-chart ref="orderLineChartRef" />
            </div>
          </div>
          <div class="order-aside">
            <ranking-list :ranking-title="'产品排名'" :ranking-list="rankingData" @on-more="onMore"></ranking-list>
          </div>
        </div>
      </div>
    </div>
    <div class="board-box card" v-if="isProductAnalysis && isSolutionAnalysis">
      <div class="content-title product-box">
        <div class="product-btn">
          <span
            v-for="item in productType"
            :class="['product-option', productTypeChoice === item.value ? 'active' : 'default']"
            @click="onChangeProductType(item.value)"
            :key="item.value"
          >
            {{ item.label }}
          </span>
        </div>
        <div class="has-choice">
          <span
            @click="onChangeProductDate('day')"
            :class="['choice-option', productDateChoice === 'day' ? 'active' : 'default']"
          >
            近一周
          </span>
          <span
            @click="onChangeProductDate('month')"
            :class="['choice-option', productDateChoice === 'month' ? 'active' : 'default']"
          >
            近一月
          </span>
        </div>
      </div>
      <div class="data-box">
        <div class="product-data">
          <div class="order-main">
            <indicator-button
              :indicator-title="'数据指标'"
              :list="dataIndicatorIndex"
              :active="productActiveBtn"
              @item-click="onChangeProduct"
            ></indicator-button>
            <div class="chart-box"><bar-chart ref="productBarChartRef" chart-id="productBarChartId" /></div>
          </div>
          <div class="order-aside">
            <ranking-list :ranking-title="rankingTitle" :ranking-list="productRankingData" @on-more="onMoreProduct">
            </ranking-list>
          </div>
        </div>
      </div>
    </div>
    <div class="board-box card" v-if="isChannelAnalysis">
      <div class="content-title">
        <span>渠道分析</span>
        <div class="has-choice">
          <span
            @click="onChangeChannelDate('day')"
            :class="['choice-option', channelDateChoice === 'day' ? 'active' : 'default']"
          >
            近一周
          </span>
          <span
            @click="onChangeChannelDate('month')"
            :class="['choice-option', channelDateChoice === 'month' ? 'active' : 'default']"
          >
            近一月
          </span>
        </div>
      </div>
      <div class="data-box">
        <channel-chart-content
          :params="channelParams"
          ref="channelChartRef"
          channel-line-id="channelLineIdHome"
          channel-pie-id="channelPieIdHome"
        />
      </div>
    </div>
    <div class="board-box card" v-if="isProductUsageAnalysis">
      <div class="content-title">
        <span>产品使用分析</span>
        <div class="has-choice">
          <span
            @click="onChangeProductUsageDate('day')"
            :class="['choice-option', productUsageDateChoice === 'day' ? 'active' : 'default']"
          >
            近一周
          </span>
          <span
            @click="onChangeProductUsageDate('month')"
            :class="['choice-option', productUsageDateChoice === 'month' ? 'active' : 'default']"
          >
            近一月
          </span>
        </div>
      </div>
      <div class="data-box">
        <product-usage :params="productUsageParams" ref="productUsageChartRef" product-usage-line-id="productUsageLineHome" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { watch, ref, reactive, onMounted, toRaw, computed } from "vue";
import { ECharts } from "echarts";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import IndicatorButton from "@/components/IndicatorButton/index.vue";
import rankingList from "./components/rankingList.vue";
import lineChart from "./components/lineChart.vue";
import pieChart from "./components/pieChart.vue";
import portalLineChart from "./components/portalLineChart.vue";
import { toThousandFilter } from "@/utils/tools";
import HBArrow from "@/components/HBArrow/index.vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { getBusinessSimpleView, getPortalUserTotalCount, getAccessTotalCount } from "@/api/modules/home";
import { getBusinessAnalysis, getBusinessAnalysisTop } from "@/api/modules/statisticAnalysis";
import { getOrderDateChat, getOrderProductTop } from "@/api/modules/statisticAnalysis";
import barChart from "@/views/statisticAnalysis/productAnalysis/components/barChart.vue";
import channelChartContent from "@/views/statisticAnalysis/channelAnalysis/components/channelChartContent.vue";
import productUsage from "@/views/statisticAnalysis/productUsageAnalysis/components/chartContent.vue";
import { useDict } from "@/hooks/useDict";
import { getAuthMenuListApi } from "@/api/modules/login";

const { portal_user_source } = useDict("portal_user_source");

interface IndexType {
  label: string;
  value: string;
}

interface UserChartType {
  num: string;
  numPer: string;
  userSource: string;
  name?: string;
  value?: string;
}

interface TopItemType {
  accumulateCount?: number; // 累计数
  netGrowthCount?: number; // 新增数
}
interface TopDataType {
  orderAccCard: TopItemType;
  productBusinessCard: TopItemType;
  solutionBusinessCard: TopItemType;
}

const router = useRouter();
// 获取子组件的ref
interface ChartExpose {
  initChart: (params: any, title?: string) => ECharts;
}
const orderLineChartRef = ref<ChartExpose>();
const productBarChartRef = ref<ChartExpose>();
const registeredUsersRef = ref<ChartExpose>();
const portalLineChartRef = ref<ChartExpose>();
const channelChartRef = ref();
const productUsageChartRef = ref();
const rankingData = ref<any>([]);
const productRankingData = ref<any>([]);

const orderDateChoice = ref("day");
const productDateChoice = ref("day");
const channelDateChoice = ref("day");
const productUsageDateChoice = ref("day");
const activeBtn = ref("1");
const productActiveBtn = ref("");

const isPortalTrafficAnalysis = ref(true);
const isRegisteredUsersAnalysis = ref(true);
const isProductAnalysis = ref(true);
const isSolutionAnalysis = ref(true);
const isOrderAnalysis = ref(true);
const isChannelAnalysis = ref(true);
const isProductUsageAnalysis = ref(true);
let menuList = ref([] as any); //统计分析菜单权限
const getMenus = async () => {
  await getAuthMenuListApi().then((result: { data: any }) => {
    let index = result.data.menus.findIndex(item => item.id == "1740554311474630657");
    // console.log("here!!!!!!!!!!!!!!!!!!!!!!!!!!11");
    // console.log(index);
    // console.log(menuList.value);
    menuList.value = result.data.menus[index].children as Array<any>;
    if (!menuList.value.some((menuList: { id: string }) => menuList.id === "1744189741252227074")) {
      isPortalTrafficAnalysis.value = false;
    }
    if (!menuList.value.some((menuList: { id: string }) => menuList.id === "1744295840928256002")) {
      isRegisteredUsersAnalysis.value = false;
    }
    if (!menuList.value.some((menuList: { id: string }) => menuList.id === "1743200003531747329")) {
      isProductAnalysis.value = false;
    }
    if (!menuList.value.some((menuList: { id: string }) => menuList.id === "1744173114926313473")) {
      isSolutionAnalysis.value = false;
    }
    if (!menuList.value.some((menuList: { id: string }) => menuList.id === "1740555561624686594")) {
      isOrderAnalysis.value = false;
    }
    if (!menuList.value.some((menuList: { id: string }) => menuList.id === "1751790739202310146")) {
      isChannelAnalysis.value = false;
    }
    if (!menuList.value.some((menuList: { id: string }) => menuList.id === "1751897486214627330")) {
      isProductUsageAnalysis.value = false;
    }
    // console.log(menuList.value);
    // console.log("111111111111111111");
    // console.log(isPortalTrafficAnalysis.value);
    // console.log(isRegisteredUsersAnalysis.value);
    // console.log(isProductAnalysis.value);
    // console.log(isSolutionAnalysis.value);
    // console.log(isOrderAnalysis.value);
    // console.log(isChannelAnalysis.value);
    // console.log(isProductUsageAnalysis.value);
    // console.log(menuList.value.some((menuList: { id: string }) => menuList.id === "1744189741252227073"));
  });
};

const rankingTitle = computed(() => (productTypeChoice.value === "product" ? "产品商机排名" : "解决方案商机排名"));

const productTypeChoice = ref("product");
const productType = [
  {
    label: "产品商机",
    value: "product"
  },
  {
    label: "解决方案商机",
    value: "solution"
  }
];
// 订单分析指标类型枚举：1-订购订单数、2-退订订单数、3-净增订单数、4-累计生效订单数、5-交易金额GMV、6-累计交易金额GMV
const orderIndex = [
  {
    label: "订购订单数",
    value: "1"
  },
  {
    label: "退订订单数",
    value: "2"
  },
  {
    label: "净增订单数",
    value: "3"
  },
  {
    label: "累计生效订单数",
    value: "4"
  }
];
const financialIndex = [
  {
    label: "交易金额GMV",
    value: "5"
  },
  {
    label: "累计交易金额GMV",
    value: "6"
  }
];
const nameList: IndexType[] = [...orderIndex, ...financialIndex];

const dataIndicatorIndex = [
  {
    label: "全部",
    value: ""
  },
  {
    label: "新增",
    value: "increase"
  },
  {
    label: "累计",
    value: "accumulate"
  }
];

// 获取当前日期并格式化为YYYY-MM-DD形式
const todayDate = dayjs().format("YYYY-MM-DD");
const yesterdayDate = dayjs().subtract(1, "day").format("YYYY-MM-DD");
const topData = reactive<TopDataType>({
  orderAccCard: {},
  productBusinessCard: {},
  solutionBusinessCard: {}
});

const getSimpleViewData = () => {
  getBusinessSimpleView({
    countEndDate: todayDate,
    countStartDate: yesterdayDate
  }).then((res: any) => {
    if (res.code === 200) {
      const { orderAccCard = {}, productBusinessCard = {}, solutionBusinessCard = {} } = res.data || {};
      topData.orderAccCard = orderAccCard;
      topData.productBusinessCard = productBusinessCard;
      topData.solutionBusinessCard = solutionBusinessCard;
    }
  });
};

const portalInfo = ref<{
  total: number | undefined;
  totalMom: string;
  chartInfo: any;
}>({
  total: undefined,
  totalMom: "",
  chartInfo: []
});

const registeredUsersInfo = ref<{
  total: number | undefined;
  totalMom: string;
  chartInfo: any;
}>({
  total: undefined,
  totalMom: "",
  chartInfo: []
});

// 访问量
const tableData = ref([]);
// 表格配置项
const columns: ColumnProps<any>[] = [
  { prop: "indexDate", label: "月份" },
  { prop: "pvCum", label: "累计访问量" },
  { prop: "pvNa", label: "新增访问量", sortable: true },
  { prop: "pvCumMom", label: "月环比", sortable: true }
];

const getPortalUser = () => {
  if (isPortalTrafficAnalysis.value) {
    // 门户访问量
    getAccessTotalCount().then((res: any) => {
      if (res.code === 200) {
        const { totalPvCum, totalPvCumMom = "", indexCount = [] } = res.data || {};
        portalInfo.value = {
          total: Number(totalPvCum) ?? undefined,
          totalMom: totalPvCumMom,
          chartInfo: indexCount
        };
        tableData.value = indexCount;
        const nameList = indexCount?.map((item: any) => item.indexDate);
        const pvCumList = indexCount?.map((item: any) => item.pvCum);
        portalLineChartRef.value?.initChart({
          nameList,
          data: pvCumList
        });
      }
    });
  }
  if (isRegisteredUsersAnalysis.value) {
    // 注册用户数
    getPortalUserTotalCount().then((res: any) => {
      if (res.code === 200) {
        const { total, totalMom = "", userSourceCount = [] } = res.data || {};
        registeredUsersInfo.value = {
          total: Number(total) ?? undefined,
          totalMom,
          chartInfo: userSourceCount
        };
      }
    });
  }
};
// 产品订单
const startDate = dayjs(new Date()).subtract(7, "days").format("YYYY-MM-DD");
const endDate = dayjs().subtract(1, "days").format("YYYY-MM-DD");
const thirtyDaysAgo = dayjs().subtract(31, "days").format("YYYY-MM-DD");

let orderParams = {
  countStartDate: startDate,
  countEndDate: endDate,
  orderAnalysisTypeKey: activeBtn.value,
  dateType: "day"
};
// 图表
const getOrderChat = (params = orderParams) => {
  getOrderDateChat({
    ...params
  }).then((res: any) => {
    if (res.code === 200) {
      const { dateList = [], countList = [] } = res.data;

      const currentItem = nameList.filter(item => item.value === activeBtn.value);
      orderLineChartRef.value?.initChart(
        {
          data: countList,
          nameList: dateList
        },
        currentItem.length ? currentItem[0].label : ""
      );
    }
  });
};

// 排名
const getOrderTop = (params = orderParams) => {
  getOrderProductTop({
    ...params
  }).then(res => {
    if (res.code === 200) {
      rankingData.value = res.data;
    }
  });
};

// 产品商机/解决方案商机
let commonParams = {
  countEndDate: endDate,
  countStartDate: startDate,
  dateType: "day",
  itemId: "",
  providerId: "",
  accumulateOrIncrease: productActiveBtn.value,
  adviceType: productTypeChoice.value === "product" ? 1 : 2 // 商机分析指标类型枚举：1-产品商机、2-解决方案商机，不传就全类型查
};

// 图表
const getProductChat = () => {
  getBusinessAnalysis({
    ...commonParams
  }).then((res: any) => {
    if (res.code === 200) {
      const { list = [] } = res.data;
      // 图表数据处理
      const dateList: number[] = [],
        addList: number[] = [],
        accumulateList: string[] = [];
      if (list && list.length > 0) {
        list.forEach((item: any) => {
          dateList.push(item.dateWithoutYear);
          addList.push(item.netGrowthCount);
          accumulateList.push(item.accumulateCount);
        });
      }
      productBarChartRef.value?.initChart({
        data1: productActiveBtn.value === "accumulate" ? [] : addList,
        data2: productActiveBtn.value === "increase" ? [] : accumulateList,
        nameList: dateList
      });
    }
  });
};

// 排名
const getProductTop = () => {
  getBusinessAnalysisTop({
    ...commonParams,
    accumulateOrIncrease: productActiveBtn.value ? productActiveBtn.value : "accumulate" // 全部传“accumulate”
  }).then(res => {
    if (res.code === 200) {
      const ranking: any = res.data || [];
      productRankingData.value = ranking.map((item: any) => {
        return {
          ...item,
          productName: item.itemName,
          count: activeBtn.value === "increase" ? item.netGrowthCount : item.accumulateCount
        };
      });
    } else {
      rankingData.value = [];
    }
  });
};
const getProductInfo = () => {
  getProductChat();
  getProductTop();
};
// 切换 tabs(产品分析/解决方案分析)
const onChangeProductType = (type: string) => {
  productTypeChoice.value = type;
  productActiveBtn.value = ""; // 重置数据指标
  commonParams.adviceType = type === "product" ? 1 : 2;
  getProductInfo();
};

// 切换数据指标
const onChangeProduct = (values: any) => {
  productActiveBtn.value = values.value;
  commonParams.accumulateOrIncrease = values.value;
  getProductInfo();
};
const onChangeProductDate = (type = "day") => {
  productDateChoice.value = type;
  commonParams = {
    ...commonParams,
    countStartDate: type == "day" ? startDate : thirtyDaysAgo
  };
  getProductInfo();
};

// 渠道分析
let channelParams = reactive({
  countEndDate: endDate,
  countStartDate: startDate
});
const onChangeChannelDate = (type = "day") => {
  channelDateChoice.value = type;
  channelParams = {
    ...channelParams,
    countStartDate: type == "day" ? startDate : thirtyDaysAgo
  };

  channelChartRef.value.getChannelCharts({ ...channelParams });
};
// 产品使用分析
let productUsageParams = reactive({
  endTime: endDate,
  startTime: startDate,
  dataType: 0,
  dataId: [],
  providerId: "",
  indexUnit: "day"
});
const onChangeProductUsageDate = (type = "day") => {
  productUsageDateChoice.value = type;
  productUsageParams = {
    ...productUsageParams,
    startTime: type == "day" ? startDate : thirtyDaysAgo
  };

  productUsageChartRef.value.getChat({ ...productUsageParams });
};

watch(
  [() => portal_user_source.value, () => registeredUsersInfo.value.chartInfo],
  ([userSourceDict, userChartData]) => {
    if (userChartData && userChartData.length > 0) {
      const data = toRaw(userChartData).map((item: UserChartType) => {
        const source = toRaw(userSourceDict).find((dict: any) => dict.itemKey === item.userSource);
        item.name = source?.itemValue;
        item.value = item.num;
        return item;
      });
      registeredUsersRef.value?.initChart({
        chartData: data,
        total: toThousandFilter(registeredUsersInfo.value?.total)
      });
    }
  },
  {
    immediate: true,
    deep: true
  }
);

onMounted(async () => {
  await getMenus();
  getSimpleViewData();
  getPortalUser();

  getOrderChat();
  getOrderTop();

  getProductInfo();

  // 渠道分析
  channelChartRef.value.getChannelCharts({ ...channelParams });
  productUsageChartRef.value.getChat({ ...productUsageParams });
});

const checkNumberRate = (value: string) => {
  const re = /^[0 -9]+\.?\d*$/;
  if (!re.test(value)) {
    return "";
  } else {
    //转换为数字格式
    const rat = value.replace(/%/g, "");
    const val = parseFloat(rat);
    if (val > 0) {
      return "1";
    } else if (val < 0) {
      return "2";
    } else {
      return "";
    }
  }
};

const onChangeOrder = (values: any) => {
  activeBtn.value = values.value;
  orderParams = {
    ...orderParams,
    orderAnalysisTypeKey: activeBtn.value
  };
  getOrderChat(orderParams);
  getOrderTop(orderParams);
};
const onChangeOrderDate = (type = "day") => {
  orderDateChoice.value = type;
  orderParams = {
    ...orderParams,
    countStartDate: type == "day" ? startDate : thirtyDaysAgo
  };
  getOrderChat(orderParams);
  getOrderTop(orderParams);
};

const onMore = () => {
  router.push({
    path: "/statisticAnalysis/orderAnalysis/orderDimension",
    query: {
      dateType: orderParams.dateType,
      countDate: [orderParams.countStartDate, orderParams.countEndDate]
    }
  });
};

const onMoreProduct = () => {
  const path =
    commonParams.adviceType === 1
      ? "/statisticAnalysis/productAnalysis/productDimension"
      : "/statisticAnalysis/solutionAnalysis/solutionDimension";
  router.push({
    path,
    query: {
      dateType: commonParams.dateType,
      countDate: [commonParams.countStartDate, commonParams.countEndDate]
    }
  });
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
