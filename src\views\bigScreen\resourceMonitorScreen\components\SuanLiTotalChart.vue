<template>
  <!-- 算力接入总数 -->
  <div id="SuanLiTotalChart" class="echarts"></div>
</template>

<script setup lang="ts">
import { ECharts, EChartsOption, init } from "echarts";
import * as echarts from "echarts";
interface ChartProp {
  label: string;
  value: string[];
}
const initChart = (data: any = {}): ECharts => {
  const charEle = document.getElementById("SuanLiTotalChart") as HTMLElement;
  const charEch: ECharts = init(charEle);
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        z: 1,
        type: "shadow",
        shadowStyle: {
          color: "rgba(255,255,255,0.1)"
        }
      },
      borderWidth: 0, //边框线宽
      padding: 0,
      backgroundColor: "transparent",
      formatter: (p: any) => {
        let str = "";
        p.forEach((val: any, index: number) => {
          str += `
          <div class="year-item">
            <span class="year-dot" style="background-color: ${data.colors[index]};"></span>
            <span class="year-name">${val.seriesName}</span>
            <span class="year-value">
                ${val.data >= 10000 ? (val.data / 10000).toFixed(2) + "w" : val.data}${index === 1 ? "%" : ""}
            </span>
          </div>
          `;
        });
        let dom = `
                    <div class="annual-tooTip">
                      <span class="annual-month">${p[0].name}月</span>
                      <div class="annual-list">
                        ${str}
                      </div>
                    </div>
                  `;
        return dom;
      }
    },
    legend: {
      right: "2%",
      top: "0%",
      itemWidth: 7,
      itemHeight: 7,
      align: "auto",
      icon: "rect",
      itemGap: 15,
      textStyle: {
        color: "#ebebf0"
      }
    },
    grid: {
      top: "17%",
      left: "3",
      right: "10",
      bottom: "5",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        axisLine: {
          show: true,
          lineStyle: {
            color: "#2C4BB8"
          }
        },
        axisLabel: {
          // 坐标轴刻度标签的相关设置
          color: "#C6DAF0",
          padding: 0,
          fontSize: 16,
          fontFamily: "PingFangSC-Regular,PingFang SC",
          formatter: function (value, index) {
            // if (index === data.columns.length - 1) {
            //   return value + "月";
            // } else {
            //   return value;
            // }
            return value;
          }
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        data: data.columns
      }
    ],
    yAxis: [
      {
        name: "PFlops",
        nameTextStyle: {
          color: "#C6DAF0",
          fontSize: 16,
          fontFamily: "PingFangSC-Regular,PingFang SC",
          padding: [0, 30, 0, 0]
        },
        // nameGap:18,
        minInterval: 1,
        // min: 4,
        splitNumber: 5,
        splitLine: {
          show: true,
          lineStyle: {
            color: "#2541A1",
            type: "dashed"
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#2C4BB8"
          }
        },
        axisLabel: {
          show: true,
          color: "#C6DAF0",
          fontSize: 16,
          fontFamily: "PingFangSC-Regular,PingFang SC",
          padding: 0,
          formatter: function (value: any) {
            if (value >= 10000) {
              value = value / 10000 + "w";
            }
            return value;
          }
        },
        axisTick: {
          show: false
        },
        boundaryGap: true
      },
      {
        // name: "%",
        nameTextStyle: {
          color: "#C6DAF0",
          fontSize: 16,
          fontFamily: "PingFangSC-Regular,PingFang SC",
          padding: [0, 0, 0, 0]
        },
        // nameGap:18,
        // min: 4,
        splitLine: {
          show: false,
          lineStyle: {
            color: "#2541A1",
            type: "dashed"
          }
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true,
          color: "#C6DAF0",
          fontSize: 16,
          fontFamily: "PingFangSC-Regular,PingFang SC",
          padding: 0,
          formatter: "{value} %"
        },
        axisTick: {
          show: false
        },
        boundaryGap: true
      }
    ],
    series: [
      {
        name: data.data[0].label,
        type: "bar",
        yAxisIndex: 0,
        barWidth: 10,
        barGap: "-10%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: "#fff"
              },
              {
                offset: 0.06,
                color: "#3B92FF"
              },
              {
                offset: 1,
                color: "rgba(59,146,255,0.05)"
              }
            ],
            false
          )
        },
        tooltip: {
          show: true
        },
        data: data.data[0].value
      },
      {
        name: data.data[1].label,
        type: "line",
        smooth: true,
        showSymbol: false,
        yAxisIndex: 1,
        lineStyle: {
          width: 1,
          color: data.colors[1] // 线条颜色
        },
        itemStyle: {
          color: data.colors[1]
        },
        tooltip: {
          show: true
        },
        data: data.data[1].value
      }
    ]
  };
  charEch.setOption(option);
  return charEch;
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
:deep(.annual-tooTip) {
  box-sizing: border-box;
  height: 100px;
  padding: 5px 16px;
  background: rgb(3 11 27 / 80%);
  border: 1px solid #5a7dc0;
  .annual-month {
    display: inline-block;
    margin-bottom: 6px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
  }
  .annual-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    .year-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 22px;
      margin-bottom: 5px;
      font-family: PingFangSC-Regular, "PingFang SC";
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      .year-dot {
        width: 8px;
        height: 8px;
        margin-right: 5px;
      }
      .year-name {
        width: 100px;
        margin: 0 2px;
      }
      .year-value {
        display: inline-block;

        // margin-left: 30px;
      }
    }
  }
}
</style>
