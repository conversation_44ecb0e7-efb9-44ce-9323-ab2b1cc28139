<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="解决方案商机列表"
      :tool-button="false"
      :columns="columns"
      :request-api="getTableList"
      :request-auto="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
    <!-- <InfoDrawer ref="drawerRef" /> -->
    <Modal ref="modalRef"></Modal>
  </div>
</template>

<script setup lang="tsx" name="solutionBusiness">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { useDownload } from "@/hooks/useDownload";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getBusinessList, businessExcel } from "@/api/modules/business";
import InfoDrawer from "./components/infoDrawer.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

import Modal from "../components/modal.vue";
import { modalOpenType } from "../components/modal";

const router = useRouter();
const proTable = ref<ProTableInstance>();
const drawerRef = ref<InstanceType<typeof InfoDrawer> | null>(null);
const modalRef = ref<{ handleOpen: (data: modalOpenType) => void } | null>(null);

onMounted(() => {
  proTable.value?.getTableList();
});

// 表格配置项
const columns: ColumnProps<any>[] = [
  // { type: "selection", fixed: "left", width: 50 },
  { prop: "itemName", label: "咨询方案", search: { el: "input", props: { maxlength: "20" } } },
  { prop: "itemCategory", label: "方案分类" },
  { prop: "contactName", label: "联系人", width: 110 },
  { prop: "phone", label: "手机号码", width: 120 },
  { prop: "email", label: "邮箱", width: 150 },
  { prop: "userAccount", label: "登录账号", width: 100 },
  { prop: "companyName", label: "公司名称" },
  { prop: "regionName", label: "所在地" },
  {
    prop: "createTime",
    label: "咨询时间",
    sortable: true,
    width: 170,
    search: {
      el: "date-picker",
      span: 2,
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" }
    }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  delete newParams.createTime;
  newParams.adviceType = 2;
  newParams.descs = "createTime";
  return getBusinessList(newParams);
};
const downloadFile = () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  delete newParams.createTime;
  newParams.adviceType = 2;
  newParams.descs = "createTime";
  ElMessageBox.confirm("确认导出解决方案商机列表?", "提示", { type: "warning" }).then(() =>
    useDownload(businessExcel, newParams)
  );
};

const toView = (row: any = {}) => {
  // drawerRef.value?.acceptParams({
  //   isView: true,
  //   title: "商机详情",
  //   row: row
  // });
  modalRef.value?.handleOpen({
    isView: true,
    title: "商机详情",
    row: row
  });
};
// 导出解决列表
// const downloadFile = async () => {
//   let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
//   newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
//   newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
//   delete newParams.createTime;
//   ElMessageBox.confirm("确认导出解决方案列表?", "提示", { type: "warning" }).then(() =>
//     useDownload(exportSolution, "解决方案列表", newParams)
//   );
// };
</script>
