import http from "@/api";
import type { CoreSystem } from "@/api/interface/coreSystem";

/* 用户配额列表 */
export const queryUserQuotaList = async (params?: CoreSystem.UserQuotaSearch) => {
  return await http.get<Array<CoreSystem.UserQuotaItem>>("/cnud-desk/admin/quota/userQuota/pageList", params);
};
/* 用户配额详情 */
export const queryUserQuotaDetail = async (id: string) => {
  return await http.get<CoreSystem.UserQuotaItemData>("/cnud-desk/admin/quota/userQuota/detail", { id });
};
/* 用户配额修改 */
export const queryUserQuotaUpdate = async (data: CoreSystem.UserQuotaItemData) => {
  return await http.post<CoreSystem.UserQuotaItem>("/cnud-desk/admin/quota/userQuota/update", data);
};

/**
 * 设置用户阀值
 * @param data
 * @returns
 */
export const updateUserQuotaThreshold = async (data: {
  thresholdBalance: number;
  userId?: string;
  totalCpuCount: number;
  totalGpuCount: number;
  totalMemoryCount: number;
}) => {
  return await http.post("/cnud-desk/admin/quota/userQuota/threshold", data);
};

/**
 * 获取用户阀值
 * @returns
 */
export const queryGetThreshold = async () => {
  return await http.get<{ thresholdBalance: number; totalCpuCount: number; totalGpuCount: number; totalMemoryCount: number }>(
    "/cnud-desk/admin/quota/userQuota/getThreshold "
  );
};
