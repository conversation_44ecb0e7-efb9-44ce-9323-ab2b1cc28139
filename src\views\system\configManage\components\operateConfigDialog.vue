<template>
  <div>
    <el-dialog
      destroy-on-close
      v-model="dialogVisible"
      :title="editTypeChinese[props.type] + '参数'"
      width="50%"
      @close="onCancel"
    >
      <el-form ref="ruleFormRef" :model="configForm" :rules="rules" label-position="left">
        <el-form-item label="参数名称" prop="paramName">
          <el-input v-model="configForm.paramName" placeholder="请输入参数名称" maxlength="255" clearable></el-input>
        </el-form-item>
        <el-form-item label="参数键名" prop="paramKey">
          <el-input v-model="configForm.paramKey" placeholder="请输入参数键名" maxlength="255" clearable></el-input>
        </el-form-item>
        <el-form-item label="输入框类型">
          <el-radio-group v-model="configForm.formatType">
            <el-radio :label="1">输入框</el-radio>
            <el-radio :label="2">富文本框</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="参数键值" prop="paramValue">
          <el-input
            v-model="configForm.paramValue"
            placeholder="请输入参数键值"
            clearable
            v-if="configForm.formatType === 1"
          ></el-input>
          <WangEditor v-if="configForm.formatType === 2" v-model:value="configForm.paramValue" height="400px" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="configForm.remark" type="textarea" placeholder="请输入备注" maxlength="255"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="operateConfigDialog">
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import WangEditor from "@/components/WangEditor/index.vue";
import { computed, ref, reactive, watch } from "vue";
import type { FormInstance } from "element-plus";
import { saveParamConfig, updateParamConfig } from "@/api/modules/system";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: 1
  },
  params: {
    type: Object,
    default: () => {}
  }
});

const ruleFormRef = ref();
const dialogVisible = ref(false);
const configForm = ref<any>({
  paramName: "",
  paramKey: "",
  paramValue: "",
  remark: "",
  formatType: 1
});
const emits = defineEmits(["update:visible", "cancel", "confirm"]);

const onCancel = () => {
  ruleFormRef.value.resetFields();
  emits("cancel");
  dialogVisible.value = false;
};

const rules = reactive({
  paramName: [{ required: true, trigger: "change", message: "请输入参数名称" }],
  paramKey: [{ required: true, trigger: "change", message: "请输入参数键名" }],
  paramValue: [{ required: true, trigger: "change", message: "请输入参数键值" }]
});
const getNewLabelName = (itemIndex: any): string => {
  let sameNumber = 0;
  const name = `新标签${itemIndex}`;
  configForm.value?.list?.forEach((item: any) => {
    item.name === name ? sameNumber++ : void 0;
  });
  //
  if (sameNumber >= 1) {
    return getNewLabelName(++itemIndex);
  } else {
    return name;
  }
};
// const dialogVisible = computed({
//   get: () => {
//     return props.visible;
//   },
//   set: value => {
//     if (props.visible) {
//       configForm.value = {
//         name: "",
//         list: []
//       };
//     }
//     emits("update:visible", value);
//   }
// });

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: saveParamConfig,
        [editType["edit"]]: updateParamConfig
      };
      const requestParamsObject = {
        [editType["add"]]: () => {
          return {
            paramName: configForm.value.paramName,
            paramKey: configForm.value.paramKey,
            paramValue: configForm.value.paramValue,
            remark: configForm.value.remark,
            formatType: configForm.value.formatType
          };
        },
        [editType["edit"]]: () => {
          return {
            paramName: configForm.value.paramName,
            paramKey: configForm.value.paramKey,
            paramValue: configForm.value.paramValue,
            remark: configForm.value.remark,
            formatType: configForm.value.formatType,
            id: props.params.id
          };
        }
      };
      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        emits("confirm");
        ElMessage.success(res.msg);
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};

watch(
  () => props.visible,
  nVal => {
    // if (!nVal) {
    //   configForm.value.paramName = "";
    //   configForm.value.paramKey = "";
    //   configForm.value.paramValue = "";
    //   configForm.value.remark = "";
    //   configForm.value.formatType = 1;
    // }
    dialogVisible.value = props.visible;
  }
);
watch(
  () => props.params,
  () => {
    if (props.visible) {
      configForm.value.paramName = props.params.paramName || "";
      configForm.value.paramKey = props.params.paramKey || "";
      configForm.value.paramValue = props.params.paramValue || "";
      configForm.value.remark = props.params.remark || "";
      configForm.value.formatType = props.params.formatType || 1;
    }
  }
);
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
</style>
