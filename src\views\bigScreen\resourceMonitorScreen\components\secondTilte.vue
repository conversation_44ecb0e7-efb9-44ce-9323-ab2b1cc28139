<template>
  <div class="screen-second-title">
    <span class="second-title">{{ str }}</span>
    <span v-if="unit" class="unit">单位：{{ unit }}</span>
  </div>
</template>

<script setup lang="ts">
defineProps({
  str: {
    type: String,
    default: ""
  },
  unit: {
    type: String,
    default: ""
  }
});
</script>

<style scoped lang="scss">
.screen-second-title {
  width: 100%;
  height: 42px;
  margin-bottom: 12px;
  line-height: 42px;
  background: url("@/assets/images/screen/second-title.png") no-repeat;
  background-size: 100% 100%;
  .second-title {
    margin-left: 20px;
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 0 6px #4584fd;
  }
  .unit {
    float: right;
    font-size: 14px;
    font-weight: 400;
    color: #90afd0;
  }
}
</style>
