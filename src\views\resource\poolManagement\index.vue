<template>
  <div class="table-box">
    <!--request-auto="false" 不自动调接口-->
    <ProTable
      ref="tableRef"
      title="资源列表"
      :request-auto="false"
      :tool-button="false"
      :columns="columns"
      :data-callback="dataCallback"
      :init-param="initParam"
      :request-api="getTableList"
      :page-param-key="pageParamKey"
      :pagination="true"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="addNewItem()">新增</el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="onDelete(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'update'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="onEdit(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <editDialog v-bind="dialogParams" v-model:visible="dialogParams.visible" @confirm="requeryTableList"></editDialog>
  </div>
</template>

<script setup lang="tsx" name="poolManagement">
import { ref, onMounted, onActivated, computed, reactive } from "vue";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance } from "@/components/ProTable/interface";
import { useDicStore } from "@/stores/modules/dictionaty";
import { queryResourcePools, removeResourcePool } from "@/api/modules/resource";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { editType } from "@/enums/dialogTypeEnum";
import { Plus } from "@element-plus/icons-vue";
import editDialog from "./components/editDialog.vue";
const { BUTTONS } = useAuthButtons();
const pageParamKey = {
  current: "currentPage",
  size: "pageSize",
  total: "total"
};
const dictionaryStore = useDicStore();
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const tableRef = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total && pageNum && pageSize 这些字段，那么你可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data: any) => {
  return {
    ...data,
    records: data.dataList.map((item: any) => {
      return {
        ...item,
        status: item.status + ""
      };
    })
  };
};
const dialogParams = reactive({
  type: editType["add"],
  listItemInfo: {},
  visible: false
});
onMounted(() => {
  // 先获取字典
  dictionaryStore.getResourceOrderStatus(); // 状态
  // // 然后
  // requeryTableList();
});
// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  requeryTableList();
});
const resourcePoolStatus: any = computed(() => dictionaryStore.resourcePoolStatus);
// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  return queryResourcePools(params);
};
const requeryTableList = () => {
  tableRef.value?.getTableList();
};
//新增
const addNewItem = () => {
  dialogParams.type = editType["add"];
  dialogParams.listItemInfo = {};
  dialogParams.visible = true;
};
// 表格配置项
const columns = [
  // { type: "selection", fixed: "left", width: 50 },
  { prop: "resourcePoolName", label: "资源池名称", search: { el: "input" } },
  { prop: "resourcePoolNumber", label: "资源池ID", width: 200 },
  { prop: "code", label: "资源池代码", width: 260 },
  { prop: "cloudProvider", label: "云平台", width: 260 },
  { prop: "cloudProductVersion", label: "版本", width: 120 },
  { prop: "status", label: "状态", width: 100, enum: resourcePoolStatus },
  {
    prop: "createTime",
    label: "创建时间",
    width: 170,
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 100 }
];

const onDelete = async (item: any) => {
  await useHandleData(
    removeResourcePool,
    { cloudProvider: item.cloudProvider, resourcePoolNumber: item.resourcePoolNumber },
    `删除${item.resourcePoolName}`,
    "warning"
  );
  requeryTableList();
};
const onEdit = async (row: any) => {
  dialogParams.listItemInfo = {
    cloudProvider: row.cloudProvider,
    resourcePoolNumber: row.resourcePoolNumber
  };

  dialogParams.type = editType["edit"];
  dialogParams.visible = true;
};
</script>
