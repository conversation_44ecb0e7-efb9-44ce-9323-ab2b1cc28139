<template>
  <el-dialog v-model="dialogVisible" title="新增订单" align-center width="600px" destroy-on-close @close="close">
    <div class="dialog-content">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px" label-position="left" status-icon>
        <el-form-item label="客户名单" prop="fileList">
          <uploadFiles
            class="upload"
            v-model:file-list="ruleForm.fileList"
            :file-type="'.doc,.xls,.docx,.xlsx,.rar,.zip,.jpg,.gif,.jpeg,.png,.pdf'"
            :api="uploadFile"
            :limit="1"
            :file-size="5"
            :show-file-list="true"
            :drag="false"
            :width="'auto'"
            :height="'auto'"
            @upload-success="onUploadSuccess"
          >
            <template #trigger>
              <el-button type="primary">上传</el-button>
            </template>
            <template #tip>
              <div class="upload-tips">
                文件类型：.doc .xls .docx .xlsx .rar .zip .jpg .gif .jpeg .png .pdf，支持1个附件上传，文件大小不超过5M
              </div>
            </template>
          </uploadFiles>
        </el-form-item>
        <el-form-item label="供应商名称" prop="supplierName">
          <el-select v-model="ruleForm.supplierName" placeholder="请选择" clearable>
            <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="区域" prop="region">
          <el-select v-model="ruleForm.region" placeholder="请选择" clearable>
            <el-option v-for="item in regionOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单金额(元)" prop="orderAmountStr">
          <el-input v-model="ruleForm.orderAmountStr" type="number" placeholder="请输入" minlength="1" maxlength="50" clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="时间" prop="date">
          <el-date-picker
            v-model="ruleForm.date"
            type="datetimerange"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            range-separator="-"
            start-placeholder="请选择"
            end-placeholder="请选择"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="confirmLoading" @click="confirmClick">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, watch } from "vue";
import uploadFiles from "@/components/Upload/Files.vue";
import { ElMessage } from "element-plus";
import { useDicStore } from "@/stores/modules/dictionaty";
import { querySupplierList, saveOrder, uploadFile } from "@/api/modules/transactionVolumeFiling";

const emit = defineEmits(["addSuccess"]);

const dictionaryStore = useDicStore();

const dialogVisible = ref(false);

const title = ref("");

const confirmLoading = ref<boolean>(false);

const ruleFormRef = ref();

const ruleForm = reactive({
  fileList: [],
  supplierName: "",
  region: "",
  orderAmountStr: "",
  date: []
});

const rules = reactive({
  fileList: [
    {
      required: true,
      message: "请上传客户名单",
      trigger: "change"
    }
  ],
  supplierName: [
    {
      required: true,
      message: "请选择供应商名称",
      trigger: "change"
    }
  ],
  region: [
    {
      required: true,
      message: "请选择区域",
      trigger: "change"
    }
  ],
  orderAmountStr: [
    {
      required: true,
      message: "请输入订单金额",
      trigger: "change"
    }
  ],
  date: [
    {
      required: true,
      message: "请选择时间",
      trigger: "change"
    }
  ]
});

let fileId: string = "";

const regionOptions = computed(() => {
  return dictionaryStore.orderRegion;
});

const supplierOptions = ref<any[]>([]);

const querySupplierOptions = () => {
  const queryData = {
    // state: "1",
    current: 1,
    size: 10000
  };
  querySupplierList(queryData).then(res => {
    const list = (res.data?.records as any[]) || [];
    supplierOptions.value = list.map(item => {
      const { supplierName } = item;
      return {
        label: supplierName,
        value: supplierName
      };
    });
  });
};

const uploadContract = e => {
  let excelFormData = new FormData();
  excelFormData.append("file", e.file);
  uploadFile(excelFormData)
    .then(res => {
      fileId = res.data?.id as string;
    })
    .catch(() => {});
};

const onUploadSuccess = (res: any) => {
  fileId = res?.id as string;
};

const show = () => {
  confirmLoading.value = false;
  dialogVisible.value = true;
  dictionaryStore.getOrderRegion();
  querySupplierOptions();
};

const close = () => {
  if (ruleFormRef.value) {
    ruleFormRef.value?.resetFields();
  }
  dialogVisible.value = false;
};

const save = () => {
  confirmLoading.value = true;
  const { fileList, date, ...others } = ruleForm;
  const [startDate, endDate] = date;
  const queryData = {
    ...others,
    customerFileId: fileId,
    startDate,
    endDate,
    source: "第三方"
  };
  saveOrder(queryData)
    .then(res => {
      confirmLoading.value = false;
      ElMessage({
        message: "新增成功",
        type: "success"
      });
      emit("addSuccess");
      close();
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const confirmClick = () => {
  console.log("ruleForm====", ruleForm);
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      save();
    }
  });
};

watch(
  () => ruleForm.fileList,
  newVal => {
    ruleFormRef.value.validateField("fileList");
  },
  {
    deep: true
  }
);

defineExpose({
  show
});
</script>

<style lang="scss" scoped>
.dialog-content {
  :deep(.el-select) {
    width: 100%;
  }
}
</style>
