<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="产品列表"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      @sort-change="sortChange"
      :default-sort="{ prop: 'createTime', order: 'descending' }"
      :tool-button="false"
    >
      <!-- 表格操作 -->
      <!-- <template>
        <el-button type="primary" link> 查看 </el-button>
      </template> -->
    </ProTable>
  </div>
</template>

<script setup lang="ts" name="systemLog">
import { reactive, ref } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance } from "@/components/ProTable/interface";

import { ColumnProps } from "@/components/ProTable/interface";
import { System } from "@/api/interface/system";
import { getLogAudit } from "@/api/modules/system";

const sortType = {
  descending: "descs",
  ascending: "ascs"
};

const proTable = ref<ProTableInstance>();
const initParam = reactive({});
const sortCreateTime = ref(sortType.descending);

const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));

  if (sortCreateTime.value) {
    newParams[sortCreateTime.value] = "createTime";
  }

  // 操作日期
  newParams.time && (newParams.timeStart = newParams.time[0]);
  newParams.time && (newParams.timeEnd = newParams.time[1]);
  delete newParams.time;
  return getLogAudit(newParams);
};

const sortChange = (e: any) => {
  if (e.order === "descending") {
    sortCreateTime.value = sortType.descending;
  } else if (e.order === "ascending") {
    sortCreateTime.value = sortType.ascending;
  } else {
    sortCreateTime.value = e.order;
  }
  proTable.value?.getTableList();
};
// 表格配置项
const columns: ColumnProps<System.LogParams>[] = [
  { prop: "bizName", label: "操作名称", search: { el: "input" } },
  { prop: "userName", label: "用户名", search: { el: "input" } },
  { prop: "userAccount", label: "用户账号" },
  {
    prop: "createTime",
    label: "操作日期",
    sortable: true
  },
  { prop: "remoteIp", label: "操作IP地址" },
  { prop: "requestUri", label: "请求URI" },
  {
    prop: "result",
    label: "请求处理结果",
    showOverflowTooltip: {
      popperClass: "overflow-tooltip-style"
    }
  },
  {
    prop: "time",
    label: "操作时间",
    isShow: false,
    width: 170,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "params",
    label: "操作提交的数据",
    showOverflowTooltip: {
      popperClass: "overflow-tooltip-style"
    }
  },
  { prop: "serverIp", label: "服务器IP地址" }
  // { prop: "operation", label: "操作", fixed: "right", width: 100 }
];
</script>
<style lang="scss" scoped>
:global(.overflow-tooltip-style) {
  max-width: calc(100% - 24px);
  white-space: pre-wrap;
}
</style>
