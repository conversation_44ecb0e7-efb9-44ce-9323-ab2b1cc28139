<template>
  <div class="table-box custom-list">
    <pro-table
      ref="proTable"
      title="门户访问量按日期统计分析总列表"
      :request-auto="false"
      :columns="columns"
      :init-param="initParam"
      :tool-button="false"
      :pagination="false"
      :data="tableData"
      :key="Math.random()"
    >
      <template #tableHeader>
        <el-button plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <template #totalUserNaMom="{ row }">
        {{ row.total.totalUserNaMom }}
        <HBArrow v-if="row.total.totalUserNaMom !== '-'" :value="checkNumberRate(row.total.totalUserNaMom)" />
      </template>
      <template #totalUserCumMom="{ row }">
        {{ row.total.totalUserCumMom }}
        <HBArrow v-if="row.total.totalUserCumMom !== '-'" :value="checkNumberRate(row.total.totalUserCumMom)" />
      </template>
      <template #gmvHB="{ row }"> {{ row.gmvHB }} <HBArrow v-if="row.gmvHBArrow" :value="row.gmvHBArrow" /> </template>
      <template #accumulateGmvHB="{ row }">
        {{ row.accumulateGmvHB }}
        <HBArrow v-if="row.accumulateGmvHBArrow" :value="row.accumulateGmvHBArrow" />
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span>
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="registeredAnalysisList">
import { ref, reactive, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDownload } from "@/hooks/useDownload";
import { Statistic } from "@/api/interface/statisticAnalysis";
import { getPortalUserTable, exportPortalUserTable } from "@/api/modules/statisticAnalysis";
import HBArrow from "@/components/HBArrow/index.vue";
import { useDict } from "@/hooks/useDict";

const { portal_user_source } = useDict("portal_user_source");

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}

enum IndicatorsType {
  all = "0"
}
const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
});

const router = useRouter();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive(props.params);
const tableParams = ref(props.params);
const tableData = ref([]);
const getTableList = (params: any = {}) => {
  tableParams.value = { ...initParam, ...params };
  getPortalUserTable({ ...initParam, ...params }).then((res: any) => {
    if (res.code === 200) {
      const list = res.data || [];
      tableData.value = list.sort((a: any, b: any) => {
        return a.indexDate < b.indexDate ? 1 : -1;
      });
    }
  });
};

const dateTitle = computed(() => (props.params.indexUnit === DateType.day ? "日" : "月"));

const getColumns = (dicts: any = []): any => {
  let oneColumn: any = [{ prop: "indexDate", label: "统计时间", minWidth: 100 }];
  let totalItem: any = {};
  if (dicts && dicts.length > 0) {
    dicts.forEach((item: any) => {
      if (item.itemKey === IndicatorsType.all) {
        totalItem = {
          prop: "total",
          label: "总计",
          _children: [
            { prop: "total.totalUserNa", label: "总新增用户数", minWidth: 110 },
            {
              prop: "total.totalUserNaMom",
              label: "总新增数日环比",
              headerRender: () => {
                return <>{`总新增数${dateTitle.value}环比`}</>;
              },
              minWidth: 150
            },
            { prop: "total.totalUserCum", label: "累计总注册数", minWidth: 110 },
            {
              prop: "total.totalUserCumMom",
              label: "累计总注册数日环比",
              headerRender: () => {
                return <>{`累计总注册数${dateTitle.value}环比`}</>;
              },
              minWidth: 160
            }
          ]
        };
      } else {
        oneColumn.push({
          prop: `userSource${item.itemKey}`,
          label: item?.itemValue || "",
          _children: [
            { prop: `userSource.${item.itemKey}.userNa`, label: "新增用户", minWidth: 90 },
            { prop: `userSource.${item.itemKey}.userCum`, label: "累计用户", minWidth: 90 }
          ]
        });
      }
    });
  }
  if (Object.keys(totalItem).length > 0) {
    oneColumn.push(totalItem);
  }
  return oneColumn;
};
// 表格配置项
let columns: ColumnProps<Statistic.OrderDateItem>[] = reactive([]);

watch(
  () => portal_user_source.value,
  () => {
    columns = getColumns(portal_user_source.value);
  },
  {
    deep: true,
    immediate: true
  }
);

const checkNumberRate = (value: string) => {
  const re = /^[0 -9]+\.?\d*$/;
  if (!re.test(value)) {
    return "";
  } else {
    //转换为数字格式
    const rat = value.replace(/%/g, "");
    const val = parseFloat(rat);
    if (val > 0) {
      return "1";
    } else if (val < 0) {
      return "2";
    } else {
      return "";
    }
  }
};

const toView = async (row: any) => {
  router.push({
    path: "/statisticAnalysis/productAnalysis/productDimension",
    query: {
      dateType: props.params.dateType,
      countDate: [row.countDate, row.countDate]
    }
  });
};

const downloadFile = () => {
  ElMessageBox.confirm("确认导出列表数据？", "提示", {
    type: "warning"
  }).then(() => {
    const queryData = {
      ...tableParams.value
    };
    useDownload(exportPortalUserTable, queryData);
  });
};
defineExpose({
  getTableList
});
</script>
<style lang="scss" scoped>
.custom-list {
  :deep(.card.table-main) {
    border: none !important;
    box-shadow: none !important;
  }
}
:deep(.el-table .el-table__header .el-table__cell > .cell, .el-table .el-table__header .el-table__cell > .cell) {
  white-space: wrap;
}
:deep(.el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell) {
  color: var(--el-color-primary);
  background-color: #ffffff;
}
</style>
