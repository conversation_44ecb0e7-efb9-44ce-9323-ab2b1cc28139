<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" title="选择部门" width="800">
      <el-tree
        class="select-department-tree"
        :data="departmentTree"
        :props="defaultProps"
        :show-checkbox="false"
        ref="treeRef"
        node-key="id"
        highlight-current
        @node-click="handleNodeClick"
        :default-expand-all="true"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <div class="name-box">
              <span
                v-if="(!data.parentId || data.parentId === '0') && data.children.length"
                class="tree-filed iconfont icon-a-filledicon_folder_opened"
              >
              </span>
              <span>{{ node.label }}</span>
            </div>
          </div>
        </template>
      </el-tree>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="onSubmit" size="large"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch, onMounted } from "vue";
import { ElNotification } from "element-plus";

import { getOrgTreeList } from "@/api/modules/system";

const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  params: {
    type: Object,
    default: () => {}
  }
});

const departmentTree = ref<Array<any>>([]);
const defaultProps = {
  children: "children",
  label: "orgName",
  id: "id"
};
const currentItem = ref<any>(null);

const emits = defineEmits(["update:visible", "cancel", "confirm"]);

onMounted(() => {
  getTypeTree();
});

const getTypeTree = () => {
  getOrgTreeList({}).then(res => {
    departmentTree.value = res.data as Array<any>;
  });
};

// 单选
const handleNodeClick = (data: { [key: string]: any }) => {
  currentItem.value = { ...data };
};

const onSubmit = () => {
  if (!currentItem.value) {
    return ElNotification({
      title: "温馨提示",
      message: "请选择新部门",
      type: "warning"
    });
  }
  emits("confirm", { orgId: currentItem.value.id, orgName: currentItem.value.orgName });
};
const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
};

const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    emits("update:visible", value);
  }
});
</script>
<style lang="scss" scoped>
// :deep(.el-dialog__body) {
//   padding-bottom: 0;
// }
.name-box {
  display: flex;
  align-items: center;
  .tree-filed {
    display: inline-block;
    margin: 0 4px;
    color: #9ca3ac;
  }
}
.select-department .custom-tree-node {
  display: block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
