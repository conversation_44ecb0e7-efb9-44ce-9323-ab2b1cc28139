<template>
  <div class="card" style="padding-left: 32px">
    <div class="infos">
      <div class="header-title">账单拨付审核详情</div>
      <el-form ref="formRef" :model="examineForm" label-width="110px" class="demo-reviewForm" :label-position="'left'">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">合作商： </span>
              <span class="info-text">{{ rowData?.providerName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">账单日期： </span>
              <span class="info-text">{{ rowData?.billDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">主商户号： </span>
              <span class="info-text"> {{ rowData?.unicomMerchantNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">子商户号： </span>
              <span class="info-text">{{ rowData?.merchantNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">总金额（元）： </span>
              <span class="info-text"> {{ rowData?.totalFee }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">手续费（元）： </span>
              <span class="info-text">{{ rowData?.commissionTotalFee }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">平台总金额(元)： </span>
              <span class="info-text">{{ rowData?.unicomTotalFee }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">合作商总金额(元)： </span>
              <span class="info-text">{{ rowData?.providerTotalFee }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">一致订单（笔）： </span>
              <span class="info-text">{{ rowData?.rightNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">一致订单总金额（元）：</span>
              <span class="info-text">{{ rowData?.rightFee }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">平账订单（笔）：</span>
              <span class="info-text">{{ rowData?.fitNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">平账订单总金额（元）：</span>
              <span class="info-text">{{ rowData?.fitFee }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">短款订单（笔）： </span>
              <span class="info-text">{{ rowData?.shortNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">短款总金额（元）： </span>
              <span class="info-text">{{ rowData?.shortFee }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">长款订单（笔）： </span>
              <span class="info-text"> {{ rowData?.longNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">长款总金额（元）： </span>
              <span class="info-text">{{ rowData?.longFee }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">错账订单（笔）：</span>
              <span class="info-text">{{ rowData?.wrongNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">错账订单金额（元）：</span>
              <span class="info-text">{{ rowData?.wrongFee }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="header-title mt10">审核记录</div>
        <div v-if="activities && activities.length > 0">
          <el-timeline>
            <el-timeline-item
              v-for="activity in activities?.slice(0, 2)"
              :type="activity.status == 2 || activity.status == 3 ? 'primary' : undefined"
              :hollow="true"
              :key="activity.id"
            >
              <div class="steps-info">
                <div class="title">
                  {{ activity.bizInfo }}
                  <span
                    :class="{
                      status: true,
                      pass: activity.status == 2,
                      noPass: activity.status == 3,
                      pending: activity.status == 1
                    }"
                  >
                    {{ useDictLabel(audit_status, activity.status) }}
                  </span>
                </div>
                <div>{{ activity.auditTime }}</div>
              </div>
              <div class="steps-info">
                <div>审核人：{{ activity.auditorName }}</div>
                <div>审核意见：{{ activity.auditMsg }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <div v-else>
          <el-timeline>
            <el-timeline-item :hollow="true" :type="'primary'">
              <div class="steps-info">
                <div class="title">一级审核 <span class="status pending"> 待审核 </span></div>
                <div></div>
              </div>
              <div class="steps-info">
                <div>审核人：</div>
                <div>审核意见：</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <template v-if="isReview">
          <div class="header-title mt10">审核信息</div>

          <el-form-item label="审核意见" prop="auditRemark" :rules="[{ required: validRequired, message: '请输入审核意见' }]">
            <el-input
              maxlength="500"
              :placeholder="pageType === PageType.bill ? '请填写审核意见' : '驳回时必须填写审核意见，通过默认审核意见为同意'"
              show-word-limit
              :rows="5"
              v-model="examineForm.auditRemark"
              type="textarea"
              autocomplete="off"
              :disabled="!isReview"
            />
          </el-form-item>
        </template>

        <el-form-item label="审核文档：" prop="reviewDocs" v-if="pageType === PageType.bill">
          <uploadFiles
            v-model:file-list="examineForm.reviewDocs"
            width="135px"
            height="135px"
            :file-type="'.doc,.docx,.pdf,.jpg,.jpeg,.bmp,.png'"
            :limit="isReview ? 10 : 20"
            :file-size="10"
            :list-type="'text'"
            :show-file-list="true"
            :disabled="!isReview"
            :hide-box="!isReview"
          >
            <template #trigger> </template>
            <template #tip v-if="isReview">
              <div class="upload-tips">上传的文件格式要求word、pdf、jpg、jpeg、bmp、png，不超过10M。</div>
            </template>
          </uploadFiles>
        </el-form-item>

        <div class="button" v-if="isReview">
          <el-button type="primary" @click="handleTask('pass')">通过</el-button>
          <el-button type="primary" @click="handleTask('noPass')">驳回</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts" name="billReview">
import { reactive, ref, computed, onMounted, nextTick } from "vue";
import type { Action, FormInstance } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import uploadFiles from "@/components/Upload/Files.vue";
import { queryAuditDetail, auditPrimary, auditSecondary } from "@/api/modules/partner";
import { PageType, operateType } from "../type";
import { closeTabItem } from "@/utils/closeTab";
import { Partner } from "@/api/interface/partner";

import { useDict, useDictLabel } from "@/hooks/useDict";
const { audit_status } = useDict("audit_status");

interface ReqType {
  id: string;
  auditMsg: string;
  auditStatus: string;
  auditUrl?: string;
}

interface AuditListItem {
  status: string | number;
  bizInfo: "二级审核";
  auditTime: string;
  auditorName: string;
  auditMsg: string;
  id: string;
}

const route = useRoute();
const router = useRouter();
const { pageType, type, id } = route.query;
const isReview = (type as string) === `${operateType.review}`;
const rowData = ref<any>({ _productDocs: [] });

const activities = ref<AuditListItem[]>([]);

const formRef = ref<FormInstance>();
const examineForm = reactive<{
  auditRemark: string;
  reviewDocs: Partner.AgreementUrlType[];
}>({
  auditRemark: "",
  reviewDocs: []
});
const validType = ref<string>("pass");

const getDetails = () => {
  queryAuditDetail({ id: id as string }).then(resp => {
    const details: any = resp.data;
    rowData.value = details;
    let list: Partner.AgreementUrlType[] = [];
    if (details?.auditList) {
      const auditList: any = details?.auditList;
      if (!isReview) {
        auditList?.forEach((item: any) => {
          if (item?.url) {
            const urls = JSON.parse(item.url);
            list = [...list, ...urls];
          }
        });
      }
      // 一级审核不通过不显示二级
      if (auditList.length === 1 && auditList[0].status !== 3) {
        auditList.push({
          bizInfo: "二级审核",
          auditTime: "",
          auditorName: "",
          auditMsg: "",
          status: 1,
          id: "0"
        });

        activities.value = details?.auditList;
      }
      activities.value = auditList;
    }

    const newList = list?.map((item: Partner.AgreementUrlType) => {
      return { ...item, name: item.originalName };
    });
    examineForm.reviewDocs = newList;
  });
};

onMounted(() => {
  getDetails();
});

const validRequired = computed(() => {
  if (pageType === PageType.introduce) {
    return true;
  }
  return validType.value === "noPass";
});

const handleTask = (index: string) => {
  validType.value = index;
  let tips = "";
  if (index === "pass") {
    tips = "是否确认审核通过";
  } else {
    tips = "是否确认驳回审核";
  }

  nextTick(() => {
    formRef.value?.validate().then(valid => {
      if (valid) {
        ElMessageBox.alert(tips, "操作提示", {
          confirmButtonText: "确定",
          callback: (action: Action) => {
            if (action === "confirm") {
              const auditRemark = examineForm.auditRemark === "" ? "同意" : examineForm.auditRemark;

              const api = rowData.value.auditStatus === "1" && !rowData.value.auditList?.length ? auditPrimary : auditSecondary;
              const toPass = index === "pass";
              const params: ReqType = {
                id: id as string,
                auditMsg: toPass ? auditRemark : examineForm.auditRemark,
                auditStatus: toPass ? "2" : "3"
              };
              if (pageType === PageType.bill) {
                const fileList = examineForm.reviewDocs?.map((item: Partner.AgreementUrlType) => {
                  return { link: item.url, uid: item.uid, originalName: item.name, name: item.link };
                });
                params.auditUrl = JSON.stringify(fileList);
              }
              api(params).then(res => {
                if (res.code == 200) {
                  ElMessage({
                    message: toPass ? "审核通过成功!" : "审核不通过完成!",
                    type: "success"
                  });
                  closeTabItem(route);
                  router.push("/verification/partnerReview");
                } else {
                  ElMessage({
                    message: toPass ? "审核通过失败!" : "审核不通过失败!",
                    type: "error"
                  });
                }
              });
            }
          }
        });
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.mt10 {
  margin-top: 10px;
}
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
.infos {
  margin-left: 10px;
  .info-item {
    margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      margin-right: 10px;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
  .demo-reviewForm.el-form {
    .el-form-item {
      :deep(.el-form-item__label) {
        color: #73787f;
      }
    }
  }
}
.status {
  display: inline-block;
  height: 24px;
  padding: 0 12px;
  margin-left: 6px;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
  border-radius: 2px;
  &.pass {
    background: #0052d9;
  }
  &.pending {
    background: #ffaa00;
  }
  &.noPass {
    background: #e5302f;
  }
}
.steps-info {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 22px;
  color: #73787f;
  .title {
    font-size: 16px;
    color: #3a3a3d;
  }
  > div {
    flex: 1;
  }
}
:deep(.infos .el-timeline-item__tail) {
  border-left-color: var(--el-color-primary);
}
</style>
