import { computed, defineComponent, nextTick, onMounted, ref, watch } from "vue";
import { useDict } from "@/hooks/useDict";
export default defineComponent({
  name: "SelectDict",
  props: {
    modelValue: {
      type: String,
      default: ""
    },
    label: {
      type: String,
      default: ""
    },
    dictKey: {
      type: String,
      default: ""
    }
  },
  emits: ["update:modelValue"],
  setup(props, { emit }) {
    const dictSet = useDict(props.dictKey);

    const handleChange = (value: string) => {
      emit("update:modelValue", value);
    };

    return {
      handleChange,
      dictSet
    };
  },
  render() {
    return (
      <el-select
        style={{ width: "100%" }}
        onChange={(value: string) => this.handleChange(value)}
        model-value={this.modelValue}
        clearable
        filterable
        placeholder={`请选择${this.label}`}
      >
        {this.dictSet?.[this.dictKey].value.map((item: { itemKey: string; itemValue: string }) => (
          <el-option label={item.itemValue} value={item.itemKey} />
        ))}
      </el-select>
    );
  }
});
