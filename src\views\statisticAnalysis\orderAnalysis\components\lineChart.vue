<template>
  <div :id="props.chartId" class="echarts"></div>
</template>

<script setup lang="tsx">
import { ECharts, EChartsOption, init } from "echarts";
import { useEcharts } from "@/hooks/useEcharts";

const props = withDefaults(defineProps<{ chartId?: string }>(), {
  chartId: "lineChartId"
});

let charEch: ECharts | null;
const initChart = (data: any = {}, lineName: string = "") => {
  const charEle = document.getElementById(props.chartId) as HTMLElement;
  if (!charEch) {
    charEch = init(charEle);
  }
  const xAxisData = data.nameList || [];
  const dataValue = data.data || [];

  const colors = ["#246ADD", "#FAD954", "#3AA0FF", "#67D287", "#E5302F", "#9CA3AC"];
  const option: EChartsOption = {
    color: colors,
    tooltip: {
      trigger: "axis"
    },
    legend: {
      icon: "circle",
      bottom: "5%",
      right: "50%",
      itemWidth: 6,
      itemGap: 20,
      textStyle: {
        color: "#556677"
      }
    },
    grid: {
      left: "3%",
      right: "5%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxisData
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        name: lineName,
        data: dataValue,
        type: "line",
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 2
        }
      }
    ]
  };

  charEch.clear();
  useEcharts(charEch, option);
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
:deep(.annual-tooTip) {
  box-sizing: border-box;
  height: 124px;
  padding: 5px 16px;
  background: rgb(3 11 27 / 80%);
  border: 1px solid #5a7dc0;
  .annual-month {
    display: inline-block;
    margin-bottom: 6px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
  }
  .annual-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    .year-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 22px;
      margin-bottom: 5px;
      font-family: PingFangSC-Regular, "PingFang SC";
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      .year-dot {
        width: 8px;
        height: 8px;
        margin-right: 5px;
      }
      .year-name {
        width: 150px;
        margin: 0 2px;
      }
      .year-value {
        display: inline-block;
      }
    }
  }
}
</style>
