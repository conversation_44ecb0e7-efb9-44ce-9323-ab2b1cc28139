/*
 * @Author: <EMAIL>
 * @Date: 2023-09-06 09:31:29
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-09-15 17:40:23
 * @Description: file content
 */
import { Label } from "@/api/interface/label";
import http from "@/api";

/**
 * @name 标签模块
 */
// 标签分类列表
export const getTagTypeList = (params: Label.TagTypeListParams) => {
  return http.get(`/cnud-product/pmsTagType/list`, params); // 正常 post json 请求  ==>  application/json
};

// 新增标签分类
export const addTagType = (params: Label.TagTypeListParams) => {
  return http.post(`/cnud-product/pmsTagType/saveWithTag`, params); // 正常 post json 请求  ==>  application/json
};
// 修改标签分类
export const updateTagType = (params: Label.TagTypeListParams) => {
  return http.post(`/cnud-product/pmsTagType/update`, params); // 正常 post json 请求  ==>  application/json
};

// 删除标签分类
export const deleteTagType = (params: Label.deleteParams) => {
  return http.post(`/cnud-product/pmsTagType/remove`, params); // 正常 post json 请求  ==>  application/json
};

// 标签列表
export const getTagList = (params: Label.TagListParams) => {
  return http.get(`/cnud-product/pmsTag/queryPage`, params); // 正常 post json 请求  ==>  application/json
};

// 新增标签
export const addTag = (params: Label.TagItem) => {
  return http.post(`/cnud-product/pmsTag/save`, params); // 正常 post json 请求  ==>  application/json
};
// 修改标签
export const updateTag = (params: Label.TagItem) => {
  return http.post(`/cnud-product/pmsTag/update`, params); // 正常 post json 请求  ==>  application/json
};

// 删除标签
export const deleteTag = (params: Label.deleteParams) => {
  return http.post(`/cnud-product/pmsTag/remove`, params); // 正常 post json 请求  ==>  application/json
};
