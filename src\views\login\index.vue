<!--
 * @Author: <EMAIL>
 * @Date: 2023-09-08 09:49:57
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-20 08:57:37
 * @Description: file content
-->
<template>
  <div class="login-container flx-center">
    <!--    <div class="app-name"><img src="@/assets/images/昊算logo.png" alt="" class="app-logo" /></div>-->
    <div class="app-name"><img :src="sysInfo.darkLogoUrl" alt="" class="app-logo" /></div>
    <div class="login-box">
      <!-- <SwitchDark class="dark" /> -->

      <div class="login-form">
        <el-tabs v-model="activeName" class="demo-tabs" :stretch="true" @tab-change="tabChange">
          <el-tab-pane label="账号登陆" name="passwordLogin">
            <password-login-form
              ref="passwordLoginRef"
              v-model:canLogin="canLogin"
              @show-verify="showVerifyBox"
              :is-send-message="isSendMessage"
              v-if="activeName == 'passwordLogin'"
            />
          </el-tab-pane>
          <el-tab-pane label="手机号登陆" name="phoneLogin">
            <phone-login-form
              ref="phoneLoginRef"
              v-if="activeName == 'phoneLogin'"
              v-model:canLogin="canLogin"
              @show-verify="showVerifyBox"
              :is-send-message="isSendMessage"
            />
          </el-tab-pane>
        </el-tabs>
        <!-- <div class="login-tips-box">
          <div class="tips-left">
            <div class="tips">还没有账号？</div>
            <div class="button-box">
              <el-button text type="primary">立即注册</el-button>
            </div>
          </div>
          <div class="tips-right">
            <el-button text type="primary">忘记密码</el-button>
          </div>
        </div> -->
        <div class="login-btn">
          <el-button
            round
            size="large"
            type="primary"
            :loading="loading"
            :class="canLogin ? 'active' : ''"
            :disabled="!canLogin"
            @click="login()"
          >
            登录
          </el-button>
        </div>
        <!-- <div class="protocol-box">
          <el-checkbox v-model="aggreProtocol" label="" size="large" />
          未注册的手机号将自动注册，勾选即代表您同意并接受《昊算用户隐私政策》
        </div> -->
        <div class="verify-bg" v-if="showVerify">
          <slider-verify
            ref="sliderVerify"
            :mobile-phone="mobilePhone"
            @success="onSuccess"
            @fail="onFail"
            @again="onAgain"
            @close="showVerify = false"
          ></slider-verify>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PasswordLoginForm from "./components/PasswordLoginForm.vue";
import PhoneLoginForm from "./components/PhoneLoginForm.vue";
import { useUserStore } from "@/stores/modules/user";
// import SwitchDark from "@/components/SwitchDark/index.vue";
import SliderVerify from "@/components/SliderVerify/SliderVerify.vue";
import { sendSms } from "@/api/modules/login";
import { onMounted, Ref, ref, computed } from "vue";
import { ElMessage } from "element-plus";
import md5 from "md5";
import { sm2 } from "sm-crypto";

const userStore = useUserStore();
const showVerify = ref(false);
// const aggreProtocol = ref(true);
const loading = ref(false);
const activeName: Ref<"passwordLogin" | "phoneLogin"> = ref("passwordLogin");
const passwordLoginRef = ref<any>(null);
const phoneLoginRef = ref<any>(null);
const canLogin = ref(false);
const sysInfo: any = computed(() => userStore.sysInfo);
const sliderVerify = ref();
let isSendMessage = ref(false);
let mobilePhone = ref();

const publicKey: any = computed(() => userStore.publicKey);

onMounted(() => {
  mobilePhone = computed(() => {
    const loginRef = {
      passwordLogin: () => passwordLoginRef.value.loginForm.account,
      phoneLogin: () => phoneLoginRef.value.loginForm.phoneNumber
    };
    return loginRef[activeName.value]();
  });
  // 登录时获取系统配置：title、logo
  userStore.getSysInfo();
  // 获取publicKey
  userStore.setPublicKey();
});
// tabs切换
const tabChange = () => {
  const loginRef = {
    passwordLogin: passwordLoginRef.value,
    phoneLogin: phoneLoginRef.value
  };
  isSendMessage.value = false;
  loginRef[activeName.value].checkLoginStatus();
};
// login
const login = () => {
  const loginRef = {
    passwordLogin: passwordLoginRef.value,
    phoneLogin: phoneLoginRef.value
  };
  loginRef[activeName.value].login();
};
//滑动验证码
const showVerifyBox = () => {
  isSendMessage.value = false;
  showVerify.value = true;
};
/* 滑动验证成功*/
const onSuccess = (captcha: any) => {
  const getSmsParams = {
    passwordLogin: () => {
      return {
        account: mobilePhone.value,
        password: sm2.doEncrypt(md5(passwordLoginRef.value.loginForm.password), publicKey.value, 1)
      };
    },
    phoneLogin: () => {
      return {
        phone: mobilePhone.value
      };
    }
  };
  const params = getSmsParams[activeName.value]();
  sendSms({
    ...params,
    value: captcha.value,
    nonceStr: captcha.nonceStr
  })
    .then(res => {
      if (res.code != 200) {
        ElMessage.error(res.msg || "验证出错");
        refresh();
      } else {
        showVerify.value = false;
        isSendMessage.value = true;
        ElMessage.success("短信已经发送");
      }
    })
    .catch(res => {
      refresh();
    });
};
/* 滑动验证失败*/
const onFail = (msg: any) => {
  ElMessage.error(msg || "验证失败，请控制拼图对齐缺口");
};
/* 滑动验证异常*/
const onAgain = () => {
  ElMessage.error("滑动操作异常，请重试");
};
/* 刷新验证码*/
const refresh = () => {
  sliderVerify.value.refresh();
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
