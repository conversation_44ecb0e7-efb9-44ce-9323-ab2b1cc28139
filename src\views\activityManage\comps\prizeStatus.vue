<template>
  <div class="prize-status">
    <div class="mb20 header-title-common">奖品领取情况</div>
    <ProTable ref="proTable" :data="dataSource" :columns="columns" :tool-button="false" :pagination="false"></ProTable>
  </div>
</template>

<script setup lang="tsx">
import { ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";

const columns: ColumnProps[] = [
  {
    prop: "productName",
    label: "奖品名称",
    align: "center"
  },
  {
    prop: "skuName",
    label: "产品规格",
    align: "center"
  },
  {
    prop: "total",
    label: "发放数量",
    align: "center"
  },
  {
    prop: "getCount",
    label: "已领取/剩余",
    align: "center",
    render: (scope: any) => `${scope.row.getCount}/${scope.row.surplusCount}`
  },
  {
    prop: "getRate",
    label: "领取率",
    align: "center"
  }
];

defineProps({
  dataSource: {
    type: Array,
    default: () => {
      return [];
    }
  }
});
</script>

<style scoped lang="scss">
.prize-status {
  margin-top: 32px;
}
</style>
