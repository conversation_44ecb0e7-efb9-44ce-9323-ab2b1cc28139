<template>
  <!-- 总算力分布占比 -->
  <div class="echarts-box">
    <div id="SuanLiDistrRef" class="echarts"></div>
    <div class="legendBg">
      <div class="title">{{ selectPieData?.name }}</div>
      <el-carousel height="170px" class="carouselStyle" :autoplay="false" arrow="never">
        <el-carousel-item v-for="k in Math.ceil(legendData.length / echartsPage)" :key="k">
          <div class="carouselItem">
            <div v-for="(child, ind) in legendData.slice((k - 1) * echartsPage, k * echartsPage)" :key="ind" class="item">
              <span class="rect" :style="{ background: currentColor(child) || '#5092FF' }"></span>
              <span class="name">{{ child.name }}：</span>
              <span class="value">{{ child.value >= 10000 ? (child.value / 10000).toFixed(2) + "w" : child.value }}</span>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ECharts, EChartsOption, init } from "echarts";

const allData = ref<any>([]);

const selected = ref(0);
const selectPieData = ref();
const legendData = ref<any>([]); // 显示的legend数组
const allLegendData = ref<any>([]); // 所有的的legend数组，用于颜色获取
const echartsPage = ref<any>(5); // 五条数据一页

const currentColor = (child: any) => {
  const data = JSON.parse(JSON.stringify(allLegendData.value));
  const item: any = data.find((item: any) => {
    return item.name == child.name && item.value == child.value;
  });
  return item?.color;
};

watch(
  () => selected.value,
  () => {
    legendData.value = allData.value[selected.value]?.children || [];
  },
  { immediate: true }
);

const color = [
  "#5092FF",
  "#28D4D6",
  "#FFAA38",
  "#216BE3",
  "#0B47A8",
  "#6152F1",
  "#4334D4",
  "#49A0FF",
  "#194EFF",
  "#45FCFE",
  "#00B8BA",
  "#007273",
  "#58E0AF",
  "#FEBF6C"
];

const initChart = (
  chartsInfo: any = {
    data: []
  }
): ECharts => {
  allData.value = chartsInfo;
  const data = chartsInfo;

  const data1 = data.map((item: any, index: number) => {
    if (index === selected.value) {
      item.selected = true;
      selectPieData.value = item;
      legendData.value = item.children;
    }
    return item;
  });
  const data2: any = [];
  data.forEach((item: any) => {
    data2.push(...item.children);
  });

  let flagIndex = 0;
  allLegendData.value = [...data, ...data2]?.map((item: any, index: number) => {
    if (index % color.length == 0) {
      flagIndex = 0;
    }
    item.color = color[flagIndex];
    flagIndex += 1;
    return item;
  });

  const charEle = document.getElementById("SuanLiDistrRef") as HTMLElement;
  const charEch: ECharts = init(charEle);

  const option: EChartsOption = {
    color,
    grid: {
      left: "20"
    },
    // legend: {
    //   icon: "rect",
    //   right: "5%",
    //   top: "center",
    //   itemWidth: 10,
    //   itemHeight: 10,
    //   bottom: 20,
    //   height: 155,
    //   data: data2.map(item => item.name),
    //   orient: "vertical",
    //   type: "scroll",
    //   // scrollDataIndex: 0,
    //   formatter(name) {
    //     let value;
    //     data2.forEach(item => {
    //       if (item.name == name) {
    //         value = item.value;
    //       }
    //     });
    //     return `{name|${name}}：{value|${value}}`;
    //   },
    //   textStyle: {
    //     color: "#fff",
    //     fontSize: 16,
    //     rich: {
    //       name: {
    //         color: "#90AFD0",
    //         lineHeight: 22
    //       },
    //       value: {
    //         color: "#fff",
    //         lineHeight: 22
    //       }
    //     }
    //   },
    //   pageIconColor: "#2C86FF", //翻页箭头颜色
    //   pageIconInactiveColor: "rgba(44,132,251,0.40)", //翻页（即翻页到头时箭头的颜色
    //   pageTextStyle: {
    //     show: false,
    //     color: "#999" //翻页数字颜色
    //   }
    // },
    tooltip: {
      trigger: "item",
      formatter: "{b}<br/>{c}",
      backgroundColor: "rgba(3,11,27,0.8)",
      borderColor: "#5A7DC0",
      borderWidth: 1,
      borderRadius: 0,
      textStyle: {
        fontSize: 16,
        color: "#fff"
      }
    },
    series: [
      {
        name: "一级分类",
        type: "pie",
        selectedMode: "single",
        center: ["30%", "50%"],
        radius: [0, "40%"],
        minAngle: 3,
        label: {
          position: "inner",
          fontSize: 14,
          color: "#fff"
        },
        labelLine: {
          show: false
        },
        data: data1
      },
      {
        name: "二级分类",
        type: "pie",
        center: ["30%", "50%"],
        radius: ["55%", "70%"],
        labelLine: {
          show: false
        },
        label: {
          show: false
        },
        data: data2
      }
    ]
  };
  charEch.setOption(option);
  charEch.on("click", "series", function (params) {
    if (params.seriesIndex === 0) {
      selected.value = params.dataIndex;
      selectPieData.value = params.data;
    }
  });
  return charEch;
};
defineExpose({
  initChart
});
</script>

<style scoped lang="scss">
.echarts-box {
  position: relative;
  width: 100%;
  height: 100%;
}
.echarts {
  width: 100%;
  height: 100%;
}
.legendBg {
  position: absolute;
  top: 5px;
  right: 0;
  box-sizing: border-box;
  width: 175px;

  // width: 166px;
  // height: 219px;
  padding: 14px 2px 14px 14px;
  background: url("../../../../../src/assets/images/screen/legend-bg.png") no-repeat;
  background-size: cover;
  .title {
    height: 22px;
    margin-bottom: 8px;
    font-size: 16px;
    color: #90afd0;
  }
  .carouselItem {
    max-height: 150px;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    .item {
      margin-bottom: 8px;
      overflow: auto;
    }
    .rect {
      display: inline-block;
      width: 10px;
      height: 10px;
    }
    .name {
      height: 22px;
      margin-left: 5px;
      font-size: 16px;
      line-height: 22px;
      color: #90afd0;
    }
    .value {
      font-size: 16px;
      color: #ffffff;
    }
  }
}
.el-carousel {
  width: 165px;
  :deep(.el-carousel__indicators--horizontal) {
    width: 100%;
    text-align: center;
  }
  :deep(.el-carousel__indicator--horizontal) {
    padding: 3px;
  }
  :deep(.el-carousel__button) {
    // 指示器按钮
    width: 16px;
    height: 3px;
    background: #5092ff;
    border-radius: 2px;
    opacity: 0.5;
  }
  :deep(.is-active .el-carousel__button) {
    // 指示器激活按钮
    background: #5092ff;
    opacity: 1;
  }
  :deep(.el-carousel__container) {
    height: 100%;
  }
}
</style>
