export namespace TransactionVolumeFiling {
  export interface OrderInfoListParams {
    current?: number; // 当前页
    size?: number; // 每页的数量
    source: string;
    orderId?: string;
  }

  export interface SupplierListParams {
    state?: string;
    current: number;
    size: number;
  }

  export interface SaveOrderParams {
    source: string;
    customerFileId: string;
    supplierName: string;
    region: string;
    orderAmountStr: string;
    startDate: string;
    endDate: string;
  }

  export interface RelContractParams {
    id: string;
    contractId: string;
  }

  export interface ContractListParams {
    id: string;
  }

  export interface DownloadAttachFileParams {
    id: string;
  }

  export interface DemandListParams {
    current: number;
    size: number;
    productName?: string;
    supplierName?: string;
  }
}
