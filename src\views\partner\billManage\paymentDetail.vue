<template>
  <div class="table-box">
    <ProTable ref="proTable" title="拨付明细表" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader>
        <!-- 默认置灰,选择后支持点击，合作商类型账号不可见  -->
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'resend'">
          <el-tooltip content="重送" placement="top" v-if="scope.row.status == '3'">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="resend(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="partnerManagement">
import { ref, reactive, onMounted, computed, onActivated } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";

import { useDownload } from "@/hooks/useDownload";
import { useDicStore } from "@/stores/modules/dictionaty";

import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getPaymentDetailPageList, exportPaymentDetailPageList, appropriateResend } from "@/api/modules/billManage";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();
const proTable = ref<ProTableInstance>();
const route = useRoute();
let billId = route.query.id;

const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "providerName", width: 130, label: "合作商" },
  {
    prop: "billDate",
    label: "账单日期",
    width: 150
  },
  { prop: "orderId", label: "订单号", width: 200, search: { el: "input" } },
  {
    prop: "createTime",
    label: "订单时间",
    width: 180,
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY/MM/DD HH:mm:ss",
        valueFormat: "YYYY/MM/DD HH:mm:ss"
      }
    }
  },
  {
    prop: "productName",
    label: "产品名称",
    align: "left",
    width: 180
  },
  {
    prop: "unicomDividendProportion",
    label: "分成比例",
    align: "left",
    width: 180,
    render: (scope: any) => {
      return (
        <span>
          {scope.row.unicomDividendProportion} : {scope.row.partnerDividendProportion}
        </span>
      );
    }
  },
  {
    prop: "commissionFee",
    label: "手续费",
    align: "left",
    width: 180
  },
  {
    prop: "unicomAccountFee",
    label: "平台金额",
    align: "left",
    width: 180
  },
  {
    prop: "providerAccountFee",
    label: "合作方金额",
    align: "left",
    width: 180
  },
  {
    prop: "status",
    label: "拨付状态",
    align: "left",
    width: 180,
    enum: [
      { label: "待拨付", value: "1" },
      { label: "拨付成功", value: "2" },
      { label: "拨付失败", value: "3" }
    ]
  },
  { prop: "operation", label: "操作", fixed: "right", width: 100 }
];

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }

  let newParams = JSON.parse(JSON.stringify(search));
  newParams.createTime && (newParams.orderTimeOfStart = newParams.createTime[0]);
  newParams.createTime && (newParams.orderTimeOfEnd = newParams.createTime[1]);
  newParams.accountId = billId;
  delete newParams.createTime;
  return getPaymentDetailPageList(newParams);
};

const downloadFile = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.orderTimeOfStart = newParams.createTime[0]);
  newParams.createTime && (newParams.orderTimeOfEnd = newParams.createTime[1]);
  delete newParams.createTime;
  newParams.accountId = billId;
  ElMessageBox.confirm("确认导出拨付明细列表?", "提示", { type: "warning" }).then(() =>
    useDownload(exportPaymentDetailPageList, newParams)
  );
};

const resend = async (data: any) => {
  ElMessageBox.confirm("是否确认重新拨付该记录?", "提示", { type: "warning" }).then(async () => {
    await appropriateResend(data.id);
    ElMessage.success("重新拨付成功！");
    proTable.value?.getTableList();
  });
  // proTable.value?.getTableList();
};

// onActivated(() => {
//   proTable.value?.getTableList();
// });
</script>

<style lang="scss" scoped>
.table-box {
  width: 100%;
}
</style>
