<template>
  <div style="position: relative; height: 100%">
    <div id="3dpie" class="echarts"></div>
    <img src="@/assets/images/screen/pie-bg.png" class="pie-bg" />
    <div class="bottom-box">
      <div class="left-box">
        <div ref="lefChartRef" class="left-echart"></div>
        <img src="@/assets/images/screen/clock-bg.png" class="clock-bg" />
      </div>
      <div class="line"></div>
      <div class="right-box">
        <div ref="rightChartRef" class="right-echart"></div>
        <img src="@/assets/images/screen/clock-bg.png" class="clock-bg" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ECharts, EChartsOption, init } from "echarts";
import "echarts-gl";
import { useEcharts } from "@/hooks/useEcharts";
import pointerImg from "@/assets/images/screen/pointer.png";

const lefChartRef = ref<HTMLElement>();
const rightChartRef = ref<HTMLElement>();
const option = ref();

const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
});
const initCloakCharts = () => {
  let myChart1: ECharts = init(lefChartRef.value as HTMLElement);
  let option1: EChartsOption = {
    // backgroundColor: "#000",
    grid: {
      containLabel: true,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    series: [
      {
        name: "刻度线及刻度值",
        type: "gauge",
        center: ["35%", "50%"], //中心位置
        radius: "80%",
        startAngle: -90,
        endAngle: 270,
        min: 0,
        max: 100,
        z: 41,
        splitNumber: 10,
        axisLine: {
          show: false
        },
        splitLine: {
          // 仪表盘分隔线
          distance: 0,
          length: 3,
          lineStyle: {
            width: 1.5,
            color: "#fff"
          }
        },
        axisLabel: {
          // 仪表盘刻度标签
          show: false
        },
        axisTick: {
          show: false
        },
        pointer: {
          // 仪表盘指针
          show: false
        },
        anchor: {
          // 表盘中指针的固定点
          show: false,
          showAbove: true
        }
      },
      {
        name: "进度条及指示器",
        type: "gauge",
        center: ["35%", "50%"],
        radius: "67%",
        startAngle: -90,
        endAngle: 270,
        z: 40,
        min: 0,
        max: 100,
        splitNumber: 10,
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false,
          distance: 25,
          color: "#fff",
          fontSize: 20
        },
        axisTick: {
          show: false
        },
        anchor: {
          show: false,
          showAbove: false
        },
        detail: {
          show: true,
          color: "#fff",
          offsetCenter: ["190%", 0],
          fontFamily: "D-DIN",
          fontSize: 24,
          formatter: "{value}{unit|%}\n{name|分配率}",
          rich: {
            unit: {
              fontSize: 16,
              fontFamily: "D-DIN",
              color: "#fff"
            },
            name: {
              fontSize: 16,
              color: "#fff",
              fontFamily: "PingFangSC-Regular,PingFang SC"
            }
          }
        },
        data: [
          {
            value: +props.params.capacityAllotedrate
          }
        ],
        pointer: {
          show: true,
          icon: "image://" + pointerImg,
          length: "95%",
          offsetCenter: [0, 1],
          keepAspect: true,
          width: 8
        },
        progress: {
          show: true,
          width: 7,
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#2ECADC"
                },
                {
                  offset: 1,
                  color: "#5092FF"
                }
              ]
            }
          }
        }
      }
    ]
  };
  useEcharts(myChart1, option1);

  let myChart2: ECharts = init(rightChartRef.value as HTMLElement);
  let option2: EChartsOption = {
    grid: {
      containLabel: true,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    series: [
      {
        name: "刻度线及刻度值",
        type: "gauge",
        center: ["35%", "50%"], //中心位置
        radius: "80%",
        startAngle: -90,
        endAngle: 270,
        min: 0,
        max: 100,
        z: 41,
        splitNumber: 10,
        axisLine: {
          show: false
        },
        splitLine: {
          // 仪表盘分隔线
          distance: 0,
          length: 3,
          lineStyle: {
            width: 1.5,
            color: "#fff"
          }
        },
        axisLabel: {
          // 仪表盘刻度标签
          show: false
        },
        axisTick: {
          show: false
        },
        pointer: {
          // 仪表盘指针
          show: false
        },
        anchor: {
          // 表盘中指针的固定点
          show: false,
          showAbove: true
        }
      },
      {
        name: "进度条及指示器",
        type: "gauge",
        center: ["35%", "50%"],
        radius: "67%",
        startAngle: -90,
        endAngle: 270,
        z: 40,
        min: 0,
        max: 100,
        splitNumber: 10,
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false,
          distance: 25,
          color: "#fff",
          fontSize: 20
        },
        axisTick: {
          show: false
        },
        anchor: {
          show: false,
          showAbove: false
        },
        detail: {
          show: true,
          color: "#fff",
          offsetCenter: ["190%", 0],
          fontFamily: "D-DIN",
          fontSize: 24,
          formatter: "{value}{unit|%}\n{name|使用率}",
          rich: {
            unit: {
              fontSize: 16,
              fontFamily: "D-DIN",
              color: "#fff"
            },
            name: {
              fontSize: 16,
              color: "#fff",
              fontFamily: "PingFangSC-Regular,PingFang SC"
            }
          }
        },
        data: [
          {
            value: +props.params.capacityUsedrate
          }
        ],
        pointer: {
          show: true,
          icon: "image://" + pointerImg,
          length: "95%",
          offsetCenter: [0, 1],
          keepAspect: true,
          width: 8
        },
        progress: {
          show: true,
          width: 7,
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#2ECADC"
                },
                {
                  offset: 1,
                  color: "#5092FF"
                }
              ]
            }
          }
        }
      }
    ]
  };
  useEcharts(myChart2, option2);
};
const initChart = (data: any = {}): ECharts => {
  const charEle = document.getElementById("3dpie") as HTMLElement;
  const charEch: ECharts = init(charEle);
  option.value = getPie3D(
    // [
    //   {
    //     name: "共享型", //名称
    //     value: 191, //值
    //     itemStyle: {
    //       color: "#5092FF"
    //     }
    //   },
    //   {
    //     name: "PPGA计算型",
    //     value: 131,
    //     itemStyle: {
    //       color: "#28D4D6"
    //     }
    //   },
    //   {
    //     name: "存储型",
    //     value: 151,
    //     itemStyle: {
    //       color: "#FFAA38"
    //     }
    //   },
    //   {
    //     name: "高主频型",
    //     value: 161,
    //     itemStyle: {
    //       color: "#FF7D33"
    //     }
    //   },
    //   {
    //     name: "异构服务型",
    //     value: 161,
    //     itemStyle: {
    //       color: "#CF76FF"
    //     }
    //   },
    //   {
    //     name: "其他",
    //     value: 51,
    //     itemStyle: {
    //       color: "#5AEA69"
    //     }
    //   }
    // ],
    data,
    0.8 // 可做为调整内环大小
  );
  //鼠标移动上去特效效果
  bindListen(charEch);
  charEch.setOption(option.value);
  initCloakCharts();
  return charEch;
};
// 生成扇形的曲面参数方程
const getPie3D = (pieData: any, internalDiameterRatio: any) => {
  let series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  let legendData = [];
  let legendBfb = [];
  let k = 1 - internalDiameterRatio;
  pieData.sort((a, b) => {
    return b.value - a.value;
  });
  // 为每一个饼图数据，生成一个 series-surface(参数曲面) 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;
    let seriesItem = {
      //系统名称
      name: typeof pieData[i].name === "undefined" ? `series${i}` : pieData[i].name,
      type: "surface",
      //是否为参数曲面（是）
      parametric: true,
      wireframe: {
        show: false //曲面图网格线（否）上面一根一根的
      },
      emphasis: {
        //中间文字显示
        show: true
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k
      }
      //设置饼图在容器中的位置(目前没发现啥用)
      //   center: ['50%', '100%']
    };

    //曲面的颜色、不透明度等样式。
    if (typeof pieData[i].itemStyle != "undefined") {
      let itemStyle = {};
      typeof pieData[i].itemStyle.color != "undefined" ? (itemStyle.color = pieData[i].itemStyle.color) : null;
      typeof pieData[i].itemStyle.opacity != "undefined" ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null;
      seriesItem.itemStyle = itemStyle;
    }
    series.push(seriesItem);
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  legendData = [];
  legendBfb = [];
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      // series[i].pieData.value
      // 我这里做了一个处理，使除了第一个之外的值都是7
      series[0].pieData.value
    );
    startValue = endValue;
    let bfb = fomatFloat(series[i].pieData.value / sumValue, 4);
    legendData.push({
      name: series[i].name,
      value: bfb
    });
    legendBfb.push({
      name: series[i].name,
      value: bfb
    });
  }
  //(第二个参数可以设置你这个环形的高低程度)
  let boxHeight = getHeight3D(series, 13); //通过传参设定3d饼/环的高度
  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  let option = {
    //图例组件
    legend: {
      data: legendData,
      //图例列表的布局朝向。
      orient: "horizontal",
      left: 10,
      top: 2,
      //图例文字每项之间的间隔
      itemGap: 15,
      textStyle: {
        color: "#ebebf0"
      },
      show: true,
      icon: "rect",
      itemWidth: 7,
      itemHeight: 7,
      //格式化图例文本（我是数值什么显示什么）
      formatter: function (name) {
        // let target;
        // for (let i = 0, l = pieData.length; i < l; i++) {
        //   if (pieData[i].name == name) {
        //     target = pieData[i].value;
        //   }
        // }
        // return `${name}: ${target}`;
        return `${name}`;
      }
      // 这个可以显示百分比那种（可以根据你想要的来配置）
      //   formatter: function(param) {
      //       let item = legendBfb.filter(item => item.name == param)[0];
      //       let bfs = that.fomatFloat(item.value * 100, 2) + "%";
      //       console.log(item.name)
      //       return `${item.name} :${bfs}`;
      //   }
    },
    tooltip: {
      formatter: params => {
        if (params.seriesName !== "mouseoutSeries" && params.seriesName !== "pie2d") {
          return (
            `${params.seriesName}<br/>` +
            `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
            `${option.series[params.seriesIndex].pieData.value}`
          );
        }
      }
    },
    title: {
      text: "asa",
      x: "center",
      top: 85,
      textStyle: {
        color: "#90AFD0",
        fontSize: 16
      }
    },
    //这个可以变形
    xAxis3D: {
      min: -1,
      max: 1
    },
    yAxis3D: {
      min: -1,
      max: 1
    },
    zAxis3D: {
      min: -1,
      max: 1
    },
    //此处是修改样式的重点
    grid3D: {
      show: false,
      boxHeight: boxHeight, //圆环的高度
      //这是饼图的位置
      top: "-6%",
      // left: "-1%",
      viewControl: {
        //3d效果可以放大、旋转等，请自己去查看官方配置
        alpha: 30, //角度(这个很重要 调节角度的)
        distance: 200, //调整视角到主体的距离，类似调整zoom(这是整体大小)
        rotateSensitivity: 0, //设置为0无法旋转
        zoomSensitivity: 0, //设置为0无法缩放
        panSensitivity: 0, //设置为0无法平移
        autoRotate: false //自动旋转
      }
    },
    series: series
  };
  // option.title.text = option.series[0].name + " " + option.series[0].pieData.value;
  option.title.text = option.series[0].name;
  return option;
};
//获取3d丙图的最高扇区的高度
const getHeight3D = (series, height) => {
  series.sort((a, b) => {
    return b.pieData.value - a.pieData.value;
  });
  return (height * 25) / series[0].pieData.value;
};
// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation//这是一个自定义计算的方法
const fomatFloat = (num, n) => {
  let f = parseFloat(num);
  if (isNaN(f)) {
    return false;
  }
  f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂
  let s = f.toString();
  let rs = s.indexOf(".");
  //判定如果是整数，增加小数点再补0
  if (rs < 0) {
    rs = s.length;
    s += ".";
  }
  while (s.length <= rs + n) {
    s += "0";
  }
  return s;
};

const bindListen = myChart => {
  let selectedIndex = "";
  let hoveredIndex = "";
  // 监听点击事件，实现选中效果（单选）
  myChart.on("click", params => {
    // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
    let isSelected = !option.value.series[params.seriesIndex].pieStatus.selected;
    let isHovered = option.value.series[params.seriesIndex].pieStatus.hovered;
    let k = option.value.series[params.seriesIndex].pieStatus.k;
    let startRatio = option.value.series[params.seriesIndex].pieData.startRatio;
    let endRatio = option.value.series[params.seriesIndex].pieData.endRatio;
    // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
    if (selectedIndex !== "" && selectedIndex !== params.seriesIndex) {
      option.value.series[selectedIndex].parametricEquation = getParametricEquation(
        option.value.series[selectedIndex].pieData.startRatio,
        option.value.series[selectedIndex].pieData.endRatio,
        false,
        false,
        k,
        // option.value.series[selectedIndex].pieData.value
        option.value.series[0].pieData.value
      );
      option.value.series[selectedIndex].pieStatus.selected = false;
    }
    // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
    option.value.series[params.seriesIndex].parametricEquation = getParametricEquation(
      startRatio,
      endRatio,
      isSelected,
      isHovered,
      k,
      // option.value.series[params.seriesIndex].pieData.value
      option.value.series[0].pieData.value
    );
    option.value.series[params.seriesIndex].pieStatus.selected = isSelected;
    // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
    isSelected ? (selectedIndex = params.seriesIndex) : null;
    // 使用更新后的 option，渲染图表
    myChart.setOption(option.value);
  });
  // 监听 mouseover，近似实现高亮（放大）效果
  myChart.on("mouseover", params => {
    // 准备重新渲染扇形所需的参数
    let isSelected;
    let isHovered;
    let startRatio;
    let endRatio;
    let k;
    // 如果触发 mouseover 的扇形当前已高亮，则不做操作
    if (hoveredIndex === params.seriesIndex) {
      return;
      // 否则进行高亮及必要的取消高亮操作
    } else {
      // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
      if (hoveredIndex !== "") {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
        isSelected = option.value.series[hoveredIndex].pieStatus.selected;
        isHovered = false;
        startRatio = option.value.series[hoveredIndex].pieData.startRatio;
        endRatio = option.value.series[hoveredIndex].pieData.endRatio;
        k = option.value.series[hoveredIndex].pieStatus.k;
        // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
        option.value.series[hoveredIndex].parametricEquation = getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          // option.value.series[hoveredIndex].pieData.value
          option.value.series[0].pieData.value
        );
        option.value.series[hoveredIndex].pieStatus.hovered = isHovered;
        // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
        hoveredIndex = "";
      }
      // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
      if (params.seriesName !== "mouseoutSeries" && params.seriesName !== "pie2d") {
        option.value.title.text = " " + option.value.series[params.seriesIndex].name;
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
        isSelected = option.value.series[params.seriesIndex].pieStatus.selected;
        isHovered = true;
        startRatio = option.value.series[params.seriesIndex].pieData.startRatio;
        endRatio = option.value.series[params.seriesIndex].pieData.endRatio;
        k = option.value.series[params.seriesIndex].pieStatus.k;
        // 对当前点击的扇形，执行高亮操作（对 option 更新）
        option.value.series[params.seriesIndex].parametricEquation = getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          // option.value.series[params.seriesIndex].pieData.value + 15
          option.value.series[0].pieData.value + option.value.series[0].pieData.value * 0.55
        );
        option.value.series[params.seriesIndex].pieStatus.hovered = isHovered;
        // 记录上次高亮的扇形对应的系列号 seriesIndex
        hoveredIndex = params.seriesIndex;
      }
      // 使用更新后的 option，渲染图表
      myChart.setOption(option.value);
    }
  });
  // 修正取消高亮失败的 bug
  myChart.on("globalout", function () {
    // 准备重新渲染扇形所需的参数
    let isSelected;
    let isHovered;
    let startRatio;
    let endRatio;
    let k;
    if (hoveredIndex !== "") {
      // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
      isSelected = option.value.series[hoveredIndex].pieStatus.selected;
      isHovered = false;
      k = option.value.series[hoveredIndex].pieStatus.k;
      startRatio = option.value.series[hoveredIndex].pieData.startRatio;
      endRatio = option.value.series[hoveredIndex].pieData.endRatio;
      // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
      option.value.series[hoveredIndex].parametricEquation = getParametricEquation(
        startRatio,
        endRatio,
        isSelected,
        isHovered,
        k,
        // option.value.series[hoveredIndex].pieData.value
        option.value.series[0].pieData.value
      );
      option.value.series[hoveredIndex].pieStatus.hovered = isHovered;
      // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
      hoveredIndex = "";
    }
    // 使用更新后的 option，渲染图表
    myChart.setOption(option.value);
  });
};
const getParametricEquation = (startRatio: any, endRatio: any, isSelected: any, isHovered: any, k: any, h: any) => {
  // 计算
  let midRatio = (startRatio + endRatio) / 2;
  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;
  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }
  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== "undefined" ? k : 1 / 3;
  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1;
  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20
    },
    x: function (u, v) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u, v) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    }
  };
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 240px;
}
.pie-bg {
  position: absolute;
  top: 73px;
  left: 106px;
  z-index: -1;
  width: 200px;
}
.bottom-box {
  position: absolute;
  bottom: 0;
  display: flex;
  width: 100%;
  height: 115px;
  .line {
    width: 1px;
    height: 48px;
    margin-top: 33px;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgb(255 255 255 / 0%), rgb(255 255 255 / 100%), rgb(151 151 151 / 0%)) 1 1;
  }
  .left-box {
    position: relative;
    width: 50%;
    .clock-bg {
      position: absolute;
      top: 15px;
      left: 30px;
      z-index: -1;
      width: 83px;
    }
    .left-echart {
      width: 100%;
      height: 100%;
    }
  }
  .right-box {
    position: relative;
    flex: 1;
    .clock-bg {
      position: absolute;
      top: 15px;
      left: 30px;
      z-index: -1;
      width: 83px;
    }
    .right-echart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
