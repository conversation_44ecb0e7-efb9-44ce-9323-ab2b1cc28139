<template>
  <div class="card">
    <div class="edit-title">企业信息</div>
    <el-form
      ref="partnerFormRef"
      :disabled="isView"
      :model="partnerForm"
      :rules="rules"
      label-position="left"
      label-width="140px"
    >
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item label="企业名称" prop="companyName">
            <el-input v-model="partnerForm.companyName" placeholder="请填写营业执照上的企业名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="社会信用统一代码" prop="usciCode">
            <el-input v-model="partnerForm.usciCode" placeholder="请填写证件上的社会信用统一代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <div style="display: flex">
            <el-form-item label="公司注册地" prop="provinceCode">
              <el-select
                v-model="partnerForm.provinceCode"
                :label-width="'100px'"
                style="width: 200px"
                placeholder="请选择省份"
                @change="onProvinceChange"
                ref="provinceCodeRef"
              >
                <el-option v-for="item in province" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
            <span style="margin: 0 8px; line-height: 32px">-</span>
            <el-form-item label="" prop="cityCode" label-width="0">
              <el-select v-model="partnerForm.cityCode" placeholder="请选择市" ref="cityCodeRef">
                <el-option v-for="item in city" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属行业" prop="industryType">
            <el-select v-model="partnerForm.industryType" placeholder="请选择所属行业" style="width: 100%">
              <el-option v-for="item in industryTypeDict" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册时间" prop="registeredTime">
            <el-date-picker
              type="date"
              v-model="partnerForm.registeredTime"
              placeholder="请选择注册时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="子商户号" prop="merchantNumber">
            <el-input v-model="partnerForm.merchantNumber" placeholder="请填写子商户号" />
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="注册资本（万元）" prop="registeredCapital">
            <el-input-number v-model="partnerForm.registeredCapital" :precision="0" style="width: 170px" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="营业执照" prop="companyCertificateUrl">
            <UploadImg
              :is-private="true"
              :file-size="10"
              :file-type="fileType"
              v-model:image-url="partnerForm.companyCertificateUrl"
              width="135px"
              height="135px"
            >
              <template #tip>
                上传的图片格式要求jpg、jpeg、bmp、png，不超过10M。
                可以添加“华为云备案、云服务申请、云服务认证、云服务备案”等水印；但不能遮挡关键信息，例如企业名称、公司证件号。
              </template>
            </UploadImg>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="LOGO" prop="logo">
            <UploadImg
              :is-private="false"
              :file-size="2"
              :file-type="fileType"
              v-model:image-url="partnerForm.logo"
              width="135px"
              height="135px"
            >
              <template #tip> 上传的图片格式要求jpg、jpeg、bmp、png，不超过2M。 </template>
            </UploadImg>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司英文名" prop="companyEnglishName">
            <el-input v-model="partnerForm.companyEnglishName" placeholder="请输入公司英文名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业简称" prop="companyShortenName">
            <el-input v-model="partnerForm.companyShortenName" placeholder="请输入企业简称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在国家" prop="country">
            <!-- <el-input v-model="partnerForm.country" placeholder="请输入所在国家" /> -->
            <el-select v-model="partnerForm.country" placeholder="请选择所在国家">
              <el-option label="中国" value="中国" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <div style="display: flex">
            <el-form-item label="所在地" prop="province">
              <el-select
                v-model="partnerForm.province"
                :label-width="'100px'"
                style="width: 200px"
                name="provinceCode"
                placeholder="请选择省份"
                @change="val => onProvinceChange(val, 'location')"
                ref="provinceRef"
              >
                <el-option v-for="item in locationProvince" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <span style="margin: 0 8px; line-height: 32px">-</span>
            <el-form-item label="" prop="city" label-width="0">
              <el-select v-model="partnerForm.city" placeholder="请选择市" ref="cityRef">
                <el-option v-for="item in locationCity" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="法定代表人" prop="legalRepresentative">
            <el-input v-model="partnerForm.legalRepresentative" placeholder="请输入法定代表人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件" prop="idcardNumber">
            <el-input v-model="partnerForm.idcardNumber" placeholder="请输入证件">
              <template #prepend>
                <el-select v-model="partnerForm.idcardType" style="width: 115px" @change="onChangeIdcardNumber">
                  <el-option
                    :key="item.value"
                    v-for="item in idcardTypeDict"
                    :label="item.label"
                    :value="item.value"
                    @change="onChangeIdcardType"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纳税人类型" prop="taxpayerType">
            <el-select v-model="partnerForm.taxpayerType" placeholder="请选择纳税人类型" style="width: 100%">
              <el-option v-for="item in taxpayerTypeDict" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="税务登记号" prop="taxNumber">
            <el-input v-model="partnerForm.taxNumber" placeholder="请输入税务登记号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构地址" prop="companyAddress">
            <el-input v-model="partnerForm.companyAddress" placeholder="请输入机构地址" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构类型" prop="companyType">
            <el-select v-model="partnerForm.companyType" placeholder="请选择机构类型" style="width: 100%">
              <el-option v-for="item in companyTypeDict" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="代码证办理日期" prop="certificateRegisteredDate">
            <el-date-picker
              type="date"
              placeholder="选择代码证办理日期"
              v-model="partnerForm.certificateRegisteredDate"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="代码证作废日期" prop="certificateInvalidDate">
            <el-date-picker
              type="date"
              placeholder="选择代码证作废日期"
              v-model="partnerForm.certificateInvalidDate"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司电话号码" prop="companyPhone">
            <el-input v-model="partnerForm.companyPhone" placeholder="请输入公司电话号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司传真号码" prop="companyFax">
            <el-input v-model="partnerForm.companyFax" placeholder="请输入公司传真号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主管机构名称" prop="masterOrg">
            <el-input v-model="partnerForm.masterOrg" placeholder="请输入主管机构名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主管机构代码" prop="masterOrgCode">
            <el-input v-model="partnerForm.masterOrgCode" placeholder="请输入主管机构代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="简介" prop="resume">
            <el-input v-model="partnerForm.resume" placeholder="请输入简介" type="textarea" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="partnerForm.sort" :min="0" placeholder="请输入排序" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <div class="edit-title mt10">银行信息</div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="户主名" prop="bankAccountUser">
            <el-input v-model="partnerForm.bankAccountUser" placeholder="请输入户主名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="银行账号" prop="bankAccountNumber">
            <el-input v-model="partnerForm.bankAccountNumber" placeholder="请输入银行账号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行名称" prop="registerBankName">
            <el-input v-model="partnerForm.registerBankName" placeholder="请输入开户行名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="银行名称" prop="bankName">
            <el-input v-model="partnerForm.bankName" placeholder="请输入银行名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行联行号" prop="interBankNumber">
            <el-input v-model="partnerForm.interBankNumber" placeholder="请输入开户行联行号" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="edit-title mt10">公司联系人</div>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人姓名" prop="companyContactName">
            <el-input v-model="partnerForm.companyContactName" placeholder="请输入联系人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人电话" prop="companyContactPhone">
            <el-input v-model="partnerForm.companyContactPhone" placeholder="请输入联系人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人邮箱" prop="companyContactEmail">
            <el-input v-model="partnerForm.companyContactEmail" placeholder="请输入联系人邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="edit-title mt10">业务联系方式</div>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人姓名" prop="businessContactName">
            <el-input v-model="partnerForm.businessContactName" placeholder="请输入联系人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人电话" prop="businessContactPhone">
            <el-input v-model="partnerForm.businessContactPhone" placeholder="请输入联系人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人邮箱" prop="businessContactEmail">
            <el-input v-model="partnerForm.businessContactEmail" placeholder="请输入联系人邮箱" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <div class="edit-title mt10">合同联系方式</div>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人姓名" prop="contractContactName">
            <el-input v-model="partnerForm.contractContactName" placeholder="请输入联系人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人电话" prop="contractContactPhone">
            <el-input v-model="partnerForm.contractContactPhone" placeholder="请输入联系人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人邮箱" prop="contractContactEmail">
            <el-input v-model="partnerForm.contractContactEmail" placeholder="请输入联系人邮箱" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <div class="edit-title mt10">意向合作产品</div>
        </el-col>
        <template v-if="partnerForm._intendCooperateProducts?.length">
          <div
            v-for="(domain, index) in partnerForm._intendCooperateProducts"
            :key="domain.key"
            style="display: flex; width: 100%"
          >
            <el-col :span="8">
              <el-form-item
                label="产品名称"
                :prop="'_intendCooperateProducts.' + index + '.name'"
                :rules="[
                  {
                    required: true,
                    message: '请输入产品名称',
                    trigger: 'blur'
                  },
                  { max: 50, validator: validateLength, trigger: 'blur' }
                ]"
              >
                <el-input v-model="domain.name" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="产品简介"
                :prop="'_intendCooperateProducts.' + index + '.value'"
                :rules="[
                  {
                    required: true,
                    message: '请输入产品简介',
                    trigger: 'blur'
                  },
                  { max: 100, validator: validateLength, trigger: 'blur' }
                ]"
              >
                <el-input v-model="domain.value" placeholder="请输入产品简介" />
              </el-form-item>
            </el-col>
            <span class="custom-icon" v-show="!isView">
              <i class="iconfont2 opera-icon icon-a-lineicon_plus_circle" @click.prevent="addDomain(domain)"></i>
              <i class="iconfont2 opera-icon icon-lajitong" @click.prevent="removeDomain(domain)"></i>
            </span>
          </div>
        </template>
        <template v-else>
          <div style="display: flex; width: 100%">
            <el-col :span="8">
              <el-form-item
                label="产品名称"
                :prop="'_intendCooperateProducts.' + index + '.name'"
                :rules="[
                  {
                    required: true,
                    message: '请输入产品名称',
                    trigger: 'blur'
                  },
                  { max: 50, validator: validateLength, trigger: 'blur' }
                ]"
              >
                <el-input v-model="domain.name" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="产品简介"
                :prop="'_intendCooperateProducts.' + index + '.value'"
                :rules="[
                  {
                    required: true,
                    message: '请输入产品简介',
                    trigger: 'blur'
                  },
                  { max: 100, validator: validateLength, trigger: 'blur' }
                ]"
              >
                <el-input v-model="domain.value" placeholder="请输入产品简介" />
              </el-form-item>
            </el-col>
            <span class="custom-icon">
              <i class="iconfont2 opera-icon icon-a-lineicon_plus_circle" @click.prevent="addDomain(domain)"></i>
              <i class="iconfont2 opera-icon icon-lajitong" @click.prevent="removeDomain(domain)"></i>
            </span>
          </div>
        </template>
        <el-col :span="24">
          <el-form-item label="产品附件文档" prop="_productDocs">
            <uploadFiles
              :is-private="true"
              v-model:file-list="partnerForm._productDocs"
              width="135px"
              height="135px"
              :file-type="'.doc,.docx,.pdf'"
              :limit="10"
              :show-file-list="true"
            >
              <template #tip>
                <div class="upload-tips">上传的文件格式要求word、pdf，不超过10M。</div>
              </template>
            </uploadFiles>
          </el-form-item>
        </el-col>
      </el-row>

      <div v-show="!isView">
        <el-button type="primary" @click="submitForm(partnerFormRef)"> 确定 </el-button>
        <el-button @click="goBack()"> 取消 </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="partnerEdit">
import { reactive, ref, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import UploadImg from "@/components/Upload/Img.vue";
import { useRoute, useRouter } from "vue-router";
import { getDictionary } from "@/api/modules/system";
import uploadFiles from "@/components/Upload/Files.vue";
import { queryRegionList, addProvider, updateExtProvider, getProviderDetail } from "@/api/modules/partner";
import { closeTabItem } from "@/utils/closeTab";
interface DetailType {
  enterprise: any;
  provider: any;
}
enum editType {
  "add" = 0,
  "edit" = 1,
  "agree" = 2,
  "view" = 3
}
const route = useRoute();
const provinceCodeRef = ref();
const cityCodeRef = ref();
const provinceRef = ref();
const cityRef = ref();

let isEdit = route.query.type === `${editType.edit}`;

/* 是否查看详情 */

const isView = computed(() => {
  if (route.query.type === `${editType.view}`) {
    return true;
  } else {
    return false;
  }
});

const getInitForm = () => {
  return {
    companyName: "",
    usciCode: "",
    province: "",
    provinceCode: "",
    city: "",
    cityCode: "",
    industryType: "",
    registeredTime: "",
    registeredCapital: 0,
    companyCertificateUrl: "",
    logo: "",
    resume: "",
    sort: "",

    companyEnglishName: "",
    companyShortenName: "",
    country: "",
    // locationProvinceCode: "",
    // locationProvince: "",
    // locationCity: "",
    // locationCityCode: "",
    legalRepresentative: "",
    idcardNumber: "",
    taxpayerType: "",
    taxNumber: "",
    companyAddress: "",
    companyType: "",
    certificateRegisteredDate: "",
    certificateInvalidDate: "",
    companyPhone: "",
    companyFax: "",
    masterOrg: "",
    masterOrgCode: "",
    bankAccountUser: "",
    bankAccountNumber: "",
    registerBankName: "",
    bankName: "",
    interBankNumber: "",
    companyContactName: "",
    companyContactPhone: "",
    companyContactEmail: "",
    businessContactName: "",
    businessContactPhone: "",
    businessContactEmail: "",
    contractContactName: "",
    contractContactPhone: "",
    contractContactEmail: "",

    agreementUrl: "",
    idcardType: "",
    _intendCooperateProducts: [{ key: 1, name: "", value: "" }],
    _productDocs: []
    // merchantNumber: ""
  };
};

const router = useRouter();
const fileType = ["image/jpeg", "image/png", "image/jpg", "image/bmp"];

const partnerFormRef = ref<FormInstance>();
const partnerForm = ref(getInitForm());
const industryTypeDict = ref<any>([]);
const taxpayerTypeDict = ref<any>([]);
const idcardTypeDict = ref<any>([]);
const companyTypeDict = ref<any>([]);
const province = ref<any>([]);
const city = ref<any>([]);
const locationProvince = ref<any>([]);
const locationCity = ref<any>([]);
const details = reactive<DetailType>({
  enterprise: {},
  provider: {}
});

onMounted(() => {
  if (route.query.type === `${editType.edit}` || route.query.type === `${editType.view}`) {
    const id = JSON.parse(JSON.stringify(route.query.id));
    getProviderDetail({ id }).then((result: any) => {
      const enterprise = result.data?.enterprise;
      const detailData: any = result.data?.provider;
      details.enterprise = enterprise;
      details.provider = detailData;
      partnerForm.value = {
        ...detailData,
        companyName: enterprise?.companyName,
        usciCode: enterprise?.usciCode,
        provinceCode: enterprise?.provinceCode,
        cityCode: enterprise?.cityCode,
        industryType: enterprise?.industryType,
        registeredTime: enterprise?.registeredTime,
        registeredCapital: enterprise?.registeredCapital,
        companyCertificateUrl: enterprise?.companyCertificateUrl
      };
      const _productDocs = JSON.parse(detailData.productDocs);

      partnerForm.value._productDocs = _productDocs.map((item: any) => {
        return { ...item, name: item.originalName, url: item.link };
      });
      partnerForm.value._intendCooperateProducts = JSON.parse(detailData.intendCooperateProducts);
      // 根据省份获取地市
      getCityList(enterprise.provinceCode);
      getCityList(detailData.provinceCode, "location");
    });
  }
  queryRegionList({})
    .then((result: any) => {
      const provinceList = result.data.map((item: { regionCode: string; regionName: string }) => {
        return { ...item, label: item.regionName, value: item.regionCode };
      });
      province.value = provinceList;
      locationProvince.value = provinceList;
    })
    .catch(err => {
      console.log(err);
    });

  getDictionary({ dictCode: "dict_industry_type" }).then((res: any) => {
    const data = res?.data;
    data.forEach((item: any = {}) => {
      item.value = item.itemKey;
      item.label = item.itemValue;
    });
    industryTypeDict.value = JSON.parse(JSON.stringify(data));
  });
  getDictionary({ dictCode: "dict_taxpayer_type" }).then((res: any) => {
    const data = res?.data;
    data.forEach((item: any = {}) => {
      item.value = item.itemKey;
      item.label = item.itemValue;
    });
    taxpayerTypeDict.value = JSON.parse(JSON.stringify(data));
  });
  getDictionary({ dictCode: "dict_personal_idcard_type" }).then((res: any) => {
    const data = res?.data;
    data.forEach((item: any = {}) => {
      item.value = item.itemKey;
      item.label = item.itemValue;
    });
    idcardTypeDict.value = JSON.parse(JSON.stringify(data));
  });
  getDictionary({ dictCode: "dict_company_type" }).then((res: any) => {
    const data = res?.data;
    data.forEach((item: any = {}) => {
      item.value = item.itemKey;
      item.label = item.itemValue;
    });
    companyTypeDict.value = JSON.parse(JSON.stringify(data));
  });
});

const onProvinceChange = (value: any, type = "") => {
  if (type === "location") {
    partnerForm.value.city = "";
  } else {
    partnerForm.value.cityCode = "";
  }
  getCityList(value, type);
};
const getCityList = (regionCode: string, type = "") => {
  queryRegionList({ parentRegionCode: regionCode })
    .then((result: any) => {
      const cityList = result.data.map((item: { regionCode: string; regionName: string }) => {
        return { ...item, label: item.regionName, value: item.regionCode };
      });
      if (type === "location") {
        locationCity.value = cityList;
      } else {
        city.value = cityList;
      }
    })
    .catch(err => {
      console.log(err);
    });
};

const max20 = [{ max: 20, message: "最多输入20个字符", trigger: "blur" }];
const max50 = [{ max: 50, message: "最多输入50个字符", trigger: "blur" }];
const checkIdcardNumber = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请输入证件"));
  }
  if (!partnerForm.value.idcardType) {
    callback(new Error("请选择证件类型"));
  }
  callback();
};

const checkPhone = (rule: any, value: any, callback: any) => {
  const regexp = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/;
  if (value === "") callback();
  if (!regexp.test(value)) {
    callback(new Error("请输入正确的手机号码"));
  } else {
    return callback();
  }
};

const checkRegisteredCapital = (rule: any, value: any, callback: any) => {
  if (value === "") callback();
  if (value.toString().length > 10) {
    callback(new Error("最多输入10个字符"));
  } else {
    return callback();
  }
};

const rules = reactive<FormRules>({
  companyName: [{ required: true, message: "请输入企业名称", trigger: "blur" }, ...max50],
  usciCode: [{ required: true, message: "请输入社会信用统一代码", trigger: "blur" }, ...max20],
  provinceCode: [{ required: true, message: "请选择省份", trigger: "blur" }],
  province: [{ required: true, message: "请选择省份", trigger: "blur" }],
  cityCode: [{ required: true, message: "请选择市", trigger: "blur" }],
  city: [{ required: true, message: "请选择市", trigger: "blur" }],
  industryType: [{ required: true, message: "请选择所属行业", trigger: "blur" }],
  registeredTime: [{ required: true, message: "请选择注册时间", trigger: "blur" }],
  registeredCapital: [
    { required: true, message: "请输入注册资本", trigger: "blur" },
    {
      validator: checkRegisteredCapital,
      trigger: "blur"
    }
  ],
  legalRepresentative: [{ required: true, message: "请输入法定代表人", trigger: "blur" }, ...max20],
  idcardNumber: [{ required: true, validator: checkIdcardNumber, trigger: "blur" }, ...max50],
  taxpayerType: [{ required: true, message: "请选择纳税人类型", trigger: "blur" }],
  companyAddress: [{ required: true, message: "请输入机构地址", trigger: "blur" }],
  companyType: [{ required: true, message: "请选择机构类型", trigger: "blur" }],
  companyPhone: [{ required: true, message: "请输入公司电话号码", trigger: "blur" }, ...max20],
  bankAccountUser: [{ required: true, message: "请输入户主名", trigger: "blur" }, ...max20],
  bankAccountNumber: [{ required: true, message: "请输入银行账号", trigger: "blur" }, ...max20],
  registerBankName: [
    { required: true, message: "请输入开户行名称", trigger: "blur" },
    { max: 100, message: "最多输入100个字符", trigger: "blur" }
  ],
  bankName: [{ required: true, message: "请输入银行名称", trigger: "blur" }, ...max20],
  companyContactName: [{ required: true, message: "请输入联系人姓名", trigger: "blur" }, ...max50],
  companyContactPhone: [
    { required: true, message: "请输入联系人电话", trigger: "blur" },
    { validator: checkPhone, trigger: "blur" }
  ],
  companyContactEmail: [
    { required: true, message: "请输入联系人邮箱", trigger: "blur" },
    {
      type: "email",
      message: "请输入正确的邮箱地址"
    },
    ...max50
  ],
  mainBusiness: [
    { required: true, message: "请输入主营业务", trigger: "blur" },
    { max: 200, message: "最多输入200个字符", trigger: "blur" }
  ],
  companyCertificateUrl: [{ required: true, message: "请上传营业执照", trigger: "blur" }],
  logo: [],
  resume: [],
  sort: [],
  productName: [{ required: true, message: "请输入产品名称", trigger: "blur" }],
  description: [{ required: true, message: "请输入产品简介", trigger: "blur" }],
  taxNumber: max20,
  companyFax: max20,
  masterOrg: max20,
  masterOrgCode: max20,
  businessContactName: max50,
  businessContactEmail: [
    ...max50,
    {
      type: "email",
      message: "请输入正确的邮箱地址"
    }
  ],
  contractContactEmail: [
    ...max50,
    {
      type: "email",
      message: "请输入正确的邮箱地址"
    }
  ],
  businessContactPhone: [{ validator: checkPhone, trigger: "blur" }],
  contractContactPhone: [{ validator: checkPhone, trigger: "blur" }],
  idcardType: [{ required: true, message: "请选择证件类型", trigger: "blur" }]
  // merchantNumber: [{ required: true, message: "请填上子商户号", trigger: "blur" }]
});

const onChangeIdcardType = (value: string) => {
  partnerForm.value.idcardType = value;
};
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const params: any = Object.assign({}, partnerForm.value);

      const fileList = params._productDocs?.map((item: { url: string; uid: number; name: string; link: string }) => {
        return { link: item.link, uid: item.uid, originalName: item.name, name: item.name };
      });
      params.productDocs = JSON.stringify(fileList);
      params.intendCooperateProducts = JSON.stringify(params._intendCooperateProducts);
      Reflect.deleteProperty(params, "_intendCooperateProducts");
      Reflect.deleteProperty(params, "_productDocs");

      const enterpriseProvince = provinceCodeRef.value?.selected?.currentLabel;
      const enterpriseCity = cityCodeRef.value?.selected?.currentLabel;

      const addressProvince = provinceRef.value?.selected?.currentLabel;
      const addressCity = cityRef.value?.selected?.currentLabel;

      const reqParams: any = {
        enterprise: {
          ...details.enterprise,
          companyName: params.companyName,
          usciCode: params.usciCode,
          city: enterpriseCity,
          cityCode: params.cityCode,
          province: enterpriseProvince,
          provinceCode: params.provinceCode,
          companyAddress: params.companyAddress,
          industryType: params.industryType,
          registeredCapital: params.registeredCapital,
          registeredTime: params.registeredTime,
          companyCertificateUrl: params.companyCertificateUrl
        },
        provider: {
          ...details.provider,
          name: params.companyName,
          bankAccountNumber: params.bankAccountNumber,
          bankAccountUser: params.bankAccountUser,
          bankName: params.bankName,
          businessContactEmail: params.businessContactEmail,
          businessContactName: params.businessContactName,
          businessContactPhone: params.businessContactPhone,
          certificateInvalidDate: params.certificateInvalidDate,
          certificateRegisteredDate: params.certificateRegisteredDate,

          city: addressCity,
          cityCode: params.city,
          province: addressProvince,
          provinceCode: params.province,

          companyAddress: params.companyAddress,
          companyContactEmail: params.companyContactEmail,
          companyContactName: params.companyContactName,
          companyContactPhone: params.companyContactPhone,
          companyEnglishName: params.companyEnglishName,
          companyFax: params.companyFax,
          companyPhone: params.companyPhone,
          companyShortenName: params.companyShortenName,
          companyType: params.companyType,
          contractContactEmail: params.contractContactEmail,
          contractContactName: params.contractContactName,
          contractContactPhone: params.contractContactPhone,
          country: params.country,
          countryCode: params.countryCode,
          createTimeEnd: params.createTimeEnd,
          createTimeStart: params.createTimeStart,
          enterpriseCertificateId: params.enterpriseCertificateId,
          idcardNumber: params.idcardNumber,
          idcardType: params.idcardType,
          intendCooperateProducts: params.intendCooperateProducts,
          interBankNumber: params.interBankNumber,
          legalRepresentative: params.legalRepresentative,
          masterOrg: params.masterOrg,
          masterOrgCode: params.masterOrgCode,
          productDocs: params.productDocs,
          registerBankName: params.registerBankName,
          taxNumber: params.taxNumber,
          taxpayerType: params.taxpayerType,
          logo: params.logo,
          resume: params.resume,
          sort: params.sort,
          usciCode: params.usciCode
          // merchantNumber: params.merchantNumber
        }
      };

      if (isEdit) {
        reqParams.enterprise.id = details.enterprise?.id;
        reqParams.provider.id = details.provider?.id;
      }
      const api = isEdit ? updateExtProvider : addProvider;
      const text = isEdit ? "更新" : "新增";
      await api(reqParams).then(res => {
        if (res.code == 200) {
          ElMessage({
            message: `合作商${text}成功`,
            type: "success"
          });

          closeTabItem(route);
          goBack();
        } else {
          ElMessage({
            message: `合作商${text}失败`,
            type: "error"
          });
        }
      });
    }
  });
};

const goBack = () => {
  router.push("/partner/partnerManagement");
};
const onChangeIdcardNumber = (value: string) => {
  partnerForm.value.idcardType = value;
};

interface DomainItem {
  key: number;
  name: string;
  value: string;
}

const removeDomain = (item: DomainItem) => {
  const index = partnerForm.value._intendCooperateProducts.indexOf(item);
  if (index !== -1 && partnerForm.value._intendCooperateProducts.length !== 1) {
    const domainsList = JSON.parse(JSON.stringify(partnerForm.value._intendCooperateProducts));
    domainsList.splice(index, 1);
    partnerForm.value._intendCooperateProducts = domainsList;
  } else {
    ElMessage.info("至少需要一个意向合作产品！");
  }
};

const validateLength = (rule: any, value: any, callback: any) => {
  if (value.length <= rule.max) {
    callback();
  } else {
    callback(new Error(`限制输入的最大长度不超过${rule.max}个字符`));
  }
};

const addDomain = () => {
  if (partnerForm.value._intendCooperateProducts && partnerForm.value._intendCooperateProducts.length >= 10) {
    ElMessage.info("最多添加10个意向合作产品！");
  } else {
    partnerForm.value._intendCooperateProducts.push({
      key: Date.now(),
      name: "",
      value: ""
    });
  }
};
</script>

<style scoped lang="scss">
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
.mt10 {
  margin-top: 10px;
}
.custom-icon {
  height: 32px;
  line-height: 32px;
}
</style>
