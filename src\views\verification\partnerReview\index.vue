<template>
  <div class="table-box">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane v-for="item in tabs" :label="item.label" :name="item.name" :key="item.name"></el-tab-pane>
    </el-tabs>

    <div v-show="activeName === PageType.qualification">
      <InletTable :columns="columns" :status-dict="applyStatusDict" :active-name="activeName" :api="getProviderApplyList" />
    </div>
    <div v-show="activeName === PageType.introduce">
      <InletTable
        :columns="tableColumns"
        :status-dict="introduceStatusDict"
        :active-name="PageType.introduce"
        :api="getProviderIntroduce"
      />
    </div>
    <div v-show="activeName === PageType.enterprise">
      <InletTable
        :columns="agreementTableColumns"
        :status-dict="agreementStatusDict"
        :active-name="PageType.enterprise"
        :api="getProviderAgreementAudit"
      />
    </div>
    <div v-show="activeName === PageType.bill">
      <InletTable
        :columns="billTableColumns"
        :status-dict="productAuditStatus"
        :active-name="PageType.bill"
        :api="queryAuditPage"
        :extra-params="{
          providerId: partnerInfo?.providerId
        }"
      />
    </div>

    <partnerModal ref="partnerRef" @selected-partner="getPartnerInfo"></partnerModal>
  </div>
</template>

<script setup lang="tsx" name="partnerReview">
import { ref, onMounted, computed } from "vue";
import type { TabsPaneContext } from "element-plus";
// import { useDicStore } from "@/stores/modules/dictionaty";
import { ColumnProps } from "@/components/ProTable/interface";
import { Partner } from "@/api/interface/partner";
import { getProviderApplyList, getProviderIntroduce, getProviderAgreementAudit, queryAuditPage } from "@/api/modules/partner";

import InletTable from "./components/InletTable.vue";
import { PageType } from "./type";
import partnerModal from "@/views/partner/agreementManagement/partnerModal.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";

const {
  dict_provider_apply_audit_status: applyStatusDict,
  dict_provider_introduce_audit_status: introduceStatusDict,
  dict_provider_agreement_audit_status: agreementStatusDict,
  dict_certificate_apply_type: certificateApplyType,
  audit_status: productAuditStatus
} = useDict(
  "dict_provider_apply_audit_status",
  "dict_certificate_apply_type",
  "dict_provider_introduce_audit_status",
  "dict_provider_agreement_audit_status",
  "audit_status"
);

const tabs = [
  {
    label: "资质管理",
    name: "qualification"
  },
  {
    label: "引入管理",
    name: "introduce"
  },
  {
    label: "合作协议管理",
    name: "enterprise"
  },
  {
    label: "账单拨付",
    name: "bill"
  }
];

// const dictionaryStore = useDicStore();
const activeName = ref<PageType>(PageType.qualification);
const partnerRef = ref();
const partnerInfo = ref<any>();
const selectParName = ref<any>();

// onMounted(() => {
//   dictionaryStore.getApplyAuditStatus();
//   dictionaryStore.getIntroduceAuditStatus();
//   dictionaryStore.getAgreementAuditStatus();
//   dictionaryStore.getdict_certificate_apply_type();
//   dictionaryStore.getProductAuditStatus();
// });

/* 
dict_provider_apply_audit_status
dict_provider_introduce_audit_status
dict_provider_agreement_audit_status
*/
// const applyStatusDict: any = computed(() => dictionaryStore.applyAuditStatus);
// const introduceStatusDict: any = computed(() => dictionaryStore.introduceAuditStatus);
// const agreementStatusDict: any = computed(() => dictionaryStore.agreementAuditStatus);
// const certificateApplyType: any = computed(() => dictionaryStore.certificateApplyType);
// const productAuditStatus: any = computed(() => dictionaryStore.productAuditStatus);
// 表格配置项
const columns: ColumnProps<Partner.ProviderList>[] = [
  {
    prop: "applyType",
    label: "审核类型",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {certificateApplyType.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    render: (scope: any) => {
      return <>{useDictLabel(certificateApplyType.value, scope.row?.applyType)}</>;
    }
  },
  {
    prop: "name",
    label: "合作商名称",
    search: { el: "input" }
  },
  { prop: "usciCode", label: "统一社会信用代码", search: { el: "input" } },
  {
    prop: "createTime",
    label: "提交时间",
    width: 170
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 180,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {applyStatusDict.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditTime",
    label: "审核时间",
    width: 170
  },
  { prop: "auditUserName", label: "审核人员", width: 180 },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const tableColumns: ColumnProps<Partner.ProviderList>[] = [
  {
    prop: "applyType",
    label: "审核类型",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {certificateApplyType.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    render: (scope: any) => {
      return <>{useDictLabel(certificateApplyType.value, scope.row?.applyType)}</>;
    }
  },
  {
    prop: "name",
    label: "合作商名称",
    search: { el: "input" }
  },
  // { prop: "usciCode", label: "社会信用统一代码" },
  { prop: "agreementName", label: "合作协议名称", search: { el: "input" } },
  {
    prop: "createTime",
    label: "提交时间",
    width: 170
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {introduceStatusDict.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    width: 180
  },
  {
    prop: "auditTime",
    label: "审核时间",
    width: 170
  },
  { prop: "auditUserName", label: "审核人员", width: 180 },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const agreementTableColumns: ColumnProps<Partner.ProviderList>[] = [
  {
    prop: "applyType",
    label: "审核类型",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {certificateApplyType.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    render: (scope: any) => {
      return <>{useDictLabel(certificateApplyType.value, scope.row?.applyType)}</>;
    }
  },
  {
    prop: "name",
    label: "合作商名称",
    search: { el: "input" }
  },
  { prop: "agreementName", label: "合作协议名称", search: { el: "input" } },
  {
    prop: "createTime",
    label: "提交时间",
    width: 170
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {agreementStatusDict.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    width: 180
  },
  {
    prop: "auditTime",
    label: "审核时间",
    width: 170
  },
  { prop: "auditUserName", label: "审核人员", width: 180 },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const billTableColumns: ColumnProps<Partner.ProviderList>[] = [
  {
    prop: "providerName",
    label: "合作商名称",
    search: {
      render: (scope: any) => {
        return (
          <el-input
            model-value={selectParName.value}
            clearable
            placeholder="请输入"
            onClick={() => {
              partnerRef.value.handleOpen(partnerInfo.value);
            }}
            onClear={() => {
              selectParName.value = "";
              partnerInfo.value = null;
            }}
          ></el-input>
        );
      }
    },
    minWidth: 100
  },
  { prop: "billDate", label: "账单日期", minWidth: 150 },
  {
    prop: "createTime",
    label: "提交时间",
    minWidth: 170
  },
  {
    prop: "totalFee",
    label: "总金额（元）",
    minWidth: 150
  },
  {
    prop: "commissionTotalFee",
    label: "手续费（元）",
    minWidth: 150
  },
  {
    prop: "unicomTotalFee",
    label: "平台总金额（元）",
    minWidth: 150
  },
  {
    prop: "providerTotalFee",
    label: "合作商金额（元）",
    minWidth: 150
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {productAuditStatus.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    width: 180
  },
  {
    prop: "auditTime",
    label: "审核时间",
    width: 170
  },
  { prop: "auditorName", label: "审核人员", width: 180 },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const getPartnerInfo = (values: any) => {
  partnerInfo.value = values;
  selectParName.value = values.name;
};

const handleClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name as PageType;
};
</script>

<style lang="scss" scoped>
:global(.overflow-tooltip-style) {
  max-width: calc(100% - 24px);
  white-space: pre-wrap;
}
</style>
