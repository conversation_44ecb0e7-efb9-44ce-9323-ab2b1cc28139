export namespace CoreSystem {
  /* 用户配额 */
  export interface UserQuotaSearch {
    account?: string;
    phone?: string;
  }

  export interface UserQuotaItem {
    account?: string;
    phone?: string;
    totalCpuCount: number;
    totalGpuCount: number;
    totalMemoryCount: number;
    useCpuCount?: number;
    useGpuCount?: number;
    useMemoryCount?: number;
    userId?: string;
    userName?: string;
    thresholdBalance?: number;
  }

  export interface UserQuotaItemData extends UserQuotaItem {
    id: string | any;
  }

  /* ----------------------------- 实例单价表配置---------------- */

  export interface InstancePriceSearch {
    hardwareCode?: string;
    instanceName?: string;
    clusterId?: string;
  }

  export interface InstancePriceItem {
    cpu: number | null;
    gpu: number | null;
    graphics: number | null;
    hardwareCode: string;
    instanceName: string;
    memory: number | null;
    storage: string;
    clusterId: string | null;
    clusterName?: string;
    platformCode?: string;
  }
  export interface InstancePriceItemDetail extends InstancePriceItem {
    id: string | null;
    price: number;
    status: string;
  }

  /* ----------------------------- 硬件资源权重表配置---------------- */
  export interface hardwareWeightSearch {
    hardwareCode?: string;
    // clusterId?: string;
    platformCode?: string;
  }

  export interface hardwareWeightItem {
    id: string;
    cpuWeight: number | null;
    cpuPrice: number | null;
    gpuWeight: number | null;
    gpuPrice: number | null;
    memoryWeight: number | null;
    memoryPrice: number | null;
    hardwareCode: string;
    // clusterId: string;
    platformCode: string;
    updateTime?: string;
  }

  /* ----------------------------- 用户资源池权限---------------- */
  export interface userPlatformSearch {
    account?: string;
    phone?: string;
  }

  export interface userPlatformItem {
    account?: string;
    phone?: string;
    userName?: string;
    userId: string;
  }

  export interface userPlatformItemDetail extends userPlatformItem {
    displayName?: string | null;
    status?: number;
    platformCode?: string;
  }

  // export interface PlatformListItem {
  //   platformCode: string;
  //   status: number;
  // }

  // export interface userPlatformSubmitItem extends userPlatformItemDetail{
  //   platformCode: string;
  //   status: number;
  //   userId: string;
  // }

  /* ----------------------------- 仪表盘---------------- */
  export interface dashboardClusterItemDetail {
    partitionName: string;
    nodeCount: number;
    nodeUsageRate: number;
    cpuUsageRate: number;
    idleCpuCount: number;
    gpuUsageRate: number;
    idleGpuCount: number;
    runningJobCount: number;
    pendingJobCount: number;
    partitionStatus: string;
  }
  export interface dashboardClusterItem {
    totalNodeCount: number;
    totalNodeUsageRate: number;
    totalIdleNodeCount: number;
    totalCpuCoreCount: number;
    totalCpuUsageRate: number;
    totalIdleCpuCount: number;
    totalGpuCoreCount: number;
    totalGpuUsageRate: number;
    totalIdleGpuCount: number;
    totalRunningJobCount: number;
    totalPendingJobCount: number;
    partitions: dashboardClusterItemDetail[];
  }
}
