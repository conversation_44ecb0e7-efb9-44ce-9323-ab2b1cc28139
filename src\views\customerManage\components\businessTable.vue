<template>
  <div class="table-box">
    <ProTable :tool-button="false" :request-auto="false" ref="proTable" :columns="columns" :request-api="getTableList">
    </ProTable>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance } from "@/components/ProTable/interface";
import { queryBusiness } from "@/api/modules/customerManage";
const props = defineProps<{ id?: string }>();
const proTable = ref<ProTableInstance>();
const columns = [
  { type: "index", label: "序号", width: 80 },
  { prop: "itemName	", label: "商机类型" },
  { prop: "itemCategory", label: "咨询产品/解决方案" },
  { prop: "consultMsg", label: "咨询问题" },
  { prop: "regionName", label: "区域" },
  { prop: "createTime	", label: "咨询时间" }
];
const getTableList = async (params: { current: number; size: number }) => {
  return await queryBusiness({ userId: props.id, ...params });
};

watch(
  () => props.id,
  () => {
    proTable.value?.getTableList();
  }
);
</script>
<style lang="scss" scoped>
::v-deep(.el-scrollbar) {
  min-height: 120px;
}
</style>
