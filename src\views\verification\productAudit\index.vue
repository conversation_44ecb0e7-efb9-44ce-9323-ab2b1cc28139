<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="产品列表"
      :request-auto="false"
      :tool-button="false"
      :columns="columns"
      :request-api="getTableList"
    >
      <template #auditStatus="{ row }">
        <CustomTag
          :type="statusTypeMap[row.auditStatus]"
          :status="row.auditStatus"
          :label="useDictLabel(verifyStatusDict, row.auditStatus) || '--'"
        ></CustomTag>
      </template>
      <template #auditOperation="{ row }">
        {{ useDictLabel(audit_operation, row?.auditOperation) }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span>
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, OperateType.detail)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'review'">
          <el-tooltip content="审核" placement="top" v-if="scope.row.auditStatus == PENDINGAUDIT">
            <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, OperateType.review)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="productManageList">
import { ref, onMounted, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useProductStore } from "@/stores/modules/product";
import { useDicStore } from "@/stores/modules/dictionaty";
import { getProductAuditList } from "@/api/modules/product";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";

const { audit_operation, audit_status } = useDict("audit_operation", "audit_status");

const statusTypeMap: { [key: string]: string } = {
  "1": "yellow",
  "2": "green",
  "3": "red"
};

const { BUTTONS } = useAuthButtons();
enum OperateType {
  "detail" = "1",
  "review" = "2"
}

const PENDINGAUDIT = 1; // 待审核
const route = useRoute();
const router = useRouter();
const productStore = useProductStore();
const dictionaryStore = useDicStore();
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();
onMounted(() => {
  proTable.value?.getTableList();
});
// 激活时获取列表（新增、编辑更新列表）
// onActivated(() => {
//   proTable.value?.getTableList();
// });

// 审核操作类型
const auditOperationDict: any = computed(() => dictionaryStore.auditOperation);
// 审核状态
const verifyStatusDict: any = computed(() => {
  const status = audit_status.value;
  status?.forEach((item: any = {}) => {
    item.value = String(item.itemKey);
  });
  return status;
});

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.descs = "createTime";
  return getProductAuditList(newParams);
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "productName",
    label: "产品名称",
    search: { el: "input" },
    render: scope => {
      console.log("scope", scope, scope.row.auditOperation !== "3");
      return scope.row.auditOperation !== "3"
        ? scope.row?.newValue?.productName || "--"
        : scope.row?.originalValue?.productName || "--";
    }
  },
  // { prop: "newValue.cooperateType", label: "合作类型", width: 90, enum: cooperateDic },
  // { prop: "newValue.providerId", label: "产品服务商", width: 100, enum: providerList },
  // {
  //   prop: "newValue.categoryFullName",
  //   label: "产品分类",
  //   width: 180
  // },
  {
    prop: "auditOperation",
    label: "审核操作",
    width: 110,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {audit_operation.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {verifyStatusDict.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "creatorName", label: "提交人员", width: 100 },
  {
    prop: "createTime",
    label: "提交时间",
    sortable: true,
    width: 170
  },
  { prop: "updaterName", label: "审核人" },
  {
    prop: "updateTime",
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const toView = (row: any = {}, type = OperateType.detail) => {
  productStore.getProductAuditDetail({ id: row.id }).then(res => {
    router.push(`/verification/productAudit/detail?id=${row?.id}&pageType=${type}`);
  });
};
watch(
  () => route.path,
  (path: string) => {
    if (path === "/verification/productAudit") {
      proTable.value?.getTableList();
    }
  }
);
</script>

<style lang="scss">
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
