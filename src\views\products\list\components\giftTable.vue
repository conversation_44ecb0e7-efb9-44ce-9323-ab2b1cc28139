<template>
  <div class="gift-table" v-if="tableList.length > 0">
    <ProTable ref="proTable" :data="tableList" :pageable="pageable" :tool-button="false" :columns="columns" :has-padding="false">
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-if="!disabled">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" :disabled="disabled" @click="handelDelete(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
      <template #pagination>
        <Pagination :pageable="pageable" :handle-size-change="() => {}" :handle-current-change="() => {}" />
      </template>
    </ProTable>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import { type ColumnProps } from "@/components/ProTable/interface/index";
import ProTable from "@/components/ProTable/index.vue";
import Pagination from "@/components/ProTable/components/Pagination.vue";
import { getCategory } from "@/api/modules/business/productArea";

const props = defineProps({
  skuIdIndex: {
    type: Number,
    default: () => 0
  },
  skuGiftList: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: () => false
  }
});

const emit = defineEmits(["update:giftSkuId", "update:skuGiftList"]);
const tableList = ref(props.skuGiftList || []);

const pageable = reactive({
  current: 1,
  size: 10,
  total: 0
});

const handelDelete = (row: any) => {
  if (props.disabled) {
    return;
  }

  const newList = tableList.value.filter((item: any) => item.id !== row.id);
  emit("update:giftSkuId", newList);
  emit("update:skuGiftList", newList);
};

watch(
  () => props.skuGiftList,
  () => {
    tableList.value = props.skuGiftList;
    pageable.total = props.skuGiftList?.length ?? 0;
    emit(
      "update:giftSkuId",
      tableList.value.map((item: any) => item.id)
    );
  },
  {
    immediate: true
  }
);

const columns = ref<ColumnProps[]>([
  {
    prop: "productName",
    label: "产品名称"
  },
  {
    prop: "categoryName",
    label: "产品分类",
    width: 120,
    enum: getCategory,
    fieldNames: {
      value: "id",
      label: "name"
    },
    isFilterEnum: false
  },
  {
    prop: "skuName",
    label: "产品规格"
  },
  {
    prop: "skuPrice",
    label: "规格单价"
  },
  {
    prop: "skuDiscountPrice",
    label: "折扣价"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
]);
</script>
<style lang="scss" scoped>
.gift-table {
  :deep(.card) {
    border: none !important;
    box-shadow: none !important;
  }
}
</style>
