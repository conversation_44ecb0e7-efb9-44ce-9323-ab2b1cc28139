<template>
  <div class="complex-content-box">
    <div class="name-box">
      <el-button type="primary" class="add-button" @click="addModel" :icon="CirclePlus">添加</el-button>
      <span class="tips"> <slot name="nameTips"></slot></span>
    </div>
    <div class="content-item" v-for="(item, index) in dataList" :key="index + 'complex'">
      <div class="item-title">
        <div class="name">{{ item.name || "模块名称" }}</div>
        <div class="right-btn">
          <el-button class="icon" link v-if="index !== dataList.length - 1" @click="xiayiItem(index, dataList, productDetail)">
            <i class="iconfont2 icon-xiayi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link v-if="index !== 0" @click="shangyiItem(index, dataList, productDetail)">
            <i class="iconfont2 icon-shangyi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link :icon="Delete" @click="deleteModel(index)"></el-button>
        </div>
      </div>
      <el-form ref="modelRuleFormRef" class="item-form" :model="item" :rules="rules" label-width="140px">
        <el-form-item label="模块名称" prop="name">
          <el-input v-model="item.name" :maxlength="10" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="模块标题" prop="title">
          <el-input v-model="item.title" :maxlength="50" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="展示类型" prop="type">
          <el-radio-group v-model="item.type" @change="handleTypeChange($event, index)">
            <el-radio v-for="(itemDic, indexDic) in theme_zone_type" :key="indexDic + 'jumpType'" :label="itemDic.itemKey">
              {{ itemDic.itemValue }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="模型分类" prop="categoryList" v-if="item.type === '6'">
          <!-- <el-input v-model="item.categoryList" clearable /> -->
          <div class="tag-list">
            <div class="condition-select-item" v-for="(item1, index1) in categoryList[index]" :key="item1">
              <p class="select-item-label">{{ item1 }}</p>
              <el-icon class="close-icon" @click="removeModelType(index, index1)"> <Close /></el-icon>
            </div>
            <span class="button-text" @click="modelTypeShow(categoryList[index], index)">新增</span>
          </div>
        </el-form-item>
        <el-form-item label="模型案例分类" prop="caseCategoryList" v-if="item.type === '7'">
          <!-- <el-input v-model="item.categoryList" clearable /> -->
          <div class="tag-list">
            <div class="condition-select-item" v-for="(item1, index1) in categoryList[index]" :key="item1">
              <p class="select-item-label">{{ item1 }}</p>
              <el-icon class="close-icon" @click="removeModelType(index, index1)"> <Close /></el-icon>
            </div>
            <span class="button-text" @click="modelTypeShow(categoryList[index], index)">新增</span>
          </div>
        </el-form-item>

        <el-form-item label="关联产品" prop="skuIds" v-if="item.type === '1'">
          <div class="name-box">
            <el-button type="primary" class="add-button" @click="selectProduct(20, index)" :icon="CirclePlus"> 添加 </el-button>
            <span class="tip-total">({{ item?.skuIds.length }}/20) </span>
            <span class="tips">最多可添加20个,至少添加2个产品</span>
          </div>
          <ProductTable :sku-ids="productDetail[index]" :max-item="20" :model-index="index" @update-sku-ids="handleSelect">
          </ProductTable>
        </el-form-item>

        <el-form-item label="播放视频" prop="videoUrl" v-if="item.type === '2'">
          <UploadVideo v-model:video-url="item.videoUrl">
            <template #tip>支持格式mp4，视频大小不超过100M</template>
          </UploadVideo>
        </el-form-item>

        <el-form-item label="展示内容" prop="richText" placeholder="请输入内容" v-if="item.type === '3'">
          <WangEditor :max="20000" v-model:value="item.richText" height="300px" />
        </el-form-item>

        <el-form-item label="卡片配置" prop="children" placeholder="请输入内容" v-if="item.type === '4' || item.type === '5'">
          <card-config
            ref="cardConfigRef"
            v-if="item.type === '4'"
            v-model:dataList="item.children"
            :model-index="index"
            @add-card="addCard"
            @delete-card="deleteCard"
            :is-small-card="true"
          >
            <!-- <template #nameTips>最多可添加8个,至少配置1个卡片</template> -->
            <template #nameTips>至少配置1个卡片</template>
          </card-config>
          <card-config
            ref="cardConfigRef"
            v-if="item.type === '5'"
            v-model:dataList="item.children"
            :model-index="index"
            @add-card="addCard"
            @delete-card="deleteCard"
            :is-small-card="false"
          >
            <template #nameTips>最多可添加6个,至少配置1个卡片</template>
          </card-config>
        </el-form-item>

        <el-form-item label="关联大模型" prop="children" v-if="item.type === '6'">
          <large-model
            ref="largeModelRef"
            v-model:dataList="item.children"
            :tags="item.tags"
            :category-list="categoryList[index]"
            :model-index="index"
            @add-large-model="addLargeModel(index)"
            @delete-large-model="deleteLargeModel"
            @update-link="handleLink"
            @update-list-tags="deleteListTags"
            @update-tags="updateTags"
            :is-case="false"
          >
            <template #nameTips>至少关联1个大模型</template>
          </large-model>
        </el-form-item>

        <el-form-item label="添加大模型" prop="children" v-if="item.type === '7'">
          <large-model
            ref="largeModelRef"
            v-model:dataList="item.children"
            :category-list="categoryList[index]"
            :model-index="index"
            @add-large-model="addLargeModel(index)"
            @delete-large-model="deleteLargeModel"
            :is-case="true"
          >
            <template #nameTips>至少添加1个大模型</template>
          </large-model>
        </el-form-item>

        <el-form-item label="关联产品" prop="skuIds" v-if="item.type === '9'">
          <div class="name-box">
            <el-button type="primary" class="add-button" @click="selectProduct(1, index)" :icon="CirclePlus"> 添加 </el-button>
            <span class="tips">最多可添加1个,至少添加1个产品</span>
          </div>
          <ProductTable :sku-ids="productDetail[index]" :max-item="1" :model-index="index" @update-sku-ids="handleSelect">
          </ProductTable>
        </el-form-item>
        <el-form-item label="卡片图片" prop="iconUrl" v-if="item.type === '9'">
          <UploadImg :file-size="2" v-model:image-url="item.iconUrl" width="135px" height="135px">
            <!-- <template #tip>建议图片尺寸1200px*400px，图片大小不超过2M</template> -->
          </UploadImg>
        </el-form-item>

        <el-form-item label="按钮文案" prop="buttonText" v-if="item.type === '8'">
          <el-input v-model="item.description" maxlength="5" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="模型选择" prop="modelId" v-if="item.type === '8'" required>
          <el-select v-model="item.modelId" placeholder="请选择" @change="handleButtonLink(index)" clearable>
            <el-option
              v-for="(timeItem, timeIndex) in model_list"
              :key="timeIndex + 'modelListUnit'"
              :label="timeItem.itemValue"
              :value="timeItem.itemKey"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="按钮跳转链接" prop="link" v-if="item.type === '8'">
          <el-input v-model="item.link" :maxlength="150" :disabled="item.type === '8'" show-word-limit clearable />
          <p class="tips-text">
            1、支持单点登录，手动填写跳转链接时请添加协议头，建议添加如：https或其他已定制的协议头，若添加http协议头有一定链接被篡改风险
          </p>
          <p class="tips-text">2、按钮跳转链接未配置时点击不可跳转</p>
        </el-form-item>
        <el-form-item label="背景图片" prop="iconUrl" v-if="item.type === '8'" required>
          <UploadImg :file-size="2" v-model:image-url="item.iconUrl" width="135px" height="135px">
            <template #tip>建议图片尺寸1200px*400px,图片大小不超过2M</template>
          </UploadImg>
        </el-form-item>
      </el-form>
      <ProductDrawer
        @update-sku-ids="handleSelect"
        v-model="dialogVisible"
        :max-item="maxItem"
        :c-list="productDetail[modelIndex]"
        :model-index="modelIndex"
      ></ProductDrawer>
    </div>
  </div>
  <ModelType ref="ModelTypeRef" @update-model-type="updateModelType" />
</template>
<script setup lang="ts">
import { onMounted, ref, computed, watch } from "vue";
import { CirclePlus, Delete } from "@element-plus/icons-vue";
import UploadVideo from "@/components/Upload/Video.vue";
import { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { useDict } from "@/hooks/useDict";
import WangEditor from "@/components/WangEditor/index.vue";
import UploadImg from "@/components/Upload/Img.vue";
import ProductDrawer from "./productDrawer.vue";
import ProductTable from "./productTable.vue";
import CardConfig from "./cardConfig.vue";
import LargeModel from "./largeModel.vue";
import ModelType from "./modelType.vue";
import { modelItem, productItem } from "@/api/interface/business/theme";
const { theme_zone_type, model_list, model_link } = useDict("theme_zone_type", "model_list", "model_link");

const props = defineProps({
  dataList: {
    type: Array<modelItem>,
    default: () => []
  },
  productDetail: {
    type: Array<Array<productItem>>,
    default: () => []
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits([
  "addModel",
  "deleteModel",
  "addCard",
  "deleteCard",
  "initModelItem",
  "addLargeModel",
  "deleteLargeModel",
  "updateModelType",
  "updateLink",
  "updateButtonLink",
  "updateSkuIds",
  "deleteListTags",
  "updateTags"
]);
const maxItem = ref(0);
const dialogVisible = ref(false);
const categoryList = ref(props.dataList.map(item => item.categoryList));

const productList = ref([]);
const modelRuleFormRef = ref<FormInstance>();

const modelIndex = ref(0);
const cardConfigRef = ref();
const largeModelRef = ref();
// const largeModelCaseRef = ref();
const drawerRef = ref();
const ModelTypeRef = ref();

const checkSkuIds = (_: any, value: any, callback: any) => {
  if (!value) {
    callback();
  } else {
    if (value.length <= 0) callback(new Error("关联产品不能为空"));
    else callback();
  }
};

const rules = {
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  type: [{ required: true, message: "展示类型不能为空", trigger: "blur" }],
  skuIds: [{ validator: checkSkuIds, trigger: "blur" }],
  richText: [{ required: true, message: "展示内容不能为空", trigger: "blur" }],
  iconUrl: [{ required: true, message: "请上传图片", trigger: "blur" }],
  videoUrl: [{ required: true, message: "请上传播放视频", trigger: "blur" }],
  modelId: [{ required: true, message: "请选择模型", trigger: "blur" }]
  // backgroundUrl: [{ validator: checkBackgroundUrl, trigger: "blur" }]
};

const handleSelect = (data: any, index: number) => {
  productList.value = data;
  // emits("updateSkuIds", selectSku, index);
  emits("updateSkuIds", data, index);
};

const selectProduct = (number: number, index: number) => {
  maxItem.value = number;
  modelIndex.value = index;
  // drawerRef.value.visible = false;
  dialogVisible.value = true;
};

const handleTypeChange = (value: any, index: number) => {
  productList.value = [];
  emits("initModelItem", index);
};

const handleLink = (data: string, modelIndex: number, index: number) => {
  // const link = ref("");
  // model_link.value.forEach((item: any) => {
  //   if (data === item.itemKey) link.value = item.itemValue;
  // });
  emits("updateLink", data, modelIndex, index);
};

const handleButtonLink = (index: number) => {
  const link = ref("");
  model_link.value.forEach((item: any) => {
    if (props.dataList[index].modelId === item.itemKey) link.value = item.itemValue;
  });
  emits("updateButtonLink", link.value, index);
};

const addModel = () => {
  emits("addModel");
};
//删除模块
const deleteModel = (index: number) => {
  emits("deleteModel", index);
};

const addLargeModel = (index: number) => {
  emits("addLargeModel", index);
};
//删除大模型
const deleteLargeModel = (index: number, cardIndex: number) => {
  emits("deleteLargeModel", index, cardIndex);
};
//删除大模型中某个标签
const deleteListTags = (modelIndex: number, largeModelIndex: number, tagIndex: number) => {
  emits("deleteListTags", modelIndex, largeModelIndex, tagIndex);
};

const updateTags = (data: any, modelIndex: number) => {
  emits("updateTags", data, modelIndex);
};

const addCard = (index: number, isSmallCard: boolean) => {
  if (isSmallCard) {
    // if (props.dataList[index].children.length >= 8) {
    //   ElMessage.error("最多添加8个小卡片");
    //   return;
    // }
  } else {
    if (props.dataList[index].children.length >= 6) {
      ElMessage.error("最多添加6个大卡片");
      return;
    }
  }
  emits("addCard", index);
};

const deleteCard = (index: number, cardIndex: number) => {
  if (props.dataList[index].children.length < 2) {
    ElMessage.error("最少需添加一个卡片");
    return;
  }
  emits("deleteCard", index, cardIndex);
  // props.dataList[index].children.splice(index, 1);
};

const shangyiItem = (index: number, list: any[], productDetail?: Array<Array<productItem>>) => {
  list[index] = list.splice(index - 1, 1, list[index])[0];
  if (productDetail) {
    productDetail[index] = productDetail.splice(index - 1, 1, productDetail[index])[0];
  }
};
const xiayiItem = (index: number, list: any[], productDetail?: Array<Array<productItem>>) => {
  list[index] = list.splice(index + 1, 1, list[index])[0];
  if (productDetail) {
    productDetail[index] = productDetail.splice(index + 1, 1, productDetail[index])[0];
  }
};

const ruleValidatePromise = () => {
  let promiseArr = [] as any;
  modelRuleFormRef.value?.forEach((itemref: any) => {
    promiseArr.push(
      new Promise((resolve, reject) => {
        itemref.validate((valid: any, fields: any) => {
          if (valid) {
            resolve("校验成功");
          } else {
            let obj = Object.keys(fields);
            itemref.scrollToField(obj[0]);
            reject("校验失败");
          }
        });
      })
    );
  });

  //校验卡片板块
  if (cardConfigRef.value) {
    cardConfigRef.value.forEach((itemref: any) => {
      promiseArr.push(...itemref.ruleValidatePromise());
    });
  }

  //校验大模型板块
  // let pro = largeModelRef.value;
  if (largeModelRef.value) {
    largeModelRef.value.forEach((itemref: any) => {
      promiseArr.push(...itemref.ruleValidatePromise());
    });
  }
  return promiseArr;
};

const modelTypeShow = (data: any, index: number) => {
  ModelTypeRef.value.openDialog(data, index);
};

const removeModelType = (index: number, index1: number) => {
  categoryList.value[index].splice(index1, 1);
  emits("updateModelType", index, categoryList.value[index]);
};

const updateModelType = (data: any, index: number) => {
  categoryList.value[index] = data;
  emits("updateModelType", index, categoryList.value[index]);
};

defineExpose({
  ruleValidatePromise
});

watch(
  () => props.dataList,
  () => {
    categoryList.value = props.dataList.map(item => item.categoryList);
    // emit(
    //   "updateSkuIds",
    //   tableList.value
    //   // tableList.value.map((item: any) => item.id)
    // );
  },
  { deep: true }
);

onMounted(() => {});
</script>

<style lang="scss" scoped>
.complex-content-box {
  width: 100%;
  .name-box {
    display: flex;
    width: fit-content;
    margin-bottom: 16px;
    .name-input {
      margin-right: 8px;
    }
    .add-button {
      margin-right: 8px;
    }
    .tips {
      margin-left: 15px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
    }
    .tip-total {
      color: var(--el-text-color-regular);
    }
  }
  .content-item {
    margin-bottom: 16px;
    border: 1px solid #c4c6cc;
    .item-title {
      display: flex;
      justify-content: space-between;
      padding: 4px 12px;
      background: #c4c6cc;
      .right-btn {
        .icon + .icon {
          margin-left: 0;
        }
      }
      .icon:hover {
        color: #3a3a3d;
      }
    }
  }
  .item-form {
    padding: 24px;
    .jf-item {
      :deep(.el-form-item__content) {
        display: block;
      }
      .jf-box {
        padding: 15px 0;
        .jf-label {
          font-size: var(--el-form-label-font-size);
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .nowrap {
    white-space: nowrap;
  }
}
.model-category {
  display: flex;
  flex-direction: row;
}
.tips-text {
  margin: 0;
  font-size: 12px;
  color: #909399;
}
.button-text {
  margin-left: 10px;
  color: #2b85e4;
  cursor: pointer;
}
.tag-list {
  display: flex;
  flex-direction: row;
}
.condition-select-item {
  display: flex;
  align-items: center;
  width: 90px;
  height: 28px;
  padding-right: 5px;
  padding-left: 12px;

  // margin-top: 16px;
  margin-right: 20px;
  border: 1px solid #e1e3e6;
  .close-icon {
    cursor: pointer;
  }

  // border-radius: $common-radius;
  &:last-child {
    margin-right: 0;
  }
  .select-item-label {
    position: relative;
    width: 65px;
    margin-right: 14px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 12px;
    font-weight: 400;
    line-height: 22px;
    color: #9ca3ac;
    text-align: center;
    word-break: keep-all;
    &::after {
      position: absolute;
      top: 50%;
      right: -7px;
      width: 1px;
      height: 16px;
      content: " ";
      background-color: #9ca3ac;
      transform: translate(0, -50%);
    }
  }
}
</style>
