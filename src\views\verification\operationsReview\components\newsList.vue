<template>
  <div class="banner-box table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <template #status="{ row }">
        <CustomTag
          :type="statusTypeMap[row.status]"
          :status="row.status"
          :label="useDictLabel(audit_status, row.status)"
        ></CustomTag>
      </template>
      <template #auditAction="{ row }">
        {{ useDictLabel(AUDIT_ACTION, row?.auditAction) }}
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'newsView'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toEdit(scope.row, OperateType.detail)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'newsReview'">
          <el-tooltip content="审核" placement="top" v-if="scope.row.status == PENDING_AUDIT">
            <i class="iconfont2 icon-shenhe opera-icon" @click="toEdit(scope.row, OperateType.review)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="newsList">
import { ref, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import type { ProTableInstance, ColumnProps } from "@/components/ProTable/interface/index";
import { newsAuditDetail, bannerAuditList } from "@/api/modules/business/banner";
// import { useDicStore } from "@/stores/modules/dictionaty";
import { newsReviewStorage } from "@/utils/storage/edit";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { OperateType } from "../type";

const { AUDIT_ACTION, audit_status } = useDict("AUDIT_ACTION", "audit_status");

const PENDING_AUDIT = 1; // 待审核
// const dictionaryStore = useDicStore();

const statusTypeMap: { [key: string]: string } = {
  "0": "yellow",
  "2": "green",
  "3": "red"
};
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const route = useRoute();
const router = useRouter();

const proTable = ref<ProTableInstance>();

onActivated(() => {
  proTable.value?.getTableList();
});

// const auditActionDict: any = computed(() => dictionaryStore.bannerAuditAction);
// const statusDict: any = computed(() => dictionaryStore.productAuditStatus);

const getTableList = async (search: any) => {
  if (!BUTTONS.value?.newsQuery) {
    return;
  }
  return await bannerAuditList({
    ...search,
    descs: "createTime",
    bizType: "news"
  });
};

const columns = ref<ColumnProps[]>([
  {
    prop: "bizInfo",
    label: "最新动态标题",
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "auditAction",
    label: "审核操作",
    align: "left",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {AUDIT_ACTION.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },

  {
    prop: "creatorName",
    align: "left",
    label: "提交账号"
  },
  {
    prop: "createTime",
    label: "提交时间",
    align: "left",
    width: 180,
    sortable: true
  },
  {
    prop: "status",
    label: "审核状态",
    align: "left",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {audit_status.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditTime",
    label: "审核时间",
    align: "left",
    width: 180
  },
  {
    prop: "auditorName",
    label: "审核人",
    align: "left"
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 130 }
]);

newsReviewStorage.clearExpired();

const toEdit = (data: any, type = OperateType.detail) => {
  const auditMsg = data.status != 0 ? data.auditMsg : undefined;

  newsAuditDetail({ bizId: data.bizId }).then(resp => {
    const respData = resp.data || {};
    newsReviewStorage.set(data.bizId, { ...respData, listId: data.id });
    router.push({
      path: "/verification/operationsReview/newsReview",
      query: {
        id: data.bizId,
        type,
        auditMsg
      }
    });
  });
};
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
</style>
