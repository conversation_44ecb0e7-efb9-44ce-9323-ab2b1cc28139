import http from "@/api";
import { TransactionVolumeFiling } from "@/api/interface/transactionVolumeFiling";

export const getOrderInfoList = (params: TransactionVolumeFiling.OrderInfoListParams) => {
  return http.get(`/cnud-order/admin/computing-portal/order/queryOrderInfo`, params);
};

export const querySupplierList = (params: TransactionVolumeFiling.SupplierListParams) => {
  return http.get(`/cnud-product/admin/computing-portal/supplier/querySupplierInfo`, params);
};

export const uploadFile = (params: FormData) => {
  return http.post(`/cnud-file/common/oss/endpoint/put-file`, params);
};

export const saveOrder = (params: TransactionVolumeFiling.SaveOrderParams) => {
  return http.post(`/cnud-order/admin/computing-portal/order/addOrder`, params);
};

export const relContract = (params: TransactionVolumeFiling.RelContractParams) => {
  return http.post(`/cnud-order/admin/computing-portal/order/uploadContract`, params);
};

/**
 * 通过 文件ID下载文件
 * @param params {id: string}
 */
export const downloadAttachFile = (params: TransactionVolumeFiling.DownloadAttachFileParams) => {
  return http.download(`/cnud-file/common/attachment/downloadById`, params);
};

export const queryContractList = (params: TransactionVolumeFiling.ContractListParams) => {
  return http.get(`/cnud-product/admin/computing-portal/contract/queryContractInfo`, params);
};

export const queryDemandList = (params: TransactionVolumeFiling.DemandListParams) => {
  return http.get(`/cnud-operation/admin/computing-portal/questionnaire/queryDemand`, params);
};
