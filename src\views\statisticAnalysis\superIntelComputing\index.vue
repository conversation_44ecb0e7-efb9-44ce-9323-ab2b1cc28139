<template>
  <div class="table-box">
    <pro-table ref="proTableRef" :init-param="initParam" :request-api="getTableList" :columns="columns" :tool-button="false">
      <template #tableHeader>
        <el-button plain @click="handleExport" v-auth="'export'">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>
      <template #source="{ row }">
        {{ useDictLabel(demand_source, row.source) }}
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="ts" name="superIntelComputing">
import { reactive, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { queryOrdDemandCZS, exportPromotionH5 } from "@/api/modules/statisticAnalysis";
import { Statistic } from "@/api/interface/statisticAnalysis";
import { useDownload } from "@/hooks/useDownload";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { useAuthButtons } from "@/hooks/useAuthButtons";
const { BUTTONS } = useAuthButtons();

const { demand_source } = useDict("demand_source");
const proTableRef = ref<ProTableInstance>();

const initParam = reactive({
  adProductType: "ChaoZhiSuan"
});

const columns: ColumnProps[] = [
  {
    align: "center",
    prop: "source",
    label: "来源"
  },
  {
    align: "center",
    prop: "companyName",
    label: "企业名称"
  },
  {
    align: "center",
    prop: "provinceName",
    label: "省份地市",
    render: (scope: any) => `${scope.row.provinceName}省${scope.row?.cityName}`
  },
  {
    align: "center",
    prop: "phone",
    label: "联系电话"
  },
  {
    align: "center",
    prop: "createTime",
    label: "提交时间",
    search: {
      el: "date-picker",
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" },
      transform: (value: any) => {
        return {
          startDate: value[0],
          endDate: value[1]
        };
      }
    }
  }
];

const getTableList = (params: Statistic.promotionH5Search) => {
  if (!BUTTONS.value?.query) {
    return Promise.resolve();
  }
  const searchParams = params;
  Reflect.deleteProperty(searchParams, "createTime");
  return queryOrdDemandCZS({ ...searchParams });
};

const handleExport = () => {
  const exportSearch = proTableRef.value?.searchParam || {};
  Reflect.deleteProperty(exportSearch, "createTime");
  // ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() =>
  //   useDownload(exportPromotionH5, { ...exportSearch, adProductTypes: initParam.adProductType })
  // );
  exportPromotionH5({ ...exportSearch, adProductTypes: initParam.adProductType })
    .then(res => {
      ElMessage.success("请前往下载管理中下载文件");
    })
    .catch(() => {
      ElMessage.error("下载有误，请重新下载");
    });
};
</script>

<style scoped></style>
