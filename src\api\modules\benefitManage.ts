import http from "@/api";
import type { PmsRights } from "@/api/interface/benefitManage";

export const getPageList = (data: any) => {
  return http.get("/cnud-product/admin/pmsRights/pageList", data);
};

export const benefitDelete = (ids: Array<string>) => {
  return http.post("/cnud-product/admin/pmsRights/delete", { ids: ids });
};
export const queryCreatePmsRights = (data: PmsRights.CreateParams) => {
  return http.post("/cnud-product/admin/pmsRights/save", data);
};
export const change = (data: any) => {
  return http.post("/cnud-product/admin/operate/pmsProvider-update", data);
};
export const pmsProviderNoPageList = (params?: any) => {
  return http.get(`/cnud-product/admin/operate/pmsProvider-list`, params);
};
/* 编辑 */
export const queryUpdatePmsRights = (data: PmsRights.EditParams) => {
  return http.post("/cnud-product/admin/pmsRights/update", data);
};

/* 产品权益表-详情 */
export const queryViewDetail = (id: string) => {
  return http.get("/cnud-product/admin/pmsRights/detail", { id });
};
