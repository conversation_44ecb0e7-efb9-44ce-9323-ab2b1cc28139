.tabs-box {
  background-color: var(--el-bg-color);
  .tabs-menu {
    position: relative;
    width: 100%;
    .close {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 36px;
      height: 36px;
      cursor: pointer;
      background-color: #ffffff;
      i {
        margin-right: 0;
        color: #3a3a3d;
      }
      &:hover {
        i {
          color: var(--el-color-primary);
        }
      }
    }
    .el-dropdown {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      .more-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 43px;
        cursor: pointer;
        border-left: 1px solid var(--el-border-color-light);
        transition: all 0.3s;
        &:hover {
          background-color: var(--el-color-info-light-9);
        }
        .iconfont {
          font-size: 12.5px;
        }
      }
    }
    :deep(.el-tabs) {
      padding: 16px;
      background: #f5f7fa;
      .el-tabs__header {
        box-sizing: border-box;
        margin: 0;
        border: none;
        .el-tabs__nav-wrap {
          position: absolute;
          width: calc(100% - 60px);
          &.is-scrollable {
            padding: 0 44px;
          }
          .el-tabs__nav-next,
          .el-tabs__nav-prev {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background: #ffffff;
            &:hover {
              i {
                color: var(--el-color-primary);
              }
            }
          }
          .el-tabs__nav {
            display: flex;
            border: none;
            .el-tabs__item {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 36px;
              padding: 0 16px;
              margin-right: 8px;
              color: #3b3b3d;
              background: #ffffff;
              border: none;
              border-radius: 4px;
              .tabs-icon {
                margin: 1.5px 4px 0 0;
                font-size: 15px;
              }
              .is-icon-close {
                width: 14px;
                margin-top: 1px;
                color: #9ca3ac;
              }
              &.is-active {
                color: var(--el-color-primary);
                &::before {
                  display: none;
                }
                .is-icon-close {
                  color: var(--el-color-primary);
                }
              }
            }
          }
        }
      }
    }
  }
}
