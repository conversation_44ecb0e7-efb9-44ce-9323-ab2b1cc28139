<template>
  <div class="table-box">
    <ProTable :request-auto="false" ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <template #tableHeader>
        <el-button v-auth="'productAttrAdd'" plain :icon="Plus" @click="handleCreate"> 新增 </el-button>
      </template>
      <template #attrType="{ row }">
        {{ useDictLabel(attr_type, row.attrType) }}
      </template>
      <template #operation="{ row }">
        <el-tooltip content="编辑" placement="top">
          <i v-auth="'productAttrEdit'" class="iconfont2 opera-icon icon-a-lineicon_edit" @click="handelEdit(row)"></i>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <i v-auth="'productAttrDelete'" class="iconfont2 opera-icon icon-lajitong" @click="handelDelete(row)"></i>
        </el-tooltip>
      </template>
    </ProTable>
    <Modal ref="modalRef" @on-submit="handleSubmit"></Modal>
  </div>
</template>

<script setup lang="tsx" name="productManageAttr">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { Plus } from "@element-plus/icons-vue";
import { pmsProductAttrDelete, pmsProductAttrDetail, pmsProductAttrList } from "@/api/modules/product";
import { onMounted, ref, unref } from "vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { Product } from "@/api/interface/product";
import Modal, { MODALACTION } from "./components/modal.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { Action, ElMessage, ElMessageBox } from "element-plus";

const { BUTTONS } = useAuthButtons();
const { attr_type } = useDict("attr_type");

const columns: ColumnProps<Product.ProductAttrListItem>[] = [
  { type: "selection" },
  { prop: "attrName", label: "属性名称", search: { el: "input" } },
  { prop: "attrCode", label: "属性编码" },
  {
    prop: "attrType",
    label: "属性类型",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {attr_type.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "createTime",
    label: "创建时间",
    search: {
      render: scope => {
        return (
          <el-date-picker
            type="datetimerange"
            value-format={"YYYY-MM-DD HH:mm:ss"}
            model-value={scope.modelValue}
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        );
      }
    }
  },
  { prop: "creatorName", label: "创建人" },
  { prop: "operation", label: "操作", fixed: "right", width: 200 }
];

const proTable = ref<ProTableInstance>();
const modalRef = ref<{ handleOpen: (modalAction: MODALACTION, rowData?: Product.ProductAttrListItem) => void }>();
/* 方法 */
const handleCreate = () => {
  modalRef.value?.handleOpen("add");
};

const handelEdit = (rowData: Product.ProductAttrListItem) => {
  pmsProductAttrDetail({ id: rowData.id as string })
    .then(({ data }) => {
      modalRef.value?.handleOpen("edit", data);
    })
    .catch(err => {});
};

const handelDelete = (rowData: Product.ProductAttrListItem) => {
  ElMessageBox.alert(`确定删除产品属性<${rowData.attrName}> ?`, "温馨提示", {
    confirmButtonText: "确定",
    showCancelButton: true,
    callback: (action: Action) => {
      if (action === "confirm") {
        pmsProductAttrDelete({ ids: [rowData.id] })
          .then(result => {
            proTable.value?.getTableList();
            ElMessage.success("删除成功");
          })
          .catch(err => {
            console.log(err);
          });
      }
    }
  });
};

/* 获取列表数据 */
const getTableList = async (params: Product.ProductAttrSearch) => {
  const values = params;
  if (Reflect.has(params, "createTime")) {
    Reflect.set(values, "createTimeStart", params?.createTime[0]);
    Reflect.set(values, "createTimeEnd", params?.createTime[1]);
    Reflect.deleteProperty(values, "createTime");
  }

  const button = unref(BUTTONS);
  if (Reflect.has(button, "queryProductAttr")) {
    return await pmsProductAttrList({ ...values, descs: "createTime" });
  } else {
    return [];
  }
};
const handleSubmit = () => {
  proTable.value?.getTableList();
};

onMounted(() => {
  proTable.value?.getTableList();
});
</script>

<style scoped></style>
