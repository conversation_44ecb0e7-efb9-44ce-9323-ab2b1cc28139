<template>
  <div class="dataScreen-container">
    <div ref="dataScreenRef" class="dataScreen">
      <div class="dataScreen-header flx-center">
        <div class="header-lf" @click="router.back">
          <img src="@/assets/images/screen/left-img.png" />
          <span>返回</span>
        </div>
        <div class="header-ct"></div>
        <div class="header-rt" @click="handleFullScreen">
          <img v-if="isFullscreen" src="@/assets/images/screen/shouqi.png" />
          <img v-else src="@/assets/images/screen/fullscreen.png" />
          <span>{{ isFullscreen ? "缩放" : "全屏" }}</span>
        </div>
      </div>
      <div class="dataScreen-main">
        <div class="dataScreen-lf">
          <div class="dataScreen-top">
            <SecondTitle str="算力使用情况" />
            <div class="chart-box">
              <SuanLiUseChart ref="SuanLiUseRef" />
            </div>
          </div>
          <div class="dataScreen-center">
            <SecondTitle str="算力接入总数" />
            <div class="chart-box">
              <SuanLiTotalChart ref="SuanLiTotalRef" />
            </div>
          </div>
          <div class="dataScreen-bottom">
            <SecondTitle str="总算力分布占比" unit="PFlops" />
            <div class="chart-box">
              <SuanLiDistribution ref="SuanLiDistrRef" />
            </div>
          </div>
        </div>
        <div class="dataScreen-md">
          <TopIndicator />
          <div class="dataScreen-map">
            <!--地图chart-->
            <div class="map-box">
              <mapChart ref="MapChartRef" />
            </div>
            <!--<img src="@/assets/images/screen/map-di.png" class="map-di" />-->
            <!-- <img src="@/assets/images/screen/map-dao.png" class="map-dao" />-->
          </div>
        </div>
        <div class="dataScreen-rt">
          <div class="dataScreen-top">
            <SecondTitle str="资源型号数量分布" unit="卡" />
            <div class="chart-box reduce-box">
              <ResourceModelChart ref="ResourceModelRef" />
            </div>
          </div>
          <div class="dataScreen-center">
            <SecondTitle str="CPU使用场景统计" />
            <div class="chart-box big-box">
              <!-- <CpuUseScene ref="CpuUseSceneRef" :data-set="list" />-->
              <CpuUseScene ref="CpuUseSceneRef" :params="cpuStatistics" />
            </div>
          </div>
          <div class="dataScreen-bottom">
            <SecondTitle str="服务器负载趋势" />
            <div class="chart-box reduce-box">
              <ServerLoadTrend ref="ServerLoadTrendRef" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="dataScreen">
import { ref, onMounted, onBeforeUnmount } from "vue";
import screenfull from "screenfull";
import { useRouter } from "vue-router";
import { useTime } from "@/hooks/useTime";
import { ECharts } from "echarts";
import Img from "@/components/Upload/Img.vue";
import { ElMessage } from "element-plus";
import mapChart from "./components/map/map.vue";
import SecondTitle from "./components/secondTilte.vue";
import TopIndicator from "./components/topIndicator.vue";
import SuanLiUseChart from "./components/SuanLiUseChart.vue";
import SuanLiTotalChart from "./components/SuanLiTotalChart.vue";
import SuanLiDistribution from "./components/SuanLiDistribution.vue";
import ServerLoadTrend from "./components/ServerLoadTrend.vue";
import ResourceModelChart from "./components/ResourceModelChart.vue";
import CpuUseScene from "./components/3DPie-type2.vue";
import { getSuanLiUse, getSuanLiTotal, getSuanLiDistr, getResourceModel, getServerLoad } from "@/api/modules/map";
import dayjs from "dayjs";

const router = useRouter();
const dataScreenRef = ref<HTMLElement | null>(null);
const isFullscreen = ref(screenfull.isFullscreen);
const cpuStatistics = ref({});

onMounted(() => {
  // 初始化时为外层盒子加上缩放属性，防止刷新界面时就已经缩放
  if (dataScreenRef.value) {
    dataScreenRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
    dataScreenRef.value.style.width = `1920px`;
    dataScreenRef.value.style.height = `1080px`;
  }
  // 初始化数据
  initData();
  // 初始化 echarts
  // initCharts();
  // 为浏览器绑定事件
  window.addEventListener("resize", resize);

  screenfull.on("change", () => {
    if (screenfull.isFullscreen) isFullscreen.value = true;
    else isFullscreen.value = false;
  });
});
const handleFullScreen = () => {
  if (!screenfull.isEnabled) ElMessage.warning("当前您的浏览器不支持全屏 ❌");
  screenfull.toggle();
};

// 根据浏览器大小推断缩放比例
const getScale = (width = 1920, height = 1080) => {
  let ww = window.innerWidth / width;
  let wh = window.innerHeight / height;
  return ww < wh ? ww : wh;
};

// 监听浏览器 resize 事件
const resize = () => {
  if (dataScreenRef.value) {
    dataScreenRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
  }
  // 使用了 scale 的echarts其实不需要需要重新计算缩放比例
  Object.values(dataScreen).forEach(chart => {
    chart && chart.resize();
  });
};

// 声明echarts实例
interface ChartProps {
  [key: string]: ECharts | null;
}
const dataScreen: ChartProps = {
  chart1: null,
  chart2: null,
  chart3: null,
  chart4: null,
  chart5: null,
  chart6: null,
  mapChart: null
};
// 获取子组件的ref
interface ChartExpose {
  initChart: (params: any) => ECharts;
}
const SuanLiUseRef = ref<ChartExpose>();
const SuanLiTotalRef = ref<ChartExpose>();
const SuanLiDistrRef = ref<ChartExpose>();
const ResourceModelRef = ref<ChartExpose>();
const CpuUseSceneRef = ref<ChartExpose>();
const ServerLoadTrendRef = ref<ChartExpose>();
const MapChartRef = ref<ChartExpose>();

let data1 = ref({
  columns: [],
  list: []
});
let data2 = ref({
  columns: [],
  list: [
    { label: "算力总数", value: [] },
    { label: "增长百分比", value: [] }
  ]
});
let data3 = ref([]);
let data4 = ref([]);
let data5 = ref([]);
let data6 = ref({
  columns: [],
  list: []
});
const initData = () => {
  getSuanLiUse().then((res: any) => {
    if (res.code === 200) {
      data1.value.columns = res.data.map((item: any = {}) => item.date.substring(item.date.length - 5, item.date.length));
      Object.keys(res.data[0].map).forEach((item: string) => {
        data1.value.list.push({ label: item, value: [] });
      });
      data1.value.list.forEach((listItem: any, index) => {
        res.data.forEach((item: any) => {
          listItem.value.push(Object.values(item.map)[index]);
        });
      });
      dataScreen.chart1 = SuanLiUseRef.value?.initChart({
        data: data1.value.list,
        columns: data1.value.columns,
        colors: ["#5092FF", "#28D4D6", "#FFAA38"]
      }) as ECharts;
    }
  });
  getSuanLiTotal().then((res: any) => {
    if (res.code === 200) {
      data2.value.columns = res.data.map((item: any = {}, index) => {
        return item.month.replace("月", "");
      });
      res.data.forEach((item: any) => {
        data2.value.list[0].value.push(item.power);
        data2.value.list[1].value.push(item.rate.toFixed(2));
      });
      dataScreen.chart2 = SuanLiTotalRef.value?.initChart({
        data: data2.value.list,
        columns: data2.value.columns,
        colors: ["#3B92FF", "#FFAA38"]
      }) as ECharts;
    }
  });
  getSuanLiDistr().then((res: any) => {
    if (res.code === 200) {
      data3.value = res.data;
      data3.value[0].selected = true;
      dataScreen.chart3 = SuanLiDistrRef.value?.initChart(data3.value) as ECharts;
    }
  });
  getResourceModel().then((res: any) => {
    if (res.code === 200) {
      res.data.processorInfoList.forEach((item: any) => {
        data4.value.push({ value: item.processorNum, name: item.processorType.name });
      });
      dataScreen.chart4 = ResourceModelRef.value?.initChart({ data: data4.value }) as ECharts;

      res.data.sceneInfoList.forEach((item: any, index: number) => {
        // 手动添加颜色
        let colorList = ["#5092FF", "#28D4D6", "#FFAA38", "#FF7D33", "#CF76FF", "#5AEA69"];
        data5.value.push({ value: item.count, name: item.sceneType.type, itemStyle: { color: colorList[index] } });
      });
      Object.assign(cpuStatistics.value, {
        capacityAllotedrate: res.data.capacityAllotedrate,
        capacityUsedrate: res.data.capacityUsedrate
      });
      dataScreen.chart5 = CpuUseSceneRef.value?.initChart(data5.value) as ECharts;
    }
  });
  getServerLoad().then((res: any) => {
    if (res.code === 200) {
      Object.keys(res.data.serverLoadInfoMap).forEach((item: string) => {
        data6.value.list.push({ label: item, value: [] });
      });
      Object.values(res.data.serverLoadInfoMap).forEach((item: any, index: number) => {
        data6.value.list[index].value = item.map((child: any = {}) => child.rate);
        data6.value.columns = item.map((child: any = {}) => dayjs(child.date).format("HH"));
      });
    }
    dataScreen.chart6 = ServerLoadTrendRef.value?.initChart({
      data: data6.value.list,
      columns: data6.value.columns,
      colors: ["#3B92FF", "#28D4D6", "#FFAA38"]
    }) as ECharts;
  });
};

// 获取当前时间
const { nowTime } = useTime();
let timer: NodeJS.Timer | null = null;
let time: Ref<string> = ref(nowTime.value);
timer = setInterval(() => {
  time.value = useTime().nowTime.value;
}, 1000);

// 销毁时触发
onBeforeUnmount(() => {
  window.removeEventListener("resize", resize);
  clearInterval(timer!);
  Object.values(dataScreen).forEach(val => val?.dispose());
});
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>
