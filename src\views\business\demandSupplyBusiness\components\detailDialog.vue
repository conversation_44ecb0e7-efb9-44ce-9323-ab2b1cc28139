<template>
  <el-dialog :title="dialogTitle" :append-to-body="true" custom-class="custom-modal" v-model="dialogVisible" width="600">
    <div v-if="dialogType === TabType.SERVICE" class="status-box">
      采纳状态：<span :class="`status-${dialogData.acceptStatus}`">{{
        useDictLabel(demandAcceptStatus, dialogData.acceptStatus || "--")
      }}</span>
    </div>

    <el-row>
      <el-col v-for="item in displayInfo" :key="item.key" :span="item.span || 12" class="mb30">
        <div class="info-label">{{ item.label }}</div>
        <div v-if="item.key === 'demandType'" class="info-text">
          {{ useDictLabel(requireType, dialogData[item.key] || "--") }}
        </div>
        <div v-else-if="item.key === 'constructionAddress'">
          {{ useDictLabel(demandConstructionAddress, dialogData[item.key] || "--") }}
        </div>
        <div v-else-if="item.key === 'description' || item.key === 'solutionDescription'">
          <el-input type="textarea" :rows="4" :value="dialogData[item.key]" readonly />
        </div>
        <div v-else class="info-text">{{ dialogData[item.key] || "--" }}</div>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script setup lang="ts" name="detailDialog">
import { ref, computed, onBeforeMount } from "vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useDictLabel } from "@/hooks/useDict";
import { getSchemeDetail } from "@/api/modules/requireRelease";
import { TabType } from "../type";

interface infoItem {
  label: string;
  key: string;
  span?: number;
}

const dictionaryStore = useDicStore();

// 审核状态
const demandAcceptStatus: any = computed(() => dictionaryStore.demandAcceptStatus);
// 需求类型
const requireType: any = computed(() => dictionaryStore.requireType);
// 实施地址
const demandConstructionAddress: any = computed(() => dictionaryStore.demandConstructionAddress);

const dialogVisible = ref(false);
const dialogTitle = ref();
const dialogType = ref();
const dialogData = ref();

const displayInfo = ref<infoItem[]>([]);
const requireInfo = [
  {
    label: "企业名称",
    key: "companyName"
  },
  {
    label: "企业地址",
    key: "companyAddress"
  },
  {
    label: "联系人",
    key: "contactName"
  },
  {
    label: "手机号",
    key: "phone"
  },
  {
    label: "需求名称",
    key: "name"
  },
  {
    label: "需求类型",
    key: "demandType"
  },
  {
    label: "需求响应截止时间",
    key: "demandEndTime"
  },
  {
    label: "需求实施地址",
    key: "constructionAddress"
  },
  {
    label: "需求描述",
    key: "description",
    span: 24
  }
];
const responseInfo = [
  {
    label: "企业名称",
    key: "solutionCompanyName"
  },
  {
    label: "企业地址",
    key: "solutionCompanyAddress"
  },
  {
    label: "联系人",
    key: "solutionContactName"
  },
  {
    label: "手机号",
    key: "solutionPhone"
  },
  {
    label: "需求名称",
    key: "demandName"
  },
  {
    label: "需求类型",
    key: "demandType"
  },
  {
    label: "方案名称",
    key: "solutionName",
    span: 24
  },
  {
    label: "方案描述:",
    key: "solutionDescription",
    span: 24
  }
];

const getSchemeData = async () => {
  try {
    const res = await getSchemeDetail({ id: dialogData.value.id });
    Object.assign(dialogData.value, res.data);
  } catch (error) {}
};

const handleOpen = (title: string, type: TabType, data: any) => {
  dialogVisible.value = true;
  dialogTitle.value = title;
  dialogType.value = type;
  dialogData.value = data;
  if (type === TabType.ENTERPRISE) {
    displayInfo.value = requireInfo;
  } else {
    displayInfo.value = responseInfo;
    getSchemeData();
  }
};

onBeforeMount(() => {
  dictionaryStore.demandAcceptStatus.length === 0 && dictionaryStore.getDemandAcceptStatus();
  dictionaryStore.requireType.length === 0 && dictionaryStore.getRequireType();
  dictionaryStore.demandConstructionAddress.length === 0 && dictionaryStore.getDemandConstructionAddress();
});
defineExpose({
  handleOpen
});
</script>
<style scoped lang="scss">
.status-box {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #3a3a3d;
}
.info-label {
  margin-bottom: 8px;
  color: #73787f;
}
.info-text {
  color: #3a3a3d;
}
.status-wait {
  color: #ffaa00;
}
.status-accept {
  color: #0052d9;
}
.status-refuse {
  color: #e5302f;
}
</style>
