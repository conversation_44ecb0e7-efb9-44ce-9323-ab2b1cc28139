<template>
  <div class="cluster-detail">
    <div class="detail-chart">
      <div class="card-box">
        <div class="card-title-box">
          <span class="card-title">节点</span>
          <span class="card-count">总数 {{ clusterData?.totalNodeCount }}</span>
        </div>
        <div class="card-chart"><dashboard-chart ref="nodeCountChart" :chart-id="'nodeCountChart'"></dashboard-chart></div>
      </div>
      <div class="card-box">
        <div class="card-title-box">
          <span class="card-title">CPU</span>
          <span class="card-count">总核心数 {{ clusterData?.totalCpuCoreCount }}</span>
        </div>
        <div class="card-chart">
          <dashboard-chart ref="cpuCountChart" :chart-id="'cpuCountChart'"></dashboard-chart>
        </div>
      </div>
      <div class="card-box">
        <div class="card-title-box">
          <span class="card-title">GPU</span>
          <span class="card-count">总卡数 {{ clusterData?.totalGpuCoreCount }}</span>
        </div>
        <div class="card-chart">
          <dashboard-chart ref="gpuCountChart" :chart-id="'gpuCountChart'"></dashboard-chart>
        </div>
      </div>
      <div class="card-box">
        <div class="card-title">作业</div>
        <div class="card-work-up">
          <div class="card-work-count">
            {{ clusterData?.totalRunningJobCount }}
          </div>
          <div class="card-text">运行中</div>
        </div>
        <div class="card-work-down">
          <div class="card-work-count">
            {{ clusterData?.totalPendingJobCount }}
          </div>
          <div class="card-text">排队中</div>
        </div>
      </div>
    </div>
    <div class="bottom-box">
      <ProTable
        ref="proTable"
        title="集群分区资源信息"
        :request-auto="false"
        :columns="columns"
        :tool-button="false"
        :pagination="false"
        :data="tableData"
      >
        <template #nodeUsageRate="{ row }">
          <PercentageBar :value="row.nodeUsageRate" />
        </template>
        <template #cpuUsageRate="{ row }">
          <PercentageBar :value="row.cpuUsageRate" />
        </template>
        <template #gpuUsageRate="{ row }">
          <PercentageBar :value="row.gpuUsageRate" />
        </template>
        <template #partitionStatus="{ row }">
          <!-- <p>{{ row.partitionStatus }}</p> -->
          {{ useDictLabel(partition_status, row?.partitionStatus) || "--" }}
        </template>
      </ProTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="dataVisualize">
import { ref, onMounted, watch } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import type { CoreSystem } from "@/api/interface/coreSystem";
import { getClusterInfo } from "@/api/modules/coreSystem/dashboard";
import { ColumnProps } from "@/components/ProTable/interface";
import PercentageBar from "./PercentageBar.vue";
import dashboardChart from "./dashboardChart.vue";
import { ECharts } from "echarts";
import { useDictLabel, useDict } from "@/hooks/useDict";

// 获取子组件的ref
interface ChartExpose {
  initChart: (params: any, name?: string) => ECharts;
}
const { partition_status } = useDict("partition_status");
const tableData = ref<CoreSystem.dashboardClusterItemDetail[]>([]);
const clusterData = ref<CoreSystem.dashboardClusterItem>();
const nodeCountChart = ref<ChartExpose>();
const cpuCountChart = ref<ChartExpose>();
const gpuCountChart = ref<ChartExpose>();
const props = defineProps({
  clusterId: {
    type: String,
    default: ""
  }
});

const getTableList = (id: string) => {
  if (id.length > 0) {
    getClusterInfo(id).then((res: any) => {
      clusterData.value = res.data;
      if (!clusterData.value) return;
      tableData.value = clusterData.value.partitions;
      nodeCountChart.value?.initChart({
        color: ["#0052D9", "#ccdcf7"],
        totalAvailable: clusterData.value?.totalIdleNodeCount,
        totalRunning: clusterData.value.totalNodeCount - clusterData.value.totalIdleNodeCount,
        percentage: 100 - clusterData.value.totalNodeUsageRate
      });
      cpuCountChart.value?.initChart({
        color: ["#19BE6B", "#d1f2e1"],
        totalAvailable: clusterData.value?.totalIdleCpuCount,
        totalRunning: clusterData.value.totalCpuCoreCount - clusterData.value?.totalIdleCpuCount,
        percentage: 100 - clusterData.value?.totalCpuUsageRate
      });
      gpuCountChart.value?.initChart({
        color: ["#FFAA00", "#ffeecc"],
        totalAvailable: clusterData.value?.totalIdleGpuCount,
        totalRunning: clusterData.value.totalGpuCoreCount - clusterData.value?.totalIdleGpuCount,
        percentage: 100 - clusterData.value?.totalGpuUsageRate
      });
    });
  }
};

const columns: ColumnProps<any>[] = [
  {
    prop: "partitionName",
    label: "分区",
    sortable: true
  },
  { prop: "nodeCount", label: "节点总数", sortable: true },
  {
    prop: "nodeUsageRate",
    label: "节点使用率",
    sortable: true,
    width: 217
  },
  {
    prop: "cpuUsageRate",
    label: "CPU使用率",
    sortable: true,
    width: 217
  },
  {
    prop: "gpuUsageRate",
    label: "GPU使用率",
    sortable: true,
    width: 217
  },
  {
    prop: "pendingJobCount",
    label: "作业排队数",
    sortable: true
  },
  {
    prop: "partitionStatus",
    label: "分区状态",
    sortable: true,
    width: 114
  }
];

onMounted(() => {
  getTableList(props.clusterId);
});
watch(
  () => props.clusterId,
  () => {
    getTableList(props.clusterId);
  },
  {
    immediate: true
  }
);
</script>

<style scoped lang="scss">
@import "../index.scss";
</style>
