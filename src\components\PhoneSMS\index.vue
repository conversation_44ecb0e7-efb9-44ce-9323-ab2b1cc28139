<!--
 * @Author: <EMAIL>
 * @Date: 2023-09-18 16:54:42
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-19 16:07:32
 * @Description: file content
-->
<template>
  <div class="sms-box">
    <el-input v-model="modelValue" type="text" placeholder="请输入短信验证码" autocomplete="new-password" maxlength="6">
      <template #prefix>
        <el-icon class="el-input__icon">
          <Message />
        </el-icon>
      </template>
      <template #append>
        <el-button @click="getSMS" text type="primary" :disabled="smsDisabled">{{
          sendSmsTime === 0 ? "获取验证码" : sendSmsTime + "秒可重发"
        }}</el-button>
      </template>
    </el-input>
  </div>
</template>
<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { Message } from "@element-plus/icons-vue";
import { checkPhoneNumber } from "@/utils/index";
const sendSmsTime = ref(0);
const props = defineProps({
  value: {
    type: String,
    default: ""
  },
  phoneNumber: {
    type: String,
    default: ""
  },
  isSendMessage: {
    type: Boolean,
    default: false
  },
  isDisable: {
    type: Boolean,
    default: false
  }
});
watch(
  () => props.isSendMessage,
  (value: boolean) => {
    if (value && sendSmsTime.value === 0) {
      beginRunTime();
    }
  }
);
let timer = null;
const emits = defineEmits(["update:value", "smsClick"]);
const smsDisabled = computed(() => {
  return !props.phoneNumber || sendSmsTime.value != 0 || props.isDisable;
});
const modelValue = computed({
  get() {
    return props.value;
  },
  set(value) {
    emits("update:value", value);
  }
});
const getSMS = () => {
  emits("smsClick");
};
const beginRunTime = () => {
  sendSmsTime.value = 60;
  timer = setInterval(() => {
    sendSmsTime.value--;
    if (sendSmsTime.value == 0) {
      clearInterval(timer!);
    }
  }, 1000);
};
</script>
<style scoped lang="scss">
.sms-box {
  :deep(.el-input-group__append) {
    width: 100px;
    padding: 0 12px;
    background: #f3f4f9;
    box-shadow: none;
    .el-button {
      height: 34px;
      padding: 0 12px;
      line-height: 34px;
      color: var(--el-color-primary);
      border-left: 1px solid #e1e3e6;
    }
  }
}
</style>
