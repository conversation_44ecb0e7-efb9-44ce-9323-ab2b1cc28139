<template>
  <div class="table-box">
    <!--request-auto="false" 不自动调接口-->
    <ProTable
      ref="tableRef"
      title="订单列表"
      :request-auto="false"
      :tool-button="false"
      :columns="columns"
      :init-param="initParam"
      :request-api="getTableList"
    >
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view_button'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'deal_button'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toVerify(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="middleground">
import { ref, onMounted, onActivated, computed, reactive } from "vue";
import { useRouter } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDicStore } from "@/stores/modules/dictionaty";
import { getOrderList } from "@/api/modules/order";
import { Product } from "@/api/interface/product";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { cloneDeep } from "lodash";
const router = useRouter();
const { BUTTONS } = useAuthButtons();
const dictionaryStore = useDicStore();
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const tableRef = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ multiPoint: "3" });

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total && pageNum && pageSize 这些字段，那么你可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行

onMounted(() => {
  // 先获取字典
  dictionaryStore.getOrderStatus(); // 订单状态
  dictionaryStore.getOrderType(); //订单类型
  // 然后
  tableRef.value?.getTableList();
});
// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  tableRef.value?.getTableList();
});
// 订单状态字典-enum
const orderStatusDic: any = computed(() => dictionaryStore.orderStatusDic);
// 订单类型字典-enum
const orderTypeDic: any = computed(() => dictionaryStore.orderTypeDic);
// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = cloneDeep(params);
  newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
  newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
  delete newParams.createTime;
  if (newParams.categoryIdList?.length > 0) {
    newParams.categoryId = newParams.categoryIdList[newParams.categoryIdList.length - 1];
    delete newParams.categoryIdList;
  }
  newParams.descs = "createTime";
  return getOrderList(newParams);
};

// 页面按钮权限（按钮权限既可以使用 hooks，也可以直接使用 v-auth 指令，指令适合直接绑定在按钮上，hooks 适合根据按钮权限显示不同的内容）
// const { BUTTONS } = useAuthButtons();

// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  // { type: "selection", fixed: "left", width: 50 },
  { prop: "customerAccount", label: "客户账号", search: { el: "input" } },
  { prop: "purchasePhone", label: "手机号码", width: 150 },
  { prop: "channelName", label: "下单触点", width: 150 },
  { prop: "parentOrderNo", label: "订单编号", width: 150, search: { el: "input" } },
  { prop: "sourceOrderNo", label: "外部订单号", width: 150, search: { el: "input" } },
  { prop: "subOrderNo", label: "子订单编号", width: 150 },
  { prop: "sourceSubOrderNo", label: "外部产品订单", width: 150 },
  { prop: "orderType", label: "订单类型", width: 90, enum: orderTypeDic },
  { prop: "status", label: "订单状态", width: 90, enum: orderStatusDic, search: { el: "select", key: "statusList" } },
  { prop: "productName", label: "产品名称", width: 85 },
  { prop: "productProviderName", label: "产品服务商", width: 85 },
  {
    prop: "subscribeTime",
    label: "订购时间",
    sortable: true
  },
  {
    prop: "effectTime",
    label: "生效时间",
    width: 170,
    sortable: true
  },

  {
    prop: "endTime",
    label: "到期时间",
    width: 170,
    sortable: true
  },
  { prop: "operatorName", label: "处理人", width: 150 },
  {
    prop: "operatorTime",
    label: "处理时间",
    width: 170,
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 200 }
];
const toVerify = (row: { parentOrderNo: string }) => {
  router.push({
    path: "/order/middleground/detail",
    query: {
      orderId: row.parentOrderNo,
      auditTag: 1
    }
  });
};
const toView = (row: { parentOrderNo: string }) => {
  router.push({
    path: "/order/middleground/detail",
    query: {
      orderId: row.parentOrderNo,
      auditTag: 0
    }
  });
};
</script>
