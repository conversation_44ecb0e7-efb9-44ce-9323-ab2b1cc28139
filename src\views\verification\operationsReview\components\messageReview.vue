<template>
  <div class="card">
    <div class="header-title">审核内容</div>
    <message-content :id="id" />

    <Examine
      :verification-id="'id'"
      :audit-msg="reviewForm.auditMsg"
      :is-disabled="viewStatus"
      :is-show-operate-btn="!viewStatus"
      :is-show-backed-btn="false"
      @on-confirm="onConfirm"
    >
    </Examine>
  </div>
</template>

<script setup lang="ts" name="messageReview">
import { ref } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { closeTabItem } from "@/utils/closeTab";
import { backtoOperationList, OperateType } from "../type";
import messageContent from "./messageContent.vue";
import Examine from "@/views/verification/examine.vue";
import { messageReviewStorage } from "@/utils/storage/edit";
import { messageDoAudit } from "@/api/modules/business/message";
const route = useRoute();
const id: string = (route.query.id as string) || "";
const type = (route.query.type as string) || OperateType.detail;
const viewStatus = type == OperateType.detail;
const auditRemark = (route.query.auditMsg as string) || "";

const reviewForm = ref({
  auditMsg: auditRemark
});

const onConfirm = (values: any) => {
  const news = messageReviewStorage.get(id);

  messageDoAudit(values.auditRemark, parseInt(id), values.type == "pass" ? "y" : "n").then(res => {
    if (res.code === 200) {
      ElMessage({
        message: values.type == "pass" ? "审核通过成功!" : "审核不通过完成!",
        type: "success"
      });
      // console.log(reviewForm.auditMsg);
      closeTabItem(route);
      backtoOperationList();
    } else {
      ElMessage({
        type: "error",
        message: res.msg
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
</style>
