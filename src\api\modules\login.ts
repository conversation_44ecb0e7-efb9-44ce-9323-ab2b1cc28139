/*
 * @Author: <EMAIL>
 * @Date: 2023-09-06 09:31:29
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-22 10:59:05
 * @Description: file content
 */
import { Login } from "@/api/interface/index";
import authMenuList from "@/assets/json/authMenuList.json";
import authButtonList from "@/assets/json/authButtonList.json";
import http from "@/api";

/**
 * @name 登录模块
 */
// 用户登录
export const loginApi = (params: Login.ReqLoginForm) => {
  return http.post<Login.ResLogin>(`/auth/token`, params, { noLoading: true }); // 正常 post json 请求  ==>  application/json
  // return http.post<Login.ResLogin>(PORT1 + `/login`, params, { noLoading: true }); // 控制当前请求不显示 loading
  // return http.post<Login.ResLogin>(PORT1 + `/login`, {}, { params }); // post 请求携带 query 参数  ==>  ?username=admin&password=123456
  // return http.post<Login.ResLogin>(PORT1 + `/login`, qs.stringify(params)); // post 请求携带表单参数  ==>  application/x-www-form-urlencoded
  // return http.get<Login.ResLogin>(PORT1 + `/login?${qs.stringify(params, { arrayFormat: "repeat" })}`); // get 请求可以携带数组等复杂参数
};

// 获取菜单列表
export const getAuthMenuListApi = () => {
  return http.get(`/system/common/operateUser/userMenu`);
  // return http.get<Menu.MenuOptions[]>(PORT1 + `/operation/list`, {}, { noLoading: true });
  // 如果想让菜单变为本地数据，注释上一行代码，并引入本地 authMenuList.json 数据
  return authMenuList;
};

// 获取按钮权限
export const getAuthButtonListApi = () => {
  return http.get(`/system/common/operateUser/userMenu`);
  // return http.get<Login.ResAuthButtons>(PORT1 + `/auth/buttons`, {}, { noLoading: true });
  // 如果想让按钮权限变为本地数据，注释上一行代码，并引入本地 authButtonList.json 数据
  return authButtonList;
};

// 获取用户的其他信息--如组织信息
export const getUserInfoApi = () => {
  return http.post(`/auth/userInfo`);
};

// 用户退出登录
export const logoutApi = () => {
  return http.get(`/auth/token/logout`);
};
//获取登陆的图片验证码
export const getVerifyCode = (mobilePhone: string, config = {}) => {
  return http.post(
    `/auth/captcha`,
    {
      mobilePhone,
      ...config
    },
    { noLoading: true }
  );
};
//验证验证码并且发送短信
export const sendSms = (data: any) => {
  return http.post("/auth/sendSms", data, { noLoading: true });
};
