<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="交易量备案列表"
      :columns="columns"
      :tool-button="false"
      :request-api="getTableList"
      :request-auto="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="showAddOrder"> 新增 </el-button>
      </template>
      <template #customerFileName="{ row }">
        <p class="link-text" @click="downloadCustomerFile(row)">{{ row.customerFileName || "--" }}</p>
      </template>
      <template #contractId="{ row }">
        <p class="link-text" @click="showContractDetail(row)">{{ row.contractId || "--" }}</p>
      </template>
      <template #region="{ row }">
        {{ useDictLabel(dictionaryStore?.orderRegion, row.region) || "--" }}
      </template>
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <span v-auth="'upload'">
          <el-tooltip content="上传合同" placement="top">
            <i class="iconfont2 opera-icon icon-lineicon_upload" @click="showUploadContract(row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
    <add-order ref="addOrderRef" @add-success="onAddSuccess"></add-order>
    <contract-detail ref="contractDetailRef"></contract-detail>
    <upload-contract ref="uploadContractRef" @updateSuccess="onUpdateSuccess"></upload-contract>
  </div>
</template>

<script setup lang="tsx" name="transactionVolumeFiling">
import { ref, onMounted, onActivated } from "vue";
import { Plus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useDictLabel } from "@/hooks/useDict";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { downloadAttachFile, getOrderInfoList } from "@/api/modules/transactionVolumeFiling";
import AddOrder from "./components/addOrder/index.vue";
import ContractDetail from "./components/contractDetail/index.vue";
import UploadContract from "./components/uploadContract/index.vue";

const { BUTTONS } = useAuthButtons();

const addOrderRef = ref();
const contractDetailRef = ref();
const uploadContractRef = ref();

const proTable = ref<ProTableInstance>();
const dictionaryStore = useDicStore();

// 表格配置项
const columns: ColumnProps[] = [
  {
    prop: "orderId",
    label: "订单号",
    minWidth: 150,
    search: { el: "input" }
  },
  {
    prop: "customerFileName",
    label: "客户名单",
    minWidth: 100
  },
  {
    prop: "region",
    label: "区域",
    minWidth: 100
  },
  {
    prop: "supplierName",
    label: "供应商名称",
    minWidth: 100
  },
  {
    prop: "contractId",
    label: "合同编码",
    minWidth: 100
  },
  {
    prop: "orderAmountStr",
    label: "订单金额(元)",
    width: 120
  },
  {
    prop: "source",
    label: "订单来源",
    minWidth: 100
  },
  {
    prop: "startDate",
    label: "订单开始时间",
    minWidth: 120
  },
  {
    prop: "endDate",
    label: "订单结束时间",
    minWidth: 120
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    width: 100
  }
];

const getTableList = async (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  return getOrderInfoList({
    ...params,
    source: "第三方"
  });
};

const showAddOrder = () => {
  addOrderRef.value?.show();
};

const onAddSuccess = () => {
  proTable.value?.getTableList();
};

const showUploadContract = (row: any) => {
  uploadContractRef.value?.show(row);
};

const onUpdateSuccess = () => {
  proTable.value?.getTableList();
};

const downloadCustomerFile = (row: any) => {
  if (!row?.customerFileId) {
    return;
  }
  const queryData = {
    id: row.customerFileId
  };
  useDownload(downloadAttachFile, queryData);
};

const showContractDetail = (row: any) => {
  contractDetailRef.value?.show(row);
};

onMounted(() => {
  proTable.value?.getTableList();
  dictionaryStore.getOrderRegion();
});

// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  proTable.value?.getTableList();
});
</script>
