<template>
  <div class="steps">
    <el-steps :active="active">
      <el-step title="基本信息" @click="next(1)"></el-step>
      <el-step title="产品信息" @click="next(2)"></el-step>
      <el-step title="销售信息" @click="next(3)"></el-step>
    </el-steps>
  </div>
</template>

<script setup lang="ts" name="default">
import { ref } from "vue";

const active = ref(0);

const next = index => {
  active.value = index;
  console.log(index);
};
</script>

<style lang="scss" scoped>
.steps {
  width: 60%;
  :deep(.el-step) {
    .el-step__head.is-process,
    .el-step__head.is-finish {
      cursor: pointer;
    }
    .el-step__title.is-finish,
    .el-step__title.is-process {
      display: inline-block;
      cursor: pointer;
    }
  }
}
</style>
