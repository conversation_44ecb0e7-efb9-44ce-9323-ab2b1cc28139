export interface Page {
  current: number;
  size: number;
}
export namespace Banner {
  export interface ListParam extends Page {
    maxPublishTime?: string;
    minPublishTime?: string;
    search?: string;
    enabled?: boolean;
  }

  export interface Record {
    actionType: string;
    actionUrl: string;
    bannerName: string;
  }
  export interface AuditListParams {
    ascs?: string; // 根据该字段排正序
    auditAction?: string; // 审核操作
    bizInfo?: string; // 搜索关键字
    bizType?: string; // 业务类型
    current?: number; // 当前页
    descs?: string; // 根据该字段排倒序
    size?: number; // 每页的数量
    status?: string | number; // 审核状态
  }
  export interface DoAuditParams {
    auditMsg?: string;
    id?: string;
    status?: number;
    bizType?: string;
  }
}
