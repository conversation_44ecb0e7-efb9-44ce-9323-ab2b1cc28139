<template>
  <template v-for="subItem in menuList" :key="subItem.path">
    <!-- ite.meta.visible:0排除隐藏的菜单 -->
    <el-sub-menu
      v-if="subItem.children?.length && subItem.children.filter(ite => ite.meta.visible == 1)?.length"
      :index="subItem.path"
    >
      <template #title>
        <el-icon v-if="!subItem.meta.customIconName">
          <!-- <component :is="subItem.meta.icon" v-if="subItem.meta.icon"></component> -->
          <SvgIcon :name="subItem.meta.icon" :icon-style="{ width: '20px', height: '20px' }" />
        </el-icon>
        <!-- <SvgIcon class="iconfont2" v-else :name="subItem.meta.customIconName" :icon-style="{ width: '20px', height: '20px' }" /> -->
        <!-- 引入自定义svg图片-->
        <i v-else :class="'iconfont2 icon-' + subItem.meta.customIconName"></i>
        <span class="sle">{{ subItem.meta.title }}</span>
      </template>
      <SubMenu :menu-list="subItem.children.filter(ite => ite.meta.visible == 1)" />
    </el-sub-menu>
    <el-menu-item v-else :index="subItem.path" @click="handleClickMenu(subItem)">
      <el-icon v-if="!subItem.meta.customIconName">
        <!-- <component :is="subItem.meta.icon" v-if="subItem.meta.icon"></component> -->
        <SvgIcon :name="subItem.meta.icon" :icon-style="{ width: '20px', height: '20px' }" />
      </el-icon>
      <!-- <SvgIcon class="iconfont2" v-else :name="subItem.meta.customIconName" :icon-style="{ width: '20px', height: '20px' }" /> -->
      <i v-else :class="'iconfont2 icon-' + subItem.meta.customIconName"></i>
      <template #title>
        <span class="sle">{{ subItem.meta.title }}</span>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import SvgIcon from "@/components/SvgIcon/index.vue";

defineProps<{ menuList: Menu.MenuOptions[] }>();

const router = useRouter();
const handleClickMenu = (subItem: Menu.MenuOptions) => {
  if (subItem.meta.isLink) return window.open(subItem.meta.isLink, "_blank");
  router.push(subItem.path);
};
</script>

<style lang="scss">
.el-sub-menu .el-sub-menu__title:hover {
  color: var(--el-menu-hover-text-color) !important;
  background-color: transparent !important;
}
.el-menu--collapse {
  .is-active {
    .el-sub-menu__title {
      color: #ffffff !important;
      background-color: var(--el-color-primary) !important;
    }
  }
}
.el-menu-item {
  &:hover {
    color: var(--el-menu-hover-text-color);
  }
  &.is-active {
    color: var(--el-menu-active-color) !important;
    background-color: var(--el-menu-active-bg-color) !important;
    &::before {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 4px;
      content: "";
      background-color: var(--el-color-primary);
    }
  }
}
.vertical,
.classic,
.transverse {
  .el-menu-item {
    &.is-active {
      &::before {
        left: 0;
      }
    }
  }
}
.columns {
  .el-menu-item {
    &.is-active {
      &::before {
        right: 0;
      }
    }
  }
}
.classic,
.transverse {
  #driver-highlighted-element-stage {
    background-color: #606266 !important;
  }
}
</style>
