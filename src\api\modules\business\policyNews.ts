import http from "@/api";
import { PolicyNews } from "@/api/interface/business/policyNews";

export const getPolicyPageList = (data: PolicyNews.ListReqParam) => {
  return http.get("/cnud-operation/admin/policy-pageList", data);
};

export const revokePolicy = (id: string) => {
  return http.post("/cnud-operation/admin/policy-revoke", { id });
};

export const deletePolicy = (ids: string[]) => {
  return http.post("/cnud-operation/admin/policy-delete", { ids });
};

export const addPolicy = (data: PolicyNews.ChangeReqParam, isPublish: boolean) => {
  return http.post("/cnud-operation/admin/policy-save", {
    news: data,
    publishNow: isPublish
  });
};

export const updatePolicy = (data: PolicyNews.ChangeReqParam, isPublish: boolean) => {
  return http.post("/cnud-operation/admin/policy-update", {
    news: data,
    publishNow: isPublish
  });
};
