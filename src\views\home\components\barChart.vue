<template>
  <div id="barChart" class="echarts"></div>
</template>

<script setup lang="ts">
import { ECharts, EChartsOption, init } from "echarts";
interface ChartProp {
  nameList: string[];
  data1: string[];
  data2: string[];
}
const initChart = (
  data: ChartProp = {
    nameList: [],
    data1: [],
    data2: []
  }
): ECharts => {
  const charEle = document.getElementById("barChart") as HTMLElement;
  const charEch: ECharts = init(charEle);
  const xAxisData = data.nameList;
  const dataValue1 = data.data1;
  const dataValue2 = data.data2;

  const colors = ["#5E9AFF", "#0052D9"];
  const option: EChartsOption = {
    color: colors,
    tooltip: {
      trigger: "axis"
    },
    legend: {
      data: ["新增", "累计"],
      textStyle: {
        color: "#B4B4B4"
      },
      itemWidth: 22,
      itemHeight: 4,
      bottom: "0%"
    },
    grid: {
      top: "5%",
      right: "2%",
      bottom: "15%",
      left: "5%",
      containLabel: true
    },
    xAxis: {
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: "#B4B4B4"
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      splitLine: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: "#B4B4B4"
        }
      }
    },

    series: [
      {
        name: "新增",
        type: "bar",
        barWidth: 10,
        data: dataValue1
      },
      {
        name: "累计",
        type: "bar",
        barGap: "-100%",
        barWidth: 10,
        z: -12,
        data: dataValue2
      }
    ]
  };
  charEch.setOption(option);
  return charEch;
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
:deep(.annual-tooTip) {
  box-sizing: border-box;
  height: 124px;
  padding: 5px 16px;
  background: rgb(3 11 27 / 80%);
  border: 1px solid #5a7dc0;
  .annual-month {
    display: inline-block;
    margin-bottom: 6px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
  }
  .annual-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    .year-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 22px;
      margin-bottom: 5px;
      font-family: PingFangSC-Regular, "PingFang SC";
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      .year-dot {
        width: 8px;
        height: 8px;
        margin-right: 5px;
      }
      .year-name {
        width: 150px;
        margin: 0 2px;
      }
      .year-value {
        display: inline-block;
      }
    }
  }
}
</style>
