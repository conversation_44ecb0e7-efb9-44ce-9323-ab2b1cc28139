<template>
  <el-drawer class="info-drawer" v-model="drawerVisible" :destroy-on-close="true" size="670px" :title="drawerProps.title">
    <div class="info-box">
      <div class="title">客户信息</div>
      <div class="infos">
        <el-row>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">客户名称：</span>
              <span class="info-text">{{ drawerProps.row?.contactName || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">手机号码：</span>
              <span class="info-text">{{ drawerProps.row?.phone || "-" }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">公司名称：</span>
              <span class="info-text">{{ drawerProps.row?.companyName || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">城市：</span>
              <span class="info-text">{{ drawerProps.row?.regionName || "-" }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <div class="info-item">
            <span class="info-label">邮箱：</span>
            <span class="info-text">{{ drawerProps.row?.email || "-" }}</span>
          </div>
        </el-row>
      </div>
    </div>
    <div class="info-box">
      <div class="title">咨询信息</div>
      <div class="infos">
        <el-row>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">咨询产品：</span>
              <span class="info-text">{{ drawerProps.row?.itemName || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="info-label">产品分类：</span>
              <span class="info-text">{{ drawerProps.row?.itemCategory || "-" }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <div class="info-item">
            <span class="info-label">咨询时间：</span>
            <span class="info-text">{{ drawerProps.row?.createTime || "-" }}</span>
          </div>
        </el-row>
        <el-row>
          <div class="info-item">
            <span class="info-label">咨询问题：</span>
            <span class="info-text">{{ drawerProps.row?.consultMsg || "-" }}</span>
          </div>
        </el-row>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts" name="UserDrawer">
import { ref } from "vue";

interface DrawerProps {
  title: string;
  isView: boolean;
  row: object;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
};

defineExpose({
  acceptParams
});
</script>

<style lang="scss" scoped>
.info-drawer {
  &:deep(.el-drawer__header) {
    font-weight: 600;
  }
}
.info-box {
  .title {
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #3a3a3d;
    border-bottom: 1px solid #e4e7ed;
  }
  .infos {
    margin-left: 10px;
  }
  .info-item {
    margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      width: 80px;
      margin-right: 10px;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
}
</style>
