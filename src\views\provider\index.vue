<template>
  <div class="table-box">
    <pro-table ref="proTable" title="服务商列表" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button v-auth="'add'" plain :icon="Plus" @click="creatProvider"> 新增 </el-button>
        <el-button
          v-auth="'delete'"
          plain
          :icon="Delete"
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          删除
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="edit(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="deleteProvider(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
    <AddOrEditDialog ref="addDialogRef" @update="updateList" />
  </div>
</template>

<script setup lang="tsx" name="provider">
import { ref, onMounted } from "vue";
import { Plus, Delete } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getProviderPageList, providerRemove } from "@/api/modules/product";
import AddOrEditDialog from "./components/addOrEditDialog.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { useProductStore } from "@/stores/modules/product";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const productStore = useProductStore();
const proTable = ref<ProTableInstance>();
const addDialogRef = ref<InstanceType<typeof AddOrEditDialog> | null>(null);
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", align: "left" },
  { prop: "name", align: "left", label: "服务商名称", search: { el: "input", props: { maxlength: "20" } } },
  { prop: "code", align: "left", label: "服务商编码" },
  {
    prop: "needPurchaseProcess",
    label: "自动开通",
    align: "left",
    enum: [
      { label: "不支持", value: false },
      { label: "支持", value: true }
    ],
    sortable: true,
    width: 110
  },
  { prop: "creatorName", align: "left", label: "创建人员" },
  { prop: "createTime", align: "left", label: "创建时间", sortable: true, width: 170 },
  { prop: "operation", align: "left", label: "操作", fixed: "right", width: 110 }
];
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.descs = "createTime";
  return getProviderPageList(newParams);
};
const edit = (row: any = {}) => {
  addDialogRef.value?.acceptParams({ show: true, title: "服务商编辑", row: row, type: "edit" });
};
const deleteProvider = async (row: any = {}) => {
  await useHandleData(providerRemove, { ids: row.id }, `删除<${row.name}>`);
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
const batchDelete = async (ids: string[]) => {
  await useHandleData(providerRemove, { ids: ids.join(",") }, "删除所选服务商");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
const creatProvider = () => {
  addDialogRef.value?.acceptParams({ show: true, title: "新增服务商", type: "add" });
};
const updateList = () => {
  proTable.value?.getTableList();
  // 更新服务商下拉列表接口
  productStore.getProviderList();
};
</script>
