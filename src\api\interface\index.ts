/*
 * @Author: <EMAIL>
 * @Date: 2023-09-08 09:49:57
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-19 16:01:12
 * @Description: file content
 */
// 请求响应参数（不包含data）
export interface Result {
  code: number;
  msg: string;
  data?: any;
}

// 请求响应参数（包含data）
export interface ResultData<T = any> extends Result {
  data: T;
}

// 分页响应参数
export interface ResPage<T> {
  records: T[];
  pages: number;
  size: number;
  total: number;
}

// 分页请求参数
export interface ReqPage {
  current: number;
  size: number;
}

// 分页请求参数
export interface ReqPageCommon extends ReqPage {
  ascs?: string;
  descs?: string;
}

// 文件上传模块
export namespace Upload {
  export interface ResFileUrl {
    fileUrl: string;
  }
}

// 登录模块
export namespace Login {
  export interface ReqLoginForm {
    account?: string;
    password?: string;
    code?: string;
    grantType?: string;
    userType?: string;
    phoneNumber?: string;
    refreshToken?: string;
    phone?: string;
    sms?: string;
    autoCreate?: number; //自动创建账号
  }
  export interface ResLogin {
    accessToken: string;
  }
  export interface ResAuthButtons {
    [key: string]: string[];
  }
}

// 用户管理模块
export namespace User {
  export interface modifyParam {
    verifyContent: number;
    newPassword?: string;
    oldPassword?: string;
    phone?: string;
    email?: string;
  }
  export interface ReqUserParams extends ReqPage {
    username: string;
    gender: number;
    idCard: string;
    email: string;
    address: string;
    createTime: string[];
    status: number;
  }
  export interface ResUserList {
    id: string;
    username: string;
    gender: number;
    user: { detail: { age: number } };
    idCard: string;
    email: string;
    address: string;
    createTime: string;
    status: number;
    avatar: string;
    photo: any[];
    children?: ResUserList[];
  }
  export interface ResStatus {
    userLabel: string;
    userValue: number;
  }
  export interface ResGender {
    genderLabel: string;
    genderValue: number;
  }
  export interface ResDepartment {
    id: string;
    name: string;
    children?: ResDepartment[];
  }
  export interface ResRole {
    id: string;
    name: string;
    children?: ResDepartment[];
  }
}
