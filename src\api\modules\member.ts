import http from "@/api";
import { Partner } from "@/api/interface/partner";

// 会员管理模块
/**
 * 列表查询(分页)
 * @param params
 */
export const getMemberList = (params: any) => {
  return http.get(`/cnud-product/admin/computing-portal/customer/queryCustomerInfo`, params);
};

/**
 * 详情
 * @param params
 */
export const getMemberDetail = (params: { id: string }) => {
  return http.get<Partner.ProviderAgreementDetail>(`/cnud-product/pmsProviderAgreement-detail`, params);
};

/**
 * 新增(返回id)
 * @param params
 */
export const addMember = (params: Partner.AddAgreementParams) => {
  return http.post(`/cnud-product/admin/computing-portal/customer/addCustomerInfo`, params);
};

/**
 * 修改
 * @param params
 */
export const updateMember = (params: Partner.AddAgreementParams) => {
  return http.post(`/cnud-product/admin/computing-portal/customer/modCustomerInfo`, params);
};

/**
 * 删除
 * @param params
 */
export const delMember = (params: { ids: [] }) => {
  return http.post(`/cnud-product/admin/computing-portal/customer/delCustomerInfo`, params);
};

/**
 * 查询供合同管理使用
 * @param params
 */
export const getCustomerList = (params: any) => {
  return http.get(`/cnud-product/admin/computing-portal/customer/queryCustomerList`, params);
};

/**
 * 查询供应商 合同管理使用
 * @param params
 */
export const getSupplierList = (params: any) => {
  return http.get(`/cnud-product/computing-portal/supplier/querySupplierInfo`, params);
};
