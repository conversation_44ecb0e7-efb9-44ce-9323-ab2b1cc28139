<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="editTypeChinese[props.type] + '分类'" width="37%">
      <el-form ref="ruleFormRef" :model="labelForm" :rules="rules" label-width="120px" status-icon label-position="top">
        <el-form-item label="父级分类" class="horizontal">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="(item, index) in params.parentList" :key="index">{{ item }}</el-breadcrumb-item>
          </el-breadcrumb>
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input class="label-name" v-model="labelForm.name" maxlength="10" show-word-limit clearable></el-input>
        </el-form-item>
        <el-form-item label="分类ICON" v-if="labelForm.level === 2">
          <UploadImg v-model:image-url="labelForm.icon" width="135px" height="135px" :file-size="0.3">
            <template #tip>建议图片尺寸为84px x 84px，图片大小不能超过0.2M，支持图片格式jpg/jpeg/png</template>
          </UploadImg>
        </el-form-item>
        <el-form-item label="背景图片" v-if="labelForm.level === 2">
          <UploadImg v-model:image-url="labelForm.pic" width="135px" height="135px" :file-size="0.6">
            <template #tip>建议图片尺寸为520px x 746px，图片大小不能超过0.6M，支持图片格式jpg/jpeg/png</template>
          </UploadImg>
        </el-form-item>
        <el-form-item label="描述">
          <el-input class="label-name" v-model="labelForm.description" type="textarea" maxlength="100" show-word-limit></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";
import UploadImg from "@/components/Upload/Img.vue";
import { solutionAddType, solutionUpdateType } from "@/api/modules/solution";
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";

const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: 1
  },
  params: {
    type: Object,
    default: () => {}
  }
});

const ruleFormRef = ref();

const labelForm = ref<any>({
  name: "",
  description: "",
  parentId: "",
  icon: "",
  pic: "",
  level: null
});

const emits = defineEmits(["update:visible", "cancel", "confirm"]);

watch(
  () => props.params,
  () => {
    labelForm.value.name = props.params.name || "";
    labelForm.value.description = props.params.description || "";
    labelForm.value.pic = props.params.pic || "";
    labelForm.value.icon = props.params.icon || "";
    labelForm.value.parentId = props.params.parentId;
    labelForm.value.level = props.params.level;
  }
);

const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
};

const rules = reactive({
  name: [{ trigger: "blur", required: true, message: "名称不能为空" }]
  // pic: [{ required: true, message: "请上传背景图", trigger: "blur" }]
});

const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    labelForm.value = {
      name: "",
      description: "",
      pic: "",
      icon: "",
      parentId: "",
      level: null
    };
    emits("update:visible", value);
  }
});

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: solutionAddType,
        [editType["edit"]]: solutionUpdateType
      };
      const requestParamsObject = {
        [editType["add"]]: () => {
          if (labelForm.value.level === 1) {
            return {
              level: 1,
              name: labelForm.value.name,
              description: labelForm.value.description
            };
          } else {
            return {
              parentId: labelForm.value.parentId,
              level: labelForm.value.level,
              name: labelForm.value.name,
              description: labelForm.value.description,
              icon: labelForm.value.icon,
              pic: labelForm.value.pic
            };
          }
        },
        [editType["edit"]]: () => {
          return {
            id: props.params.id,
            level: labelForm.value.level,
            name: labelForm.value.name,
            description: labelForm.value.description,
            icon: labelForm.value.icon,
            pic: labelForm.value.pic
          };
        }
      };
      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        emits("confirm");
        ElMessage.success(res.msg);
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
.horizontal.el-form-item {
  display: flex;
  align-items: center;
  :deep(.el-form-item__label) {
    margin-bottom: 0;
  }
}
</style>
