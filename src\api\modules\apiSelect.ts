import http from "@/api";

export const fetchApiOptionList = async () => {
  try {
    const data = await http.get("/system/admin/sysApi-list");
    return (data as any).data.map(({ id, apiName, apiPath, serviceName, module }: any) => ({
      value: id,
      label: `【${serviceName}】${module} | ${apiName}(${apiPath})`
    }));
  } catch (e) {}
  return [];
};

export const fetchApiIdList = async (id: string) => {
  try {
    const data = await http.get("/system/menu/info/", { id });
    return (data as any).data.apiList.map(({ id }: any) => id);
  } catch (e) {}
  return [];
};

export const callApiRegister = async () => {
  return await http.post("/system/admin/sysApi-register");
};
