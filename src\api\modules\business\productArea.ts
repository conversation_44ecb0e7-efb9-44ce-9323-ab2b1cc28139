import http from "@/api";

export const getPageList = (data: any) => {
  return http.get("/cnud-operation/admin/productZone-pageList", data);
};

export const deleteItem = (id: string) => {
  return http.post("/cnud-operation/admin/productZone-delete", { ids: [id] });
};

export const change = (data: any) => {
  return http.post("/cnud-operation/admin/productZone-update", data);
};

export const add = (data: any) => {
  return http.post("/cnud-operation/admin/productZone-save", data);
};

export const getProductDetailList = (ids: any) => {
  return http.post("/cnud-product/pmsProduct/getProductList", { ids });
};

export const getProductPageList = (data: any) => {
  return http.get("/cnud-product/pmsProduct/queryPage", data);
};

export const getCategory = () => {
  return http.post("/cnud-product/pmsCategory/list/withChildren");
};
