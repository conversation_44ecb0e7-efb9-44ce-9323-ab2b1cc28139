<template>
  <div class="status-type" :class="`product__${productStatus}`">
    审核状态：{{ useDictLabel(verifyStatusDict, productStatus) }}
  </div>
</template>

<script setup lang="ts">
import { useDictLabel } from "@/hooks/useDict";
import { computed } from "vue";
import { useDicStore } from "@/stores/modules/dictionaty";

// 待审核、审核通过、审核不通过
const dictionaryStore = useDicStore();
// 审核状态
const verifyStatusDict: any = computed(() => {
  const status = dictionaryStore.productAuditStatus;
  status?.forEach((item: any = {}) => {
    item.value = String(item.itemKey);
  });
  return status;
});

const props = defineProps({
  productStatus: {
    type: String,
    request: true
  },
  statusRemark: {
    type: String,
    default: ""
  }
});
</script>

<style scoped lang="scss">
.status-type {
  height: 84px;
  padding-left: 84px;
  margin-bottom: 10px;
  font-weight: 500;
  line-height: 84px;
  span {
    margin-left: 16px;
    font-size: 14px;
    color: rgb(115 120 127 / 100%);
  }
  &.product {
    color: #3a3a3d;

    /* 待审核 */
    &__1 {
      background-color: rgb(255 170 0 / 10%);
      background-image: url("@/assets/images/status_icon_5.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }

    /* 审核不通过 */
    &__3 {
      background-color: rgb(115 120 127 / 10%);
      background-image: url("@/assets/images/status_icon_2.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }

    /* 审核通过 */
    &__2 {
      background-color: rgb(25 190 107 / 10%);
      background-image: url("@/assets/images/status_icon_6.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }
  }
}
</style>
