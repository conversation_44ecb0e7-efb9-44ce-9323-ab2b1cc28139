<template>
  <div class="info-box">
    <div class="header-title-common">{{ title }}</div>
    <div :class="isHide ? 'maxHeight basic' : 'basic'" :style="{ 'max-height': isHide ? defaultHeight + 'px' : '' }">
      <el-row :gutter="24">
        <div ref="basicTopRef">
          <el-col :span="24" class="detail-info-item">
            <span class="detail-info-label">问题描述：</span>
            <span class="detail-info-value">{{ baseDetail?.description }}</span>
          </el-col>
          <el-col :span="24" class="detail-info-item">
            <span class="detail-info-label">工单状态：</span>
            <span class="detail-info-value">
              <CustomTag
                :type="orderStatus[baseDetail?.orderStatus as OrderStatus]?.[0]"
                :label="useDictLabel(work_order_status, baseDetail?.orderStatus)"
              ></CustomTag>
              <span class="tips-icon">
                <el-popover :width="'auto'" placement="top" trigger="hover" :content="tips">
                  <template #reference>
                    <el-icon class="icon" size="16px"><Warning /></el-icon>
                  </template>
                </el-popover>
              </span>
            </span>
          </el-col>
        </div>
        <el-col :span="8" class="detail-info-item">
          <span class="detail-info-label">所选产品/服务：</span>
          <span class="detail-info-value">{{ baseDetail?.orderType }}</span>
        </el-col>
        <el-col :span="8" class="detail-info-item">
          <span class="detail-info-label">紧急程度：</span>
          <span class="detail-info-value">{{ useDictLabel(work_order_urgency, baseDetail?.urgency) }}</span>
        </el-col>
        <el-col :span="8" class="detail-info-item">
          <span class="detail-info-label">手机号码：</span>
          <span class="detail-info-value">{{ baseDetail?.phone }}</span>
        </el-col>
        <el-col :span="24" class="detail-info-item">
          <div class="file-wrap">
            <span class="detail-info-label">附件：</span>
            <div class="detail-info-value file-list">
              <UploadFiles
                class="custom-upload"
                v-model:file-list="productDocs"
                :file-type="'.doc, .xls, .docx, .xlsx, .rar, .zip, .jpg, .gif, .jpeg, .png, .pdf'"
                :limit="100"
                :file-size="10"
                :show-file-list="true"
                :is-show-download="true"
                :drag="false"
              >
                <!-- <el-button type="default"> <i class="iconfont2 icon-lineicon_upload"></i><span>上传文件</span> </el-button> -->
              </UploadFiles>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="moreCon" :style="{ marginTop: isHide ? '22px' : '12px' }" @click="getMoreAddress">
      <el-icon>
        <i :class="`iconfont2 menu-icon ${isHide ? 'icon-dianjizhankai' : 'icon-dianjishouqi'}`"></i>
      </el-icon>
      <text class="txt">更多提交信息</text>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from "vue";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
import UploadFiles from "@/components/Upload/Files.vue";

import { orderStatus, OrderStatus } from "../type";
const { work_order_status, work_order_urgency } = useDict("work_order_status", "work_order_urgency");

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  baseDetail: {
    type: Object,
    required: true
  },
  defaultRow: {
    type: Number,
    default: 2
  }
});
const tips = `工单状态为"已处理"时，如在7个工作日内未确认是否解决，工单状态将自动变为"已关闭"。`;
const isHide = ref<boolean>(true);
const basicTopRef = ref();

const defaultHeight = ref();

watch(
  () => props.baseDetail,
  () => {
    if (basicTopRef.value) {
      nextTick(() => {
        defaultHeight.value = basicTopRef.value?.offsetHeight + 2;
      });
    }
  }
);
const productDocs = computed(() => {
  const doc = props.baseDetail?.attachmentIds;
  if (doc) {
    const docs = JSON.parse(doc);
    return docs?.map((item: any) => {
      return {
        ...item,
        link: item.url
      };
    });
  }
  return [];
});

const getMoreAddress = () => {
  isHide.value = !isHide.value;
};
</script>

<style lang="scss" scoped>
.info-box {
  padding: 24px 0 40px;
  .basic {
    overflow: hidden;
  }
  .basic.maxHeight {
    height: auto;
  }
  .moreCon {
    height: 22px;
    font-family: PingFangSC, "PingFang SC", sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #0052d9;
    cursor: pointer;
    .menu-icon {
      font-size: 16px;
    }
  }
}
.detail-info-item {
  margin-top: 24px;
  .detail-info-label {
    height: 22px;
    font-family: PingFangSC-Regular, "PingFang SC", sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #73787f;
  }
  .detail-info-value {
    height: 22px;
    font-family: PingFangSC-Regular, "PingFang SC", sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #3a3a3d;
    .tips-icon {
      display: inline-block;
      padding-top: 2px;
      margin-left: 6px;
      vertical-align: middle;
    }
  }
  .file-list {
    flex: 1;
    height: auto;
  }
}
.file-wrap {
  display: flex;
}
:deep(.custom-upload) {
  .upload .el-upload {
    display: none;
  }
  .upload .el-upload-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0;
  }
  .upload .el-upload-list__item {
    width: auto;
    padding-right: 10px;
    margin-right: 24px;
  }
}
</style>
