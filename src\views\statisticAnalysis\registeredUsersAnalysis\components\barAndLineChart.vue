<template>
  <div id="orderLineChart" class="echarts"></div>
</template>

<script setup lang="ts">
import { ECharts, EChartsOption, init } from "echarts";
import { useEcharts } from "@/hooks/useEcharts";

interface ChartProp {
  nameList: string[];
  data1: string[];
  data2: string[];
  data3: string[];
}
const initChart = (
  data: ChartProp = {
    nameList: [],
    data1: [],
    data2: [],
    data3: []
  }
) => {
  const charEle = document.getElementById("orderLineChart") as HTMLElement;
  const charEch: ECharts = init(charEle);
  const xAxisData = data.nameList;
  const dataValue1 = data.data1;
  const dataValue2 = data.data2;
  const dataValue3 = data.data3;

  const colors = ["#5E9AFF", "#0052D9", "#8498BB"];
  const option: EChartsOption = {
    color: colors,
    tooltip: {
      trigger: "axis"
    },
    legend: {
      data: ["新增", "累计", "日环比"],
      textStyle: {
        color: "#B4B4B4"
      },
      itemWidth: 22,
      itemHeight: 4,
      bottom: "0%"
    },
    grid: {
      top: "3%",
      right: "2%",
      bottom: "15%",
      left: "3%",
      containLabel: true
    },
    xAxis: {
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: "#B4B4B4"
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        splitLine: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: "#B4B4B4"
          }
        }
      },
      {
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        axisTick: {
          show: false
        }
      }
    ],

    series: [
      {
        name: "新增",
        type: "bar",
        barWidth: 10,
        data: dataValue1
      },
      {
        name: "累计",
        type: "bar",
        barGap: "-100%",
        barWidth: 10,
        z: -12,
        data: dataValue2
      },
      {
        name: "日环比",
        type: "line",
        smooth: true,
        showAllSymbol: true,
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          borderColor: "#fff",
          borderWidth: 2
        },
        yAxisIndex: 1,
        data: dataValue3
      }
    ]
  };
  useEcharts(charEch, option);
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
:deep(.annual-tooTip) {
  box-sizing: border-box;
  height: 124px;
  padding: 5px 16px;
  background: rgb(3 11 27 / 80%);
  border: 1px solid #5a7dc0;
  .annual-month {
    display: inline-block;
    margin-bottom: 6px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
  }
  .annual-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    .year-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 22px;
      margin-bottom: 5px;
      font-family: PingFangSC-Regular, "PingFang SC";
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      .year-dot {
        width: 8px;
        height: 8px;
        margin-right: 5px;
      }
      .year-name {
        width: 150px;
        margin: 0 2px;
      }
      .year-value {
        display: inline-block;
      }
    }
  }
}
</style>
