<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="云账号解绑审核"
      :request-auto="false"
      :tool-button="false"
      :columns="columns"
      :request-api="getTableList"
    >
      <template #auditStatus="{ row }">
        <CustomTag
          :type="statusTypeMap[row.auditStatus]"
          :status="row.auditStatus"
          :label="useDictLabel(verifyStatusDict, row.auditStatus) || '--'"
        ></CustomTag>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'review'">
          <el-tooltip content="审核通过" placement="top">
            <i class="iconfont2 opera-icon icon-shenhe" @click="handleUnbind(scope.row.id, 1)"></i>
          </el-tooltip>
          <el-tooltip content="驳回" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="handleUnbind(scope.row.id, 0)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="productManageList">
import { ref, onMounted, computed } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDicStore } from "@/stores/modules/dictionaty";
import { accountUnbind, getPageList } from "@/api/modules/accountAudit";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDictLabel } from "@/hooks/useDict";
import { ElMessage, ElMessageBox } from "element-plus";

const statusTypeMap: { [key: string]: string } = {
  "1": "yellow",
  "2": "green",
  "3": "red"
};

const { BUTTONS } = useAuthButtons();
enum OperateType {
  "detail" = "1",
  "review" = "2"
}

const dictionaryStore = useDicStore();
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();
onMounted(() => {
  dictionaryStore.getAuditOperation(); // 审核操作
  dictionaryStore.getProductAuditStatus(); // 审核状态
  proTable.value?.getTableList();
});

// 审核操作类型
const auditOperationDict: any = computed(() => dictionaryStore.auditOperation);
// 审核状态
const verifyStatusDict: any = computed(() => {
  const status = dictionaryStore.productAuditStatus;
  status?.forEach((item: any = {}) => {
    item.value = String(item.itemKey);
  });
  return status;
});

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  // newParams.descs = "createTime";
  return getPageList(newParams);
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: "startDate",
    label: "开始时间",
    isShow: false,
    search: {
      el: "date-picker",
      props: {
        type: "date",
        format: "YYYY-MM-DD",
        valueFormat: "YYYY-MM-DD"
      }
    }
  },
  {
    prop: "endDate",
    label: "结束时间",
    isShow: false,
    search: {
      el: "date-picker",
      props: {
        type: "date",
        format: "YYYY-MM-DD",
        valueFormat: "YYYY-MM-DD"
      }
    }
  },
  { prop: "tenantUuid", label: "云账号uuid" },
  { prop: "userId", label: "关系id" },
  { prop: "cloudProvider", label: "云服务商" },
  { prop: "resourceName", label: "资源池名称" },
  { prop: "accountName", label: "云账号名称" },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const handleUnbind = async (id: string, auditState: number) => {
  ElMessageBox.confirm(`请确认是否${auditState == 1 ? "审批通过" : "驳回申请"}？`, "确认操作").then(async () => {
    await accountUnbind({ id, auditState });
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};
</script>

<style lang="scss">
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
