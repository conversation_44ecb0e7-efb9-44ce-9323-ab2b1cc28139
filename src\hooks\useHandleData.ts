/*
 * @Author: <EMAIL>
 * @Date: 2023-10-17 15:32:32
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-10-23 09:38:39
 * @Description: file content
 */
import { ElMessageBox, ElMessage } from "element-plus";
import { HandleData } from "./interface";

/**
 * @description 操作单条数据信息 (二次确认【删除、禁用、启用、重置密码】)
 * @param {Function} api 操作数据接口的api方法 (必传)
 * @param {Object} params 携带的操作数据参数 {id,params} (必传)
 * @param {String | Object} message 提示信息 (必传) object: msg--提示信息，successMsg--成功提示信息
 * @param {String} confirmType icon类型 (不必传,默认为 warning)
 * @returns {Promise}
 */
export const useHandleData = (
  api: (params: any) => Promise<any>,
  params: any = {},
  message:
    | string
    | {
        msg: string;
        successMsg: string;
        isShowText?: boolean;
      },
  confirmType: HandleData.MessageType = "warning",
  config: { showRequestMsg: Boolean } = {
    showRequestMsg: false
  }
) => {
  return new Promise((resolve, reject) => {
    const isMsgString = typeof message === "string";
    ElMessageBox.confirm(`${isMsgString ? `是否${message}?` : message.msg}`, "温馨提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: confirmType,
      draggable: true
    }).then(async () => {
      const res = await api(params);
      if (res.code != 200) return reject(false);
      if (config?.showRequestMsg) {
        ElMessage({
          type: "success",
          message: res.msg
        });
      } else {
        ElMessage({
          type: "success",
          message: `${isMsgString ? message : message.successMsg}成功!`
        });
      }

      resolve(true);
    });
  });
};
