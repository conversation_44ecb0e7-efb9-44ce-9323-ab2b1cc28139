import http from "@/api";
import { WorkOrderType } from "../interface/workOrder";

// 工单信息表-列表查询(分页)
export const getWorkOrderList = (params: { id: string }) => {
  return http.get(`/cnud-operation/admin/workOrder/pageList`, params);
};

// 工单信息表-详情
export const getWorkOrderDetail = (params: { id: string }) => {
  return http.post<WorkOrderType.WorkOrderDetail>(`/cnud-operation/admin/workOrder/detail`, params);
};

// 工单-导出
export const exportWorkOrderList = (params: { id: string }) => {
  return http.download(`/cnud-operation/admin/workOrder/export`, params);
};

// 工单处理记录日志表-新增(返回id)
export const saveWorkOrderLog = (params: any) => {
  return http.post(`/cnud-operation/admin/workOrder/saveWorkLog`, params);
};

// 确认解决
export const confirmWorkOrder = (params: any) => {
  return http.post(`/cnud-operation/admin/workOrder/confirm`, params);
};
// 确认解决
export const changeWorkOrderStatus = (params: any) => {
  return http.post(`/cnud-operation/admin/workOrder/changeStatus`, params);
};
