<template>
  <div class="banner-box table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => toEdit()"> 新增消息 </el-button>
      </template>

      <template #auditStatus="{ row }">
        <el-popover placement="top" title="" :width="400" trigger="hover">
          <div>
            <div>审核人：{{ row.auditName }}</div>
            <div>审核状态：{{ useDictLabel(statusAuditDict, row.auditStatus) }}</div>
            <!-- <div>审核状态：{{ row?.auditStatus?.label }}</div> -->
            <div>审核意见：{{ row.auditRemark }}</div>
            <div>审核时间：{{ row.auditTime }}</div>
          </div>
          <template #reference>
            {{ useDictLabel(statusAuditDict, row.auditStatus) }}
          </template>
        </el-popover>
      </template>

      <template #status="{ row }">
        <CustomTag
          :type="statusTypeMap[row.status]"
          :status="row.status"
          :label="useDictLabel(dictionaryStore.noticeStatus, row.status)"
        ></CustomTag>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="() => toView(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i
              class="iconfont2 opera-icon icon-a-lineicon_edit"
              v-if="scope.row.status == '0' || scope.row.status == '4'"
              @click="() => toEdit(scope.row)"
            ></i>
          </el-tooltip>
        </span>
        <span v-auth="'revoke'">
          <el-tooltip content="撤回" placement="top">
            <i class="iconfont2 opera-icon icon-chexiao" v-if="scope.row.status == '2'" @click="() => revoke(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i
              class="iconfont2 opera-icon icon-lajitong"
              v-if="scope.row.status == '0' || scope.row.status == '4'"
              @click="() => withdraw(scope.row)"
            ></i>
          </el-tooltip>
        </span>
        <span v-auth="'unpublish'">
          <el-tooltip content="取消发布" placement="top">
            <i
              class="iconfont2 opera-icon icon-quxiaofabu"
              v-if="scope.row.status == '3'"
              @click="() => cancelPublish(scope.row)"
            ></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="message">
import { ref, watch, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { type ProTableInstance, type ColumnProps, type HeaderRenderScope } from "@/components/ProTable/interface/index";
import { Plus } from "@element-plus/icons-vue";
import { getPageList, cancel, del, messageRevoke } from "@/api/modules/business/message";
import { useDicStore } from "@/stores/modules/dictionaty";
import { messageStorage } from "@/utils/storage/edit";
import { getDictionary } from "@/api/modules/system";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDictLabel } from "@/hooks/useDict";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const dictionaryStore = useDicStore();
const statusTypeMap: { [key: string]: string } = {
  "1": "blue",
  "2": "yellow",
  "3": "green",
  "4": "grey",
  "5": "red"
};

const { BUTTONS } = useAuthButtons();

const route = useRoute();

const router = useRouter();

const proTable = ref<ProTableInstance>();
const newsStatus = ref([]);

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }

  let newParams = JSON.parse(JSON.stringify(search));

  // let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.minCreateTime = newParams.createTime[0]);
  newParams.createTime && (newParams.maxCreateTime = newParams.createTime[1]);
  newParams.publishTime && (newParams.minPublishTime = newParams.publishTime[0]);
  newParams.publishTime && (newParams.maxPublishTime = newParams.publishTime[1]);
  delete newParams.createTime;
  delete newParams.publishTime;

  return getPageList(newParams);
};

onMounted(() => {
  dictionaryStore.getNoticeStatus();
  dictionaryStore.getNoticeAuditStatus();
  dictionaryStore.getNoticeTypeDicList();
});
const statusDict: any = computed(() => dictionaryStore.noticeStatus);
const statusAuditDict: any = computed(() => dictionaryStore.noticeAuditStatus);
const noticeTypeDic: any = computed(() => dictionaryStore.noticeTypeDic);

const columns = ref<ColumnProps[]>([
  {
    prop: "title",
    label: "消息标题",
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "bizTypeId",
    label: "消息类型",
    align: "left",
    enum: noticeTypeDic,
    // enum: [
    //   { label: "全部", value: "-1" },
    //   { label: "系统通知", value: "1" },
    //   { label: "活动通知", value: "2" },
    //   { label: "产品通知", value: "2" },
    //   { label: "其他", value: "4" }
    // ],
    search: { el: "select" }
  },
  {
    prop: "status",
    align: "left",
    label: "状态",
    enum: statusDict,
    // enum: () => {
    //   return getDictionary({ dictCode: "notice_status" });
    // },
    fieldNames: { label: "itemValue", value: "itemKey" },
    search: { el: "tree-select", props: { filterable: true } }
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    align: "left",
    enum: statusAuditDict,
    search: { el: "select" }
  },
  {
    prop: "creatorName",
    align: "left",
    label: "创建人员",
    search: { el: "input" }
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY-MM-DD",
        valueFormat: "YYYY-MM-DD"
      }
    },
    width: 180,
    sortable: true
  },
  {
    prop: "publishTime",
    label: "发布时间",
    align: "left",
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY-MM-DD",
        valueFormat: "YYYY-MM-DD"
      }
    },
    width: 180,
    sortable: true
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 130 }
]);

const cancelPublish = async (params: any) => {
  ElMessageBox.confirm("请确认是否取消发布？", "确认操作").then(async () => {
    await cancel(params.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

const withdraw = async (params: any) => {
  ElMessageBox.confirm("请确认是否删除此项动态配置？", "确认操作").then(async () => {
    await del(params.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

const revoke = async (params: any) => {
  ElMessageBox.confirm("请确认是否撤回此项动态配置？", "确认操作").then(async () => {
    await messageRevoke(params.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

// messageReviewStorage.clearExpired();
let type = "edit";
const toEdit = (data?: any) => {
  //编辑消息
  if (data) {
    messageStorage.set(data.id, data);
    router.push({
      path: "/operation/messageManage/messageRelease",
      query: {
        id: data.id,
        type: type
      }
    });
    //新增消息
  } else {
    router.push({
      path: "/operation/messageManage/messageRelease"
      // query: { type: "add" }
    });
  }
};
const toView = (data: any) => {
  messageStorage.set(data.id, data);
  router.push({
    path: "/operation/messageManage/messageRelease",
    query: {
      id: data.id
      // type: "view"
    }
  });
};

watch(
  () => route.path,
  (path: string) => {
    if (path === "/operation/messageManage") {
      proTable.value?.getTableList();
    }
  }
);
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
</style>
