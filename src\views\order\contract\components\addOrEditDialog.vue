<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="title" width="70%" @close="onCancel">
      <el-form name="test" ref="ruleFormRef" :model="dialogForm" :rules="rules" :label-width="120" label-position="right">
        <el-form-item label="合同名称" prop="contractName">
          <el-input v-model="dialogForm.contractName" maxlength="50" :disabled="isEdit || isDisable" clearable></el-input>
        </el-form-item>

        <el-form-item label="供应商名称" prop="supplierName">
          <el-select
            v-model="dialogForm.supplierName"
            filterable
            remote
            :remote-method="getSupplier"
            :disabled="isDisable"
            clearable
            :loading="selectLoading"
            placeholder="请输入名称搜索"
            @change="handleSupplier"
          >
            <el-option v-for="item in supplierList" :key="item.name" :label="item.name" :value="item.name" />
          </el-select>
        </el-form-item>

        <el-form-item label="供应商编码" prop="supplierId">
          <el-input v-model="dialogForm.supplierId" maxlength="20" disabled clearable></el-input>
        </el-form-item>

        <el-form-item label="客户名称" prop="companyName">
          <el-select
            v-model="dialogForm.companyName"
            filterable
            remote
            :remote-method="getCompanyList"
            :disabled="isDisable"
            clearable
            :loading="selectLoading"
            placeholder="请输入名称搜索"
            @change="handleCompany"
          >
            <el-option v-for="item in companyList" :key="item.companyName" :label="item.companyName" :value="item.companyName" />
          </el-select>
        </el-form-item>

        <el-form-item label="客户编码" prop="customerId">
          <el-input v-model="dialogForm.customerId" maxlength="20" disabled clearable></el-input>
        </el-form-item>
        <div class="data-range">
          <el-form-item class="flex-item" label="合同有效期" prop="startDate">
            <el-date-picker
              type="date"
              v-model="dialogForm.startDate"
              placeholder="有效期开始时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="isDisable"
            />
          </el-form-item>
          <span class="flex-mid">至</span>
          <el-form-item class="flex-sec" prop="validDate">
            <el-date-picker
              type="date"
              v-model="dialogForm.validDate"
              placeholder="有效期结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="isDisable"
            />
          </el-form-item>
        </div>

        <el-form-item label="合同金额(万元)" prop="price">
          <el-input v-model="dialogForm.price" type="number" :disabled="isDisable" clearable></el-input>
        </el-form-item>

        <el-form-item label="合同描述" prop="detail">
          <el-input v-model="dialogForm.detail" type="textarea" maxlength="100" :disabled="isDisable"></el-input>
        </el-form-item>

        <el-form-item label="合同附件" prop="contractFileId">
          <div class="el-link file-link" @click="downloadAttach(dialogForm.contractFileId)">
            {{ dialogForm.contractFileName }}
          </div>
          <uploadFile
            class="upload"
            :file-type="'.doc,.xls,.docx,.xlsx,.rar,.zip,.jpg,.gif,.jpeg,.png,.pdf'"
            :limit="1"
            :file-size="5"
            :show-file-list="true"
            :drag="false"
            :width="'auto'"
            :height="'auto'"
            @upload-success="onUploadSuccess"
          >
            <el-button type="primary" ref="uploadBtnRef">上传附件</el-button>
            <template #tip>
              <div class="upload-tips">
                文件类型：.doc .xls .docx .xlsx .rar .zip .jpg .gif .jpeg .png .pdf，支持1个附件上传，文件大小不超过5M
              </div>
            </template>
          </uploadFile>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { editType, editTypeChinese } from "@/enums/dialogTypeEnum";
import { computed, ref, reactive, watch, onMounted } from "vue";
import { FormRules } from "element-plus";
import type { FormInstance } from "element-plus";
import { getCustomerList } from "@/api/modules/member";
import { addContract, modContract } from "@/api/modules/order";
import uploadFile from "@/components/Upload/Files.vue";
import { useDownload } from "@/hooks/useDownload";
import { downloadAttachFile } from "@/api/modules/transactionVolumeFiling";
import { pmsProviderNoPageList } from "@/api/modules/benefitManage";

const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: editType.add
  },
  params: {
    type: Object,
    default: () => {}
  }
});

const ruleFormRef = ref();
const isShowSelectDepart = ref(false);

const dialogForm = ref<any>({
  id: "", //	合同编码		false
  contractName: "", //	合同名称		true
  companyName: "", //	公司名称		true
  customerId: "", //	客户编码		false
  contractFileName: "", //	合同附件id，如果有多份则用逗号分隔 “ ,”		false
  contractFileId: "", //	合同附件id，如果有多份则用逗号分隔 “ ,”		false
  detail: "", //	合同描述		false
  price: "", //	合同金额(万元)		false
  supplierId: "", //	供应商编码		true
  supplierName: "", //	供应商名称		true
  startDate: "", //	生效时间		false
  validDate: "" //	失效时间
});

const title = computed(() => `${editTypeChinese[props.type] || editTypeChinese[props.type]}合同`);
const isDisable = computed(() => !editTypeChinese[props.type]);
const isEdit = computed(() => props.type === editType.edit);

const emits = defineEmits(["update:visible", "cancel", "confirm"]);

watch(
  () => props.params,
  () => {
    if (props.type !== editType.add) {
      dialogForm.value = {
        id: props.params.id,
        contractName: props.params.contractName,
        companyName: props.params.companyName,
        customerId: props.params.customerId,
        contractFileName: props.params.contractFileName,
        contractFileId: props.params.contractFileId,
        detail: props.params.detail,
        price: props.params.price,
        supplierId: props.params.supplierId,
        supplierName: props.params.supplierName,
        startDate: props.params.startDate,
        validDate: props.params.validDate
      };
    } else {
      dialogForm.value.id = "";
    }
  },
  {
    immediate: true
  }
);

// 获取客户信息
const selectLoading = ref<boolean>(false);
const supplierList = ref<any>([]);

const getSupplier = (query: string) => {
  selectLoading.value = true;
  let params = {};
  if (query) {
    params = {
      name: query
    };
  }
  pmsProviderNoPageList(params)
    .then(res => {
      if (res.code === 200) {
        supplierList.value = res.data;
      }
    })
    .finally(() => {
      selectLoading.value = false;
    });
};

const handleSupplier = (val: any) => {
  if (val) {
    const findItem = supplierList.value.find(item => item.name === val);
    if (findItem.id) {
      dialogForm.value.supplierId = findItem.id;
    }
  }
};

const companyList = ref<any>([]);
const getCompanyList = (query: string) => {
  selectLoading.value = true;
  let params = {};
  if (query) {
    params = {
      contactsName: query
    };
  }
  getCustomerList(params)
    .then(res => {
      if (res.code === 200) {
        companyList.value = res.data;
      }
    })
    .finally(() => {
      selectLoading.value = false;
    });
};

const handleCompany = (val: any) => {
  if (val) {
    const findItem = companyList.value.find(item => item.companyName === val);
    if (findItem.id) {
      dialogForm.value.customerId = findItem.id;
    }
  }
};

const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
  isShowSelectDepart.value = false;
};

const rules = reactive<FormRules>({
  contractName: [{ trigger: "blur", required: true, message: "合同名称不能为空" }],
  supplierId: [{ trigger: "blur", required: true, message: "供应商名称不能为空" }],
  customerId: [{ trigger: "blur", required: true, message: "客户名称不能为空" }],
  price: [
    { trigger: "blur", required: true, message: "合同金额不能为空" },
    {
      validator: (_, value, callback) => {
        const reg = /(^[1-9](\d+)?(\.\d{1,2})?$)|(^0$)|(^\d\.\d{1,2}$)/;

        if (!reg.test(value)) {
          callback("金额格式不正确");
        } else {
          callback();
        }
      }
    }
  ],
  startDate: [{ trigger: "blur", required: true, message: "合同有效期开始时间不能为空" }],
  validDate: [{ trigger: "blur", required: true, message: "合同有效期结束时间不能为空" }]
});

// 监听弹窗
const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    dialogForm.value = {
      id: "",
      contractName: "",
      companyName: "",
      customerId: "",
      contractFileId: "",
      detail: "",
      price: "",
      supplierId: "",
      supplierName: "",
      startDate: "",
      validDate: ""
    };
    emits("update:visible", value);
  }
});

// 上传成功
const onUploadSuccess = (res: any) => {
  dialogForm.value.contractFileId = res?.id as string;
};

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: addContract,
        [editType["edit"]]: modContract
      };
      const formVal = {
        contractName: dialogForm.value.contractName,
        companyName: dialogForm.value.companyName,
        customerId: dialogForm.value.customerId,
        contractFileId: dialogForm.value.contractFileId,
        detail: dialogForm.value.detail,
        price: dialogForm.value.price,
        supplierId: dialogForm.value.supplierId,
        supplierName: dialogForm.value.supplierName,
        startDate: dialogForm.value.startDate,
        validDate: dialogForm.value.validDate
      };
      const requestParamsObject = {
        [editType["add"]]: () => {
          return {
            ...formVal
          };
        },
        [editType["edit"]]: () => {
          return {
            id: props.params.id,
            ...formVal
          };
        }
      };

      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        emits("confirm");

        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};

// 通过ID下载附件
const downloadAttach = (id: string) => {
  const queryData = {
    id
  };
  useDownload(downloadAttachFile, queryData);
};

onMounted(() => {
  getSupplier("");
  getCompanyList("");
});
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
.horizontal.el-form-item {
  display: flex;
  align-items: center;
  :deep(.el-form-item__label) {
    margin-bottom: 0;
  }
}
.detail-title {
  margin-bottom: 18px;
  font-size: 16px;
  font-weight: 500;
  color: #3a3a3d;
  &.mt8 {
    margin-top: 8;
  }
}
.select-new-depart {
  margin-left: 10px;
  cursor: pointer;
}
.data-range {
  display: flex;
  align-items: flex-start;
  .flex-item {
    flex: 1;
  }
  .flex-mid {
    margin: 0 16px;
    line-height: 32px;
  }
  .flex-sec {
    flex: 1;
    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }
}
.file-link {
  display: block;
}
</style>
