<template>
  <div class="table-box">
    <ProTable ref="proTable" :request-api="getTableList" :columns="columns" :tool-button="false">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => toEdit()"> 新增 </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <el-tooltip effect="dark" content="编辑" placement="top">
            <i class="iconfont2 icon-a-lineicon_edit opera-icon" @click="() => toEdit(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip v-if="!scope.row.enabled" effect="dark" content="删除" placement="top">
            <i class="iconfont2 icon-lajitong opera-icon" @click="del(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <EditSortDialog
      v-if="productAreaItem && showEditDialog"
      v-model:show-dialog="showEditDialog"
      :sort="productAreaItem.sort"
      @save="saveSort"
    />
  </div>
</template>

<script setup lang="tsx" name="productArea">
import { ref, watch } from "vue";
import { productAreaStorage } from "@/utils/storage/edit";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import EditSortDialog from "@/components/EditSortDialog/index.vue";
import { type ProTableInstance, type ColumnProps, type HeaderRenderScope } from "@/components/ProTable/interface/index";
import { Plus } from "@element-plus/icons-vue";
import { getPageList, deleteItem, change } from "@/api/modules/business/productArea";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const route = useRoute();

const router = useRouter();

const proTable = ref<ProTableInstance>();
const showEditDialog = ref<boolean>(false);
const productAreaItem = ref<any>({});

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const { zoneName, current, size } = search;
  return getPageList({
    search: zoneName,
    current,
    size,
    descs: "createTime"
  });
};

// 表格配置项
const columns: ColumnProps[] = [
  {
    prop: "zoneName",
    label: "专区名称",
    align: "left",
    search: { el: "input", props: { maxLength: 10 } }
  },
  {
    prop: "productIdList",
    align: "left",
    label: "商品数量",
    width: 120,
    sortable: true,
    render: (scope: any) => {
      return scope.row.productIdList && scope.row.productIdList.length;
    }
  },
  {
    prop: "enabled",
    align: "left",
    label: "状态",
    render: (scope: any) => {
      return (
        <el-switch
          model-value={scope.row.enabled}
          active-text={scope.row.enabled ? "启用" : "禁用"}
          active-value={true}
          inactive-value={false}
          onClick={() => changeEnabled(scope.row)}
        />
      );
    }
  },
  {
    prop: "sort",
    align: "left",
    label: "排序",
    render: (scope: any) => {
      return (
        <span class={"sort-btn"} onClick={() => setShowEditDialog(scope.row)}>
          {scope.row.sort}
        </span>
      );
    }
  },
  {
    prop: "creatorName",
    align: "left",
    label: "创建人员"
  },
  {
    prop: "createTime",
    align: "left",
    label: "创建时间",
    width: 180,
    sortable: true
  },
  { prop: "operation", align: "left", label: "操作", fixed: "right", width: 95 }
];

const del = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此项产品？", "确认操作").then(async () => {
    await deleteItem(data.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

const setShowEditDialog = (data: any) => {
  productAreaItem.value = data;
  showEditDialog.value = true;
};
const saveSort = async (sort: number) => {
  await change({ ...productAreaItem.value, sort: sort });
  ElMessage.success("操作成功！");
  proTable.value?.getTableList();
};

const changeEnabled = async (data: any) => {
  ElMessageBox.confirm("是否修改启用状态？", "确认操作").then(async () => {
    await change({ ...data, enabled: !data.enabled });
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

watch(
  () => route.path,
  (path: string) => {
    if (path === "/operation/productArea") {
      proTable.value?.getTableList();
    }
  }
);

productAreaStorage.clearExpired();
const toEdit = (data?: any) => {
  if (data) {
    productAreaStorage.set(data.id, data);
    router.push({
      path: "/operation/productArea/edit",
      query: {
        id: data.id
      }
    });
  } else {
    router.push({
      path: "/operation/productArea/edit"
    });
  }
};
</script>
<style lang="scss">
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
