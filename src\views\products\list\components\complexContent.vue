<template>
  <div class="complex-content-box">
    <div class="name-box">
      <el-button type="primary" class="add-button" @click="addItem" :disabled="viewControl">添加</el-button>
      <span class="tips"> <slot name="nameTips"></slot></span>
    </div>
    <div class="content-item" v-for="(item, index) in dataList as any[]" :key="index + 'complex'">
      <div class="item-title">
        <div class="name">{{ item.title || prefixion + "名称：" }}</div>
        <el-button class="icon" link :icon="Delete" @click="deleteItem(index)" :disabled="viewControl"></el-button>
      </div>

      <el-form ref="ruleFormRef" class="item-form" :model="item" :rules="rules" label-width="120px">
        <el-form-item :label="`${prefixion}名称`" prop="title">
          <el-input v-model="item.title" maxlength="10" :disabled="viewControl" clearable show-word-limit />
        </el-form-item>
        <el-form-item :label="`${prefixion}描述`" prop="description">
          <el-input
            v-model="item.description"
            type="textarea"
            :disabled="viewControl"
            :maxlength="limitNum"
            clearable
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="展示图片" prop="picUrl">
          <UploadImg v-model:image-url="item.picUrl" width="135px" height="135px" :file-size="fileSize" :disabled="viewControl">
            <template #tip>{{ imgTips }}</template>
          </UploadImg>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from "vue";
import { Delete } from "@element-plus/icons-vue";
// import { ElMessage } from "element-plus";
import UploadImg from "@/components/Upload/Img.vue";
import { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const props = defineProps({
  dataList: {
    type: Array,
    default: () => []
  },
  prefixion: {
    type: String,
    default: ""
  },
  imgTips: {
    type: String,
    default: "支持图片格式jpg/jpeg/png"
  },
  fileSize: {
    type: Number,
    default: 2
  },
  viewControl: {
    type: Boolean,
    default: false // 查看详情就禁用新增和修改,由父组件的viewStatus传来
  },
  limitNum: {
    type: Number,
    default: 100
  }
});
const emits = defineEmits(["addItem", "deleteItem"]);

const rules = {
  title: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  description: [{ required: true, message: "描述不能为空", trigger: "blur" }],
  picUrl: [{ required: true, message: "请上传图片", trigger: "blur" }]
};

const addItem = () => {
  emits("addItem");
};
//删除元素
const deleteItem = (index: number) => {
  // if (contentDataList.value.length <= 1) {
  //   ElMessage.error("至少有一项内容");
  // } else {
  //   emits("deleteItem", index);
  // }
  emits("deleteItem", index);
};
const ruleValidatePromise = () => {
  // return ruleFormRef.value;
  let promiseArr = [] as any;
  ruleFormRef.value?.forEach((itemref: any) => {
    promiseArr.push(
      new Promise((resolve, reject) => {
        itemref.validate((valid: any, fields: any) => {
          if (valid) {
            resolve("校验成功");
          } else {
            let obj = Object.keys(fields);
            itemref.scrollToField(obj[0]);
            reject("校验失败");
          }
        });
      })
    );
  });
  return promiseArr;
};
defineExpose({
  ruleValidatePromise
});
</script>

<style lang="scss" scoped>
.complex-content-box {
  width: 100%;
  .name-box {
    display: flex;
    width: fit-content;
    margin-bottom: 16px;
    .name-input {
      margin-right: 8px;
    }
    .add-button {
      margin-right: 8px;
    }
    .tips {
      margin-left: 15px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
    }
  }
  .content-item {
    margin-bottom: 16px;
    border: 1px solid #c4c6cc;
    .item-title {
      display: flex;
      justify-content: space-between;
      padding: 4px 12px;
      background: #c4c6cc;
      .icon:hover {
        color: #3a3a3d;
      }
    }
  }
  .item-form {
    padding: 24px;
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .nowrap {
    white-space: nowrap;
  }
}
</style>
