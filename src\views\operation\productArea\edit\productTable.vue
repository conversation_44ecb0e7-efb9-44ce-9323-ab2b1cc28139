<template>
  <el-table class="product-table" row-key="id" :data="productStore.productDetailList" style="width: 100%">
    <el-table-column prop="productName" label="产品名称" />
    <el-table-column prop="categoryName" label="产品分类" />
    <el-table-column prop="provider" label="产品服务商" />
    <el-table-column label="操作">
      <template #default="scope">
        <el-button link type="danger" size="small" @click="() => deletePro(scope.$index)">移除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>
<script lang="ts" setup>
import { defineProps, PropType, defineEmits, onMounted, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import Sortable from "sortablejs";
import { change, getProductDetailList } from "@/api/modules/business/productArea";
import { useProductAreaStore } from "@/stores/modules/productArea";

const productStore = useProductAreaStore();

const props = defineProps({
  productIdList: {
    required: true,
    type: Object as PropType<Array<any>>
  }
});
const { productIdList } = props;

if (productIdList && productIdList.length) {
  getProductDetailList(productIdList).then(data => {
    productStore.productDetailList = data.data as any;
  });
} else {
  productStore.productDetailList = [];
}

onMounted(() => {
  tabsDrop();
});

const emits = defineEmits(["update:productIdList"]);
// tabs 拖拽排序
const tabsDrop = () => {
  const el = document.querySelectorAll(".product-table .el-table__body > tbody")[0];
  Sortable.create(el as HTMLElement, {
    draggable: ".el-table__row",
    animation: 100,
    onEnd({ newIndex, oldIndex }) {
      const list = [...productStore.productDetailList];
      const currRow = list.splice(oldIndex as number, 1)[0];
      list.splice(newIndex as number, 0, currRow);
      productStore.$patch(state => {
        state.productDetailList = [...list];
      });
      emits(
        "update:productIdList",
        list.map((item: any) => item.id)
      );
    }
  });
};

const deletePro = (index: number) => {
  ElMessageBox.confirm("是否删除该项产品？", "确认操作").then(async () => {
    const list = [...productIdList];
    emits("update:productIdList", list.splice(index, 1));
    productStore.productDetailList.splice(index, 1);
  });
};
</script>
<style scoped lang="scss">
.product-table {
  width: 400px;
}
</style>
