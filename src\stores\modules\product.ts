import { defineStore } from "pinia";
import piniaPersistConfig from "@/config/piniaPersist";
import { getProviderList, productDetail, getProductAuditDetail } from "@/api/modules/product";
import { solutionDetail } from "@/api/modules/solution";

import { ProductState } from "@/stores/interface";

export const useProductStore = defineStore({
  id: "cnud-product",
  state: (): ProductState => ({
    productDetailObj: {}, // 产品详情对象
    sendProductDetailParam: {}, // 新增/编辑产品传参对象
    providerList: [], // 产品服务商
    solutionDetailObj: {}, // 解决方案详情对象
    productAuditDetailObj: {} // 产品审核详情对象
  }),
  actions: {
    async getProductDetail(params: { id: string }) {
      this.productDetailObj[params.id] = {}; // 先清空对象，防止接口报错拿上一个数据
      return new Promise((resolve, reject) => {
        productDetail(params).then(res => {
          this.productDetailObj[params.id] = res.data as object;
          resolve(res);
        });
      });
    },
    // Set sendProductDetailParam
    setProductDetail(id: string, params: any = {}) {
      if (this.sendProductDetailParam[id]) {
        // 因为新增/编辑产品是按步骤进行，不能直接覆盖sendProductDetailPara
        Object.assign(this.sendProductDetailParam[id], params);
      } else {
        Object.assign(this.sendProductDetailParam, { [id]: params });
      }
    },
    resetSendProductDetailParam(id = "add_product") {
      this.sendProductDetailParam[id] = {};
    },
    // 产品服务商列表
    async getProviderList(params: any = {}) {
      const { data }: any = await getProviderList(params);
      data.forEach((item: any = {}) => {
        item.value = item.id;
        item.label = item.name;
      });
      this.providerList = data;
    },
    async getSolutionDetail(params: any = {}) {
      this.solutionDetailObj = {}; // 先清空对象，防止接口报错拿上一个数据
      const { data }: any = await solutionDetail(params);
      this.solutionDetailObj = data;
    },
    async getProductAuditDetail(params: any) {
      this.productAuditDetailObj = {}; // 先清空对象，防止接口报错拿上一个数据
      return new Promise((resolve, reject) => {
        getProductAuditDetail(params).then(res => {
          this.productAuditDetailObj = res.data as object;
          this.setProductDetail(params.id, res.data?.newValue);
          resolve(res);
        });
      });
    }
  },
  persist: piniaPersistConfig("cnud-product")
});
