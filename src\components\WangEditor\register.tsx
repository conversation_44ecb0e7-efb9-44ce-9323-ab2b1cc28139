import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, t, <PERSON><PERSON> } from "@wangeditor/editor";
import { Link } from "@element-plus/icons-vue";
import { defineComponent } from "vue";
export type LinkElement = {
  type: "link";
  url: string;
  target?: string;
  children: Text[];
};
type EmptyText = {
  text: "";
};

export type ImageStyle = {
  width?: string;
  height?: string;
};

export type ImageElement = {
  type: "image";
  src: string;
  alt?: string;
  href?: string;
  style?: ImageStyle;
  children: EmptyText[];
};

class CustomViewLink implements IButtonMenu {
  readonly title = "查看";

  readonly tag = "button";

  private getSelectedLinkElem(editor: IDomEditor): LinkElement | ImageElement | null {
    const node = DomEditor.getSelectedNodeByType(editor, "link") || DomEditor.getSelectedNodeByType(editor, "image");
    if (node == null) return null;
    return node as unknown as LinkElement | ImageElement;
  }

  getValue(editor: IDomEditor): string | boolean {
    const linkElem = this.getSelectedLinkElem(editor);

    if (linkElem) {
      return (linkElem as LinkElement).url || (linkElem as ImageElement).href || "";
    }

    return "";
  }

  isActive(editor: IDomEditor): boolean {
    // 无需 active
    return false;
  }

  isDisabled(editor: IDomEditor): boolean {
    if (editor.selection == null) return true;

    const linkElem = this.getSelectedLinkElem(editor);
    if (linkElem == null) {
      // 选区未处于 link node ，则禁用
      return true;
    }
    return false;
  }

  exec(editor: IDomEditor, value: string | boolean) {
    if (this.isDisabled(editor)) return;

    if (!value || typeof value !== "string") {
      throw new Error(`View link failed, link url is '${value}'`);
    }

    editor.emit("openLink-key", value);

    // 查看链接
    // window.open(value, "_blank");
  }
}

const OpenLink = {
  key: "openLink",
  factory() {
    return new CustomViewLink(); // 把 `YourMenuClass` 替换为你菜单的 class
  }
};

Boot.registerMenu(OpenLink);
