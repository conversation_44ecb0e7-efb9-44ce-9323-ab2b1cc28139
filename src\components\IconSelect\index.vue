<!-- <AUTHOR> -->
<template>
  <div class="icon-body">
    <el-input v-model="name" class="icon-search" clearable placeholder="请输入图标名称" @clear="filterIcons" @input="filterIcons">
      <template #prefix>
        <el-icon><Search /></el-icon>
      </template>
    </el-input>
    <div class="icon-list">
      <div class="list-container">
        <div v-for="(item, index) in iconList" class="icon-item-wrapper" :key="index" @click="selectedIcon(item)">
          <div :class="['icon-item', { active: props.activeIcon === item }]">
            <SvgIcon :name="item" :style="{ height: '25px', width: '16px' }" />
            <span>{{ item }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="IconSelect">
import { ref, computed } from "vue";
import { Search } from "@element-plus/icons-vue";
import SvgIcon from "@/components/SvgIcon/index.vue";
import icons from "./requireIcons";
const props = defineProps({
  activeIcon: {
    type: String,
    default: () => ""
  }
});

const emits = defineEmits(["selected"]);
// const icons = [
//   "xianxingdaoyu",
//   "xianxingdiqiu",
//   "xianxingditu",
//   "xianxingfanchuan",
//   "xianxingfeiji",
//   "xianxinglvhangriji",
//   "xianxingtianqiyubao",
//   "xianxingxiangjipaizhao",
//   "xianxingxiarilengyin",
//   "xianxingyoulun",
//   "xianxingxiarilengyin"
// ];
const iconList = ref(icons);
const name = ref("");
// const activeIcon = computed(()=>);
console.log("activeIcon", props.activeIcon);

const filterIcons = () => {
  iconList.value = icons;
  if (name.value) {
    iconList.value = iconList.value.filter(item => item.includes(name.value));
  }
};
const selectedIcon = (name: string) => {
  emits("selected", name);
  document.body.click();
};

const reset = () => {
  name.value = "";
  iconList.value = icons;
};
defineExpose({
  reset
});
// export default {
//   name: 'IconSelect',
//   props: {
//     activeIcon: {
//       type: String
//     }
//   },
//   data() {
//     return {
//       name: '',
//       iconList: icons
//     }
//   },
//   methods: {
//     filterIcons() {
//       this.iconList = icons
//       if (this.name) {
//         this.iconList = this.iconList.filter(item => item.includes(this.name))
//       }
//     },
//     selectedIcon(name) {
//       this.$emit('selected', name)
//       document.body.click()
//     },
//     reset() {
//       this.name = ''
//       this.iconList = icons
//     }
//   }
// }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.icon-body {
  width: 100%;
  padding: 10px;
  .icon-search {
    position: relative;
    margin-bottom: 5px;
  }
  .icon-list {
    height: 200px;
    overflow: auto;
    .list-container {
      display: flex;
      flex-wrap: wrap;
      .icon-item-wrapper {
        display: flex;
        width: calc(100% / 3);
        height: 25px;
        line-height: 25px;
        cursor: pointer;
        .icon-item {
          display: flex;
          max-width: 100%;
          height: 100%;
          padding: 0 5px;
          &:hover {
            background: #ececec;
            border-radius: 5px;
          }
          .icon {
            flex-shrink: 0;
          }
          span {
            display: inline-block;
            padding-left: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: -0.15em;
            fill: currentColor;
          }
        }
        .icon-item.active {
          background: #ececec;
          border-radius: 5px;
        }
      }
    }
  }
}
</style>
