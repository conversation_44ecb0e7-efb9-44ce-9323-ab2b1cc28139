<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="title" width="70%" @close="onCancel">
      <el-form name="test" ref="ruleFormRef" :model="dialogForm" :rules="rules" :label-width="100" label-position="right">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="dialogForm.companyName" maxlength="50" :disabled="isEdit || isDisable" clearable></el-input>
        </el-form-item>

        <el-form-item label="关联用户" prop="userId">
          <div class="user-box">
            <p class="user-name" v-if="dialogForm.account">{{ dialogForm.account }}</p>
            <el-button type="primary" @click="showUserSelect">选择用户</el-button>
          </div>
        </el-form-item>

        <el-form-item label="联系人姓名" prop="contactsName">
          <el-input v-model="dialogForm.contactsName" maxlength="20" :disabled="isEdit || isDisable" clearable></el-input>
        </el-form-item>

        <el-form-item label="联系人电话" prop="contactsTel">
          <el-input v-model="dialogForm.contactsTel" maxlength="11" :disabled="isDisable" clearable></el-input>
        </el-form-item>

        <el-form-item label="联系人微信" prop="contactsVx">
          <el-input v-model="dialogForm.contactsVx" maxlength="50" :disabled="isDisable" clearable></el-input>
        </el-form-item>

        <el-form-item label="联系人邮箱" prop="contactsEmail">
          <el-input v-model="dialogForm.contactsEmail" maxlength="50" :disabled="isDisable" clearable></el-input>
        </el-form-item>

        <el-form-item label="联系人职务" prop="contactsPosition">
          <el-input v-model="dialogForm.contactsPosition" maxlength="20" :disabled="isDisable" clearable></el-input>
        </el-form-item>

        <el-form-item label="备注" prop="memo">
          <el-input v-model="dialogForm.memo" type="textarea" maxlength="100" :disabled="isDisable"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <select-user-dialog ref="selectUserDialogRef" @confirm="onSelectConfirm"></select-user-dialog>
  </div>
</template>

<script setup lang="ts">
import { editType, editTypeChinese } from "@/enums/dialogTypeEnum";
import { computed, ref, reactive, watch, onMounted } from "vue";
import type { FormInstance } from "element-plus";
import { FormRules } from "element-plus";
import { getAllUserList } from "@/api/modules/system";
import { addMember, updateMember } from "@/api/modules/member";
import { ResPage } from "@/api/interface/index";
import { checkPhoneNumber } from "@/utils/eleValidate";
import SelectUserDialog from "./selectUserDialog.vue";

const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: editType.add
  },
  params: {
    type: Object,
    default: () => {}
  }
});

const selectUserDialogRef = ref();

const ruleFormRef = ref();
const isShowSelectDepart = ref(false);

const dialogForm = ref<any>({
  id: "",
  userId: "",
  account: "",
  companyName: "",
  contactsEmail: "",
  contactsName: "",
  contactsPosition: "",
  contactsTel: "",
  contactsVx: "",
  memo: ""
});

const title = computed(() => `${editTypeChinese[props.type] || editTypeChinese[props.type]}会员`);
const isDisable = computed(() => !editTypeChinese[props.type]);
const isEdit = computed(() => props.type === editType.edit);

const emits = defineEmits(["update:visible", "cancel", "confirm"]);

watch(
  () => props.params,
  () => {
    if (props.type !== editType.add) {
      dialogForm.value = {
        id: props.params.id,
        userId: props.params.userId,
        account: props.params.account,
        companyName: props.params.companyName,
        contactsEmail: props.params.contactsEmail,
        contactsName: props.params.contactsName,
        contactsPosition: props.params.contactsPosition,
        contactsTel: props.params.contactsTel,
        contactsVx: props.params.contactsVx,
        memo: props.params.memo
      };
    } else {
      dialogForm.value.id = "";
    }
  },
  {
    immediate: true
  }
);

const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
  isShowSelectDepart.value = false;
};

const rules = reactive<FormRules>({
  companyName: [{ trigger: "blur", required: true, message: "公司名称不能为空" }],
  userId: [{ trigger: "blur", required: true, message: "关联用户不能为空" }],
  contactsEmail: [{ type: "email", message: "请输入正确的邮箱地址" }],
  contactsTel: [
    {
      validator: (_: any, value: any, callback: any) => {
        const regexp = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/;
        if (value && !regexp.test(value)) {
          callback(new Error("请输入正确的手机号码"));
        } else {
          return callback();
        }
      }
    }
  ],
  contactsName: [
    {
      validator: (_: any, value: any, callback: any) => {
        const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9_\s]+$/;
        if (value && !reg.test(value)) {
          callback(new Error("不允许输入特殊字符，且限制输入1-20位字符"));
        }
        callback();
      }
    }
  ]
});
// 监听弹窗
const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    dialogForm.value = {
      id: "",
      userId: "",
      companyName: "",
      contactsEmail: "",
      contactsName: "",
      contactsPosition: "",
      contactsTel: "",
      contactsVx: "",
      memo: ""
    };
    emits("update:visible", value);
  }
});

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: addMember,
        [editType["edit"]]: updateMember
      };
      const formVal = {
        userId: dialogForm.value.userId,
        companyName: dialogForm.value.companyName,
        contactsEmail: dialogForm.value.contactsEmail,
        contactsName: dialogForm.value.contactsName,
        contactsPosition: dialogForm.value.contactsPosition,
        contactsTel: dialogForm.value.contactsTel,
        contactsVx: dialogForm.value.contactsVx,
        memo: dialogForm.value.memo
      };
      const requestParamsObject = {
        [editType["add"]]: () => {
          return {
            ...formVal
          };
        },
        [editType["edit"]]: () => {
          return {
            id: props.params.id,
            ...formVal
          };
        }
      };

      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        emits("confirm");

        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};

const showUserSelect = () => {
  selectUserDialogRef.value?.show();
};

const onSelectConfirm = ({ userId, account }) => {
  dialogForm.value.userId = userId;
  dialogForm.value.account = account;
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
.horizontal.el-form-item {
  display: flex;
  align-items: center;
  :deep(.el-form-item__label) {
    margin-bottom: 0;
  }
}
.detail-title {
  margin-bottom: 18px;
  font-size: 16px;
  font-weight: 500;
  color: #3a3a3d;
  &.mt8 {
    margin-top: 8;
  }
}
.select-new-depart {
  margin-left: 10px;
  cursor: pointer;
}
.user-box {
  display: flex;
  .user-name {
    padding: 0;
    margin: 0 20px 0 0;
  }
}
</style>
