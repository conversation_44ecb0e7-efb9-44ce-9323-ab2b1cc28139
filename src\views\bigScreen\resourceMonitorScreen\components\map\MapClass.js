import * as echarts from "echarts";
import { getMapJson, getPrivincePoint } from "@/api/modules/map";
import proviceNameObject from "./proviceNameObject";
import mapPointBG from "@/assets/images/screen/map_point.png";
export class BigScreenMap {
  // 现在地图子集的名称
  mapNameData = {};
  // 需要展示的省会城市
  needShowProvincialCapitals = [];
  // 区域的数据
  pointData = {};
  // 地市需要展示的信息
  provicePoint = [];
  // 动态选中的区域名称
  selectName = "";
  // 底图
  baseMapName = "";
  // 数据全面的地图
  mainMapName = "";
  myChart = null;
  //地图下载的JSON缓存
  mapCache = {};
  toolTipHtmlFun = item => {
    let proviceName = item.name.replace(/省|市|自治区|特别行政区|壮族|回族|维吾尔/g, "");
    if (proviceName.indexOf("新疆") != -1) {
      proviceName = "新疆";
    }
    if (proviceName.indexOf("宁夏") != -1) {
      proviceName = "宁夏";
    }
    if (this.pointData[proviceName]) {
      return `<div class="map-info-box">
            <p class="title"> ${proviceName}</p>
            <p class="item"><span>算力余量：${
              this.pointData[proviceName] ? this.pointData[proviceName].computePowerFree : "--"
            }</span></p>
            <p class="item"><span>CPU余量：${
              this.pointData[proviceName] ? this.pointData[proviceName].cpuFreeRate : "--"
            }</span></p>
            <p class="item"><span>内存余量：${
              this.pointData[proviceName] ? this.pointData[proviceName].memoryFreeRate : "--"
            }</span></p>
            <p class="item"><span>存储余量：${
              this.pointData[proviceName] ? this.pointData[proviceName].capacityFreeRate : "--"
            }</span></p>
          </div>`;
    }
    return "";
  };
  constructor({ id, clickCallback, customToolTipFun, mapChange }) {
    this.myChart = echarts.init(document.getElementById(id)); // 初始echarts
    customToolTipFun && typeof customToolTipFun === "function" ? (this.toolTipHtmlFun = customToolTipFun) : "";
    mapChange && typeof mapChange === "function" ? (this.mapCache = mapChange) : "";
    this.toMap({
      baseMapName: "china",
      mainMapName: "empchina"
    });
    this.myChart.on("click", params => {
      // 处理点击事件
      try {
        if (params.name) {
          clickCallback && typeof clickCallback === "function" && clickCallback(params);
          this.toMap({
            baseMapName: params.name,
            mainMapName: params.name
          });
        }
      } catch (error) {}
    });
    this.dynamicSelectOrg();
  }
  //设置参数
  createOption() {
    // 省会城市Coordinate 隐射表
    const provinceCoordinates = {
      安徽: [117.194778, 31.86577],
      北京: [116.403694, 39.949459],
      重庆: [106.553263, 29.556681],
      福建: [119.292069, 26.144144],
      甘肃: [103.856737, 36.094212],
      广东: [113.239359, 23.185545],
      广西: [108.345678, 22.861984],
      贵州: [106.616332, 26.707352],
      海南: [110.350983, 19.968035],
      河北: [114.508772, 38.083783],
      河南: [113.644099, 34.769161],
      黑龙江: [126.522207, 45.801617],
      湖北: [114.361594, 30.601078],
      湖南: [112.926605, 28.217167],
      吉林: [125.326383, 43.797768],
      江苏: [118.832137, 32.038322],
      江西: [115.851775, 28.672488],
      辽宁: [123.486653, 41.682522],
      内蒙古: [111.785972, 40.849642],
      宁夏: [106.257585, 38.482579],
      青海: [101.851432, 36.622494],
      山东: [117.194778, 36.652148],
      山西: [112.595453, 37.858034],
      陕西: [109.026378, 34.350591],
      上海: [121.518142, 31.211845],
      四川: [104.132697, 30.561282],
      天津: [117.286764, 39.001295],
      西藏自治区: [91.144205, 29.649484],
      新疆维吾尔自治区: [87.667116, 43.817754],
      云南: [102.881681, 24.866897],
      浙江: [120.211934, 30.274265],
      香港: [114.242011, 22.272474],
      澳门: [113.579709, 22.169692],
      台湾: [121.591732, 25.034634]
    };

    // 需要展示的省会城市数据
    const getProvincialCapitals = names => {
      if (this.provicePoint && this.provicePoint.length > 0) return this.provicePoint;
      let res = [];
      const len = names.length;
      for (let i = 0; i < len; i++) {
        let geoCoord = provinceCoordinates[names[i]];
        if (geoCoord) {
          res.push({
            name: names[i],
            value: geoCoord
          });
        }
      }
      return res;
    };
    const positionY = this.baseMapName === "china" ? 70 : 50;
    const layoutSize = this.baseMapName === "china" ? 120 : 100;
    let option = {
      tooltip: {
        show: true
      },

      geo: [
        // 边框 map
        {
          zlevel: 6,
          id: "mainMapName",
          map: this.mainMapName,
          // 取消一些交互

          // 位置大小调整
          layoutCenter: ["50%", `${positionY}%`], //位置
          layoutSize: `${layoutSize}%`, //大小

          // 框颜色
          itemStyle: {
            color: "transparent",
            opacity: 1,
            borderWidth: 1,
            borderColor: "#73A8FF",
            borderCap: "round",
            shadowBlur: 20,
            shadowColor: "#97BBE4"
          },
          tooltip: {
            trigger: "item",
            show: true,
            enterable: true,
            formatter: this.toolTipHtmlFun,
            // position: [10, 10],
            backgroundColor: "transparent",
            borderColor: "transparent",
            extraCssText: "box-shadow: none;",
            borderWidth: 0,
            padding: 0
          },
          emphasis: {
            itemStyle: {
              areaColor: "#6BA2FE",
              color: "#6BA2FE"
            }
          }
        },
        // 顶层map
        {
          map: this.baseMapName,
          zlevel: 5,
          z: 4,

          // 位置大小调整
          layoutCenter: ["50%", `${positionY}%`], //位置
          layoutSize: `${layoutSize}%`, //大小

          // 调整颜色
          itemStyle: {
            // areaColor:'#264684',
            areaColor: "#032A74",
            borderWidth: 1.2,
            borderCap: "round",
            borderColor: "RGBA(81, 123, 165, 1)"
            // shadowColor: "#fff",
            // shadowBlur: 20,
          }
        },
        // 第二层
        {
          // 下层地图，防3d效果
          zlevel: 5,
          z: 3,

          map: this.mainMapName,
          silent: true,

          // 位置大小调整
          layoutCenter: ["50.07%", `${positionY + 0.1}%`], //位置
          layoutSize: `${layoutSize}%`, //大小
          // 颜色
          itemStyle: {
            borderWidth: 0,
            borderCap: "round",
            areaColor: "#B8FFFF"
          }
        },
        // 第三层
        {
          // 下层地图，防3d效果
          zlevel: 5,
          z: 2,

          map: this.mainMapName,
          silent: true,

          // 位置大小调整
          layoutCenter: ["50.14%", `${positionY + 1}%`], //位置
          layoutSize: `${layoutSize}%`, //大小

          // 颜色
          itemStyle: {
            borderWidth: 0,
            borderCap: "round",
            areaColor: "#B8FFFF"
          }
        },
        // 第四层
        {
          // 下层地图，防3d效果
          zlevel: 5,
          z: 1,

          map: this.mainMapName,

          silent: true,

          // 位置大小调整
          layoutCenter: ["50.21%", `${positionY + 1.3}%`], //位置
          layoutSize: `${layoutSize - 0.5}%`, //大小
          // 颜色
          itemStyle: {
            borderColor: "rgba(0, 0, 0, 0.3)",
            borderWidth: 1,
            borderCap: "round",
            areaColor: "#B8FFFF",
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.6)"
          }
        }
      ],
      series: [
        // 其他省份
        {
          name: "otherProvincialCapital",
          // symbol 形状修改
          type: "scatter",
          symbol: "circle",
          symbolSize: this.baseMapName != "china" ? [34, 49] : 4,
          // //背景
          symbol: this.baseMapName != "china" ? "image://" + mapPointBG : "",
          symbolOffset: [0, "-100%"],
          // 显示层级
          zlevel: 7,
          z: 1,
          coordinateSystem: "geo",
          geoIndex: 1,
          itemStyle: {
            //坐标点颜色
            show: true,
            color: "#fff"
          },
          // label样式
          label: {
            show: this.baseMapName != "china" ? false : true,
            position: "top",
            formatter: "{b}",
            fontSize: 10,
            color: "rgba(255,255,255,0.8)",
            emphasis: {
              show: true
            },
            padding: 3,
            borderWidth: 0,
            borderRadius: 2,
            // borderColor: "rgba(7, 19, 37, 1)",
            fontWeight: 300,
            backgroundColor: "rgba(255, 255, 255, 0.1)" // 字体背景色
          },
          silent: true,
          data: getProvincialCapitals(this.needShowProvincialCapitals)
        }
      ]
    };

    return Object.freeze(option);
  }
  //是否要动态
  dynamicSelectOrg() {
    let timer = null;
    let timeoutTimer = null;
    this.myChart.on("mouseover", params => {
      // mouseover
      clearInterval(timer);
      clearTimeout(timeoutTimer);
      this.unselectedMapOrigin();
      this.selectName = "";
    });
    this.myChart.on("globalout", params => {
      //globalout
      clearInterval(timer);
      clearTimeout(timeoutTimer);
      timeoutTimer = setTimeout(() => {
        timer = setInterval(() => {
          this.selectedMapOrign();
        }, 3000);
      }, 3 * 1000);
    });
    timer = setInterval(() => {
      this.selectedMapOrign();
    }, 3000);
  }
  selectedMapOrign() {
    try {
      this.unselectedMapOrigin();
      const randomNum = this.getRandomInt(0, this.mapNameData[this.mainMapName].length);
      this.selectName = this.mapNameData[this.mainMapName][randomNum];
      //高亮
      this.myChart.dispatchAction({
        type: "highlight",
        geoId: "mainMapName",
        name: this.selectName
      });
      //showTip

      this.myChart.dispatchAction({
        type: "showTip",
        geoId: "mainMapName",
        name: this.selectName
      });
    } catch (error) {}
  }
  unselectedMapOrigin() {
    try {
      if (this.selectName) {
        //高亮
        this.myChart.dispatchAction({
          type: "downplay",
          geoId: "mainMapName",
          name: this.selectName
        });
      }
    } catch (error) {}
  }
  backToTop() {
    try {
      this.toMap({
        baseMapName: "china",
        mainMapName: "empchina"
      });
    } catch (error) {}
  }
  async toMap(options) {
    try {
      if (proviceNameObject[options.baseMapName]) {
        if (!this.mapCache[options.baseMapName]) {
          const res = await getMapJson(proviceNameObject[options.baseMapName]);
          echarts.registerMap(options.baseMapName, res);
          !this.mapNameData[options.baseMapName]
            ? (this.mapNameData[options.baseMapName] = res.features.map(item => item.properties.name))
            : "";
        }
        if (!this.mapCache[options.mainMapName]) {
          const res = await getMapJson(proviceNameObject[options.mainMapName]);
          echarts.registerMap(options.mainMapName, res);
          !this.mapNameData[options.mainMapName]
            ? (this.mapNameData[options.mainMapName] = res.features.map(item => item.properties.name))
            : "";
        }
        const pointParams = {};
        if (options.baseMapName != "china") {
          pointParams.privence = proviceNameObject[options.baseMapName];
        }
        const result = await getPrivincePoint(pointParams);
        this.pointData = {};
        this.provicePoint = [];
        this.needShowProvincialCapitals = [];
        //省的
        result.data &&
          result.data.cloudInfoList &&
          result.data.cloudInfoList.forEach(item => {
            this.pointData[item.city] = item;
            this.provicePoint.push({ name: item.city, value: [item.longtidu, item.latidu], ...item });
          });
        //全国的
        result.data &&
          result.data.cloudInfosList &&
          result.data.cloudInfosList.forEach(item => {
            this.pointData[item.zone] = item;
            this.needShowProvincialCapitals.push(item.zone);
          });
        this.baseMapName = options.baseMapName;
        this.mainMapName = options.mainMapName;
        const option = this.createOption();
        this.myChart.clear();
        this.myChart.setOption(option);
        this.mapCache(options);
      }
    } catch (error) {}
  }
  getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
}
