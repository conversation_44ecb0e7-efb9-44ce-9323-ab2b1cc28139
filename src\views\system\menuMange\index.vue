<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="菜单列表"
      :indent="20"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :pagination="false"
      :tool-button="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button plain :icon="Plus" @click="toView({}, editType.add)">新增</el-button>
        <el-button plain :icon="Delete" :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          删除
        </el-button>
      </template>
      <!-- 菜单操作 -->
      <template #operation="scope">
        <el-tooltip content="编辑" placement="top">
          <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toView(scope.row, editType.edit)"></i>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <i class="iconfont2 opera-icon icon-lajitong" @click="deleteMenuItem(scope.row)"></i>
        </el-tooltip>
      </template>
    </ProTable>
    <operate-menu-dialog
      :visible="dialogInfo.dialogVisible"
      :type="dialogInfo.dialogType"
      :params="dialogInfo.dialogData"
      @cancel="onCancel"
      @confirm="onComfirm"
    ></operate-menu-dialog>
  </div>
</template>

<script setup lang="tsx" name="menuMange">
import { ref, reactive } from "vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { Delete, Plus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { editType } from "@/enums/dialogTypeEnum";
import { useHandleData } from "@/hooks/useHandleData";

import { getTreeList, deleteMenu } from "@/api/modules/system";
import operateMenuDialog from "./components/operateMenuDialog.vue";
import CustomTag from "@/components/CustomTag/index.vue";
import SvgIcon from "@/components/SvgIcon/index.vue";

const proTable = ref();

const defaultDialogInfo = { dialogVisible: false, dialogType: editType.add, dialogData: {} };
const dialogInfo = reactive({ ...defaultDialogInfo });

// const menuData = ref(authMenuList.data);
const initParam = reactive({});

const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  return getTreeList(newParams);
};

// 表格配置项
const columns: ColumnProps[] = [
  { type: "selection", fixed: "left", width: 50 },
  { type: "index", label: "序号", width: 150 },
  { prop: "menuName", label: "菜单名称", align: "left", search: { el: "input" } },
  {
    prop: "icon",
    label: "菜单图标",
    width: 100,
    render: ({ row }) => {
      return <SvgIcon name={row.icon} style={{ width: "20px", height: "20px" }} />;
    }
  },
  { prop: "path", label: "路由地址" },
  { prop: "sort", label: "排序", width: 100 },
  {
    prop: "status",
    label: "状态",
    width: 100,
    search: { el: "select" },
    render: scope => {
      return (
        <CustomTag
          type={scope.row.status === 1 ? "success" : "danger"}
          status={String(scope.row.status)}
          label={scope.row.status === 1 ? "正常" : "停用"}
        ></CustomTag>
      );
    },
    enum: [
      {
        label: "正常",
        value: 1
      },
      {
        label: "停用",
        value: 0
      }
    ]
  },
  { prop: "createTime", label: "创建时间", width: 200 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" }
];

const toView = async (row: any, type = editType.add) => {
  dialogInfo.dialogVisible = true;
  dialogInfo.dialogType = type;

  if (row.id) {
    // 获取单个配置信息
    dialogInfo.dialogData = row;
    // await getInfo({ paramKey: row.paramKey }).then(res => {
    //   dialogInfo.dialogData = res.data as Object;
    // });
  }
};

// 批量删除
const batchDelete = async (ids: string[]) => {
  await useHandleData(deleteMenu, { ids: ids }, { msg: `删除后不能恢复，是否确认删除所选记录？`, successMsg: "删除" });
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
// 单个删除
const deleteMenuItem = async (row: any) => {
  if (!row.id) {
    return;
  }
  await useHandleData(deleteMenu, { ids: [row.id] }, `删除<${row.menuName}>`);
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const onCancel = () => {
  dialogInfo.dialogType = editType.add;
  dialogInfo.dialogData = {};
  dialogInfo.dialogVisible = false;
};

const onComfirm = () => {
  onCancel();
  proTable.value?.getTableList();
};
</script>
@/components/ProTable/interface
