<!-- 订单详情-产品信息 -->

<template>
  <div class="info-box card">
    <div class="box-title">
      <div class="title">产品信息</div>
      <div class="sub-tips" v-show="data?.length > 1 && auditTagType === 1">该订单为组合交易订单，订单内产品需一同审核</div>
    </div>
    <el-table
      :data="data"
      style="width: 100%"
      border
      :header-cell-style="{ fontWeight: 'bold', color: '#303133', background: '#f5f7fa' }"
    >
      <el-table-column align="left" prop="subOrderNo" label="子订单编号" width="200">
        <template #default="{ row }">
          <div class="text">
            <el-tooltip class="box-item" :content="row?.subOrderNo" placement="top-start">
              {{ row?.subOrderNo }}
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" prop="providerSkuId" label="外系统订单编号" width="130">
        <template #default="{ row }">
          <div class="text">
            <el-tooltip class="box-item" effect="dark" :content="row?.providerSkuId" placement="top-start">
              {{ row?.providerSkuId }}
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" prop="productName" label="产品名称" width="120">
        <template #default="{ row }">
          <div class="text">
            <el-tooltip class="box-item" effect="dark" :content="row?.productName" placement="top-start">
              {{ row?.productName }}
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" width="200" prop="skuName" label="产品规格">
        <template #default="{ row }">
          <div v-html="row?.skuName"></div>
        </template>
      </el-table-column>
      <el-table-column align="left" width="200" prop="skuConfig" label="规格配置">
        <template #default="{ row }">
          <div v-html="row?.skuConfig"></div>
        </template>
      </el-table-column>
      <el-table-column align="left" width="120" prop="deliveryMethod" label="交付方式">
        <template #default="{ row }">
          {{ useDictLabel(pms_delivery_method, row?.deliveryMethod) }}
        </template>
      </el-table-column>
      <el-table-column align="left" width="120" prop="billingMode" label="计费模式">
        <template #default="{ row }">
          {{ useDictLabel(sku_billing_mode_type, row?.billingMode) }}
        </template>
      </el-table-column>
      <el-table-column align="left" width="85" prop="probationFlag" label="试用产品">
        <template #default="{ row }">
          {{ useDictLabel(yes_no_type, row?.probationFlag) }}
        </template>
      </el-table-column>
      <el-table-column align="left" width="85" prop="orderType" label="订单类型">
        <template #default="{ row }">
          {{ useDictLabel(ord_order_type, row?.orderType) }}
        </template>
      </el-table-column>
      <el-table-column align="right" width="85" prop="count" label="购买数量">
        <template #default="{ row }">
          <div v-if="row?.count">
            {{ row?.count }}
            {{ useDictLabel(sku_quantity_uit, row?.countUnit) }}
          </div>
          <div v-else>/</div>
        </template>
      </el-table-column>
      <el-table-column align="right" width="85" prop="buyDuration" label="购买时长">
        <template #default="{ row }">
          <div v-if="row?.buyDuration">
            {{ row?.buyDuration }}
            {{ useDictLabel(pms_time_uit, row?.buyDurationUnit) }}
          </div>
          <div v-else>/</div>
        </template>
      </el-table-column>
      <el-table-column align="right" width="105" prop="skuPrice" label="单价（元）">
        <template #default="{ row }">{{ toPrice(row.skuPrice, 3) }} </template>
      </el-table-column>
      <el-table-column align="right" width="115" prop="skuDiscountPrice" label="折扣价（元）">
        <template #default="{ row }">{{ row.skuDiscountPrice ? toPrice(row.skuDiscountPrice, 3) : "/" }} </template>
      </el-table-column>
      <el-table-column align="left" prop="effective" label="有效期" width="150">
        <template #default="{ row }">
          <div class="time-text">
            <span>{{ row.effectTime }}</span> <span> -</span> <span>{{ row.endTime }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" width="100" prop="productProviderName" label="产品服务商"> </el-table-column>
      <el-table-column align="right" prop="prePayFee" label="订单金额（元）" width="130">
        <template #default="{ row }">
          <div class="pay-fee" style="font-size: 16px; color: #0052d9">{{ toPrice(row.prePayFee) }}</div>
        </template>
      </el-table-column>
      <el-table-column align="right" prop="payFee" label="应付金额（元）" width="130">
        <template #default="{ row }">
          <div class="pay-fee" style="font-size: 16px; color: #0052d9">{{ toPrice(row.payFee) }}</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="total-price" v-if="!targetVisible"><span>订单总金额：</span>¥{{ toPrice(totalPrice ? totalPrice : "0") }}</div>
  </div>
</template>

<script setup lang="ts">
interface TablePropType {
  data: any[];
  auditTagType: number;
  totalPrice?: string | number;
  targetVisible: Boolean;
}

import { toPrice } from "@/utils/index";
import { useDict, useDictLabel } from "@/hooks/useDict";
const { pms_delivery_method, pms_time_uit, sku_quantity_uit, ord_order_type, yes_no_type, sku_billing_mode_type } = useDict(
  "pms_delivery_method",
  "pms_time_uit",
  "sku_quantity_uit",
  "ord_order_type",
  "yes_no_type",
  "sku_billing_mode_type"
);

defineProps<TablePropType>();
</script>

<style scoped lang="scss">
@import "../order.module.scss";
.text {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.time-text {
  span {
    display: block;
  }
}
.total-price {
  margin-top: 36px;
  font-size: 24px;
  font-weight: 600;
  color: #0052d9;
  text-align: right;
  span {
    font-size: 14px;
    font-weight: 400;
    color: #3a3a3d;
  }
}
</style>
