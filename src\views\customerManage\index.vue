<template>
  <div class="page-container">
    <sourceCount></sourceCount>
    <div class="table-box">
      <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
        <template #tableHeader>
          <el-button v-auth="'export'" plain @click="downloadFile">
            <i class="iconfont2 icon-a-lineicon_share"></i>
            导出
          </el-button>
        </template>
        <template #userSource="{ row }">
          {{ useDictLabel(dict_user_source, row?.userSource) }}
        </template>
        <template #verifyLevel="{ row }">
          {{ useDictLabel(portal_verify_level, row?.verifyLevel) }}
        </template>
        <template #status="{ row }">
          <!-- <el-tag v-if="row?.status === 1" type="success">{{ useDictLabel(dict_status, row?.status) }}</el-tag>
          <el-tag :color="'#FDEEEE'" style="color: #e84b4a" v-else type="info">{{
            useDictLabel(dict_status, row?.status)
          }}</el-tag> -->

          <CustomTag
            :type="row.status ? 'success' : 'danger'"
            :status="row.status"
            :label="useDictLabel(dict_status, row.status)"
          ></CustomTag>
        </template>
        <template #operation="{ row }">
          <span v-auth="'enable'">
            <el-tooltip v-auth="'enable'" :content="row.status === 1 ? '停用' : '启用'" placement="top">
              <i
                :class="['iconfont2 opera-icon', row.status === 1 ? 'icon-tingyong' : 'icon-qiyong']"
                @click="handleChange(row)"
              ></i>
            </el-tooltip>
          </span>
        </template>
      </ProTable>
    </div>
  </div>
</template>

<script setup lang="tsx" name="customerManage">
import { h, onActivated, ref } from "vue";
import sourceCount from "./components/sourceCount.vue";
import ProTable from "@/components/ProTable/index.vue";
import { Download } from "@element-plus/icons-vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getPortalUserList, querySetStatus, queryCustomerListExcel } from "@/api/modules/customerManage";
import { System } from "@/api/interface/system";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { Action, ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { useDownload } from "@/hooks/useDownload";
import CustomTag from "@/components/CustomTag/index.vue";
/*  */
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const router = useRouter();
const { dict_user_source, portal_verify_level } = useDict("dict_user_source", "portal_verify_level");

const dict_status = [
  {
    itemKey: 1,
    itemValue: "正常"
  },
  {
    itemKey: 0,
    itemValue: "停用"
  }
];

const proTable = ref<ProTableInstance>();

const columns: ColumnProps[] = [
  {
    prop: "account",
    label: "账号",
    search: { el: "input" },
    render: ({ row }: System.PortalUserParams) => {
      return (
        <el-button
          type="text"
          onClick={() => {
            router.push({
              path: "/customerManage/customerDetail",
              query: {
                id: row.id
              }
            });
          }}
        >
          {row.account}
        </el-button>
      );
    }
  },
  { prop: "phone", label: "手机号码", search: { el: "input" } },
  { prop: "email", label: "用户邮箱", search: { el: "input" } },
  {
    prop: "userSource",
    label: "客户来源",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {dict_user_source.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "areaName", label: "所在城市", search: { el: "input" } },
  {
    prop: "verifyLevel",
    label: "认证状态",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {portal_verify_level.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "company", label: "企业名称", search: { el: "input" } },
  {
    prop: "status",
    label: "状态",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {dict_status.map(item => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "createTime",
    label: "注册时间",
    search: {
      render: scope => {
        return (
          <el-date-picker
            type="datetimerange"
            value-format={"YYYY-MM-DD HH:mm:ss"}
            model-value={scope.modelValue}
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        );
      }
    }
  },
  {
    prop: "lastLoginTime",
    label: "最近登录时间",
    search: {
      render: scope => {
        return (
          <el-date-picker
            type="datetimerange"
            value-format={"YYYY-MM-DD HH:mm:ss"}
            model-value={scope.modelValue}
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        );
      }
    }
  },

  { prop: "operation", label: "操作", fixed: "right", width: 200 }
];

const handleChange = (data: System.PortalUserParams) => {
  const msg = data.status === 0 ? `是否启用<${data?.account}>` : `是否停用<${data?.account}>`;
  ElMessageBox.alert(msg, "温馨提示", {
    confirmButtonText: "确定",
    showCancelButton: true,
    callback: (action: Action) => {
      if (action === "confirm") {
        querySetStatus({ id: data.id, status: data.status === 0 ? 1 : 0 }).then(data => {
          proTable.value?.getTableList();
        });
      }
    }
  });
};
/*  */
const getTableList = async (params: System.PortalUserParams) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const values = params;
  if (Reflect.has(params, "createTime")) {
    Reflect.set(values, "createTimeStart", params?.createTime[0]);
    Reflect.set(values, "createTimeEnd", params?.createTime[1]);
    Reflect.deleteProperty(values, "createTime");
  }

  if (Reflect.has(params, "lastLoginTime")) {
    Reflect.set(values, "lastLoginTimeStart", params?.lastLoginTime[0]);
    Reflect.set(values, "lastLoginTimeEnd", params?.lastLoginTime[1]);
    Reflect.deleteProperty(values, "lastLoginTime");
  }

  return await getPortalUserList(values);
};

const downloadFile = () => {
  let params = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  const values = params;
  if (Reflect.has(params, "createTime")) {
    Reflect.set(values, "createTimeStart", params?.createTime[0]);
    Reflect.set(values, "createTimeEnd", params?.createTime[1]);
    Reflect.deleteProperty(values, "createTime");
  }

  if (Reflect.has(params, "lastLoginTime")) {
    Reflect.set(values, "lastLoginTimeStart", params?.lastLoginTime[0]);
    Reflect.set(values, "lastLoginTimeEnd", params?.lastLoginTime[1]);
    Reflect.deleteProperty(values, "lastLoginTime");
  }
  values.size = 1000;
  values.current = 1;
  let newParams = JSON.parse(JSON.stringify(values));
  // useDownload(queryCustomerListExcel, newParams);
  queryCustomerListExcel(newParams)
    .then(res => {
      ElMessage.success("请前往下载管理中下载文件");
    })
    .catch(() => {
      ElMessage.error("下载有误，请重新下载");
    });
};
/* 请求数据 */
onActivated(() => {
  proTable.value?.getTableList();
});
</script>

<style scoped lang="scss">
::v-deep(.el-scrollbar) {
  min-height: 120px;
}
</style>
