<template>
  <el-form ref="prodForm" :model="productAttr" :rules="rules">
    <el-form-item label="属性名称" prop="attrName" label-width="80">
      <el-input v-model="productAttr.attrName" placeholder="请输入属性名称" />
    </el-form-item>

    <el-form-item label="属性类型" prop="attrType" label-width="80">
      <el-select v-model="productAttr.attrType" placeholder="请选择属性类型">
        <el-option v-for="item in attr_type" :key="item.itemKey" :label="item.itemValue" :value="item.itemKey" />
      </el-select>
    </el-form-item>
    <el-form-item label="属性编码" label-width="80" prop="attrCode">
      <el-input v-model="productAttr.attrCode" placeholder="请输入属性编码" />
    </el-form-item>
    <template v-if="productAttr.attrType === '2'">
      <el-form-item label="枚举类型" prop="attrEnumType" label-width="80">
        <el-select v-model="productAttr.attrEnumType" placeholder="请选择枚举类型">
          <el-option v-for="item in attr_value_type" :key="item.itemKey" :label="item.itemValue" :value="item.itemKey" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="small" plain :icon="Plus" @click="handleAdd"> 添加枚举项 </el-button>
      </el-form-item>
      <draggable v-model="productAttr._attrList" handle=".sortBtn" animation="300" item-key="id">
        <template #item="{ element, index }">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="枚举项名称"
                :prop="'_attrList.' + index + '.name'"
                :rules="[
                  {
                    required: true,
                    message: '请输入枚举项名称',
                    trigger: 'blur'
                  },
                  {
                    validator: validateString
                  }
                ]"
              >
                <el-input v-model="element.name" placeholder="最长不超过10个字" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                :prop="'_attrList.' + index + '.value'"
                label="枚举值"
                :rules="[
                  {
                    required: true,
                    message: '请输入枚举值',
                    trigger: 'blur'
                  },
                  {
                    validator: validateEnum
                  }
                ]"
              >
                <el-input v-model="element.value" placeholder="请输入枚举值" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button link text plain :icon="Delete" @click="handleDelete(index)"></el-button>
              <el-button link class="sortBtn" text plain :icon="Operation"></el-button>
            </el-col>
          </el-row>
        </template>
      </draggable>
    </template>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, toRaw, toRefs } from "vue";
import { useDict } from "@/hooks/useDict";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { Product } from "@/api/interface/product";
import { Plus, Operation, Delete } from "@element-plus/icons-vue";
import { pmsProductAttrSave, pmsProductAttrUpdate } from "@/api/modules/product";
import draggable from "vuedraggable";

const validateEnum = (rule: any, value: any, callback: any) => {
  if (productAttr.attrEnumType === "2") {
    const regex = /^(?:0|[1-9]\d{0,5})(?:\.\d+)?$/;
    if (regex.test(value)) {
      callback();
    } else {
      callback(new Error("请输入0-1000000的数字"));
    }
  } else {
    if (value.length <= 10) {
      callback();
    } else {
      callback(new Error("只能输入10个字符"));
    }
  }
};
const validateCode = (rule: any, value: any, callback: any) => {
  const state = toRaw(productAttr);
  if (productAttr.attrType === "2") {
    if (state?._attrList?.length > 0) {
      callback();
    } else {
      callback(new Error("最小添加一个枚举项"));
    }
  } else {
    callback();
  }
};
const validateString = (rule: any, value: any, callback: any) => {
  if (value.length <= 10) {
    callback();
  } else {
    callback(new Error("只能输入10个字符"));
  }
};

const { attr_type, attr_value_type } = useDict("attr_type", "attr_value_type");
const prodForm = ref<FormInstance>();
const productAttr = reactive<Product.CreateParams>({
  attrName: "",
  attrCode: "",
  attrType: "1",
  attrEnumType: "1",
  attrValueList: "",
  _attrList: []
});
const rules = reactive<FormRules>({
  attrName: [
    { required: true, message: "请输入属性名称", trigger: "blur" },
    {
      validator: validateString
    }
  ],
  attrType: [{ required: true, message: "请输入属性类型", trigger: "blur" }],
  attrEnumType: [{ required: true, message: "请输入枚举类型", trigger: "blur" }, { validator: validateCode }],
  attrCode: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value.length <= 50) {
          callback();
        } else {
          callback(new Error("只能输入50个字符"));
        }
      }
    }
  ]
});

const handleAdd = () => {
  productAttr?._attrList?.push({
    value: "",
    name: ""
  });
};

const submit = () => {
  return new Promise((resolve, reject) => {
    prodForm.value
      ?.validate()
      .then(result => {
        const values = productAttr;
        Reflect.set(values, "attrValueList", JSON.stringify(values._attrList));

        if (Reflect.has(productAttr, "id")) {
          pmsProductAttrUpdate(values)
            .then(result => {
              if (result.code === 200) {
                Reflect.deleteProperty(values, "_attrList");
                ElMessage.success("编辑成功");
                resolve(1);
              }
            })
            .catch(err => {
              reject(err);
            });
        } else {
          pmsProductAttrSave(values)
            .then(res => {
              if (res.code === 200) {
                Reflect.deleteProperty(values, "_attrList");
                ElMessage.success("新增成功");
                resolve(1);
              }
            })
            .catch(err => {
              reject(err);
            });
        }
      })
      .catch(err => {
        console.log(err);
      });
  });
};

const handleDelete = (idx: number) => {
  productAttr._attrList?.splice(idx, 1);
};

const formSetValues = (data: Product.ProductAttrListItem) => {
  productAttr.attrType = data.attrType;
  productAttr.attrCode = data.attrCode;
  productAttr.attrEnumType = data.attrEnumType;
  productAttr.attrName = data.attrName;
  productAttr.attrValueList = data.attrValueList;
  productAttr._attrList = JSON.parse(data.attrValueList);
  productAttr.id = data.id;
};

defineExpose({
  formSetValues,
  submit
});
</script>

<style scoped lang="scss"></style>
