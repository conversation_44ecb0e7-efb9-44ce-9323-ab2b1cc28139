<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="合同备案列表"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
    >
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="openDialog({}, editType.add)"> 新增 </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="openDialog(scope.row, editType.details)"></i>
          </el-tooltip>
        </span>
        <el-tooltip content="编辑" placement="top">
          <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="openDialog(scope.row, editType.edit)"></i>
        </el-tooltip>
      </template>
    </ProTable>
    <!--  新增编辑弹窗  -->
    <add-edit-dialog
      v-model:visible="labelDialogVisible"
      :type="labelDialogParams.type"
      :params="labelDialogParams.params"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-dialog>
  </div>
</template>

<script setup lang="tsx" name="orderContract">
import { ref, reactive, onMounted, onActivated } from "vue";
import { Plus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getOrderContractList } from "@/api/modules/order";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import AddEditDialog from "./components/addOrEditDialog.vue";

const { BUTTONS } = useAuthButtons();
enum editType {
  "add" = 1,
  "edit" = 2,
  "details" = 4
}

interface typeDialog {
  type: number;
  params: Object;
}
// 编辑弹窗
const labelDialogVisible = ref(false);
const labelDialogParams = ref<typeDialog>({
  params: {},
  type: editType.add
});

const onCancel = () => {};
const onConfirm = () => {
  proTable.value?.getTableList();
};

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

onMounted(() => {
  proTable.value?.getTableList();
});

// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  proTable.value?.getTableList();
});

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }

  return getOrderContractList(params);
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  { prop: "id", label: "合同编码", search: { el: "input" } },
  {
    prop: "contractName",
    label: "合同名称"
  },
  {
    prop: "supplierId",
    label: "供应商编码"
  },
  {
    prop: "supplierName",
    label: "供应商名称"
  },
  {
    prop: "companyName",
    label: "公司名称"
  },
  {
    prop: "customerId",
    label: "客户编码"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

const openDialog = async (row: any, operateType = editType.add) => {
  //  新增 编辑
  const dialogParams = {
    type: operateType,
    params: {
      ...row
    }
  };
  labelDialogVisible.value = true;
  labelDialogParams.value = dialogParams;
};
</script>

<style lang="scss" scoped>
:global(.overflow-tooltip-style) {
  max-width: calc(100% - 24px);
  white-space: pre-wrap;
}
</style>
