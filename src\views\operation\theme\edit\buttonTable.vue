<template>
  <pro-table :columns="columns" :data="dataList" :is-simple-table="true" :pagination="false" :tool-button="false">
    <template #empty>
      <!-- <img src="@/assets/images/notData.png" alt="notData" /> -->
      <p>暂无数据</p>
    </template>
    <template #name="scope">
      <div>
        <span v-if="scope.$index !== editIndex">{{ scope.row.name }}</span>
        <el-input v-else :maxlength="10" v-model="scope.row.name" placeholder="请填写"></el-input>
      </div>
    </template>
    <template #context="scope">
      <div>
        <span v-if="scope.$index !== editIndex">{{ scope.row.context }}</span>
        <el-input v-else :maxlength="150" v-model="scope.row.context" placeholder="请输入跳转链接"></el-input>
      </div>
    </template>
    <template #operation="scope">
      <el-button v-if="scope.$index === editIndex" plain @click="() => toSave()"> 保存 </el-button>
      <el-button v-if="scope.$index === editIndex" plain @click="() => toCancel()"> 取消 </el-button>
      <el-button v-if="scope.$index !== editIndex" plain @click="() => toEdit(scope.$index)"> 修改 </el-button>
      <el-button v-if="scope.$index !== editIndex" plain @click="() => toDelete(scope.$index)"> 删除 </el-button>
    </template>
    <template #append>
      <div class="add-label-append">
        <el-button
          type="primary"
          class="add-label-btn"
          @click.stop="handleAddLabel"
          title="添加"
          :disabled="editIndex != -1 || dataList.length >= 2"
        >
          <span>+ 添加</span>
          <!-- <span>添加标签 ({{ dataList.length }}/2)</span> -->
        </el-button>
      </div>
    </template>
  </pro-table>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { buttonItemType } from "@/api/interface/business/theme";

const props = defineProps({
  buttonData: {
    type: Array<buttonItemType>,
    default: () => []
  }
});
const emits = defineEmits(["updateButton"]);
const dataList = ref<buttonItemType[]>(props.buttonData);
const originalDataList = ref<buttonItemType[]>([]); //修改前的值

const editIndex = ref(-1); // 当前编辑的行索引
const columns = [
  {
    prop: "name",
    label: "操作按钮名称",
    align: "left",
    editable: true
  },
  {
    prop: "context",
    label: "链接内容",
    align: "left",
    editable: true
  },
  {
    prop: "operation",
    label: "操作",
    align: "left"
  }
];

const handleAddLabel = () => {
  editIndex.value = dataList.value.length;
  originalDataList.value = JSON.parse(JSON.stringify(dataList.value));
  dataList.value.push({
    name: "",
    context: ""
  });
};

const toEdit = (index: number) => {
  originalDataList.value = JSON.parse(JSON.stringify(dataList.value));
  editIndex.value = index;
};
const toSave = () => {
  // props.buttonData = dataList.value;
  originalDataList.value = JSON.parse(JSON.stringify(dataList.value));
  emits("updateButton", dataList.value);
  editIndex.value = -1;
};
const toCancel = () => {
  dataList.value = JSON.parse(JSON.stringify(originalDataList.value));
  editIndex.value = -1;
};
const toDelete = (index: number) => {
  dataList.value.splice(index, 1);
  emits("updateButton", dataList.value);
  originalDataList.value = JSON.parse(JSON.stringify(dataList.value));
};

watch(
  () => props.buttonData,
  () => {
    dataList.value = props.buttonData;
  }
  // { deep: true }
);

onMounted(() => {});
</script>
<style lang="scss" scoped>
// @import "@/styles/details-common.scss";
:deep(.table__empty-block) {
  min-height: 0 !important;
}
.collapse-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  .add-icon {
    width: 36px;
    height: 36px;
  }
}
.add-label-append {
  display: flex;
  align-items: center;
  justify-content: center;
  .add-label-btn {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
</style>
