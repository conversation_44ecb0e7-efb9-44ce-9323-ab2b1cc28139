<template>
  <el-dialog :title="dialogTitle" :append-to-body="true" custom-class="custom-modal" v-model="dialogVisible" width="600">
    <div v-if="dialogType === TabType.SERVICE" class="status-box">
      采纳状态：<span :class="`status-${dialogData.acceptStatus}`">{{
        useDictLabel(demandAcceptStatus, dialogData.acceptStatus || "--")
      }}</span>
    </div>

    <el-row>
      <el-col v-for="item in displayInfo" :key="item.key" :span="item.span || 12" class="mb30">
        <div class="info-label">{{ item.label }}</div>
        <div v-if="item.key === 'demandType'" class="info-text">
          {{ useDictLabel(requireType, dialogData[item.key] || "--") }}
        </div>

        <span v-else-if="item.key === 'categoryName'" class="info-text">{{ formatCategoryName }}</span>
        <span v-else-if="item.key === 'demandPicUrl'" class="info-text">
          <img v-if="dialogData?.demandPicUrl" :src="dialogData?.demandPicUrl" alt="需求图片" class="demand-image" />
          <span v-else>--</span>
        </span>
        <span v-else-if="item.key === 'price'" class="info-text">{{
          dialogData ? (dialogData.isNegotiation === 1 ? "面谈" : dialogData.price ? dialogData.price + "万元" : "--") : "--"
        }}</span>
        <span v-else-if="item.key === 'demandUrl'" class="info-text">
          <el-icon>
            <Document />
          </el-icon>
          <a :href="dialogData?.demandUrl" class="file-name" v-if="dialogData?.demandUrl">下载附件</a>
          <span v-else>暂无附件</span>
        </span>

        <span v-else-if="item.key === 'solutionUrl'" class="info-text">
          <el-icon>
            <Document />
          </el-icon>
          <a :href="dialogData?.solutionUrl" class="file-name" v-if="dialogData?.solutionUrl">下载附件</a>
          <span v-else>暂无附件</span>
        </span>

        <div v-else class="info-text">{{ dialogData[item.key] || "--" }}</div>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script setup lang="ts" name="detailDialog">
import { ref, computed, onBeforeMount } from "vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useDictLabel } from "@/hooks/useDict";
import { getSchemeDetail, queryOclDemandCategory } from "@/api/modules/requireReleaseJbgs";
import { TabType } from "../type";

interface infoItem {
  label: string;
  key: string;
  span?: number;
}

const dictionaryStore = useDicStore();

// 审核状态
const demandAcceptStatus: any = computed(() => dictionaryStore.demandAcceptStatus);
// 需求类型
const requireType: any = computed(() => dictionaryStore.jbgsRequireType);
// 实施地址
// const demandConstructionAddress: any = computed(() => dictionaryStore.demandConstructionAddress);

const dialogVisible = ref(false);
const dialogTitle = ref();
const dialogType = ref();
const dialogData = ref();

const displayInfo = ref<infoItem[]>([]);
const requireInfo = [
  {
    label: "企业名称",
    key: "companyName"
  },
  {
    label: "企业地址",
    key: "companyAddress"
  },
  {
    label: "联系人",
    key: "contactName"
  },
  {
    label: "手机号",
    key: "phone"
  },
  {
    label: "需求名称",
    key: "name"
  },
  {
    label: "关键字",
    key: "keyWord"
  },
  {
    label: "需求地点",
    key: "constructionAddressName"
  },
  {
    label: "需求类别",
    key: "demandType"
  },
  {
    label: "需求预算",
    key: "price"
  },
  {
    label: "所属领域",
    key: "categoryName",
    formatter: () => formatCategoryName.value,
    span: 12
  },
  {
    label: "需求图片",
    key: "demandPicUrl",
    span: 6
  },
  {
    label: "需求说明",
    key: "description",
    span: 24
  },
  {
    label: "现有情况",
    key: "currentStatus",
    span: 24
  },
  {
    label: "需求附件",
    key: "demandUrl",
    span: 24
  }
];
const responseInfo = [
  {
    label: "需求企业名称",
    key: "demandCompanyName"
  },
  {
    label: "需求企业地址",
    key: "demandCompanyAddress"
  },
  {
    label: "需求联系人",
    key: "demandContactName"
  },
  {
    label: "需求手机号",
    key: "demandPhone"
  },
  {
    label: "需求名称",
    key: "name"
  },
  {
    label: "关键字",
    key: "keyWord"
  },
  {
    label: "需求地点",
    key: "constructionAddressName"
  },
  {
    label: "需求类别",
    key: "demandType"
  },
  {
    label: "需求预算",
    key: "price"
  },
  {
    label: "所属领域",
    key: "categoryName",
    formatter: () => formatCategoryName.value,
    span: 12
  },
  {
    label: "需求图片",
    key: "demandPicUrl",
    span: 6
  },
  {
    label: "需求说明",
    key: "description",
    span: 24
  },
  {
    label: "现有情况",
    key: "currentStatus",
    span: 24
  },
  {
    label: "需求附件",
    key: "demandUrl",
    span: 24
  },
  {
    label: "方案企业名称",
    key: "solutionCompanyName"
  },
  {
    label: "方案企业地址",
    key: "solutionCompanyAddress"
  },
  {
    label: "方案联系人",
    key: "solutionContactName"
  },
  {
    label: "方案手机号",
    key: "solutionPhone"
  },
  {
    label: "方案名称",
    key: "solutionName",
    span: 24
  },
  {
    label: "方案描述:",
    key: "solutionDescription",
    span: 24
  },
  {
    label: "方案附件",
    key: "solutionUrl",
    span: 24
  }
];

const getSchemeData = async () => {
  try {
    const res = await getSchemeDetail({ id: dialogData.value.id });
    Object.assign(dialogData.value, res.data);
  } catch (error) {}
};

const handleOpen = (title: string, type: TabType, data: any) => {
  dialogVisible.value = true;
  dialogTitle.value = title;
  dialogType.value = type;
  dialogData.value = data;
  console.log(dialogData.value, 1);
  if (type === TabType.ENTERPRISE) {
    displayInfo.value = requireInfo;
  } else {
    displayInfo.value = responseInfo;
    getSchemeData();
  }
};
const getOclDemandCategory = async () => {
  try {
    const res = await queryOclDemandCategory();
    oclDemandCategory.value = res.data as any[];
  } catch (error) {
    console.log(error);
  }
};

const oclDemandCategory = ref<any[]>([]);

// 添加计算属性，根据categoryName字段的值从oclDemandCategory中获取对应的名称
const formatCategoryName = computed(() => {
  // 处理所属领域
  const categoryNames = [];

  // 查找一级分类名称
  const level1 = oclDemandCategory.value.find(cat => cat.id === dialogData.value.categoryIdL1);
  if (level1) {
    categoryNames.push(level1.name);

    // 在一级分类的子分类中查找二级分类
    const level2 = level1.children?.find(cat => cat.id === dialogData.value.categoryIdL2);
    if (level2 && level2.id !== dialogData.value.categoryIdL1) {
      categoryNames.push(level2.name);

      // 在二级分类的子分类中查找三级分类
      const level3 = level2.children?.find(cat => cat.id === dialogData.value.categoryId);
      if (level3 && level3.id !== dialogData.value.categoryIdL2) {
        categoryNames.push(level3.name);
      }
    }
  }
  console.log(categoryNames, 1);
  return categoryNames.join("/") || "--";
});

onBeforeMount(() => {
  dictionaryStore.demandAcceptStatus.length === 0 && dictionaryStore.getDemandAcceptStatus();
  dictionaryStore.jbgsRequireType.length === 0 && dictionaryStore.getJbgsRequireType();
  getOclDemandCategory();
  // dictionaryStore.demandConstructionAddress.length === 0 && dictionaryStore.getDemandConstructionAddress();
});
defineExpose({
  handleOpen
});
</script>
<style scoped lang="scss">
.status-box {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #3a3a3d;
}
.info-label {
  margin-bottom: 8px;
  color: #73787f;
}
.info-text {
  color: #3a3a3d;
}
.status-wait {
  color: #ffaa00;
}
.status-accept {
  color: #0052d9;
}
.status-refuse {
  color: #e5302f;
}
.demand-image {
  width: 240px;
  height: 180px;
  object-fit: fill;
  border-radius: 4px;
}
</style>
