<template>
  <div class="prod-download-info-box">
    <div class="name-box">
      <el-button type="primary" class="add-button" @click="addItem" :disabled="viewControl">添加</el-button>
      <span class="tips"> <slot name="nameTips"></slot></span>
    </div>
    <div class="content-item" v-for="(item, index) in dataList" :key="index + 'info'">
      <el-form ref="downloadInfoFormRef" class="item-form" :model="item" :rules="rules" label-width="120px">
        <el-row :gutter="24">
          <div class="item-info">
            <span>
              <el-button class="icon" primary link :icon="Remove" @click="deleteItem(index)" :disabled="viewControl"></el-button>
            </span>
            <el-col :span="12">
              <el-form-item :label="`${prefixion}名称`" prop="originalName">
                <el-input
                  v-model="item.originalName"
                  maxlength="20"
                  :disabled="viewControl"
                  clearable
                  :placeholder="`请输入${prefixion}名称`"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="`${prefixion}地址`" prop="link">
                <el-input
                  v-model="item.link"
                  maxlength="150"
                  :disabled="viewControl"
                  clearable
                  :placeholder="`请输入${prefixion}地址`"
                />
              </el-form-item>
            </el-col>
          </div>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PropType, ref } from "vue";
import { Remove } from "@element-plus/icons-vue";
import { ElMessage, FormInstance } from "element-plus";

const downloadInfoFormRef = ref<FormInstance>();

const props = defineProps({
  dataList: {
    type: Array as PropType<{ originalName: string; link: string }[]>,
    default: () => []
  },
  prefixion: {
    type: String,
    default: ""
  },
  viewControl: {
    type: Boolean,
    default: false // 查看详情就禁用新增和修改,由父组件的viewStatus传来
  },
  limitNum: {
    type: Number,
    default: 5
  }
});
const emits = defineEmits(["addItem", "deleteItem", "update:dataList"]);

const rules = {
  originalName: [{ required: true, message: `${props.prefixion}名称不能为空`, trigger: "blur" }],
  link: [{ required: true, message: `${props.prefixion}地址不能为空`, trigger: "blur" }]
};

const addItem = () => {
  if (props.dataList.length >= props.limitNum) {
    ElMessage.error(`最多添加${props.limitNum}个下载信息`);
    return;
  }
  emits("update:dataList", [...props.dataList, { originalName: "", link: "" }]);
  emits("addItem");
};
//删除元素
const deleteItem = (index: number) => {
  emits("deleteItem", index);
};
const ruleValidatePromise = () => {
  let promiseArr = [] as any;
  downloadInfoFormRef.value?.forEach((itemref: any) => {
    promiseArr.push(
      new Promise((resolve, reject) => {
        itemref.validate((valid: any, fields: any) => {
          if (valid) {
            resolve("校验成功");
          } else {
            let obj = Object.keys(fields);
            itemref.scrollToField(obj[0]);
            reject("校验失败");
          }
        });
      })
    );
  });
  return promiseArr;
};
defineExpose({
  ruleValidatePromise
});
</script>

<style lang="scss" scoped>
.prod-download-info-box {
  width: 100%;
  .name-box {
    display: flex;
    width: fit-content;
    margin-bottom: 16px;
    .name-input {
      margin-right: 8px;
    }
    .add-button {
      margin-right: 8px;
    }
    .tips {
      margin-left: 15px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
    }
  }
  .content-item {
    margin-bottom: 16px;
    .icon {
      margin-right: 16px;
      color: var(--el-color-primary);
    }
    .item-info {
      display: flex;
      width: calc(100% - 36px);
    }
  }
  .item-form {
    padding-left: 12px;
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .nowrap {
    white-space: nowrap;
  }
}
</style>
