<template>
  <div class="top-indicator-container">
    <div class="item-box">
      <img src="@/assets/images/screen/jrzsl.png" />
      <div class="item-box-right">
        <div class="item-box-num">{{ data?.computePowerTotal?.toFixed(2) || "-" }}</div>
        <div class="item-box-text">接入总算力(PFlops)</div>
      </div>
    </div>
    <div class="item-box">
      <img src="@/assets/images/screen/yfpsl.png" />
      <div class="item-box-right">
        <div class="item-box-num">{{ data?.computePowerAllotedTotal?.toFixed(2) || "-" }}</div>
        <div class="item-box-text">已分配算力(PFlops)</div>
      </div>
    </div>
    <div class="item-box">
      <img src="@/assets/images/screen/slyl.png" />
      <div class="item-box-right">
        <div class="item-box-num">{{ data?.computePowerFree?.toFixed(2) || "-" }}</div>
        <div class="item-box-text">算力余量(PFlops)</div>
      </div>
    </div>
    <div class="item-box">
      <img src="@/assets/images/screen/syslbfb.png" />
      <div class="item-box-right">
        <div class="item-box-num">
          {{ (data?.computePowerUsedRate * 100).toFixed(2) }}
          <span style="font-size: 18px">%</span>
        </div>
        <div class="item-box-text">使用算力百分比</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getTopIndicatInfo } from "@/api/modules/map";

let data = ref({});
onMounted(() => {
  getTopIndicatInfo().then((res: any) => {
    if (res.code === 200) {
      data.value = res.data;
    }
  });
});
</script>

<style scoped lang="scss">
.top-indicator-container {
  display: flex;
  justify-content: space-between;
  width: 920px;
  margin-top: 56px;
  .item-box {
    display: flex;
    img {
      width: 85px;
      height: 60px;
      margin-right: 3px;
    }
    .item-box-num {
      font-family: D-DIN-Bold;
      font-size: 32px;
      font-weight: bold;
      color: #ffffff;
    }
    .item-box-text {
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
    }
  }
}
</style>
