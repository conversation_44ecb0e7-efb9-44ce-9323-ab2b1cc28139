<template>
  <div class="card" style="padding-left: 32px">
    <div class="header-title">
      {{ pageType === "add" ? "新增解决方案" : pageType === "edit" ? "解决方案编辑" : "查看详情" }}
    </div>
    <el-form ref="solutionFormRef" :model="solutionForm" :rules="rules" label-width="120px" label-position="left">
      <el-form-item label="解决方案名称" prop="solutionName">
        <el-input
          v-model="solutionForm.solutionName"
          :disabled="viewStatus"
          @blur="checkName"
          maxlength="20"
          clearable
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="解决方案分类" prop="classify">
        <el-cascader
          v-model="solutionForm.classify"
          placeholder="请选择解决方案分类"
          :disabled="viewStatus"
          :options="typeOptions"
          filterable
          clearable
          style="width: 400px"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="合作类型" prop="cooperateType">
        <el-radio-group v-model="solutionForm.cooperateType" :disabled="viewStatus">
          <el-radio v-for="(item, index) in cooperateDic as any[]" :key="index + 'cooperation'" :label="item.itemKey">
            {{ item.itemValue }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="方案服务商" prop="providerId">
        <el-select
          ref="providerRef"
          v-model="solutionForm.providerId"
          :disabled="viewStatus"
          placeholder="请选择方案服务商"
          style="width: 300px"
          clearable
        >
          <el-option v-for="(item, index) in providerList" :key="index + 'providerList'" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="方案主图" prop="mainUrl">
        <UploadImg v-model:image-url="solutionForm.mainUrl" width="135px" height="135px" :file-size="0.5" :disabled="viewStatus">
          <template #tip>尺寸建议596px x 344px，图片大小不能超过 0.5M，支持图片格式jpg/jpeg/png</template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="方案详情头图" prop="photo">
        <UploadImg v-model:image-url="solutionForm.photo" width="135px" height="135px" :file-size="1" :disabled="viewStatus">
          <template #tip>尺寸建议7680px x 700px，图片大小不能超过 1M，支持图片格式jpg/jpeg/png</template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="方案简介" prop="desc">
        <el-input v-model="solutionForm.desc" :disabled="viewStatus" type="textarea" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="方案介绍" prop="introduce">
        <el-input v-model="solutionForm.introduce" :disabled="viewStatus" type="textarea" maxlength="200" show-word-limit />
      </el-form-item>
      <el-form-item label="方案展示图">
        <UploadImgs
          v-model:file-list="solutionForm.showImg"
          :limit="5"
          :file-size="1"
          :disabled="viewStatus"
          height="140px"
          width="140px"
        >
          <template #tip>尺寸建议2400px x 1350px，图片大小不能超过 1M，最多可上传5张图片 </template>
        </UploadImgs>
      </el-form-item>
      <el-form-item label="方案优势">
        <complex-content
          ref="advantageRef"
          prefixion="优势"
          :file-size="0.2"
          img-tips="建议图片尺寸192px x 192px，图片大小不能超过0.2M，支持图片格式jpg/jpeg/png"
          :view-control="viewStatus"
          v-model:dataList="solutionForm.advantage"
          @add-item="addAdvantage"
          @delete-item="deleteAdvantage"
        >
          <template #nameTips>最多可添加8个</template>
        </complex-content>
      </el-form-item>
      <el-form-item label="应用场景">
        <complex-content
          ref="sceneRef"
          prefixion="场景"
          :file-size="1"
          img-tips="建议图片尺寸 1448px x 786px，图片大小不能超过1M，支持图片格式jpg/jpeg/png"
          :view-control="viewStatus"
          v-model:dataList="solutionForm.applicationScene"
          @add-item="addApplicationScene"
          @delete-item="deleteApplicationScene"
        >
          <template #nameTips><div class="nowrap">最多可添加8个</div> </template>
        </complex-content>
      </el-form-item>
      <el-form-item label="客户案例">
        <complex-content
          ref="casesRef"
          prefixion="案例"
          :file-size="0.2"
          img-tips="建议图片尺寸 400px x 260px，图片大小不能超过0.2M，支持图片格式jpg/jpeg/png"
          :view-control="viewStatus"
          v-model:dataList="solutionForm.case"
          @add-item="addCase"
          @delete-item="deleteCase"
        >
          <template #nameTips><div class="nowrap">最多可添加8个</div> </template>
        </complex-content>
      </el-form-item>
      <div class="step-btn">
        <el-button plain @click="backtoList"> 返回 </el-button>
        <el-button type="primary" v-if="!viewStatus" @click="submitForm(solutionFormRef)"> 提交 </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="solutionManageListDetail">
import { reactive, ref, computed, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import complexContent from "./components/complexContent.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import UploadImg from "@/components/Upload/Img.vue";
import UploadImgs from "@/components/Upload/Imgs.vue";
import { useRoute, useRouter } from "vue-router";
import { solutioJudgeName } from "@/api/modules/solution";
import { useProductStore } from "@/stores/modules/product";
import { useDicStore } from "@/stores/modules/dictionaty";
import { solutionCategoryTree, solutionEdit, solutionSave } from "@/api/modules/solution";

const route = useRoute();
const router = useRouter();
const { pageType } = route.query;
const productStore = useProductStore();
const dictionaryStore = useDicStore();
const viewStatus = pageType === "view"; // 是否是查看详情，用来控制输入框的disabled
const solutionDetailObj = computed(() => productStore.solutionDetailObj);
const cooperateDic = computed(() => dictionaryStore.cooperateDic); // 合作类型字典
const providerList = computed(() => productStore.providerList); // 服务商列表

const solutionFormRef = ref<FormInstance>();
const advantageRef = ref();
const sceneRef = ref();
const casesRef = ref();
const providerRef = ref();
const solutionForm = reactive({
  solutionName: "",
  classify: [],
  cooperateType: "",
  providerId: "",
  mainUrl: "",
  photo: "",
  showImg: [],
  desc: "",
  introduce: "",
  advantage: [],
  applicationScene: [],
  case: []
});
const rules = reactive<FormRules>({
  solutionName: [{ required: true, message: "请输入解决方案名称", trigger: "blur" }],
  classify: [{ required: true, message: "请选择解决方案分类", trigger: "change" }],
  cooperateType: [{ required: true, message: "请选择合作类型", trigger: "change" }],
  providerId: [{ required: true, message: "请选择方案服务商", trigger: "change" }],
  mainUrl: [{ required: true, message: "请上传方案主图", trigger: "blur" }],
  photo: [{ required: true, message: "请上传方案详情头图", trigger: "blur" }],
  desc: [{ required: true, message: "请输入方案简介", trigger: "blur" }],
  introduce: [{ required: true, message: "请输入方案介绍", trigger: "blur" }]
});
const typeOptions = ref([]); // 解决方案分类
const soludtionDetailParam = ref({}); // 解决方案传参对象
const repetJudgeFlag = ref(true);

onMounted(() => {
  // 解决方案分类下拉列表
  getTypeTreeOption();
  // 产品服务商列表
  productStore.getProviderList();
  // 编辑和查看的回显：
  if (pageType !== "add") {
    Object.assign(solutionForm, {
      solutionName: solutionDetailObj.value.solutionName || "",
      classify: [solutionDetailObj.value.categoryIdL1, solutionDetailObj.value.categoryId], // 产品分类
      cooperateType: solutionDetailObj.value.cooperateType || "", // 合作类型
      providerId: solutionDetailObj.value.providerId || "",
      mainUrl: solutionDetailObj.value.mainUrl || "",
      photo: solutionDetailObj.value.iconUrl || "",
      desc: solutionDetailObj.value.description || "",
      introduce: solutionDetailObj.value.introduce || "",
      showImg: solutionDetailObj.value.solutionPics || [],
      advantage: solutionDetailObj.value.solutionAdvantage || [],
      applicationScene: solutionDetailObj.value.applyScenes || [],
      case: solutionDetailObj.value.customerCases || []
    });
    // 处理方案展示图（多张图片组件）
    solutionForm.showImg.forEach(item => {
      item.url = item.picUrl;
      item.name = item.id;
    });
  }
});

const getTypeTreeOption = () => {
  solutionCategoryTree().then((res: any) => {
    typeOptions.value = res.data;
    typeOptions.value.forEach((item: any = {}) => {
      item.value = item.id;
      item.label = item.name;
      if (item.children.length > 0) {
        item.children.forEach((child: any = {}) => {
          child.value = child.id;
          child.label = child.name;
        });
      } else {
        item.disabled = true;
      }
    });
  });
};
//新增优势
const addAdvantage = () => {
  if (solutionForm.advantage.length >= 8) {
    ElMessage.error("最多添加八个方案优势");
    return;
  }
  solutionForm.advantage.push({ title: "", description: "", picUrl: "" });
};
//删除优势
const deleteAdvantage = (index: number) => {
  solutionForm.advantage.splice(index, 1);
};
//应用场景
const addApplicationScene = () => {
  if (solutionForm.applicationScene.length >= 8) {
    ElMessage.error("最多添加八个场景");
    return;
  }
  solutionForm.applicationScene.push({ title: "", description: "", picUrl: "" });
};
const deleteApplicationScene = (index: number) => {
  solutionForm.applicationScene.splice(index, 1);
};
//客户案例
const addCase = () => {
  if (solutionForm.case.length >= 8) {
    ElMessage.error("最多添加八个案例");
    return;
  }
  solutionForm.case.push({ title: "", description: "", picUrl: "" });
};
const deleteCase = (index: number) => {
  solutionForm.case.splice(index, 1);
};

// 校验解决方案名称是否重复
const checkName = () => {
  let param = { solutionName: solutionForm.solutionName };
  if (pageType === "edit") {
    param.id = route.params.id;
  }
  solutioJudgeName(param).then((res: any) => {
    if (!res.data.canUse) {
      repetJudgeFlag.value = false;
      ElMessageBox.alert("解决方案重复，请重新命名", "提示", { type: "error" }).then(() => {});
    } else {
      repetJudgeFlag.value = true;
      // ElMessageBox.alert("解决方案名称查重校验通过", "提示", { type: "success" }).then(() => {});
    }
  });
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  if (!repetJudgeFlag.value) {
    ElMessageBox.alert("解决方案重复，请重新命名", "提示", { type: "error" }).then(() => {});
    return;
  }
  await formEl.validate((valid, fields) => {
    if (valid) {
      let advantagePromise = advantageRef.value?.ruleValidatePromise();
      let scenePromise = sceneRef.value?.ruleValidatePromise();
      let casesPromise = casesRef.value?.ruleValidatePromise();
      Promise.all([...advantagePromise, ...scenePromise, ...casesPromise])
        .then(result => {
          console.log(result);
          // 处理方案展示图
          const picsList = solutionForm.showImg?.map((item: any) => {
            return {
              picUrl: item.url,
              title: item.name,
              description: item.name // 因为暂无description,所以沟通后决定先传name值
            };
          });
          soludtionDetailParam.value = {
            solutionName: solutionForm.solutionName,
            categoryIdL1: solutionForm.classify[0], // 方案分类第一级
            categoryId: solutionForm.classify[1], // 方案分类第二级
            cooperateType: solutionForm.cooperateType, // 合作类型
            providerId: solutionForm.providerId, // 产品服务商id
            provider: providerRef.value.selectedLabel, // 产品服务商name
            mainUrl: solutionForm.mainUrl,
            iconUrl: solutionForm.photo,
            description: solutionForm.desc,
            introduce: solutionForm.introduce,
            solutionPics: picsList,
            solutionAdvantage: solutionForm.advantage,
            applyScenes: solutionForm.applicationScene,
            customerCases: solutionForm.case
          };
          if (pageType === "edit") {
            soludtionDetailParam.value.id = route.params.id;
          }
          const apiHandle = {
            add: solutionSave,
            edit: solutionEdit
          };
          apiHandle[pageType](soludtionDetailParam.value).then((res: any) => {
            ElMessage.success(res.msg);
            router.push("/solution/solutionList");
          });
        })
        .catch(error => {
          console.log(error);
        });
    } else {
      console.log("error submit!", fields);
    }
  });
};
const backtoList = () => {
  router.push("/solution/solutionList");
};
</script>

<style scoped lang="scss">
.el-form {
  width: 80%;
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
.step-btn {
  margin-top: 30px;
  margin-bottom: 10px;
  text-align: left;
  :deep(.el-button) {
    height: 36px;
    margin-right: 16px;
  }
}
</style>
