<template>
  <div class="card personalContainer" style="height: 100%">
    <div class="info-box">
      <div class="info-item">
        <span class="info-label">账号</span>
        <span class="info-text">{{ userInfo?.account || "-" }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">用户姓名</span>
        <span class="info-text">{{ userInfo?.userName || "-" }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">手机号码</span>
        <span class="info-text">{{ formatPhone(userInfo?.phone) || "-" }}</span>
        <el-button v-if="userInfo?.phone" type="text" @click="modifyPhoneOrEmail(ModifyType.phone)">修改</el-button>
      </div>
      <div class="info-item">
        <span class="info-label">用户邮箱</span>
        <span class="info-text">{{ formatEmail(userInfo?.email) || "-" }}</span>
        <el-button v-if="userInfo?.email" type="text" @click="modifyPhoneOrEmail(ModifyType.email)">修改</el-button>
      </div>
      <div class="info-item">
        <span class="info-label">账号类型</span>
        <span class="info-text">
          <el-tag>{{ userInfo?.userType == 1 ? "内部用户" : "合作商用户" || "-" }}</el-tag>
        </span>
      </div>
      <div class="info-item">
        <span class="info-label">密码</span>
        <span class="info-text">******</span>
        <el-button type="text" @click="modifyPhoneOrEmail(ModifyType.password)">修改</el-button>
      </div>
    </div>
    <PasswordDialog ref="passwordRef"></PasswordDialog>
    <PhoneOrEmailDialog ref="phoneRef" :modify-info="modifyInfo"></PhoneOrEmailDialog>
  </div>
</template>

<script setup lang="ts" name="personal">
import { ref, computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
import PasswordDialog from "./components/PasswordDialog.vue";
import PhoneOrEmailDialog from "./components/PhoneOrEmailDialog.vue";
import { formatEmail, formatPhone } from "@/utils/tools";
import { ModifyType } from "./personalEnum";
// import { userType } from "@/utils/serviceDict";

const userStore = useUserStore();
const passwordRef = ref<InstanceType<typeof PasswordDialog> | null>(null);
const phoneRef = ref<InstanceType<typeof PhoneOrEmailDialog> | null>(null);
const userInfo = computed(() => userStore.userInfo);

const modifyInfo = ref({
  modifyType: ModifyType.phone
});
// const modifyWord = () => {
//   passwordRef.value?.openDialog();
// };

const modifyPhoneOrEmail = (type = ModifyType.phone) => {
  modifyInfo.value = {
    modifyType: type
  };
  phoneRef.value?.openDialog();
};
</script>

<style lang="scss" scoped>
.personalContainer {
  padding: 32px;
}
.info-box {
  padding: 0;
  font-size: var(--el-font-size-base);
  .info-item {
    margin-bottom: 40px;
    .info-label {
      display: inline-block;
      width: 80px;
      color: #73787f;
      text-align: left;
    }
    .info-text {
      display: inline-block;
      width: 300px;
      color: #3a3a3d;
    }
  }
}
</style>
