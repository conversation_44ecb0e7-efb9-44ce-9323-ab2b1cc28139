<template>
  <BaseDrawer ref="drawerRef" size="75%" @sure-callback="sureCallback">
    <div class="table-box">
      <pro-table
        ref="proTable"
        title="客户列表"
        :columns="columns"
        :request-api="getTableList"
        :request-auto="false"
        :tool-button="false"
      ></pro-table>
    </div>
  </BaseDrawer>
</template>

<script setup lang="tsx" name="stepOne">
import { ref, nextTick } from "vue";
import BaseDrawer from "@/components/BaseDrawer/index.vue";
import { ElMessage } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getPortalUserList } from "@/api/modules/system";
import { userSource, userStatus, verifyLevel } from "@/utils/serviceDict";
import { System } from "@/api/interface/system";
import CustomTag from "@/components/CustomTag/index.vue";

let emits = defineEmits(["selectedList"]);
const drawerRef = ref<{ acceptParams: (params: any) => void } | null>(null);
const proTable = ref<ProTableInstance>();

const showDrawer = () => {
  const params = {
    title: "选择客户",
    sureText: "选择",
    show: true,
    hasSure: true,
    hasCancel: true
  };
  drawerRef.value?.acceptParams(params);
  nextTick(() => {
    proTable.value?.getTableList();
  });
};
const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.status = 1;
  return getPortalUserList(newParams);
};
// 表格配置项
const columns: ColumnProps<System.PortalUserParams>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "account", label: "昊算账号", width: 100, search: { el: "input" } },
  { prop: "phone", label: "手机号码", search: { el: "input" } },
  { prop: "email", label: "用户邮箱", search: { el: "input" } },
  { prop: "userSource", label: "客户来源", width: 100, enum: userSource },
  { prop: "areaName", label: "所在城市" },
  { prop: "verifyLevel", label: "认证级别", width: 100, enum: verifyLevel },
  { prop: "company", label: "企业名称" },
  {
    prop: "status",
    label: "状态",
    width: 90,
    enum: userStatus,
    render: scope => {
      return (
        <CustomTag
          type={scope.row.status === 1 ? "success" : ""}
          status={String(scope.row.status)}
          label={scope.row.status === 1 ? "启用" : "停用"}
        ></CustomTag>
      );
    }
  }
];
const sureCallback = () => {
  if (proTable.value?.selectedList.length > 1) {
    ElMessage.error("只能选择1个客户");
  } else {
    drawerRef.value?.acceptParams({ show: false });
    if (proTable.value?.selectedList.length === 0) return;
    emits("selectedList", proTable.value?.selectedList);
  }
};
defineExpose({
  showDrawer
});
</script>

<style scoped lang="scss">
:deep(.el-scrollbar) {
  min-height: 120px;
}
.el-form {
  width: 90%;
  .text-center {
    text-align: center;
  }
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
</style>
