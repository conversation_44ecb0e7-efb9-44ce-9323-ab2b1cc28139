/*
 * @Author: <EMAIL>
 * @Date: 2023-09-06 09:31:29
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-06 14:53:44
 * @Description: file content
 */
import * as ProductClassify from "@/api/interface/productClassify";
import http from "@/api";

/**
 * @name 资源池
 */
// 资源池列表
export const queryResourcePools = (params: any) => {
  return http.post(`/cnud-product/skyVault/queryResourcePools`, params); // 正常 post json 请求  ==>  application/json
};

// 删除资源池
export const removeResourcePool = (params: any) => {
  return http.post(`/cnud-product/skyVault/removeResourcePool`, params); // 正常 post json 请求  ==>  application/json
};

// 新增资源池
export const addResourcePool = (params: any) => {
  return http.post(`/cnud-product/skyVault/addResourcePool`, params); // 正常 post json 请求  ==>  application/json
};
// 修改资源池
export const editResourcePool = (params: any) => {
  return http.post(`/cnud-product/skyVault/editResourcePool`, params);
};
// 查询地区
export const queryRegion = (params: any) => {
  return http.post(`/cnud-product/skyVault/queryRegion`, params); // 正常 post json 请求  ==>  application/json
};
// 查询详情
export const queryResourcePoolDetail = (params: any) => {
  return http.post(`/cnud-product/skyVault/queryResourcePool`, params); // 正常 post json 请求  ==>  application/json
};
/**
 * @name 云账号
 */
// 查询云账号
export const queryCloudAccounts = (params: any) => {
  return http.post(`/cnud-product/skyVault/queryCloudAccounts`, params); // 正常 post json 请求  ==>  application/json
};

// 新增云账号
export const addCloudAccount = (params: any) => {
  return http.post(`/cnud-product/skyVault/addCloudAccount`, params); // 正常 post json 请求  ==>  application/json
};
// 编辑云账号
export const editCloudAccount = (params: any) => {
  return http.post(`/cnud-product/skyVault/editCloudAccount`, params); // 正常 post json 请求  ==>  application/json
};
// 删除云账号
export const removeCloudAccount = (params: any) => {
  return http.post(`/cnud-product/skyVault/removeCloudAccount`, params); // 正常 post json 请求  ==>  application/json
};
// 通过云平台查询资源池
export const queryResourcePoolByCloudProvider = (params: any) => {
  return http.post(`/cnud-product/skyVault/queryResourcePoolByCloudProvider`, params); // 正常 post json 请求  ==>  application/json
};
// 检查AKSK
export const executeCloudAccountVerify = (params: any) => {
  return http.post(`/cnud-product/skyVault/executeCloudAccountVerify`, params); // 正常 post json 请求  ==>  application/json
};
