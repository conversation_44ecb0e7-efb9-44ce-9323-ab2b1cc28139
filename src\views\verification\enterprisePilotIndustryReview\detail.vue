<template>
  <div class="card pl32">
    <div class="infos">
      <div class="header-title">
        审核状态：<span :class="`status-${detailData.auditStatus}`">{{
          useDictLabel(requireReleaseStatus, detailData.auditStatus)
        }}</span>
      </div>
      <el-form ref="formRef" label-width="110px" class="demo-reviewForm" :label-position="'left'">
        <el-row>
          <el-col v-for="item in baseInfo" :key="item.key" :span="8">
            <div class="info-item">
              <span class="info-label">{{ item.label }}:</span>
              <span v-if="item.key === 'releaseType'" class="info-text">{{ isRequireDetail ? "需求发布" : "需求应答" }}</span>
              <span v-else-if="item.key === 'auditStatus'" class="info-text">{{
                useDictLabel(requireReleaseStatus, detailData.auditStatus || "--")
              }}</span>
              <span v-else class="info-text">{{ detailData[item.key] || "--" }}</span>
            </div>
          </el-col>
          <el-col :span="24" class="header-title">审核信息</el-col>
          <el-col :span="24" class="pl20 mb20">需求方</el-col>
          <el-col :span="24" class="pl20">
            <el-row>
              <el-col v-for="item in demanderInfo" :key="item.key" :span="item.span || 6">
                <div class="info-item">
                  <span class="info-label">{{ item.label }}:</span>
                  <span v-if="item.key === 'demandType'" class="info-text">{{
                    useDictLabel(requireType, detailData.demandType || "--")
                  }}</span>
                  <span v-else-if="item.key === 'constructionAddress'" class="info-text">{{
                    useDictLabel(demandConstructionAddress, detailData.constructionAddress || "--")
                  }}</span>
                  <span v-else class="info-text">{{ detailData[item.key] || "--" }}</span>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <template v-if="!isRequireDetail">
            <el-col :span="24" class="pl20 mb20">供应方</el-col>
            <el-col :span="24" class="pl20">
              <el-row>
                <el-col v-for="item in supplierInfo" :key="item.key" :span="item.span || 6">
                  <div class="info-item">
                    <span class="info-label">{{ item.label }}:</span>
                    <span class="info-text">{{ detailData[item.key] || "--" }}</span>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </template>
          <el-col class="mt20">
            <Examine
              :verification-id="'id'"
              :audit-msg="auditMsg"
              :is-disabled="isReadOnly"
              :is-show-operate-btn="!isReadOnly"
              @on-confirm="onConfirm"
              @on-back="onBack"
            ></Examine>
          </el-col>
          <div class="last-next">
            <!-- 因为列表是时间逆序返回：after 对应上一条，brfore 对应下一条 -->
            <el-col v-if="beforeAndAfterData.afterName" @click="toView(beforeAndAfterData.afterId)">
              上一条 {{ beforeAndAfterData.afterName }}
            </el-col>
            <el-col v-else class="disable">上一条</el-col>
            <el-col v-if="beforeAndAfterData.beforeName" @click="toView(beforeAndAfterData.beforeId)">
              下一条 {{ beforeAndAfterData.beforeName }}
            </el-col>
            <el-col v-else class="disable">下一条</el-col>
          </div>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts" name="enterprisePilotIndustryReviewDetail">
import { ref, reactive, computed, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import Examine from "@/views/verification/examine.vue";
import { closeTabItem } from "@/utils/closeTab";
import { useDictLabel } from "@/hooks/useDict";
import { useDicStore } from "@/stores/modules/dictionaty";
import { getRequireDetail, getSchemeDetail, getBeforeAndAfter, reviewRequire, reviewScheme } from "@/api/modules/requireRelease";
import { OperateType, TabType, AuditStatusEnum } from "./type";

const route = useRoute();
const router = useRouter();
const dictionaryStore = useDicStore();
const isRequireDetail = route.query.pageType === TabType.REQUIRE;
const isReadOnly = ref(false);
isReadOnly.value = route.query.operateType === OperateType.DETAIL;
// 审核状态
const requireReleaseStatus: any = computed(() => dictionaryStore.requireReleaseStatus);
// 需求类型
const requireType: any = computed(() => dictionaryStore.requireType);
// 实施地址
const demandConstructionAddress: any = computed(() => dictionaryStore.demandConstructionAddress);

const baseInfo = [
  {
    label: "发布类型",
    key: "releaseType"
  },
  {
    label: "提交人员",
    key: isRequireDetail ? "creatorName" : "solutionCreatorName"
  },
  {
    label: "提交时间",
    key: "createTime"
  },
  {
    label: "审核状态",
    key: "auditStatus"
  },
  {
    label: "审核人",
    key: "auditorName"
  },
  {
    label: "审核时间",
    key: "auditTime"
  }
];
const demanderInfo = [
  {
    label: "企业名称",
    key: isRequireDetail ? "companyName" : "demandCompanyName"
  },
  {
    label: "企业地址",
    key: isRequireDetail ? "companyAddress" : "demandCompanyAddress"
  },
  {
    label: "联系人",
    key: isRequireDetail ? "contactName" : "demandContactName"
  },
  {
    label: "联系人电话",
    key: isRequireDetail ? "phone" : "demandPhone"
  },
  {
    label: "需求名称",
    key: isRequireDetail ? "name" : "demandName"
  },
  {
    label: "需求类型",
    key: "demandType"
  },
  {
    label: "截止时间",
    key: "demandEndTime"
  },
  {
    label: "实施地址",
    key: "constructionAddress"
  },
  {
    label: "需求描述",
    key: "description",
    span: 24
  }
];
const supplierInfo = [
  {
    label: "企业名称",
    key: "solutionCompanyName"
  },
  {
    label: "企业地址",
    key: "solutionCompanyAddress"
  },
  {
    label: "联系人",
    key: "solutionContactName"
  },
  {
    label: "联系人电话",
    key: "solutionPhone"
  },
  {
    label: "方案名称",
    key: "solutionName",
    span: 24
  },
  {
    label: "方案描述",
    key: "solutionDescription",
    span: 24
  }
];
const detailData = reactive<any>({
  auditStatus: "wait"
});
const auditMsg = ref();

const getDetail = async () => {
  try {
    const res = isRequireDetail ? await getRequireDetail({ id: route.query.id }) : await getSchemeDetail({ id: route.query.id });
    Object.assign(detailData, res.data);
    auditMsg.value = res.data?.auditMsg;
    if (!isReadOnly.value) {
      isReadOnly.value = detailData.auditStatus !== AuditStatusEnum.WAIT;
    }
  } catch (error) {}
};

const onConfirm = async (value: any) => {
  auditMsg.value = value.auditRemark;
  const auditStatus = value.type == "pass" ? "pass" : "refuse";
  const params = {
    id: route.query.id,
    auditStatus: auditStatus,
    auditMsg: auditMsg.value
  };
  try {
    const res = isRequireDetail ? await reviewRequire(params) : await reviewScheme(params);
    ElMessage({
      message: value.type == "pass" ? "审核通过成功!" : "审核不通过完成!",
      type: "success"
    });
    closeTabItem(route);
    onBack();
  } catch (error) {}
};
const onBack = () => {
  router.push("/verification/enterprisePilotIndustryReview");
};

const beforeAndAfterData = reactive<any>({});
const getBeforeAndAfterRecord = async () => {
  try {
    const res = await getBeforeAndAfter({ id: route.query.id, type: route.query.pageType });
    Object.assign(beforeAndAfterData, res.data);
  } catch (error) {}
};
const toView = (id: string) => {
  router.push(
    `/verification/enterprisePilotIndustryReview/detail?id=${id}&operateType=${route.query.operateType}&pageType=${route.query.pageType}`
  );
};

onBeforeMount(() => {
  dictionaryStore.requireReleaseStatus.length === 0 && dictionaryStore.getRequireReleaseStatus();
  dictionaryStore.requireType.length === 0 && dictionaryStore.getRequireType();
  dictionaryStore.demandConstructionAddress.length === 0 && dictionaryStore.getDemandConstructionAddress();
  getDetail();
  getBeforeAndAfterRecord();
});
</script>
<style scoped lang="scss">
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
  .status-wait {
    color: #ffaa00;
  }
  .status-pass {
    color: #0052d9;
  }
  .status-refuse {
    color: #e5302f;
  }
}
.infos {
  margin-left: 10px;
  .info-item {
    margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      margin-right: 10px;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: pre-wrap;
    }
  }
  .demo-reviewForm.el-form {
    .el-form-item {
      :deep(.el-form-item__label) {
        color: #73787f;
      }
    }
  }
}
.last-next {
  margin-top: 10px;
  font-size: 12px;
  color: var(--el-color-primary);
  cursor: pointer;
  .disable {
    color: #c0c4cc;
    cursor: auto;
  }
}
</style>
