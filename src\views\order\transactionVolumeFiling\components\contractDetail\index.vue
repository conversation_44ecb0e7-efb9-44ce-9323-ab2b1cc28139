<template>
  <el-dialog v-model="dialogVisible" title="合同详情" align-center width="600px">
    <div class="dialog-content">
      <div class="info-box">
        <div class="info-item">
          <p class="info-item-label">合同编码</p>
          <p class="info-item-value">{{ contractInfo.contractName || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">客户名称</p>
          <p class="info-item-value">{{ contractInfo.contractId || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">供应商编码</p>
          <p class="info-item-value">{{ contractInfo.supplierId || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">供应商名称</p>
          <p class="info-item-value">{{ contractInfo.supplierName || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">客户编码</p>
          <p class="info-item-value">{{ contractInfo.customerId || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">公司名称</p>
          <p class="info-item-value">{{ contractInfo.companyName || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">合同金额</p>
          <p class="info-item-value">{{ contractInfo.price || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">备注</p>
          <p class="info-item-value">{{ contractInfo.detail || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">合同附件</p>
          <p
            class="info-item-value contract-file"
            @click="downloadAttach(contractInfo.contractFileId)"
            v-if="contractInfo.contractFileName && contractInfo.contractFileId"
          >
            {{ contractInfo.contractFileName }}
          </p>
          <p class="info-item-value" v-else>-</p>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { downloadAttachFile, queryContractList } from "@/api/modules/transactionVolumeFiling";
import { useDownload } from "@/hooks/useDownload";

const dialogVisible = ref(false);

const contractInfo = ref({
  contractName: "",
  contractId: "",
  supplierId: "",
  supplierName: "",
  customerId: "",
  companyName: "",
  price: "",
  detail: "",
  contractFileName: ""
});

const queryDetail = (contractId: string) => {
  const queryData = {
    id: contractId
  };
  queryContractList(queryData)
    .then(res => {
      if (res?.data?.records?.[0]) {
        contractInfo.value = {
          ...contractInfo.value,
          ...res.data.records[0]
        };
      }
    })
    .catch(() => {});
};

const show = (row: any) => {
  if (!row?.contractId) {
    return;
  }
  dialogVisible.value = true;
  contractInfo.value = row;
  queryDetail(row.contractId);
};

const close = () => {
  dialogVisible.value = false;
};

const downloadAttach = (id: string) => {
  const queryData = {
    id
  };
  useDownload(downloadAttachFile, queryData);
};

defineExpose({
  show
});
</script>

<style lang="scss" scoped>
.dialog-content {
  .info-box {
    .info-item {
      display: flex;
      align-items: flex-start;
      .info-item-label {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 136px;
        text-align: right;
        &::after {
          content: ":";
        }
      }
      .info-item-value {
        flex: 1;
        width: 0;
        margin-left: 10px;
      }
      .contract-file {
        color: var(--el-color-primary);
        cursor: pointer;
      }
    }
  }
}
</style>
