<template>
  <div class="table-box">
    <!--request-auto="false" 不自动调接口-->
    <ProTable
      ref="proTable"
      title="产品列表"
      :request-auto="false"
      :tool-button="false"
      :columns="columns"
      :request-api="getTableList"
    >
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader="scope">
        <el-button v-auth="'add'" plain :icon="Plus" @click="creatProdutct"> 新增 </el-button>
        <el-button v-auth="'tag'" plain :disabled="!scope.isSelected" @click="creatLabelRelation">
          <i class="iconfont2 icon-dabiaoqian"></i>
          打标签
        </el-button>
        <!--<el-button plain @click="uploadFile">-->
        <!-- <i class="iconfont2 icon-daoru"></i>-->
        <!-- 导入-->
        <!--</el-button>-->
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
        <el-button v-auth="'PromotionWord'" plain :disabled="!scope.isSelected" @click="dialogPromotionWord = true">
          <i class="iconfont2 icon-xiaoshouxinxi1"></i>
          推广词配置
        </el-button>
        <el-button
          v-auth="'delete'"
          plain
          :icon="Delete"
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds, scope.selectedList)"
        >
          删除
        </el-button>
      </template>
      <template #provider="{ row }">
        {{ row.provider || "--" }}
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <!--<el-button type="primary" link @click="toView(scope.row)"> 查看 </el-button>-->
        <!--<el-button type="primary" link @click="putOn(scope.row)">-->
        <!--{{ scope.row.publishStatus === 2 ? "下架" : "上架" }}-->
        <!--</el-button>-->
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toDetail(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'shelf'">
          <el-tooltip :content="scope.row.publishStatus === 2 ? '下架' : '上架'" placement="top">
            <i
              :class="['iconfont2 opera-icon', scope.row.publishStatus === 2 ? 'icon-lineicon_download' : 'icon-lineicon_upload']"
              @click="putOnOrOff(scope.row)"
            ></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="deletePro(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <ImportExcel ref="dialogRef" />
    <label-drawer ref="labelRef" @selected-list-ids="getSelectedListIds"></label-drawer>
    <EditSortDialog
      v-if="saveItem && showEditDialog"
      v-model:show-dialog="showEditDialog"
      :sort="saveItem.sort"
      @save="saveSort"
    />
    <applyModal ref="applyModalRef" @on-submit="handleSubmit" />

    <!-- 推广词 -->
    <el-dialog
      destroy-on-close
      v-model="dialogPromotionWord"
      title="推广词配置"
      width="500"
      @close="handleCloseDialog"
      :before-close="handleCloseDialog"
    >
      <div class="flex gap-4 mb-4">
        <span style="margin-right: 10px">推广词</span>
        <el-input v-model.trim="keyWord" maxlength="5" style="width: 240px" placeholder="请输入推广词" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogPromotionWord = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitKey"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx" name="productManageList">
import { ref, reactive, onMounted, onActivated, computed } from "vue";
import { useRouter } from "vue-router";
import { useHandleData } from "@/hooks/useHandleData";
import { useDownload } from "@/hooks/useDownload";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import labelDrawer from "./components/labelDrawer.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Plus, Delete, Download, Notebook } from "@element-plus/icons-vue";
import { productStatus } from "@/utils/serviceDict";
import { useProductStore } from "@/stores/modules/product";
import { useDicStore } from "@/stores/modules/dictionaty";
import {
  getProductList,
  putOnOff,
  deleteProduct,
  batchAddLabel,
  exportProduct,
  productUpdateSort,
  updatePromotionWord
} from "@/api/modules/product";
import { getTypeTree } from "@/api/modules/productClassify";
import { Product } from "@/api/interface/product";
import CustomTag from "@/components/CustomTag/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import EditSortDialog from "@/components/EditSortDialog/index.vue";
import applyModal from "./components/applyModal.vue";

const { BUTTONS } = useAuthButtons();

const router = useRouter();
const productStore = useProductStore();
const dictionaryStore = useDicStore();
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();
const labelRef = ref();
const showEditDialog = ref<boolean>(false);
const saveItem = ref<any>({});
const applyModalRef = ref();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
// const initParam = reactive({ type: 1 });

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total && pageNum && pageSize 这些字段，那么你可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行

const keyWord = ref("");

const dialogPromotionWord = ref(false);

const handleCloseDialog = () => {
  dialogPromotionWord.value = false;
  keyWord.value = "";
};

const handleSubmitKey = () => {
  const ids: Array<string> = proTable.value?.selectedListIds ?? [];
  if (ids.length > 0) {
    updatePromotionWord({ ids, promotionWordSave: keyWord.value })
      .then(result => {
        ElMessage({
          message: "配置成功",
          type: "success"
        });
        proTable.value?.getTableList();
        handleCloseDialog();
      })
      .catch(err => {
        ElMessage({
          message: "配置失败",
          type: "warning"
        });
      });
  }
};

onMounted(() => {
  // 先获取字典
  dictionaryStore.queryProdCustomerType(); // 产品销售对象
  dictionaryStore.getCooperateDicList(); // 合作类型
  dictionaryStore.getOrderPayDicList(); // 支付方式
  dictionaryStore.getDeliveryDicList(); // 交付方式
  dictionaryStore.getJumpTypeDicList(); // 获取跳转类型字典
  dictionaryStore.getProductOpenMethodDicList(); // 获取产品-产品开通接口字典

  dictionaryStore.getskuTypeDicList(); //规格类型字典
  // 产品服务商列表
  // productStore.getProviderList();
  // 然后
  // proTable.value?.getTableList();
});
// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  proTable.value?.getTableList();
});
// 合作类型字典-enum
const cooperateDic: any = computed(() => dictionaryStore.cooperateDic);
// 产品服务商列表
// const providerList: any = computed(() => productStore.providerList);
// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
  newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
  delete newParams.createTime;
  if (newParams.categoryIdList?.length > 0) {
    newParams.categoryId = newParams.categoryIdList[newParams.categoryIdList.length - 1];
    delete newParams.categoryIdList;
  }
  newParams.descs = "createTime";
  return getProductList(newParams);
};

// 页面按钮权限（按钮权限既可以使用 hooks，也可以直接使用 v-auth 指令，指令适合直接绑定在按钮上，hooks 适合根据按钮权限显示不同的内容）
// const { BUTTONS } = useAuthButtons();

// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "productCode", label: "产品编码", width: 100, search: { el: "input" } },
  { prop: "productName", label: "产品名称", width: 150, search: { el: "input" } },
  { prop: "cooperateType", label: "合作类型", width: 90, enum: cooperateDic, search: { el: "select" } },
  { prop: "provider", label: "产品服务商", width: 100 },
  {
    prop: "categoryName",
    label: "产品分类",
    width: 180,
    enum: getTypeTree,
    isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格
    search: { el: "cascader", key: "categoryIdList" },
    fieldNames: {
      value: "id",
      label: "name"
    }
  },
  {
    prop: "publishStatus",
    label: "上架状态",
    width: 110,
    search: { el: "select" },
    // 字典，可格式化单元格内容，还可以作为搜索框的下拉选项（字典可以为 API 请求函数）
    enum: productStatus, // 产品上架状态字典
    sortable: true, // 排序功能开启
    render: scope => {
      return (
        // <el-tag type={scope.row.publishStatus === 2 ? "success" : "danger"}>
        //   {scope.row.publishStatus === 2 ? "上架" : "下架"}
        // </el-tag>
        <CustomTag
          type={scope.row.publishStatus === 2 ? "success" : ""}
          status={String(scope.row.publishStatus)}
          label={scope.row.publishStatus === 2 ? "上架" : "下架"}
        ></CustomTag>
      );
    }
  },
  {
    prop: "sort",
    label: "排序",
    align: "left",
    render: (scope: any) => {
      return (
        <span class={"sort-btn"} onClick={() => setShowEditDialog(scope.row)}>
          {scope.row.sort}
        </span>
      );
    }
  },
  {
    prop: "promotionWord",
    label: "推广词",
    width: 85
  },
  { prop: "creatorName", label: "创建人员", width: 85 },
  {
    prop: "createTime",
    label: "创建时间",
    width: 170,
    sortable: true,
    search: {
      el: "date-picker",
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" }
      // defaultValue: []
    }
  },
  {
    prop: "publishTime",
    label: "上架时间",
    // width: 170,
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 200 }
];

// 批量删除
const batchDelete = async (
  ids: string[],
  selectedList: {
    [key: string]: any;
  }[]
) => {
  console.log("selectedList", selectedList);
  if (selectedList.length) {
    let publishItem = selectedList.find(o => o.publishStatus === 2);
    if (publishItem) {
      ElMessageBox.alert("存在上架状态记录，请先下架后再操作。", "提示", { type: "warning" }).then(() => {});
      return;
    }
    let obj = selectedList.find(o => o.verifyStatus === 1);
    if (obj) {
      ElMessageBox.alert("存在待审核记录，不支持该操作", "提示", { type: "warning" }).then(() => {});
      return;
    }
  }
  await useHandleData(deleteProduct, { ids: ids.join(",") }, "删除所选产品");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
const deletePro = async (row: any = {}) => {
  if (row.publishStatus === 2) {
    ElMessageBox.alert("上架状态不可删除，请先下架后再操作。", "提示", { type: "warning" }).then(() => {});
    return;
  }
  // 产品如有“待审核”状态的审核记录时，则该产品不允许编辑、上架、下架、删除操作
  if (row.verifyStatus === 1) {
    ElMessageBox.alert("该产品存在待审核记录，不支持该操作", "提示", { type: "warning" }).then(() => {});
    return;
  }
  await useHandleData(deleteProduct, { ids: row.id }, `删除<${row.productName}>`);
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const putOnOrOff = (row: any = {}) => {
  // 产品如有“待审核”状态的审核记录时，则该产品不允许编辑、上架、下架、删除操作
  if (row.verifyStatus === 1) {
    ElMessageBox.alert("该产品存在待审核记录，不支持该操作", "提示", { type: "warning" }).then(() => {});
    return;
  }
  const action = row.publishStatus === 2 ? "off" : "on";
  applyModalRef.value.handleOpen(action, row);
};
// 上下架操作
const putOn = async (row: any = {}) => {
  let handlePublishStr = row.publishStatus === 2 ? "下架" : "上架";
  let handlePublishCode = row.publishStatus === 2 ? 1 : 2;
  await useHandleData(
    putOnOff,
    { id: row.id, publishStatus: handlePublishCode, applicationMessage: row?.applicationMessage || "" },
    `${handlePublishStr} <${row.productName}>`
  );
  applyModalRef.value.handleClose();
  proTable.value?.getTableList();
};

const handleSubmit = (value: any) => {
  putOn({ ...value });
};

// 导出产品列表
const downloadFile = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
  newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
  delete newParams.createTime;
  ElMessageBox.confirm("确认导出产品列表?", "提示", { type: "warning" }).then(() => useDownload(exportProduct, newParams));
};

// 产品编辑页
const toDetail = (row: any = {}) => {
  // if (row.publishStatus === 2) {
  //   ElMessageBox.alert("上架状态不可编辑，请先下架后再操作。", "提示", { type: "warning" }).then(() => {});
  //   return;
  // } // 修改为上架也可编辑
  // 产品如有“待审核”状态的审核记录时，则该产品不允许编辑、上架、下架、删除操作
  if (row.verifyStatus === 1) {
    ElMessageBox.alert("该产品存在待审核记录，不支持该操作", "提示", { type: "warning" }).then(() => {});
    return;
  }
  productStore.getProductDetail({ id: row.id }).then(res => {
    router.push(`/productManage/list/detail/${row.id}?pageType=edit`);
  });
};
const toView = (row: any = {}) => {
  productStore.getProductDetail({ id: row.id }).then(res => {
    router.push(`/productManage/list/detail/${row.id}?pageType=view`);
  });
};
const creatProdutct = () => {
  // 新增产品先重置中间参数
  productStore.resetSendProductDetailParam();
  router.push(`/productManage/list/detail/add?pageType=add`);
};
// 批量打标签
const creatLabelRelation = () => {
  labelRef.value?.showLabel();
};
const getSelectedListIds = async (data: string[]) => {
  const { code } = await batchAddLabel({ ids: proTable.value?.selectedListIds, tagIds: data });
  if (code === 200) {
    ElMessage.success("批量打标签成功");
    proTable.value?.clearSelection();
  }
};
const setShowEditDialog = (data: any) => {
  saveItem.value = data;
  showEditDialog.value = true;
};
const saveSort = async (sort: number) => {
  await productUpdateSort({ id: saveItem.value.id, sort: sort });
  ElMessage.success("更改排序成功！");
  proTable.value?.getTableList();
};
</script>

<style lang="scss">
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
