<template>
  <div class="page-order-detail">
    <OrderStatus :order-status="orderInfo.status" :status-remark="statusRemark" v-if="orderInfo.status" />
    <!-- 客户信息 -->
    <CustomerInfo :customer-info="customerInfo" :phone="orderInfo?.purchasePhone" />
    <!-- 订单详情 -->
    <OrderFormInfo :order-info="orderInfo" />
    <!-- 客户经理信息 -->
    <InfoTemplate v-if="targetVisible" :data-info="managerInfo" />
    <!-- 产品信息-->
    <OrderTable
      :data="tableData"
      :audit-tag-type="auditTagType"
      :total-price="orderInfo.prePayFee"
      :target-visible="targetVisible"
    />
    <!-- 生效模式 -->
    <div class="info-box card" v-show="orderInfo.effectTime && orderInfo.endTime">
      <div class="box-title">
        <div class="title">生效模式</div>
      </div>
      <div class="infos">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">生效模式：</span>
              <span class="info-text"> {{ orderInfo.effectType === "0" ? "立即生效" : "自定义有效期" }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">生效时间：</span>
              <span class="info-text">{{ orderInfo.effectTime }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">到期时间：</span>
              <span class="info-text">{{ orderInfo.endTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 折扣价 -->
    <DiscountInfo
      :show="'view'"
      :table-data="discountData"
      :total-price="orderInfo.prePayFee"
      :pay-fee="orderInfo.payFee"
      v-if="targetVisible"
    />

    <!-- 合同及其他附件 -->
    <div class="info-box card" v-if="targetVisible">
      <div class="box-title">
        <div class="title">合同及其他附件</div>
      </div>
      <div class="file-list">
        <div class="file-list-item" v-for="item in fileList" :key="item.id">
          <span>
            <el-icon color="#73787f"><Document /></el-icon>
          </span>
          <a :download="item.attrName" :href="item.attrValue">{{ item.attrName }}</a>
        </div>
      </div>
    </div>
    <!-- 其它信息 -->
    <div
      class="info-box card"
      v-show="orderInfo?.otherRemark !== '' && orderInfo?.otherRemark !== null && orderInfo?.orderType !== '3'"
    >
      <div class="box-title">
        <div class="title">其它信息</div>
      </div>
      <el-col :span="24">
        <div class="info-item">
          <span class="info-label default">备注：</span>
          <span class="info-text">{{ orderInfo?.otherRemark }}</span>
        </div>
      </el-col>
    </div>
    <!-- 审核信息 -->
    <ReviewInfo :order-info="orderInfo" v-if="orderInfo.auditStatus === '3' || orderInfo.auditStatus === '4'" />
    <!--  账号信息 -->

    <!-- auditTagType === 1时候是点审核按进来，auditTagType === 0时详情进来 -->
    <template v-if="auditTagType === 1 && orderInfo.auditStatus === '2'">
      <examine :order-id="orderInfo.parentOrderNo" @on-change="handleChange"></examine>
    </template>

    <!-- 账号信息 -->
    <template v-if="queryPageType === 'activate' || orderInfo.isManualOrder">
      <AccountInfoTable
        :phone="orderInfo?.purchasePhone"
        :customer-account="orderInfo.customerAccount"
        :parent-order-no="orderInfo.parentOrderNo"
        :is-manual-order="orderInfo.isManualOrder"
      />
      <!-- 处理信息 -->
      <DealWithInfo v-if="queryPageType === 'activate'" :order-info="orderInfo" @on-change="handleChange"></DealWithInfo>
      <!-- 处理信息详情 -->
      <div class="info-box card" v-if="orderInfo.isManualOrder && queryPageType !== 'activate'">
        <div class="box-title">
          <div class="title">处理信息</div>
        </div>
        <div class="infos">
          <el-row>
            <el-col :span="8">
              <div class="info-item">
                <span class="info-label">处理状态：</span>
                <span class="info-text">{{ orderInfo.operatorName ? "报竣" : "未处理" }} </span>
              </div>
            </el-col>
            <el-col :span="8" :push="1">
              <div class="info-item">
                <span class="info-label">处理人：</span>
                <span class="info-text">{{ orderInfo.operatorName }}</span>
              </div>
            </el-col>
            <el-col :span="8" :push="2">
              <div class="info-item">
                <span class="info-label">处理时间：</span>
                <span class="info-text">{{ orderInfo.operatorTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="infos">
          <el-row>
            <el-col :span="8">
              <div class="info-item">
                <span class="info-label">实际生效模式：</span>
                <span class="info-text"> {{ orderInfo.effectType === "0" ? "立即生效" : "自定义有效期" }}</span>
              </div>
            </el-col>
            <el-col :span="8" :push="1">
              <div class="info-item">
                <span class="info-label">实际生效时间：</span>
                <span class="info-text">{{ orderInfo.effectTime }}</span>
              </div>
            </el-col>
            <el-col :span="8" :push="2">
              <div class="info-item">
                <span class="info-label">实际到期时间：</span>
                <span class="info-text">{{ orderInfo.endTime }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="info-item">
          <span class="info-label default">处理意见：</span>
          <span class="info-text">{{ orderInfo?.operateRemark }}</span>
        </div>
      </div>
    </template>

    <div class="order-info" v-if="auditTagType !== 1 || orderInfo.auditStatus !== '2'">
      <el-button plain @click="handleBack"> 返回订单列表 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="viewPortalOrder">
import DiscountInfo from "./components/discountInfo.vue";
import CustomerInfo from "./components/customerInfo.vue";
import OrderTable from "./components/orderTable.vue";
import { computed, onMounted, ref, reactive } from "vue";
import InfoTemplate from "./components/infoTemplate.vue";
import OrderStatus from "./components/orderStatus.vue";
import Examine from "./components/examine.vue";
import ReviewInfo from "./components/reviewInfo.vue";
import { getManualOrderDetail, parentOrderDetail, subOrderDetail } from "@/api/modules/order";
import { useRoute, useRouter } from "vue-router";
import AccountInfoTable from "./components/accountInfoTable.vue";
import OrderFormInfo from "./components/orderFormInfo.vue";
import { userCertificateInfo } from "@/api/modules/system";
import { useTargetUser } from "../useOrder";
import DealWithInfo from "./components/dealWithInfo.vue";

const route = useRoute();
const router = useRouter();
const orderInfo = ref<Record<string, any>>({
  customerManagerDept: null,
  customerManagerName: null,
  customerManagerPhone: null,
  subOrderDetailList: []
});
const auditTagType = ref<number>(0);

/* 手动开通详情 */
const queryPageType = ref<string>("");

const discountData = ref<any>([]);
const fileList = ref<{ id: string; attrName: string; attrValue: string }[]>([]);
const customerInfo = ref<any>({
  enterpriseCertificate: {},
  personalCertificate: {},
  portalUser: {}
});

const targetCustomer = ref("1");
const targetVisible = useTargetUser(computed(() => targetCustomer.value));
const otherInfoVal = reactive({
  developerCode: "",
  provinceName: "",
  cityName: ""
});

const handleChange = () => {
  parentOrderDetail({
    parentOrderNo: orderInfo.value.parentOrderNo,
    auditTag: 1
  }).then(res => {
    orderInfo.value = res.data;
    customerInfo.value = res.data?.certificateInfo;
  });
};

const tableData = computed(() => {
  if (Reflect.has(route.query, "orderId") && Reflect.has(route.query, "auditTag")) {
    return orderInfo.value.subOrderDetailList;
  } else {
    return [
      {
        subOrderNo: orderInfo.value.subOrderNo,
        providerSkuId: orderInfo.value.providerSkuId,
        orderType: orderInfo.value.orderType,
        productName: orderInfo.value.productName,
        skuName: orderInfo.value.skuName,
        skuConfig: orderInfo.value.skuConfig,
        productProviderName: orderInfo.value.productProviderName,
        skuPrice: orderInfo.value.skuPrice,
        count: orderInfo.value.count,
        buyDuration: orderInfo.value.buyDuration,
        payFee: orderInfo.value.payFee,
        prePayFee: orderInfo.value.prePayFee,
        skuPriceUnit: orderInfo.value.skuPriceUnit,
        countUnit: orderInfo.value.countUnit,
        buyDurationUnit: orderInfo.value.buyDurationUnit,
        effectTime: orderInfo.value.effectTime,
        endTime: orderInfo.value.endTime,
        unsubscribeTime: orderInfo.value.unsubscribeTime,
        deliveryMethod: orderInfo.value.deliveryMethod,
        probationFlag: orderInfo.value.probationFlag,
        skuDiscountPrice: orderInfo.value.skuDiscountPrice,
        billingMode: orderInfo.value.billingMode
      }
    ];
  }
});

const handleBack = () => {
  router.back();
};

// const getCustomerInfo = (id: string) => {
//   userCertificateInfo({ id })
//     .then(result => {
//       customerInfo.value = result.data;
//     })
//     .catch(err => {
//       console.log(err);
//     });
// };

/* 子订单详情结果 更换 adminStatusRemark */
const statusRemark = computed(() => {
  if (Reflect.has(route.query, "subOrderId")) {
    if (orderInfo.value.status === "4") {
      return orderInfo.value.adminStatusRemark;
    } else {
      return orderInfo.value.statusRemark;
    }
  } else {
    return orderInfo.value.statusRemark;
  }
});

const managerInfo = computed(() => {
  return [
    {
      label: "客户经理姓名：",
      value: orderInfo.value.customerManagerName
    },
    {
      label: "所属部门：",
      value: orderInfo.value.customerManagerDept
    },
    {
      label: "手机号码：",
      value: orderInfo.value.customerManagerPhone
    },
    {
      label: "推荐人编码：",
      value: otherInfoVal.developerCode
    },
    {
      label: "省分地市：",
      value: otherInfoVal.provinceName + otherInfoVal.cityName || ""
    }
  ];
});
const computeAttributes = (attributes: Array<any>) => {
  if (attributes.length > 0) {
    attributes.forEach((item: any) => {
      if (item.attrCode === "contractFiles") {
        if (Array.isArray(item?.children) && item?.children.length > 0) {
          fileList.value = item.children;
        }
      }
      if (item.attrCode === "discountInfo") {
        if (Array.isArray(item?.children) && item?.children.length > 0) {
          if (item?.children[0].attrCode === "agreementPrice") {
            discountData.value = item ? [item?.children[0]] : {};
          }
        }
      }
      if (item.attrCode === "developerCode") {
        otherInfoVal.developerCode = item.attrValue;
      }
      if (item.attrCode === "provinceCode") {
        otherInfoVal.provinceName = item.attrValueName;
      }
      if (item.attrCode === "cityCode") {
        otherInfoVal.cityName = item.attrValueName;
      }
    });
  } else {
    fileList.value = [];
    discountData.value = [];
  }
};

/* 调手动开通详情 */

const fetchOrderOpen = (auditTag, parentOrderNo) => {
  getManualOrderDetail({ auditTag, parentOrderNo }).then(res => {
    orderInfo.value = res.data;
    computeAttributes(res.data.subOrderDetailList[0]?.attributes);
    customerInfo.value = res.data?.certificateInfo;
    // getCustomerInfo(orderInfo.value.customerId);
    targetCustomer.value = res.data.subOrderDetailList[0]?.targetCustomer;
    orderInfo.value.finishTime = res.data.subOrderDetailList[0]?.finishTime;
    orderInfo.value.closeTime = res.data.subOrderDetailList[0]?.closeTime;
    orderInfo.value.createTime = res.data.subOrderDetailList[0]?.createTime;
    orderInfo.value.payTime = res.data.subOrderDetailList[0]?.payTime;
    orderInfo.value.effectTime = res.data.subOrderDetailList[0]?.effectTime;
    orderInfo.value.endTime = res.data.subOrderDetailList[0]?.endTime;
    orderInfo.value.effectType = res.data.subOrderDetailList[0]?.effectType;
    orderInfo.value.buyDuration = res.data.subOrderDetailList[0]?.buyDuration;
    orderInfo.value.buyDurationUnit = res.data.subOrderDetailList[0]?.buyDurationUnit;
  });
};

onMounted(() => {
  const { orderId, auditTag, subOrderId, pageType } = route.query;

  auditTagType.value = Number(auditTag);

  if (Reflect.has(route.query, "orderId") && Reflect.has(route.query, "auditTag") && !pageType) {
    parentOrderDetail({
      parentOrderNo: orderId,
      auditTag: 1
    }).then(res => {
      orderInfo.value = res.data;
      // const attributes = res.data.subOrderDetailList[0].attributes[1] || {};
      // discountData.value = [attributes];
      // fileList.value = res.data.subOrderDetailList[0]?.attributes[0]?.children;
      computeAttributes(res.data.subOrderDetailList[0]?.attributes);
      // getCustomerInfo(orderInfo.value.customerId);
      customerInfo.value = res.data?.certificateInfo;
      orderInfo.value.finishTime = res.data.subOrderDetailList[0]?.finishTime;
      orderInfo.value.effectTime = res.data.subOrderDetailList[0]?.effectTime;
      orderInfo.value.endTime = res.data.subOrderDetailList[0]?.endTime;
      orderInfo.value.effectType = res.data.subOrderDetailList[0]?.effectType;
    });
  }
  if (Reflect.has(route.query, "subOrderId") && !pageType) {
    subOrderDetail({
      subOrderId: subOrderId
    }).then(res => {
      orderInfo.value = res.data;

      // const attributes = res.data.attributes[1] || {};
      // discountData.value = [attributes];
      // fileList.value = res.data.attributes[0]?.children;
      computeAttributes(res.data.attributes);

      // getCustomerInfo(orderInfo.value.customerId);
      customerInfo.value = res.data?.certificateInfo;
      targetCustomer.value = res.data?.targetCustomer;
      orderInfo.value.createTime = res.data.createTime;
    });
  }

  /**
   * pageType === activate 走手动开通（手动开通的页面）
   */
  if (pageType === "activate") {
    queryPageType.value = "activate";
    fetchOrderOpen(auditTag, orderId);
  }
});
</script>

<style scoped lang="scss">
@import "./order.module.scss";
.order-info {
  margin-top: 30px;
  margin-bottom: 10px;
  text-align: left;
  :deep(.el-button) {
    height: 36px;
    margin-right: 16px;
  }
}
.page-title {
  padding: 26px 23px;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}
.table {
  padding: 25px;
}
.file-list {
  &-item {
    span {
      display: inline-block;
      margin-top: 4px;
      margin-right: 10px;
      font-size: 20px;
      vertical-align: middle;
    }
    a {
      color: var(--el-color-primary);
      text-decoration: none;
      vertical-align: middle;
    }
  }
}
</style>
