<template>
  <div class="banner-box table-box">
    <ProTable
      ref="proTable"
      title="用户列表"
      :columns="columns"
      :request-api="getTableList"
      @sort-change="sortChange"
      :tool-button="false"
    >
      <template #industryType="{ row }">
        {{ useDictLabel(dict_industry_type, row.industryType) }}
      </template>
      <template #auditStatus="{ row }">
        <CustomTag
          :type="row.auditStatus == 0 ? 'green' : 'blue'"
          :status="row.auditStatus"
          :label="useDictLabel(coupon_apply_status, row.auditStatus) || '--'"
        ></CustomTag>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'deal'" v-if="scope.row.auditStatus === 0">
          <el-tooltip content="处理" placement="top">
            <i class="iconfont2 opera-icon icon-querenchuli1" @click="toHandle(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="computingCouponManage">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { getCouponApplyList, handleCouponApply } from "@/api/modules/business/compCouponManage";
import { type ProTableInstance, type ColumnProps } from "@/components/ProTable/interface/index";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useDictLabel, useDict } from "@/hooks/useDict";
import CustomTag from "@/components/CustomTag/index.vue";

const { dict_industry_type, coupon_apply_status } = useDict("dict_industry_type", "coupon_apply_status");

const { BUTTONS } = useAuthButtons();

const router = useRouter();
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();
const sortType = {
  descending: "descs",
  ascending: "ascs"
};
const sortCreateTime = ref<string | null>(null);

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const { companyName, usciCode, registeredTime, enabled, current, size } = search;

  const newParams: any = {
    companyName,
    usciCode,
    enabled,
    current,
    size,
    createTimeStart: registeredTime && registeredTime[0],
    createTimeEnd: registeredTime && registeredTime[1]
  };
  if (sortCreateTime.value) {
    newParams[sortCreateTime.value] = "createTime";
  }
  return getCouponApplyList(newParams);
};

const sortChange = (e: any) => {
  if (e.order === "descending") {
    sortCreateTime.value = sortType.descending;
  } else if (e.order === "ascending") {
    sortCreateTime.value = sortType.ascending;
  } else {
    sortCreateTime.value = e.order;
  }
  proTable.value?.getTableList();
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: "companyName",
    label: "企业名称",
    align: "left",
    search: { el: "input", props: { maxLength: 10 } },
    minWidth: 100
  },
  {
    prop: "usciCode",
    label: "社会信用统一代码",
    align: "left",
    search: { el: "input", props: { maxLength: 10 } },
    minWidth: 170
  },
  {
    prop: "industryType",
    label: "所属行业",
    align: "left",
    minWidth: 100
  },
  {
    prop: "province",
    label: "公司注册地",
    align: "left",
    render: ({ row: { province, city } }: any) => {
      return province + "-" + city;
    },
    minWidth: 100
  },
  {
    prop: "registeredTime",
    align: "left",
    label: "注册时间",
    minWidth: 120
  },
  {
    prop: "contactName",
    label: "联系人",
    align: "left",
    minWidth: 100
  },
  {
    prop: "phone",
    label: "联系人号码",
    align: "left",
    minWidth: 120
  },
  {
    prop: "creatorName",
    label: "客户账号",
    align: "left",
    minWidth: 120
  },
  {
    prop: "registeredTime",
    label: "登记时间",
    align: "left",
    sortable: true,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } },
    minWidth: 120
  },
  {
    prop: "auditStatus",
    label: "状态",
    align: "left",
    minWidth: 100
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 95 }
];

const toView = (data: any) => {
  router.push({
    path: "/operation/computingCouponManage/detail",
    query: {
      id: data.id
    }
  });
};

const toHandle = (row: any = {}) => {
  ElMessageBox.confirm("是否确认受理登记单吗？", "确认操作").then(async () => {
    await handleCouponApply({ id: row?.id });
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
