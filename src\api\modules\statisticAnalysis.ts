import http from "@/api";
import { Statistic } from "@/api/interface/statisticAnalysis";

// 订单按日期统计分析图表
export const getOrderDateChat = (params: Statistic.OrderDateListParams) => {
  return http.get(`/cnud-order/admin/analysis/order-date-chat`, params);
};

// 获取产品订单排名统计
export const getOrderProductTop = (params: Statistic.OrderDateListParams) => {
  return http.get(`/cnud-order/admin/analysis/order-product-top`, params);
};

// 订单按日期统计分析总列表
export const getOrderDateList = (params: Statistic.OrderDateListParams) => {
  return http.get(`/cnud-order/admin/analysis/order-date-list`, params);
};

// 产品列表
export const getProductList = () => {
  return http.get(`/cnud-order/admin/analysis/product-list`);
};

// 合作商列表
export const getProviderList = () => {
  return http.get(`/cnud-order/admin/analysis/provider-list`);
};

// 订单分析表导出
export const exportOrderDateList = (params: Statistic.OrderDateListParams) => {
  return http.download(`/cnud-order/admin/analysis/order-date-list-export`, params);
};

// 产品维度订单分析表导出
export const exportOrderProductList = (params: Statistic.OrderDateListParams) => {
  return http.download(`/cnud-order/admin/analysis/order-product-list-export`, params);
};

// 获取产品维度订单分析数据
export const getOrderProductList = (params: Statistic.OrderDateListParams) => {
  return http.get(`/cnud-order/admin/analysis/order-product-list`, params);
};

/** 门户访问量分析 start */
// 表格访问量
export const getAccessTableData = (params: Statistic.PortalTrafficParams) => {
  return http.post(`/behavior/admin/analysis/accessTableData`, params);
};

// 图表访问量
export const getAccessChartData = (params: Statistic.PortalTrafficParams) => {
  return http.post(`/behavior/admin/analysis/accessChartData`, params);
};

// 导出接口
export const exportAccessTable = (params: Statistic.OrderDateListParams) => {
  return http.download(`/behavior/admin/analysis/accessTableExport`, params);
};
/** 门户访问量分析 end */
/** 注册用户分析 start*/
// 注册用户图表数据接口
export const getPortalUserChart = (params: Statistic.PortalParams) => {
  return http.post(`/system/admin/analysis/portalUserChart`, params);
};

// 注册用户列表数据接口
export const getPortalUserTable = (params: Statistic.PortalParams) => {
  return http.post(`/system/admin/analysis/portalUserTable`, params);
};

// 注册用户列表数据导出接口
export const exportPortalUserTable = (params: Statistic.OrderDateListParams) => {
  return http.download(`/system/admin/analysis/portalUserTableExport`, params);
};
/** 注册用户分析 end */

/** 产品商机/解决方案商机公用接口 */
// 商机按日期统计分析图表/表格
export const getBusinessAnalysis = (params: Statistic.BusinessParams) => {
  return http.get(`/cnud-order/admin/analysis/business-analysis`, params);
};

// 商机按日期统计分析排名
export const getBusinessAnalysisTop = (params: Statistic.BusinessParams) => {
  return http.get(`/cnud-order/admin/analysis/business-analysis-top`, params);
};

// 商机分析表导出
export const exportBusinessDateList = (params: Statistic.OrderDateListParams) => {
  return http.download(`/cnud-order/admin/analysis/business-date-list-export`, params);
};

// 商机产品维度表导出
export const exportBusinessDetail = (params: Statistic.OrderDateListParams) => {
  return http.download(`/cnud-order/admin/analysis/business-detail-list-export`, params);
};

// 商机分析详情 -- 纬度列表
export const getBusinessDetail = (params: any) => {
  return http.get(`/cnud-order/admin/analysis/business-date-list-detail`, params);
};

// 商机分析详情 -- 商机名称列表
export const getBusinessNameList = (params: { adviceType: string }) => {
  return http.get(`/cnud-order/admin/analysis/business-itemList`, params);
};

// 商机分析详情 -- 商机服务商列表
export const getBusinessProviderList = (params: any) => {
  return http.get(`/cnud-order/admin/analysis/business-providerList`, params);
};

// 订单渠道按日期统计分析折线图
export const getChannelChartData = (params: any) => {
  return http.get(`/cnud-order/admin/analysis/order-date-channel`, params);
};

// 订单渠道按日期统计分析饼图
export const getChannelPieData = (params: any) => {
  return http.get(`/cnud-order/admin/analysis/order-count-channel`, params);
};

// 订单渠道按日期统计分析总列表
export const getChannelList = (params: any) => {
  return http.get(`/cnud-order/admin/analysis/order-channel-list`, params);
};

// 订单渠道表导出
export const exportChannelDetail = (params: any) => {
  return http.download(`/cnud-order/admin/analysis/order-channel-list-export`, params);
};

// 产品权益表-下拉框列表查询
export const queryProductRightIdList = () => {
  return http.get(`/cnud-product/admin/pmsRights/queryList`);
};

// 产品使用分析-图表
export const queryProductUseChart = (params: any) => {
  return http.get(`/cnud-product/admin/analysis/product-use-chart`, params);
};

// 产品使用分析-图表
export const queryProductUseList = (params: any) => {
  return http.get(`/cnud-product/admin/analysis/product-use-table`, params);
};

// 产品使用分析报表-导出
export const exportProductUse = (params: Statistic.OrderDateListParams) => {
  return http.download(`/cnud-product/admin/analysis/product-use-export`, params);
};

/**、
 * H5推广数据统计（列表）
 */
export const queryOrdDemandH5 = (data: Statistic.promotionH5Search) => {
  return http.post<Array<Statistic.promotionH5Item>>(`/cnud-order/admin/ordDemand/search`, data);
};

/*  H5推广数据统计（导出） */
export const exportPromotionH5 = (data: Statistic.promotionH5Search) => {
  return http.post(`/cnud-order/admin/ordDemand/exportCZX`, data);
};

// 宣传提交查询接口--超智算
export const queryOrdDemandCZS = (data: Statistic.promotionH5Search) => {
  return http.post<Array<Statistic.promotionH5Item>>(`/cnud-order/admin/ordDemand/searchCZS`, data);
};
