import http from "@/api";

export const getPageList = (data: any) => {
  return http.get("/cnud-operation/admin/news-pageList", data);
};

export const cancel = (id: string) => {
  return http.post("/cnud-operation/admin/news-cancel", { id });
};

export const newsDelete = (id: string) => {
  return http.post("/cnud-operation/admin/news-delete", { ids: [id] });
};

export const change = (data: any, isPublish: boolean) => {
  return http.post("/cnud-operation/admin/news-update", {
    news: data,
    publishNow: isPublish
  });
};

export const add = (data: any, isPublish: boolean) => {
  return http.post("/cnud-operation/admin/news-save", {
    news: data,
    publishNow: isPublish
  });
};

export const revoke = (id: string) => {
  return http.post("/cnud-operation/admin/news-revoke", { id });
};
