<template>
  <el-drawer v-model="visible" :destroy-on-close="true" size="800px" title="选择产品">
    <div class="table-box">
      <ProTable ref="proTable" :request-api="getTableList" :tool-button="false" :columns="columns">
        <!-- 表格 header 按钮 -->
        <template #tableHeader="scope">
          <el-button type="primary" @click="() => confirm(scope.selectedListIds)">确认</el-button>
          <el-button @click="cancel">取消</el-button>
        </template>
      </ProTable>
    </div>
  </el-drawer>
</template>
<script setup lang="ts" name="UserDrawer">
import { ref, reactive, defineProps, defineExpose } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { type ProTableInstance, type ColumnProps, type HeaderRenderScope } from "@/components/ProTable/interface/index";
import ProTable from "@/components/ProTable/index.vue";
import { getCategory } from "@/api/modules/business/productArea";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { getProductList, add } from "@/api/modules/business/discount";
// import { useDiscountProductStore } from "@/stores/modules/productArea";
// import { useProductAreaStore } from "@/stores/modules/productArea";

// const productStore = useProductAreaStore();
const { BUTTONS } = useAuthButtons();
const visible = ref(false);
const emit = defineEmits(["refresh"]);
// const props = defineProps({});
// const productStore = useDiscountProductStore();

const getTableList = (search: any) => {
  const { productName, current, size, categoryName = [] } = search;
  return getProductList({
    productName,
    categoryId: categoryName.length ? categoryName[categoryName.length - 1] : undefined,
    current,
    size
  });

  // if (!BUTTONS.value?.query) {
  //   return;
  // }

  // let newParams = JSON.parse(JSON.stringify(search));
  // return getProductList(newParams);
};

defineExpose({ visible });

const columns = ref<ColumnProps[]>([
  {
    type: "selection",
    fixed: "left",
    width: 80
    // selectable: row => {
    //   return !productStore.productSkuIdList.filter((item: any) => item === row.skuId).length;
    // }
  },
  {
    prop: "productName",
    label: "产品名称",
    search: { el: "input", props: { maxLength: 10 } }
  },
  {
    prop: "categoryName",
    label: "产品分类",
    width: 120,
    enum: getCategory,
    fieldNames: {
      value: "id",
      label: "name"
    },
    isFilterEnum: false,
    search: { el: "cascader" }
  },
  {
    prop: "skuName",
    label: "产品规格"
  },
  {
    prop: "skuPrice",
    label: "规格单价"
  },
  {
    prop: "skuDiscountPrice",
    label: "折扣价"
  }
]);

const cancel = () => {
  visible.value = false;
};

const confirm = async (ids: Array<string>) => {
  //   const num = 6 - productStore.productDetailList.length;
  //   if (selectedList.length > num) {
  //     ElMessage({
  //       message: `选择的产品总和不能超过6个！此次最多能选${num}个。`,
  //       type: "error"
  //     });
  //     return;
  //   }
  // productStore.productDetailList = [...productStore.productDetailList, ...selectedList] as any;
  // console.log("1111111111111111111111");
  // console.log([...ids]);
  if (ids.length) {
    await add([...ids]);
    visible.value = false;
    ElMessage.success("操作成功！");
    emit("refresh");
  } else {
    visible.value = false;
  }
};
</script>
