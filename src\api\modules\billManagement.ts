import http from "@/api";
import { Bill } from "@/api/interface/billManagement";

// 用户实时余额表
export const getBillPage = (params: Bill.BillListParams) => {
  return http.get("/cnud-desk/admin/dskUserBalance/pageList", params);
};

// 用户实时余额表-全量导出
export const exportBalanceUser = (params: Bill.ExportBalanceParams) => {
  return http.post(`/cnud-desk/admin/dskUserBalance/export`, params, { responseType: "blob" });
};

// 用户余额变更记录明细表-列表查询(分页)
export const getDskBalanceRecord = (params: Bill.BalanceRecordParams) => {
  return http.get(`/cnud-desk/admin/dskBalanceRecord/pageList`, params);
};

// 用户余额变更记录明细表-全量导出
export const exportDskBalance = (params: Bill.BalanceRecordParams) => {
  return http.post(`/cnud-desk/admin/dskBalanceRecord/export`, params, { responseType: "blob" });
};

// 每小时作业账单明细表-列表查询(分页)
export const getDskJobBill = (params: Bill.DskJobBillParams) => {
  return http.get("/cnud-desk/admin/dskJobBill/pageList", params);
};

// 每小时作业账单明细表-查询关联订单列表
export const relativeOrder = (params: Bill.RelativParams) => {
  return http.get("/cnud-desk/admin/dskJobBill/relativeOrder", params);
};

// 用户每月账单-列表查询(分页)
export const getDskBill = (params: Bill.DskBillParams) => {
  return http.get("/cnud-desk/admin/dskBill/pageList", params);
};

// 用户每月账单-全量导出
export const exportDskBill = (params: Bill.DskBillParams) => {
  return http.post(`/cnud-desk/admin/dskBill/export`, params, { responseType: "blob" });
};

// 用户余额变更记录明细表-全量导出
export const exportDskJobBill = (params: Bill.DskBillParams) => {
  return http.post(`/cnud-desk/admin/dskJobBill/export`, params, { responseType: "blob" });
};

// 核时平台一体化接口-获取有效的集群信息
export const getCluster = (params?: any) => {
  return http.get<Array<Bill.IClusterItem>>(`/cnud-desk/admin/cluster`, params);
};

export const queryGetCluster = () => {
  return http.get<Array<Bill.ICluster>>(`/cnud-desk/admin/platform`);
};
