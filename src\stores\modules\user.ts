import { defineStore } from "pinia";
import { UserState } from "@/stores/interface";
import piniaPersistConfig from "@/config/piniaPersist";
import { getPublicKey, manageSiteConfig } from "@/api/modules/user";

export const useUserStore = defineStore({
  id: "cnud-user",
  state: (): UserState => ({
    token: "",
    userInfo: { userName: "CNUD" },
    sysInfo: {},
    publicKey: "",
    orgs: []
  }),
  getters: {
    sysInfoGet: state => state.sysInfo
  },
  actions: {
    // Set Token
    setToken(token: string) {
      this.token = token;
    },
    // Set setUserInfo
    setUserInfo(userInfo: UserState["userInfo"]) {
      this.userInfo = userInfo;
    },
    async getSysInfo() {
      this.sysInfo = {}; // 先清空对象，防止接口报错拿上一个数据
      const { data }: any = await manageSiteConfig();
      this.sysInfo = data;
    },
    async setPublicKey() {
      this.publicKey = ""; // 先清空，防止接口报错拿上一个数据
      const { data }: any = await getPublicKey();
      this.publicKey = data.sm2Pub;
    },
    /* 所属部门 */
    setOrgs(orgs: any[]) {
      this.orgs = orgs;
    }
  },
  persist: piniaPersistConfig("cnud-user")
});
