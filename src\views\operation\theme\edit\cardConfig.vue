<template>
  <div class="complex-content-box">
    <div class="name-box">
      <el-button type="primary" class="add-button" @click="addCard" :icon="CirclePlus"> 添加 </el-button>
      <span class="tips"> <slot name="nameTips"></slot></span>
    </div>
    <div class="content-item" v-for="(item, index) in dataList" :key="index + 'complex'">
      <div class="item-title">
        <div class="name">{{ item.title || "卡片名称" }}</div>
        <div class="right-btn">
          <el-button class="icon" link v-if="index !== dataList.length - 1" @click="xiayiItem(index, dataList)">
            <i class="iconfont2 icon-xiayi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link v-if="index !== 0" @click="shangyiItem(index, dataList)">
            <i class="iconfont2 icon-shangyi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link :icon="Delete" @click="deleteCard(index)"></el-button>
        </div>
      </div>
      <el-form ref="cardRuleFormRef" class="item-form" :model="item" :rules="rules" label-width="120px">
        <el-form-item label="上传类型" prop="uploadType">
          <el-radio-group v-model="item.uploadType">
            <el-radio v-for="(itemDic, indexDic) in theme_zone_upload_type" :key="indexDic + 'jumpType'" :label="itemDic.itemKey">
              {{ itemDic.itemValue }}
            </el-radio>
            <!-- <el-radio label="1">图片上传</el-radio>
            <el-radio label="2">视频上传</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="卡片描述" prop="description" v-if="isSmallCard">
          <div class="item-small-card">
            <UploadImg
              class="item-small-card-item"
              :file-size="2"
              v-model:image-url="item.iconUrl"
              v-if="item.uploadType === '1'"
            >
              <template #tip>建议图片尺寸288px*162px，图片大小不超过2M，支持图片格式jpg/jpeg/png;</template>
            </UploadImg>
            <!-- <p>建议图片尺寸288px*162px，图片大小不超过2M，支持图片格式jpg/jpeg/png;</p> -->
            <UploadVideo class="item-small-card-item" v-model:video-url="item.videoUrl" v-if="item.uploadType === '2'">
              <template #tip>建议视频比例为16:9，视频大小不超过100M，支持格式mp4;</template>
            </UploadVideo>
            <el-input
              class="item-small-card-item"
              v-model="item.title"
              maxlength="10"
              placeholder="请输入标题（必填）"
              show-word-limit
              clearable
            />
            <el-input
              class="item-small-card-item"
              v-model="item.description"
              placeholder="请输入描述内容（必填）"
              type="textarea"
              maxlength="200"
              show-word-limit
              clearable
            />
          </div>
          <!-- <p>支持上传图片和视频</p> -->
        </el-form-item>
        <el-form-item label="跳转链接" prop="link" v-if="isSmallCard">
          <el-input v-model="item.link" maxlength="150" show-word-limit clearable> </el-input>
          <p class="tips-text">
            手动填写跳转链接时请添加协议头，建议添加如：https或其他已定制的协议头，若添加http协议头有一定链接被篡改风险
          </p>
        </el-form-item>

        <el-form-item label="卡片标题" prop="title" v-if="!isSmallCard" required>
          <el-input v-model="item.title" maxlength="10" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="卡片描述" prop="additionalTitles" v-if="!isSmallCard">
          <div class="item-big-card">
            <!-- <div class="item-big-card-left"> -->
            <UploadImg :file-size="2" v-model:image-url="item.iconUrl" v-if="item.uploadType === '1'">
              <template #tip>建议上传图片尺寸736px*414px，图片大小不超过2M，支持图片格式jpg/jpeg/png</template>
            </UploadImg>
            <UploadVideo v-model:video-url="item.videoUrl" v-if="item.uploadType === '2'">
              <template #tip>建议视频比例为16:9，视频大小不超过100M，支持格式mp4</template>
            </UploadVideo>
            <!-- </div> -->
            <div class="item-big-card-additionalTitles">
              <el-input
                class="item-big-card-title"
                v-model="item.additionalTitles[0].title"
                maxlength="10"
                placeholder="子标题一（必填）"
                show-word-limit
                clearable
              />
              <el-input
                class="item-big-card-context"
                v-model="item.additionalTitles[0].context"
                type="textarea"
                maxlength="200"
                placeholder="描述文本一（必填）"
                show-word-limit
                clearable
              />
              <el-input
                class="item-big-card-title"
                v-model="item.additionalTitles[1].title"
                maxlength="10"
                placeholder="子标题二（选填）"
                show-word-limit
                clearable
              />
              <el-input
                class="item-big-card-context"
                v-model="item.additionalTitles[1].context"
                type="textarea"
                maxlength="200"
                placeholder="描述文本二（选填）"
                show-word-limit
                clearable
              />
            </div>
          </div>
          <!-- <p>支持上传图片和视频</p> -->
        </el-form-item>
        <el-form-item label="卡片操作按钮" prop="operationButtons" v-if="!isSmallCard">
          <!-- <el-input v-model="item.skuConfig"  clearable /> -->
          <button-table v-model:buttonData="item.operationButtons" @update-button="updateButton"></button-table>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, computed, reactive } from "vue";
import { CirclePlus, Delete } from "@element-plus/icons-vue";
import ButtonTable from "./buttonTable.vue";
import UploadImg from "@/components/Upload/Img.vue";
import UploadVideo from "@/components/Upload/Video.vue";
import { FormInstance } from "element-plus";
import buttonTable from "./buttonTable.vue";
import { modelChildren } from "@/api/interface/business/theme";
import { useDict } from "@/hooks/useDict";
const { theme_zone_upload_type } = useDict("theme_zone_upload_type");

const props = defineProps({
  modelIndex: {
    type: Number,
    default: 0
  },
  dataList: {
    type: Array<modelChildren>,
    default: () => []
  },

  isSmallCard: {
    type: Boolean,
    default: false
  }
});

// const uploadType = ref("1"); //大小卡片描述上传类型
const emits = defineEmits(["addCard", "deleteCard"]);
const cardRuleFormRef = ref<FormInstance>();

const ruleForm = reactive({
  name: "",
  description: "",
  iconUrl: "",
  operationButtons: []
});

const rules = {
  title: [{ required: true, message: "卡片标题不能为空", trigger: "blur" }],
  uploadType: [{ required: true, message: "请选择上传类型", trigger: "blur" }]
};

const addCard = () => {
  emits("addCard", props.modelIndex, props.isSmallCard);
};
//删除元素
const deleteCard = (cardIndex: number) => {
  emits("deleteCard", props.modelIndex, cardIndex);
};
const shangyiItem = (index: number, list: any[]) => {
  list[index] = list.splice(index - 1, 1, list[index])[0];
};
const xiayiItem = (index: number, list: any[]) => {
  list[index] = list.splice(index + 1, 1, list[index])[0];
};

const updateButton = (data: any) => {
  ruleForm.operationButtons = data;
};

const ruleValidatePromise = () => {
  let promiseArr = [] as any;
  cardRuleFormRef.value?.forEach((itemref: any) => {
    promiseArr.push(
      new Promise((resolve, reject) => {
        itemref.validate((valid: any, fields: any) => {
          if (valid) {
            resolve("校验成功");
          } else {
            let obj = Object.keys(fields);
            itemref.scrollToField(obj[0]);
            reject("校验失败");
          }
        });
      })
    );
  });
  return promiseArr;
};

onMounted(() => {});

defineExpose({
  ruleValidatePromise
});
</script>

<style lang="scss" scoped>
.complex-content-box {
  width: 100%;
  .name-box {
    display: flex;
    width: fit-content;
    margin-bottom: 16px;
    .name-input {
      margin-right: 8px;
    }
    .add-button {
      margin-right: 8px;
    }
    .tips {
      margin-left: 15px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
    }
  }
  .content-item {
    margin-bottom: 16px;
    border: 1px solid #c4c6cc;
    .item-title {
      display: flex;
      justify-content: space-between;
      padding: 4px 12px;
      background: #c4c6cc;
      .right-btn {
        .icon + .icon {
          margin-left: 0;
        }
      }
      .icon:hover {
        color: #3a3a3d;
      }
    }
  }
  .item-form {
    padding: 24px;
    .item-small-card {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding-right: 20px;
      border: #c4c6cc;
      border-style: solid;
      border-width: 1px;
      .item-small-card-item {
        &:first-child {
          margin-top: 10px;
        }

        margin-bottom: 10px;
        margin-left: 10px;
      }
    }
    .item-big-card {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 10px;
      border: #c4c6cc;
      border-style: solid;
      border-width: 1px;
      .item-big-card-left {
        flex-direction: column;
      }
      .item-big-card-additionalTitles {
        flex-direction: column;

        // margin-left: 10px;
        .item-big-card-title {
          height: 50px;
          margin-bottom: 10px;
        }
        .item-big-card-context {
          // height: 80px;
          margin-bottom: 10px;
        }
      }
    }
    .jf-item {
      :deep(.el-form-item__content) {
        display: block;
      }
      .jf-box {
        padding: 15px 0;
        .jf-label {
          font-size: var(--el-form-label-font-size);
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .nowrap {
    white-space: nowrap;
  }
}
.tips-text {
  margin: 0;
  font-size: 12px;
  color: #909399;
}
:deep(.upload-box .el-upload__tip) {
  margin-bottom: 10px;
  text-align: left;
}
</style>
