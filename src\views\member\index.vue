<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="合作商协议管理列表"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
    >
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="openDialog({}, editType.add)"> 新增 </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="openDialog(scope.row, editType.details)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="openDialog(scope.row, editType.edit)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="handleDelete(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <!--  新增编辑弹窗  -->
    <add-edit-member-dialog
      v-model:visible="labelDialogVisible"
      :type="labelDialogParams.type"
      :params="labelDialogParams.params"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-member-dialog>
  </div>
</template>

<script setup lang="tsx" name="memberManagement">
import { ref, reactive, onMounted, onActivated } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { delMember, getMemberList } from "@/api/modules/member";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import AddEditMemberDialog from "./components/addOrEditMemberDialog.vue";

const { BUTTONS } = useAuthButtons();
enum editType {
  "add" = 1,
  "edit" = 2,
  "revoke" = 3,
  "details" = 4
}

interface typeDialog {
  type: number;
  params: Object;
}
// 编辑弹窗
const labelDialogVisible = ref(false);
const labelDialogParams = ref<typeDialog>({
  params: {},
  type: editType.add
});

const onCancel = () => {};
const onConfirm = () => {
  proTable.value?.getTableList();
};

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

onMounted(() => {
  proTable.value?.getTableList();
});

onActivated(() => {
  proTable.value?.getTableList();
});

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  return getMemberList(newParams);
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  { prop: "id", label: "客户编码", search: { el: "input" } },
  {
    prop: "companyName",
    label: "公司名称"
  },
  {
    prop: "contactsName",
    label: "联系人姓名"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

// 删除用户信息
const handleDelete = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此会员，删除后数据不可恢复？", "确认操作").then(async () => {
    await delMember({ ids: [data.id] });
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

const openDialog = async (row: any, operateType = editType.add) => {
  //  新增
  //  编辑

  const dialogParams = {
    type: operateType,
    params: {
      ...row
    }
  };
  labelDialogVisible.value = true;
  labelDialogParams.value = dialogParams;
};
</script>

<style lang="scss" scoped>
:global(.overflow-tooltip-style) {
  max-width: calc(100% - 24px);
  white-space: pre-wrap;
}
</style>
