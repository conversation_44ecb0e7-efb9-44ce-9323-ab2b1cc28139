<template>
  <div>
    <div class="ranking-title">
      <span class="title">{{ rankingTitle }}</span>
      <span v-if="more" class="more" @click="onClickMore">更多>></span>
    </div>
    <div class="ranking-list">
      <template v-if="rankingList && rankingList.length > 0">
        <div class="ranking-list-item" v-for="(item, index) in rankingList?.slice(0, 5)" :key="item.productName + index">
          <span class="num">{{ index + 1 }}</span>
          <span class="name">{{ item.productName }}</span>
          <span class="total">{{ toThousandFilter(item.count) }}</span>
        </div>
      </template>
      <el-empty v-else description="暂无数据 ~" />
    </div>
  </div>
</template>

<script setup lang="ts" name="rankingList">
import { PropType } from "vue";
import { toThousandFilter } from "@/utils/tools";

type RankingType = {
  productName: string;
  count: string;
};

defineProps({
  rankingTitle: {
    type: String,
    default: ""
  },
  more: {
    type: Boolean,
    default: () => true
  },
  rankingList: {
    type: Array as PropType<RankingType[]>,
    default: () => []
  }
});

const emits = defineEmits(["onMore"]);

const onClickMore = () => {
  emits("onMore");
};
</script>

<style lang="scss" scoped>
.ranking-title {
  width: 100%;
  margin-bottom: 24px;
  .title {
    height: 22px;
    font-family: "PingFangSC,PingFang SC";
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #000000d9;
  }
  .more {
    float: right;
    font-size: 14px;
    color: #0052d9;
    cursor: pointer;
  }
}
.ranking-list {
  .num {
    width: 20px;
    height: 20px;
    font-family: "HelveticaNeue, HelveticaNeue";
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    color: #000000a6;
    text-align: center;
    background: #f0f2f5;
    border-radius: 50%;
  }
  .ranking-list-item {
    display: flex;
    flex-wrap: nowrap;
    gap: 24px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #000000a6;
    &:first-child .num {
      color: #ffffff;
      background: linear-gradient(180deg, #ffdc00 0%, #ffb600 100%);
    }
    &:nth-child(2) .num {
      color: #ffffff;
      background: linear-gradient(180deg, #ffbf00 0%, #ff8a00 100%);
    }
    &:nth-child(3) .num {
      color: #ffffff;
      background: linear-gradient(180deg, #c6cbcc 0%, #959c9d 100%);
    }
  }
  .name {
    flex: 1;
    overflow: hidden;
    font-size: 14px;
    font-weight: 400;
    color: #000000a6;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .total {
    font-family: HelveticaNeue;
  }
}
</style>
