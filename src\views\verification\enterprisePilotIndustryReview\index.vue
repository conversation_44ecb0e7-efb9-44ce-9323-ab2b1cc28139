<template>
  <div class="table-box">
    <!-- <el-tabs v-model="activeName" class="demo-tabs tab-box" @tab-click="handleTabClick">
      <el-tab-pane label="诊断报告" :name="TabType.REQUIRE"></el-tab-pane>
      <el-tab-pane label="合同签订" :name="TabType.SCHEME"> </el-tab-pane>
    </el-tabs> -->

    <div v-show="activeName === TabType.SCHEME" class="list-box">
      <pro-table
        ref="schemeTableRef"
        title="合同签订审核"
        :request-auto="false"
        :tool-button="false"
        :columns="columns"
        :request-api="getEnterprisePilotAgreementIndustryReviewList"
      >
        <template #tableHeader>
          <el-button v-auth="'export'" plain @click="exportFile('traction_unit_review', '牵引单位')">
            <i class="iconfont2 icon-a-lineicon_share"></i>
            导出
          </el-button>
        </template>
        <template #auditStatus="{ row }">
          <custom-tag
            :type="statusTypeMap[row.auditStatus]"
            :status="row.auditStatus"
            :label="getAuditStatusLabel(row, 'final')"
          ></custom-tag>
        </template>
        <template #renovationAgreementUrl="{ row }">
          <el-button type="primary" size="small" @click="handleDownload(row.renovationAgreementUrl)">下载</el-button>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span>
            <el-tooltip
              v-if="scope.row.stepType === 'traction_unit_review' && scope.row.auditStatus === 'wait'"
              content="审核"
              placement="top"
            >
              <i class="iconfont2 opera-icon icon-shenhe" @click="openReviewDialog(scope.row)"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>
    <el-dialog v-model="reviewDialogVisible" title="审核" width="500px">
      <el-form>
        <el-form-item label="企业名称">
          <span>{{ auditForm.companyName }}</span>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input
            v-model="auditForm.auditMsg"
            type="textarea"
            :rows="4"
            placeholder="驳回时必须填写审核意见，通过默认审核意见为同意"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="submitReview('refuse')">审核不通过</el-button>
          <el-button type="primary" @click="submitReview('pass')">审核通过</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="tsx" name="enterprisePilotIndustryReview">
import { ref, computed, onBeforeMount, onActivated, reactive } from "vue";
import { useRouter } from "vue-router";
import type { TabsPaneContext } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import CustomTag from "@/components/CustomTag/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDictLabel } from "@/hooks/useDict";
import { useDicStore } from "@/stores/modules/dictionaty";
import {
  getEnterprisePilotReportReviewList,
  getEnterprisePilotAgreementIndustryReviewList,
  postEnterprisePilotAudit,
  exportEnterprisePilotList
} from "@/api/modules/enterprisePilot";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { OperateType, TabType, AuditStatusEnum } from "./type";
import { useUserStore } from "@/stores/modules/user";
import axios from "axios";
import { ElMessage } from "element-plus";

const statusTypeMap: { [key: string]: string } = {
  wait: "yellow",
  pass: "blue",
  refuse: "red",
  close: "grey"
};
const dictionaryStore = useDicStore();
const { BUTTONS } = useAuthButtons();
const router = useRouter();

const reviewDialogVisible = ref(false);
const auditForm = reactive({
  id: "",
  companyName: "",
  auditMsg: ""
});
const exportFile = async (flowStatus: string, fileName: string) => {
  try {
    const res = await exportEnterprisePilotList({ flowStatus });
    const blob = new Blob([res.data as BlobPart]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    //中小企试点-诊断报告审核列表yyyyMMddHHmmSS.xlsx
    const d = new Date();
    const pad = (n: number) => n.toString().padStart(2, "0");
    const fileName1 = `中小企试点-${fileName}审核列表${[
      d.getFullYear(),
      pad(d.getMonth() + 1),
      pad(d.getDate()),
      pad(d.getHours()),
      pad(d.getMinutes()),
      pad(d.getSeconds())
    ].join("")}.xlsx`;
    // const fileName = `中小企试点-诊断报告审核列表${new Date().toLocaleString().replace(/\//g, "-")}.xlsx`;
    link.setAttribute("download", fileName1);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("导出失败", error);
    ElMessage.error("导出失败");
  }
};
const openReviewDialog = (row: any) => {
  auditForm.id = row.id;
  auditForm.companyName = row.companyName;
  auditForm.auditMsg = "";
  reviewDialogVisible.value = true;
};

const submitReview = async (status: "pass" | "refuse") => {
  try {
    await postEnterprisePilotAudit({
      id: auditForm.id,
      auditStatus: status,
      auditMsg: auditForm.auditMsg
    });
    ElMessage.success("审核操作成功");
    reviewDialogVisible.value = false;
    if (activeName.value === TabType.REQUIRE) {
      requireTableRef.value?.getTableList();
    } else {
      schemeTableRef.value?.getTableList();
    }
  } catch (error) {
    console.error("审核失败", error);
  }
};

const getAuditStatusLabel = (row: any, context: "initial" | "final") => {
  if (row.auditStatus === "pass") return "通过";
  if (row.auditStatus === "refuse") return "不通过";
  if (row.auditStatus === "wait") {
    const expectedStep = context === "initial" ? "city_initial_review" : "traction_unit_review";
    return row.stepType === expectedStep ? "待审核" : "非审核状态";
  }
  return "非审核状态";
};

const handleDownload = async (url: string) => {
  if (!url) return;
  const userStore = useUserStore();
  try {
    const res = await axios.get(url, {
      responseType: "blob",
      headers: {
        "cnud-auth": `bearer ${userStore.token}`,
        "cnud-tenant": "haosuan" // 临时租户字段
      }
    });

    const blob = new Blob([res.data]);
    let fileName = "";
    const contentDisposition = res.headers["content-disposition"];
    if (contentDisposition) {
      const match = contentDisposition.match(/filename="?([^"]+)"?/);
      if (match && match[1]) {
        fileName = decodeURIComponent(match[1]);
      }
    }
    if (!fileName) {
      fileName = url.substring(url.lastIndexOf("/") + 1);
    }

    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = downloadUrl;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error("下载文件失败", error);
    ElMessage.error("下载文件失败");
  }
};

// 审核状态
const requireReleaseStatus: any = computed(() => {
  return dictionaryStore.requireReleaseStatus;
});
// 需求类型
const requireType: any = computed(() => dictionaryStore.requireType);
// 实施地址
const demandConstructionAddress: any = computed(() => dictionaryStore.demandConstructionAddress);

// 表格配置项
const requireTableRef = ref<ProTableInstance>();
const columns: ColumnProps<any>[] = [
  {
    prop: "companyName",
    minWidth: 110,
    label: "企业名称",
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "renovationAgreementUrl",
    minWidth: 100,
    label: "合同协议"
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120
  },
  {
    prop: "createTime",
    minWidth: 110,
    label: "提交时间",
    sortable: true
  },
  {
    prop: "auditorName",
    minWidth: 90,
    label: "审核人"
  },
  {
    prop: "auditTime",
    minWidth: 110,
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];
// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getRequireTableList = (params: any = {}) => {
  // if (!BUTTONS.value?.query) {
  //   return;
  // }
  return getEnterprisePilotReportReviewList(params);
};

const schemeTableRef = ref<ProTableInstance>();
const schemeColumns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "demandName",
    label: "需求名称",
    minWidth: 90,
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "demandType",
    minWidth: 90,
    label: "需求类型",
    enum: requireType,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireType.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "demandCompanyName",
    minWidth: 110,
    label: "需方企业名称"
  },
  {
    prop: "demandCompanyAddress",
    minWidth: 110,
    label: "需方企业地址"
  },
  {
    prop: "demandContactName",
    minWidth: 100,
    label: "需方联系人"
  },
  {
    prop: "demandPhone",
    minWidth: 110,
    label: "需方联系电话"
  },
  {
    prop: "demandEndTime",
    minWidth: 110,
    label: "需求截止时间"
  },
  {
    prop: "constructionAddress",
    minWidth: 110,
    label: "需求实施地址",
    enum: demandConstructionAddress
  },
  {
    prop: "solutionName",
    minWidth: 90,
    label: "方案名称",
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "solutionCompanyName",
    minWidth: 110,
    label: "供方企业名称"
  },
  {
    prop: "solutionCompanyAddress",
    minWidth: 110,
    label: "供方企业地址"
  },
  {
    prop: "solutionContactName",
    minWidth: 100,
    label: "供方联系人"
  },
  {
    prop: "solutionPhone",
    minWidth: 110,
    label: "供方联系电话"
  },
  {
    prop: "createTime",
    minWidth: 110,
    label: "提交时间",
    sortable: true
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120,
    enum: requireReleaseStatus,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireReleaseStatus.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditorName",
    minWidth: 90,
    label: "审核人"
  },
  {
    prop: "auditTime",
    minWidth: 110,
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];
const getSchemeTableList = (params: any = {}) => {
  // if (!BUTTONS.value?.schemeQuery) {
  //   return;
  // }
  return getEnterprisePilotAgreementReviewList(params);
};
const toView = (row: any = {}, type = OperateType.DETAIL) => {
  router.push({
    path: "/verification/enterprisePilotReview/detail",
    query: {
      id: row?.id,
      operateType: type,
      pageType: activeName.value
    }
  });
};

const activeName = ref(TabType.SCHEME);
const handleTabClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name as TabType;
  if (tab.props.name === TabType.REQUIRE) {
    requireTableRef.value?.getTableList();
  } else {
    schemeTableRef.value?.getTableList();
  }
};

onBeforeMount(() => {
  dictionaryStore.requireReleaseStatus.length === 0 && dictionaryStore.getRequireReleaseStatus();
  dictionaryStore.requireType.length === 0 && dictionaryStore.getRequireType();
  dictionaryStore.demandConstructionAddress.length === 0 && dictionaryStore.getDemandConstructionAddress();
});
onActivated(() => {
  if (activeName.value === TabType.REQUIRE) {
    requireTableRef.value?.getTableList();
  } else {
    schemeTableRef.value?.getTableList();
  }
});
</script>
<style scoped lang="scss">
.tab-box {
  background-color: #ffffff;
}
::v-deep(.el-tabs__item) {
  width: 100px !important;
}
.list-box {
  display: flex;
  flex-direction: column;
  height: calc(100% - 54px);
}
</style>
