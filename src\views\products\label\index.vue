<!--
 * @Author: <EMAIL>
 * @Date: 2023-09-06 16:21:28
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-19 11:25:39
 * @Description: file content
-->
<template>
  <div class="label-main">
    <label-type-list
      :type-list="typeList"
      title="标签分类"
      :config-type="configType"
      :item-operate="itemOperate"
      v-model:active-index="activeIndex"
      @delete-item="onDelete"
    >
      <template #operateBtn>
        <div class="operate-btn">
          <el-button v-auth="'add'" :icon="Plus" plain @click="addLabelType">新增</el-button>
          <el-button v-auth="'edit'" :icon="EditPen" plain @click="editLabelType">编辑</el-button>
        </div>
      </template>
    </label-type-list>
    <label-list :type-item="activeItem"></label-list>
    <add-edit-type-dialog
      v-model:visible="dialogVisible"
      :type="dialogType"
      :params="labelTypeParams"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-type-dialog>
  </div>
</template>
<script setup lang="ts" name="productManageLabel">
import { ref, onMounted, computed } from "vue";
import labelTypeList from "@/components/TypeList/index.vue";
import labelList from "./components/labelList.vue";
import { Plus, EditPen } from "@element-plus/icons-vue";
import addEditTypeDialog from "./components/addOrEditTypeDialog.vue";
import { Label } from "@/api/interface/label";
import { getTagTypeList, deleteTagType } from "@/api/modules/label";
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import { useHandleData } from "@/hooks/useHandleData";
import { ResPage } from "@/api/interface/index";
import { ElMessage, ElMessageBox } from "element-plus";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const labelTypeParams = ref({});
const dialogType = ref<1 | 2>(1);
const activeIndex = ref(0);
const dialogVisible = ref(false);
const activeItem = computed(() => {
  return typeList.value[activeIndex.value] || {};
});
onMounted(() => {
  getTypeList(typeListParams.value);
});
const addLabelType = () => {
  if (typeList.value.length >= 20) {
    ElMessage.error("标签分类最多存在20个");
    return;
  }
  labelTypeParams.value = {};
  dialogType.value = editType["add"];
  dialogVisible.value = true;
};
const editLabelType = () => {
  dialogType.value = editType["edit"];
  labelTypeParams.value = typeList.value[activeIndex.value];
  dialogVisible.value = true;
};
const onConfirm = () => {
  dialogVisible.value = false;
  labelTypeParams.value = {};
  getTypeList(typeListParams.value);
};
const onCancel = () => {
  dialogVisible.value = false;
  labelTypeParams.value = {};
};
const configType = {
  name: "name",
  key: "id"
};
const typeListParams = ref<Label.TagTypeListParams>({
  current: 1,
  size: 100,
  descs: "updateTime"
});
const getTypeList = (params: Label.TagTypeListParams) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  getTagTypeList(params).then(res => {
    typeList.value = (res.data as ResPage<any>).records as Array<Label.TagType>;
    activeIndex.value = 0;
  });
};

const itemOperate = BUTTONS.value.delete ? ["delete"] : [];
const typeList = ref<Array<Label.TagType>>([]);
const onDelete = async (item: Label.TagItem) => {
  ElMessageBox.confirm(`是否删除${item.name}标签分类?`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true
  }).then(async () => {
    const res = await deleteTagType({ ids: item.id });
    if (res.code === 200) {
      getTypeList(typeListParams.value);
      ElMessage({
        type: "success",
        message: res.msg
      });
    } else {
      ElMessage({
        type: "error",
        message: res.msg
      });
    }
  });
};
</script>
<style lang="scss" scoped>
.label-main {
  display: flex;
  height: 100%;
  overflow: auto;
  background: var(--el-bg-color-page);
}
.operate-btn {
  display: flex;
  justify-content: space-between;
  :deep(.el-button) {
    flex: 1;
  }
}
</style>
