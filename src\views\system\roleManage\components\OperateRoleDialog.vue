<template>
  <el-dialog v-model="dialogVisible" :title="title + '角色'" width="70%" @close="onCancel">
    <el-form ref="roleFormRef" :model="roleForm" :rules="rules" label-position="left">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-col :span="24">
            <el-form-item label="角色名称" prop="roleName">
              <el-input
                placeholder="请输入角色名称"
                v-model="roleForm.roleName"
                type="text"
                clearable
                :disabled="isDisable"
                maxlength="50"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="角色编码" prop="roleCode">
              <el-input
                placeholder="请输入角色编码"
                v-model="roleForm.roleCode"
                type="text"
                clearable
                :disabled="isDisable || props.dialogType === 'edit'"
                maxlength="50"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="确认描述" prop="description">
              <el-input v-model="roleForm.description" type="textarea" clearable :disabled="isDisable" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权限分配" prop="perms">
            <div class="permsBoxStyle">
              <el-tree
                class="classify-tree"
                :checked-keys="roleForm.perms"
                :data="permsList"
                :props="defaultProps"
                show-checkbox
                ref="treeRef"
                node-key="id"
                default-expand-all
                @check="handleCheckChange"
              >
                <template #default="{ node, data }">
                  <div class="custom-tree-node">
                    <!-- 子节点不可选中 -->
                    <div class="custom-tree-node-item">
                      <div>
                        <span
                          v-if="!data.parentId && data.children.length"
                          class="tree-filed iconfont icon-a-filledicon_folder_opened"
                        >
                        </span>
                        <span v-if="node.isLeaf" @click.stop class="disabled">{{ node.label }}</span>
                        <span v-else>{{ node.label }}</span>
                      </div>

                      <el-select
                        size="small"
                        :key="data.id"
                        v-if="(data.menuType === menuType.menu || data.menuType === menuType.button) && data.permissionType"
                        placeholder="请选择"
                        :disabled="isDisable"
                        v-model="data.permissionCode"
                      >
                        <el-option
                          v-for="item in getOptions(data.permissionType)"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </template>
              </el-tree>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel" size="large">取消</el-button>
        <el-button v-if="!isDisable" type="primary" @click="saveData(roleFormRef)" size="large">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, watch, onMounted } from "vue";
import { FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";
import { getTreeList, saveRole, updateRole } from "@/api/modules/system";
import { ElTree } from "element-plus";
import { useDicStore } from "@/stores/modules/dictionaty";

const dictionaryStore = useDicStore();

interface TreeData {
  name?: string;
  permissionType?: string;
  permissionCode?: string;
  roleId?: string;
  id: number | string;
  children?: TreeData[];
}

enum DialogType {
  add = "add",
  edit = "edit"
}
// 菜单类型（1目录 2菜单 3按钮）
enum menuType {
  catalog = 1,
  menu = 2,
  button = 3
}

const treeRef = ref<InstanceType<typeof ElTree>>();

const props = defineProps({
  dialogType: {
    type: String,
    default: () => "details"
  },
  dialogData: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

const title = computed(() => (props.dialogType === "details" ? "查看" : props.dialogType === "edit" ? "编辑" : "新增"));
const isDisable = computed(() => props.dialogType === "details");

const roleFormRef = ref();
const roleForm = ref({
  roleName: "",
  roleCode: "",
  description: "",
  perms: [] as any[]
});
// // 添加根节点
const rootMenu = {
  id: "Root",
  menuName: "全部菜单",
  children: []
};

const permsList = ref<Array<TreeData>>([]);
const defaultPermsTree = ref<Array<TreeData>>([]); // 存储默认的权限数据

const defaultProps = {
  children: "children",
  label: "menuName",
  id: "id"
};

const arrMerge = (treeData: TreeData[], arr2: any[]) => {
  const combined = treeData.map(item1 => {
    arr2.forEach(item2 => {
      if (item1.id === item2.resourceId) {
        Object.assign(item1, { permissionCode: item2.permissionCode, roleId: item2.roleId });
      } else if (item1.children && item1.children.length > 0) {
        arrMerge(item1.children, arr2);
      }
    });
    return item1;
  });
  return combined;
};

const addTreeKey = (treeData: any, perms: any = []) => {
  return treeData.map((item: any) => {
    const permsL = perms.find((perm: any) => perm.resourceId === item.id) || { permissionCode: "", roleId: null };
    return {
      ...item,
      permissionCode: permsL?.permissionCode,
      roleId: permsL?.roleId,
      children: item.children ? addTreeKey(item.children, perms) : []
    };
  });
};

const getPermTree = () => {
  getTreeList({}).then(res => {
    const list = addTreeKey(res.data as Array<TreeData>);
    let menuList = list;
    if (menuList?.length > 0) {
      // 有菜单时候添加“全部菜单”根节点
      menuList = [
        {
          ...rootMenu,
          children: list
        }
      ];
    }

    permsList.value = menuList as Array<TreeData>;
  });
};

onMounted(() => {
  getPermTree();
  dictionaryStore.getDataPermission();
});
// 数据权限字典
const dataSelectList: any = computed(() => dictionaryStore.dataPermission);

const getOptions = (permissionType: string) => {
  let options = [];
  const list = JSON.parse(JSON.stringify(dataSelectList.value));
  options = list?.filter((items: any) => {
    if (permissionType?.split(",").includes(items.value)) {
      return items;
    }
  });
  return options;
};

// 多选
const handleCheckChange = () => {
  const checkedList = treeRef.value?.getCheckedNodes();
  const params = checkedList?.map(item => {
    return {
      permissionType: item.permissionType,
      permissionCode: item.permissionCode,
      roleId: item.roleId,
      menuId: item.id
    };
  });
  roleForm.value.perms = params as any;
};

const checkedList: string[] = [];
// 获取权限回显keys
const checked = (id: string, data: TreeData[], newArr: any) => {
  data.forEach(item => {
    if (item.id == id) {
      if (item?.children?.length == 0) {
        newArr.push(item.id);
      }
    } else {
      if (item?.children?.length != 0) {
        checked(id, item?.children || [], newArr);
      }
    }
  });
};

watch(
  () => props.dialogData,
  () => {
    checkedList.length = 0;
    const { roleName = "", roleCode = "", description = "" } = props.dialogData?.role || {};

    roleForm.value.roleName = roleName || "";
    roleForm.value.roleCode = roleCode || "";
    roleForm.value.description = description || "";

    const perms = props.dialogData?.perms || [];

    // perms处理
    if (perms.length > 0 && !(props.dialogType === DialogType.add)) {
      const data = JSON.parse(JSON.stringify(perms));
      const newPermsList = addTreeKey(permsList.value, data);
      permsList.value = newPermsList;
    } else {
      const newPermsList = addTreeKey(permsList.value);
      permsList.value = newPermsList;
    }

    // 权限树选中keys
    const permsIds = perms.map((item: any) => item.resourceId);

    // 处理半选节点返回子节点选中项keys
    permsIds.forEach((item: string) => {
      checked(item, permsList.value, checkedList);
    });

    roleForm.value.perms = permsIds as any;
    treeRef.value?.setCheckedKeys(checkedList || []);
  }
);

const emits = defineEmits(["update:visible", "cancel", "confirm"]);

const onCancel = () => {
  roleFormRef?.value?.resetFields();
  roleFormRef?.value?.clearValidate();
  dialogVisible.value = false;
  emits("cancel");
};

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const checkedList = treeRef.value?.getCheckedNodes();
      // 半选父节点也需入参
      const halfCheckedNodesArr = treeRef.value?.getHalfCheckedNodes();
      const permissionCode = [...JSON.parse(JSON.stringify(checkedList)), ...JSON.parse(JSON.stringify(halfCheckedNodesArr))];
      const permissionCodeParams = permissionCode
        ?.filter(item => item.id !== "Root")
        ?.map(item => {
          return {
            permissionType: item.permissionType,
            permissionCode: item.permissionCode,
            roleId: item.roleId,
            menuId: item.id
          };
        });
      let params: any = {
        description: roleForm.value.description,
        roleCode: roleForm.value.roleCode,
        roleName: roleForm.value.roleName,
        rolePerms: permissionCodeParams
      };
      if (!(props.dialogType === DialogType.add)) {
        params = {
          ...params,
          initStatus: props.dialogData.role.initStatus,
          roleId: props.dialogData.role.id
        };
      }
      const api = props.dialogType == DialogType.add ? saveRole : updateRole;
      api(params).then((res: any) => {
        emits("confirm");
        ElMessage.success(res.msg);
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};

const rules = reactive<FormRules>({
  roleName: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
  roleCode: [{ required: true, message: "请输入角色编码", trigger: "blur" }],
  perms: [{ required: true, message: "请选择权限", trigger: "blur" }]
});

const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
};

defineExpose({ openDialog });
</script>

<style scoped lang="scss">
.tips {
  color: rgb(153 153 153);
  text-align: center;
}
.classify-tree {
  width: 100%;
  .custom-tree-node {
    display: flex;
    align-items: center;
    width: 100%;
    .tree-filed {
      display: inline-block;
      margin: 0 4px;
      color: #9ca3ac;
    }
  }
  .custom-tree-node-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}
.permsBoxStyle {
  width: 100%;
  max-height: 400px;
  overflow: auto;
}
</style>
