<template>
  <div class="dialog-box">
    <el-dialog
      ref="tagDialogRef"
      v-model="dialogVisible"
      width="470"
      title="新增分类"
      @close="updateModelType"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-form ref="tagFormRef" :model="tagForm" :rules="rules" label-width="100px">
        <el-form-item label="分类名称" prop="name" required class="name-input">
          <el-input maxlength="5" show-word-limit :controls="false" v-model="tagForm.name" style="width: 250px" />
          <el-button type="primary" class="add-btn" :disabled="tagForm.name === ''" @click="confirm(tagFormRef)">新增</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="tagDialog">
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
// import { tagItem } from "@/api/interface/business/theme";

// const props = defineProps<Props>();
// const props = defineProps({
//   tagList: {
//     type: Array as () => string[][],
//     default: () => []
//   }
//   // dialogVisible: {
//   //   type: Boolean,
//   //   default: false
//   // }
// });
const modelIndex = ref<number>(0);
const dialogVisible = ref(false);
const emit = defineEmits(["updateModelType"]);
// const dataList = ref<string[][]>(props.tagList);
const dataList = ref<string[]>([]);
const tagFormRef = ref<FormInstance>();
const tagForm = reactive({
  name: ""
});

const openDialog = (data: any, index: number) => {
  modelIndex.value = index;
  dataList.value = data;
  dialogVisible.value = true;
};
defineExpose({
  openDialog
});
const checkName = (_: any, value: any, callback: any) => {
  if (!value) {
    callback();
  } else {
    const flag = ref<boolean>(false);
    dataList.value.forEach(item => {
      if (item === value) {
        flag.value = true;
      }
    });
    if (flag.value) {
      callback(new Error("该名称已存在"));
    } else {
      callback();
    }
  }
};

const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { validator: checkName, trigger: "blur" }
  ]
});

const updateModelType = () => {
  emit("updateModelType", dataList.value, modelIndex.value);
};

const confirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      if (dataList.value.length >= 20) {
        ElMessage.error("最多只能添加20个分类");
      } else {
        dataList.value.push(tagForm.name);
        ElMessage.success("新增成功！");
        tagForm.name = "";
      }
    }
  });
};
</script>
<style scoped lang="scss">
.tips {
  margin-left: 53px;
  font-size: 14px;
  line-height: 16px;
  color: #9ca3ac;
}
.name-input {
  display: flex;
  flex-direction: row;
  .add-btn {
    margin-left: 10px;
  }
}
.tag-list {
  display: flex;
  flex-direction: row;
}
.condition-select-item {
  display: flex;
  align-items: center;
  width: 70px;
  height: 28px;
  padding-left: 12px;
  margin-top: 16px;
  margin-right: 20px;
  border: 1px solid #e1e3e6;

  // border-radius: $common-radius;
  &:last-child {
    margin-right: 0;
  }
  .select-item-label {
    position: relative;
    margin-right: 14px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 12px;
    font-weight: 400;
    line-height: 22px;
    color: #9ca3ac;
    word-break: keep-all;
    &::after {
      position: absolute;
      top: 50%;
      right: -7px;
      width: 1px;
      height: 16px;
      content: " ";
      background-color: #9ca3ac;
      transform: translate(0, -50%);
    }
  }
  .select-item-value {
    flex: 1;
    padding-right: 8px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 12px;
    font-weight: 400;
    line-height: 22px;
    color: var(--el-color-primary);
  }
  .close-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 100%;
    cursor: pointer;
    .close-icon {
      width: 10px;
      font-size: 10px;
      color: #9ca3ac;
      cursor: pointer;
    }
    img {
      width: 10px;
      height: 10px;
    }
  }
  &:hover {
    border: 1px solid var(--el-color-primary);
    .close-box {
      background-color: var(--el-color-primary);
      .close-icon {
        color: #ffffff;
      }
    }
  }
}
</style>
