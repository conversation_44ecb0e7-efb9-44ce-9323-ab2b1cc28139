<template>
  <div class="banner-box table-box">
    <pro-table ref="proTableRef" :columns="columns" :request-api="getTableList" :tool-button="false">
      <template #status="{ row }">
        <custom-tag
          :type="statusTypeMap[row.status]"
          :status="row.status"
          :label="useDictLabel(audit_status, row.status)"
        ></custom-tag>
      </template>
      <template #auditAction="{ row }">
        {{ useDictLabel(AUDIT_ACTION, row?.auditAction) }}
      </template>
      <template #operation="{ row }">
        <auth-button :row-data="row" :auth-list="authList"></auth-button>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="policyList">
import { ref, onActivated } from "vue";
import { useRouter } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import type { ProTableInstance, ColumnProps } from "@/components/ProTable/interface/index";
import { policyAuditDetail, bannerAuditList } from "@/api/modules/business/banner";
import { policyNewsStorage } from "@/utils/storage/edit";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { IAuthButtonItem } from "@/components/AuthButton/typings";
import CustomTag from "@/components/CustomTag/index.vue";
import { OperateType } from "../type";

const { AUDIT_ACTION, audit_status } = useDict("AUDIT_ACTION", "audit_status");

const statusTypeMap: { [key: string]: string } = {
  "0": "yellow",
  "2": "green",
  "3": "red"
};

const router = useRouter();
const proTableRef = ref<ProTableInstance>();
const PENDING_AUDIT = 1; // 待审核
const { BUTTONS } = useAuthButtons();

onActivated(() => {
  proTableRef.value?.getTableList();
});

const authList: IAuthButtonItem[] = [
  {
    contentName: "查看",
    iconClass: "View",
    itemClick: scope => toEdit(scope, OperateType.detail),
    authName: "policyReviewView"
  },
  {
    contentName: "审核",
    iconClass: "Start",
    itemClick: scope => toEdit(scope, OperateType.review),
    show: scope => scope.status === PENDING_AUDIT,
    authName: "policyReview"
  }
];

const getTableList = async (search: any) => {
  if (!BUTTONS.value?.policyQuery) {
    return;
  }
  return await bannerAuditList({
    ...search,
    descs: "createTime",
    bizType: "policy"
  });
};

const columns = ref<ColumnProps[]>([
  {
    prop: "bizInfo",
    label: "政策标题",
    align: "left",
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "auditAction",
    label: "审核操作",
    align: "left",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {AUDIT_ACTION.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },

  {
    prop: "creatorName",
    align: "left",
    label: "提交账号"
  },
  {
    prop: "createTime",
    label: "提交时间",
    align: "left",
    width: 180,
    sortable: true
  },
  {
    prop: "status",
    label: "审核状态",
    align: "left",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {audit_status.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditTime",
    label: "审核时间",
    align: "left",
    width: 180
  },
  {
    prop: "auditorName",
    label: "审核人",
    align: "left"
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 130 }
]);

policyNewsStorage.clearExpired();

const toEdit = (data: any, type = OperateType.detail) => {
  const auditMsg = data.status != 0 ? data.auditMsg : undefined;

  policyAuditDetail({ bizId: data.bizId, id: data.id })
    .then(resp => {
      const respData = resp.data || {};
      policyNewsStorage.set(data.bizId, { ...respData, listId: data.id });
      router.push({
        path: "/verification/operationsReview/policyNewsReview",
        query: {
          id: data.bizId,
          type,
          auditMsg
        }
      });
    })
    .catch(err => {
      console.log(err);
    });
};
</script>
