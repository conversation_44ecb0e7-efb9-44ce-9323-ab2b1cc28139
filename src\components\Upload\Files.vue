<template>
  <div class="upload-box">
    <el-upload
      :id="uuid"
      ref="uploadRef"
      action="#"
      :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '', hideBox ? 'hide-box' : '']"
      :multiple="true"
      :disabled="self_disabled"
      :show-file-list="showFileList"
      :http-request="handleHttpUpload"
      :before-upload="beforeUpload"
      :on-success="uploadSuccess"
      :on-preview="isShowDownload ? undefined : handlePreview"
      :on-error="uploadError"
      :on-remove="deleteVideo"
      :on-exceed="handleExceed"
      :drag="drag"
      :accept="fileType"
      :limit="limit"
      v-model:file-list="_fileList"
    >
      <template #file="{ file }" v-if="isShowDownload">
        <div class="custom-file-item el-upload-list__item-name">
          <el-icon><Document /></el-icon>
          <span>{{ file.name }}</span>
          <el-button size="small" type="text" @click="handlePreview(file)">下载</el-button>
        </div>
      </template>

      <template #trigger>
        <slot name="trigger">
          <div class="upload-empty">
            <el-icon><Plus /></el-icon>
          </div>
        </slot>
      </template>
      <template #default>
        <slot name="default"> </slot>
      </template>
    </el-upload>
    <div class="el-upload__tip">
      <slot name="tip"></slot>
    </div>
  </div>
</template>

<script setup lang="ts" name="UploadImg">
import { ref, computed, inject, watch } from "vue";
import { generateUUID } from "@/utils";
import { uploadImg, deleteImg as deleteImgApi, uploadImgPrivate } from "@/api/modules/upload";
import { ElMessage, ElNotification, formContextKey, formItemContextKey } from "element-plus";
import type { UploadFile, UploadFiles, UploadProps, UploadRequestOptions, UploadUserFile } from "element-plus";
import http from "@/api";
import { downloadUtil } from "@/utils/download";

interface UploadFileProps {
  // fileUrl: string; // 文件地址 ==> 必传
  fileList: UploadUserFile[];
  api?: any; // 上传文件的 api 方法，一般项目上传都是同一个 api 方法，在组件里直接引入即可 ==> 非必传
  drag?: boolean; // 是否支持拖拽上传 ==> 非必传（默认为 true）
  disabled?: boolean; // 是否禁用上传组件 ==> 非必传（默认为 false）
  limit?: number;
  fileSize?: number; // 文件大小限制 ==> 非必传（默认为 5M）
  fileType?: string; // 文件类型限制 ==> 非必传（默认为 ["image/jpeg", "image/png", "image/jpg"]）
  height?: string; // 组件高度 ==> 非必传（默认为 150px）
  width?: string; // 组件宽度 ==> 非必传（默认为 150px）
  borderRadius?: string; // 组件边框圆角 ==> 非必传（默认为 8px）
  showFileList?: boolean;
  hideBox?: boolean; // 是否显示上传触发框 ==> 非必传（默认为 false）
  isPrivate?: boolean;
  isShowDownload?: boolean;
}

// 接受父组件参数
const props = withDefaults(defineProps<UploadFileProps>(), {
  fileList: () => [],
  api: null,
  drag: true,
  disabled: false,
  fileSize: 20,
  limit: 1,
  fileType: ".rar,.zip,.doc,.docx,.pdf",
  height: "50px",
  width: "150px",
  borderRadius: "8px",
  showFileList: false,
  hideBox: false,
  isPrivate: false,
  isShowDownload: false
});

// 生成组件唯一id
const uuid = ref("id-" + generateUUID());

const uploadRef = ref(null);

// 查看文件
// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0);
// 获取 el-form-item 组件上下文
const formItemContext = inject(formItemContextKey, void 0);
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
  return props.disabled || formContext?.disabled;
});

const _fileList = ref<UploadUserFile[]>(props.fileList);

// 监听 props.fileList 列表默认值改变
watch(
  () => props.fileList,
  (n: UploadUserFile[]) => {
    _fileList.value = n;
  }
);
// const fileList = ref<UploadUserFile[]>([]);
/**
 * @description 文件上传
 * @param options upload 所有配置项
 * */
interface UploadEmits {
  (e: "update:fileList", value: UploadUserFile[]): void;
  (e: "uploadSuccess", value: any): void;
}
const emit = defineEmits<UploadEmits>();

const handleHttpUpload = async (options: UploadRequestOptions) => {
  let formData = new FormData();
  formData.append("file", options.file);
  try {
    // const api = props.api ?? uploadImg;
    const api = props.api ? props.api : props.isPrivate ? uploadImgPrivate : uploadImg;
    const { data } = await api(formData);

    options.onSuccess(data);
  } catch (error) {
    options.onError(error as any);
  }
};

/**
 * @description 删除文件
 * */
const deleteVideo = (file: UploadFile) => {
  _fileList.value = _fileList.value.filter(item => item.url !== file.url || item.name !== file.name);
  emit("update:fileList", _fileList.value);
};

/**
 * @description 文件上传之前判断
 * @param rawFile 选择的文件
 * */
const beforeUpload: UploadProps["beforeUpload"] = rawFile => {
  const fileSize = rawFile.size / 1024 / 1024 < props.fileSize;
  const type = rawFile.name.substring(rawFile.name.lastIndexOf("."));
  const fileType = props.fileType.includes(type);
  if (!fileType)
    ElNotification({
      title: "温馨提示",
      message: `上传的文件不符合所需的格式,仅支持的文件格式为${props.fileType}！`,
      type: "warning"
    });
  if (!fileSize)
    setTimeout(() => {
      ElNotification({
        title: "温馨提示",
        message: `上传的文件大小不能超过 ${props.fileSize}M！`,
        type: "warning"
      });
    }, 0);
  return fileType && fileSize;
};

/**
 * @description 文件上传成功
 * */
const uploadSuccess = (response: { link: string } | undefined, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (!response) return;
  uploadFile.url = response.link;
  // console.log("uploadFiles", uploadFiles);
  // emit("update:fileList", uploadFiles);

  const rebase = _fileList.value.map((item: any) => {
    if (Reflect.has(item, "raw")) {
      return { ...response, name: item.name };
    } else {
      return { ...item };
    }
  });
  emit("uploadSuccess", response);
  emit("update:fileList", rebase);
  // 调用 el-form 内部的校验方法（可自动校验）
  formItemContext?.prop && formContext?.validateField([formItemContext.prop as string]);
  ElNotification({
    title: "温馨提示",
    message: "文件上传成功！",
    type: "success"
  });
};

/**
 * @description 文件上传错误
 * */
const uploadError = () => {
  ElNotification({
    title: "温馨提示",
    message: "文件上传失败，请您重新上传！",
    type: "error"
  });
};
/**
 * @description 文件数超出
 * */
const handleExceed = () => {
  ElNotification({
    title: "温馨提示",
    message: `当前最多只能上传 ${props.limit} 个文件，请移除后上传！`,
    type: "warning"
  });
};

// 点击文件列表中已上传的文件时的钩子,进行下载
const handlePreview = (file: any) => {
  if (props.isPrivate) {
    let newStr = file.link.replace("/api", "");
    http.download(newStr).then(res => {
      downloadUtil.downloadFile(res.data, file.name);
    });
  } else {
    let link = document.createElement("a"); // 定义一个a标签
    link.download = file.name; // 下载后的文件名称
    link.href = file.link; // 需要生成一个 URL 来实现下载
    link.click(); // 模拟在按钮上实现一次鼠标点击
    window.URL.revokeObjectURL(link.href);
    console.log(link);
  }
};

const triggerUpload = () => {
  uploadRef.value?.onChange();
};

defineExpose({
  triggerUpload
});
</script>

<style scoped lang="scss">
.is-error {
  .upload {
    :deep(.el-upload),
    :deep(.el-upload-dragger) {
      border: 1px dashed var(--el-color-danger) !important;
      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}
:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color);
    border: 1px dashed var(--el-border-color-darker) !important;
    &:hover {
      border: 1px dashed var(--el-border-color-darker) !important;
    }
  }
}
.upload-box {
  .no-border {
    :deep(.el-upload) {
      border: none !important;
    }
  }
  .hide-box {
    :deep(.el-upload.el-upload--text) {
      display: none;
    }
  }
  :deep(.upload) {
    .el-upload {
      position: relative;

      // display: flex;
      align-items: center;
      justify-content: center;
      width: v-bind(width);
      height: v-bind(height);
      overflow: hidden;

      // border: 1px dashed var(--el-border-color-darker);
      border-radius: v-bind(borderRadius);
      transition: var(--el-transition-duration-fast);
      &:hover {
        border-color: var(--el-color-primary);
        .upload-handle {
          opacity: 1;
        }
      }
      .el-upload-dragger {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0;
        overflow: hidden;
        background-color: transparent;
        border: 1px dashed var(--el-border-color-darker);
        border-radius: v-bind(borderRadius);
        &:hover {
          border: 1px dashed var(--el-color-primary);
        }
      }
      .el-upload-dragger.is-dragover {
        background-color: var(--el-color-primary-light-9);
        border: 2px dashed var(--el-color-primary) !important;
      }
      .upload-image {
        width: 100%;
        height: 100%;
        font-size: 32px;
        color: #0052d9;
      }
      .upload-empty {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        line-height: 30px;
        color: var(--el-color-info);
        .el-icon {
          font-size: 28px;
          color: var(--el-text-color-secondary);
        }
      }
      .upload-handle {
        position: absolute;
        top: 0;
        right: 0;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        cursor: pointer;
        background: rgb(0 0 0 / 60%);
        opacity: 0;
        transition: var(--el-transition-duration-fast);
        .handle-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 0 6%;
          color: aliceblue;
          .el-icon {
            margin-bottom: 40%;
            font-size: 130%;
            line-height: 130%;
          }
          span {
            font-size: 85%;
            line-height: 85%;
          }
        }
      }
    }
  }
  .el-upload__tip {
    line-height: 18px;
    text-align: center;
  }
  .custom-file-item:hover {
    color: var(--el-color-primary);
  }
}
</style>
