<template>
  <div class="visits">
    <div class="mb20 header-title-common">访问/参与量</div>
    <ProTable ref="proTable" :data="dataSource" :columns="columns" :tool-button="false" :pagination="false"></ProTable>
  </div>
</template>

<script setup lang="ts">
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { ref } from "vue";

defineProps({
  dataSource: {
    type: Array,
    default: () => {
      return [];
    }
  }
});

const proTable = ref<ProTableInstance>();

const columns: ColumnProps[] = [
  {
    prop: "pv",
    label: "页面PV",
    align: "center"
  },
  {
    prop: "uv",
    label: "页面UV",
    align: "center"
  },
  {
    prop: "loginNum",
    label: "登录人数",
    align: "center"
  },
  {
    prop: "joinNum",
    label: "参与人数",
    align: "center"
  }
];
</script>

<style scoped lang="scss">
.visits {
  margin-top: 10px;
}
</style>
