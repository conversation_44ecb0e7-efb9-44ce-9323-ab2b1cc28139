import { Platform } from "@/api/interface/platform";
import http from "@/api";

/**
 * 合作平台账号列表
 * @param params
 */
export const getPlatformAccountList = (params: Platform.PlatformListParams) => {
  return http.get(`/system/sysPlatformAccount/pageList`, params);
};

/**
 * 合作平台账号绑定/解绑
 * @param params
 */
export const platformAccountBind = (params: Platform.PlatformBindParams) => {
  return http.post(`/system/sysPlatformAccount/bind`, params);
};

/**
 * 合作平台账号新增
 * @param params
 */
export const platformAccountAdd = (params: Platform.PlatformAccountAddOrEditParams) => {
  return http.post(`/system/sysPlatformAccount/save`, params);
};

/**
 * 合作平台账号编辑
 * @param params
 */
export const platformAccountEdit = (params: Platform.PlatformAccountAddOrEditParams) => {
  return http.post(`/system/sysPlatformAccount/update`, params);
};

/**
 * 合作平台账号删除
 * @param params
 */
export const platformAccountDelete = (params: Platform.PlatformAccountDeleteParams) => {
  return http.post(`/system/sysPlatformAccount/delete`, params);
};

/**
 * 合作平台账号导出
 * @param params
 */
export const platformAccountExport = (params: Platform.PlatformAccountExportParams) => {
  return http.download(`/system/sysPlatformAccount/export`, params);
};

/**
 * 合作平台导入模板下载
 * @param params
 */
export const platformAccountImportTemplate = (params: Platform.PlatformAccountImportTemplateParams) => {
  return http.post(`/system/paramConfig/search`, params);
};

/**
 * 合作平台导入
 * @param params
 */
export const platformAccountImport = (params: FormData) => {
  return http.post(`/system/sysPlatformAccount/import`, params);
};
