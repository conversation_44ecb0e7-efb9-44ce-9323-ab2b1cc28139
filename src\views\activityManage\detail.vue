<template>
  <div class="activity-detail">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="活动信息" name="activeInfo">
        <ActiveEditComps></ActiveEditComps>
      </el-tab-pane>
      <el-tab-pane label="活动数据" name="activeData">
        <!-- 访问量 -->
        <Visits :data-source="dataSource" />
        <!-- 明细 -->
        <Detailed />
        <!-- 奖品领取情况 -->
        <PrizeStatus :data-source="productAnalysis" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import ActiveEditComps from "./edit.vue";
import Visits from "./comps/visits.vue";
import PrizeStatus from "./comps/prizeStatus.vue";
import Detailed from "./comps/detailed.vue";

import type { TabsPaneContext } from "element-plus";
import { queryMarketingGetAnalysis } from "@/api/modules/activityH5";
import { AnalysisItem } from "./typings";
import { useRoute } from "vue-router";
import { activityH5 } from "@/api/interface/activityH5";

const route = useRoute();

const activeName = ref("activeInfo");

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};

const dataSource = ref<Array<AnalysisItem>>([]);
const productAnalysis = ref<Array<activityH5.getAnalysisItem>>([]);

const fetch = () => {
  queryMarketingGetAnalysis({ marketingId: route.query.marketingId as string }).then(res => {
    dataSource.value = [{ pv: res.data.pv, uv: res.data.uv, loginNum: res.data.loginNum, joinNum: res.data.joinNum }];
    productAnalysis.value = res.data.productAnalysis;
  });
};
fetch();
</script>

<style scoped lang="scss">
.activity-detail {
  padding: 24px;
  background-color: #ffffff;
}
:deep(.activity-page) {
  padding: 0;
}
</style>
