// ? Element 常用表单校验规则
import dayjs from "dayjs";

/**
 *  @rule 手机号
 */
export function checkPhoneNumber(rule: any, value: any, callback: any) {
  const regexp = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/;
  if (value === "") callback("请输入手机号码");
  if (!regexp.test(value)) {
    callback(new Error("请输入正确的手机号码"));
  } else {
    return callback();
  }
}

/**
 *  @rule 正整数
 *  限制输入1-10000000的整数
 */
export function zNumber(rule: any, value: any, callback: any) {
  // /^(\d|[1-9]\d|9999)?$/;
  const regexp = /^(?!0)(?:[0-9]{1,7}|10000000)$/;
  if (value === "") callback("请输入");
  if (!regexp.test(value)) {
    callback(new Error("限制输入1-10000000的整数"));
  } else {
    return callback();
  }
}

/**
 * 限制时间只能选择最多12个月，非必填
 */
export const checkMax12Months = (_: any, value: any, callback: any) => {
  if (!value) {
    callback();
  } else {
    // 定义两个日期字符串
    const dateString1 = value[0];
    const dateString2 = value[1];
    // 将日期字符串转换为 dayjs 对象
    const date1 = dayjs(dateString1);
    const date2 = dayjs(dateString2);

    // 获取两个日期之间的月数差
    const monthDiff = Math.abs(date2.diff(date1, "month"));

    if (monthDiff > 11) {
      callback(new Error("最多只能查询12个月的数据"));
    }
  }
  callback();
};
