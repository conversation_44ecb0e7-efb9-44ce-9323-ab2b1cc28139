<template>
  <el-button
    style="min-width: 80px"
    size="small"
    type="primary"
    :disabled="countDownButtonArgs.timing"
    @click="emit('click', successCallback)"
    >{{
      loading
        ? loadingText
        : countDownButtonArgs.timing
        ? countDownButtonArgs.count + "s后可重新获取"
        : countDownButtonArgs.isRetrieve
        ? retrieveButtonText
        : buttonText
    }}
  </el-button>
</template>
<script setup lang="ts">
import { reactive } from "vue";

const props = defineProps({
  maxSecondNum: {
    type: Number,
    default: 60
  },
  buttonText: {
    type: String,
    default: "获取验证码"
  },
  retrieveButtonText: {
    type: String,
    default: "重新获取"
  },
  loadingText: {
    type: String,
    default: "发送中..."
  },
  loading: {
    type: Boolean,
    default: false
  }
});
type EmitsType = { (e: "click", successCallback: () => void): void };
const emit = defineEmits<EmitsType>();

const countDownButtonArgs = reactive<{ timing: boolean; count: number; isRetrieve: boolean }>({
  timing: false,
  count: 0,
  isRetrieve: false
});

const successCallback = () => {
  countDownButtonArgs.timing = true;
  countDownButtonArgs.count = props.maxSecondNum;
  const timer = setInterval(() => {
    const { count } = countDownButtonArgs;
    if (count > 0 && count <= props.maxSecondNum) {
      countDownButtonArgs.count--;
    } else {
      countDownButtonArgs.timing = false;
      clearInterval(timer);
      countDownButtonArgs.count = 0;
      countDownButtonArgs.isRetrieve = true;
    }
  }, 1000);
};
</script>
