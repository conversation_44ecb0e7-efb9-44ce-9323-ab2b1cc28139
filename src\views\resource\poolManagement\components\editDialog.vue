<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-28 09:16:50
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-28 16:55:43
 * @Description: file content
-->
<template>
  <el-dialog v-model="dialogVisible" title="新增" width="30%" @closed="handleClose">
    <el-form ref="formRef" :model="formData" label-width="140px" :rules="rules" label-position="left">
      <el-form-item label="云平台：" prop="cloudProvider">
        <el-select
          v-model="formData.cloudProvider"
          placeholder="请选择"
          :disabled="type === editType['edit']"
          style="width: 100%"
        >
          <el-option
            :label="item.itemValue"
            :value="item.itemValue"
            v-for="(item, index) in cloudPlatformList"
            :key="item.itemKey + index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="区域名称：" prop="regionNumber">
        <el-select v-model="formData.regionNumber" placeholder="请选择" :disabled="type === editType['edit']" style="width: 100%">
          <el-option
            :label="item.regionName"
            :value="item.regionNumber"
            v-for="(item, index) in regionList"
            :key="item.itemKey + index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资源池名称：" prop="resourcePoolName">
        <el-input v-model="formData.resourcePoolName" type="text" placeholder="请输入资源池名称" maxlength="40" />
      </el-form-item>

      <el-form-item label="资源池代码：" prop="code">
        <el-input
          v-model="formData.code"
          type="text"
          placeholder="请输入资源池代码"
          maxlength="20"
          :disabled="type === editType['edit']"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel" size="large">取消</el-button>
        <el-button type="primary" @click="saveData(formRef)" size="large">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import { useDicStore } from "@/stores/modules/dictionaty";
import { addResourcePool, editResourcePool, queryRegion, queryResourcePoolDetail } from "@/api/modules/resource";
const formRef = ref<FormInstance>();
const dictionaryStore = useDicStore();
const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  type: {
    type: Number,
    default: editType["add"]
  },
  listItemInfo: {
    type: Object,
    default: () => {}
  }
});
const regionList: any = ref([]);
const emits = defineEmits(["update:visible", "confirm"]);
const dialogVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    !value && closeDialog();
    emits("update:visible", value);
  }
});
const formData = ref({
  regionNumber: "",
  resourcePoolName: "",
  code: "",
  cloudProvider: ""
});
watch(
  () => formData.value.cloudProvider,
  value => {
    getRegionList();
  }
);
watch(
  () => props.listItemInfo,
  async value => {
    if (props.type === editType["edit"]) {
      const { data }: { data: any } = await queryResourcePoolDetail(props.listItemInfo);
      formData.value.cloudProvider = data.cloudProvider;
      formData.value.regionNumber = data.regionNumber;
      formData.value.resourcePoolName = data.resourcePoolName;
      formData.value.code = data.code;
    } else {
      formData.value = {
        regionNumber: "",
        resourcePoolName: "",
        code: "",
        cloudProvider: ""
      };
      formRef?.value?.resetFields();
    }
  },
  {
    deep: true
  }
);

const checkCode = (rule: any, value: any) => {
  return value && /^[A-Za-z]+$/.test(value);
};
const rules = reactive({
  cloudProvider: [
    {
      required: true,
      message: "请选择云平台",
      trigger: "change"
    }
  ],
  regionNumber: [
    {
      required: true,
      message: "请选择区域",
      trigger: "change"
    }
  ],
  resourcePoolName: [
    {
      required: true,
      message: "请输入资源池名称",
      trigger: "blur"
    },
    { max: 40, message: "最长40个字符", trigger: "blur" }
  ],
  code: [
    {
      required: true,
      message: "请输入资源池代码",
      trigger: "blur"
    },
    { validator: checkCode, message: "只能输入字母", trigger: "blur" },
    { max: 20, message: "最长20个字符", trigger: "blur" }
  ]
});
//云平台字典
const cloudPlatformList: any = computed(() => dictionaryStore.cloudPlatformList);
// 提交数据
const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: addResourcePool,
        [editType["edit"]]: editResourcePool
      };
      const praamsObject = {
        [editType["add"]]: () => {
          return {
            regionName: regionList.value.find((item: any) => item.regionNumber === formData.value.regionNumber).regionName,
            regionNumber: formData.value.regionNumber,
            resourcePoolName: formData.value.resourcePoolName,
            code: formData.value.code,
            cloudProvider: formData.value.cloudProvider,
            country: "中国",
            province: "北京市",
            city: "北京市"
          };
        },
        [editType["edit"]]: () => {
          return {
            cloudProvider: formData.value.cloudProvider,
            resourcePoolName: formData.value.resourcePoolName,
            resourcePoolNumber: props.listItemInfo.resourcePoolNumber,
            country: "中国",
            province: "北京市",
            city: "北京市"
          };
        }
      };

      apiObject[props.type as 1 | 2](praamsObject[props.type as 1 | 2]()).then((res: any) => {
        if (res.code === 200) {
          ElMessage.success("操作成功");
          emits("confirm");
          dialogVisible.value = false;
        }
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
const onCancel = () => {
  dialogVisible.value = false;
};

const getAccountList = () => {
  // 先获取字典
  dictionaryStore.getCloudPlatformList(); // 状态
};

const getRegionList = () => {
  if (formData.value.cloudProvider) {
    queryRegion({
      cloudProvider: formData.value.cloudProvider
    }).then(res => {
      regionList.value = res.data;
    });
  } else {
    regionList.value = [];
  }
};
const handleClose = () => {
  dialogVisible.value = false;
};
const closeDialog = () => {
  formRef?.value?.clearValidate();
  emits("update:visible", false);
};
getAccountList();
onMounted(() => {});
</script>
<style scoped lang="scss"></style>
