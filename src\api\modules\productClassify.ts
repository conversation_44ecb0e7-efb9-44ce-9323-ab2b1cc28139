/*
 * @Author: <EMAIL>
 * @Date: 2023-09-06 09:31:29
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-09-25 10:03:49
 * @Description: file content
 */
import * as ProductClassify from "@/api/interface/productClassify";
import http from "@/api";

/**
 * @name 产品分类
 */
// 产品分类树
export const getTypeTree = () => {
  return http.post(`/cnud-product/pmsCategory/list/withChildren`); // 正常 post json 请求  ==>  application/json
};

// 删除分类
export const deleteType = (params: ProductClassify.deleteParams) => {
  return http.post(`/cnud-product/pmsCategory/remove`, params); // 正常 post json 请求  ==>  application/json
};

//查询分页
export const typePage = (params: ProductClassify.pageParams) => {
  return http.get(`/cnud-product/pmsCategory/queryPage`, params); // 正常 get json 请求  ==>  application/form
};
//新增分类
export const addType = (params: ProductClassify.typeParams) => {
  return http.post(`/cnud-product/pmsCategory/save`, params); // 正常 post json 请求  ==>  application/json
};
//编辑分类
export const updateType = (params: ProductClassify.typeParams) => {
  return http.post(`/cnud-product/pmsCategory/update`, params); // 正常 post json 请求  ==>  application/json
};
//分类排序
export const sortType = (params: ProductClassify.typeParams) => {
  return http.post(`/cnud-product/pmsCategory/sort`, params); // 正常 post json 请求  ==>  application/json
};
