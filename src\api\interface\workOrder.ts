export namespace WorkOrderType {
  interface WorkOrderLog {
    attachmentIds: string;
    content: string;
    createTime: string;
    creatorId: string;
    creatorName: string;
    id: string;
    images: string;
    updateTime: string;
    updaterId: string;
    updaterName: string;
    workOrderId: string;
  }
  export interface WorkOrderDetail {
    attachmentIds: string;
    code: string;
    createTime: string;
    creatorId: string;
    creatorName: string;
    description: string;
    durationDesc: string;
    handleAccount: string;
    handleBy: string;
    handleName: string;
    handleParty: string;
    handleTime: string;
    id: string;
    orderStatus: string;
    orderType: string;
    phone: string;
    updateTime: string;
    updaterId: string;
    updaterName: string;
    urgency: string;
    workOrderLogList: WorkOrderLog[];
  }
}
