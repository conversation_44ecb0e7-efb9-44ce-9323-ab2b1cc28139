<template>
  <el-table :data="data" class="w100" border :header-cell-style="{ fontWeight: 'bold', color: '#303133', background: '#f5f7fa' }">
    <el-table-column align="left" prop="orderType" label="订单类型">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" :content="useDictLabel(ord_order_type, row?.orderType)" placement="top-start">
            {{ useDictLabel(ord_order_type, row?.orderType) }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="status" label="处理状态">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="useDictLabel(zq_operate_status, row?.status)"
            placement="top-start"
          >
            {{ row?.status == "4" || row?.status == "5" ? useDictLabel(zq_operate_status, row?.status) : "" }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="operateRemark" label="处理意见">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="row?.operateRemark" placement="top-start">
            {{ row?.operateRemark }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="operatorName" label="处理人">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="row?.operatorName" placement="top-start">
            {{ row?.operatorName }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="operatorTime" label="处理时间">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="row?.operatorTime" placement="top-start">
            {{ row?.operatorTime }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
interface TablePropType {
  data: any[];
}

import { useDicStore } from "@/stores/modules/dictionaty";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { storeToRefs } from "pinia";
import { ref, watch } from "vue";
const dictionaryStore = useDicStore();

/* 获取字典 */
// const { orderTypeDic, operateStatus } = storeToRefs(dictionaryStore);
const { zq_operate_status, ord_order_type } = useDict("zq_operate_status", "ord_order_type");
// console.log("1111111111111111111111111111");
// console.log(zq_operate_status.value);
// console.log(ord_order_type.value);
// console.log(orderTypeDic);
// console.log(operateStatus);

defineProps<TablePropType>();
</script>

<style scoped lang="scss">
.text {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.time-text {
  span {
    display: block;
  }
}
.w100 {
  width: 100%;
}
</style>
