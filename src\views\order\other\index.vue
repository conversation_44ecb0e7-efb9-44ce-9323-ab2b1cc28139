<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="第三方订单"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
    >
      <template #status="{ row }">
        <CustomTag
          :type="'order'"
          :status="row.status"
          :label="useDictLabel(dictionaryStore?.orderStatusDic, row.status)"
        ></CustomTag>
      </template>

      <template #contractId="{ row }">
        <p class="link-text" @click="showContractDetail(row)">{{ row.contractId || "--" }}</p>
      </template>

      <template #productType="{ row }">
        {{ useDictLabel(dictionaryStore?.orderProdType, row.productType) }}
      </template>

      <template #state="{ row }">
        {{ useDictLabel(dictionaryStore?.orderState, row.state) || "--" }}
      </template>

      <template #score="{ row }">
        <CustomTag
          :type="'order'"
          :status="row.score"
          :label="useDictLabel(dictionaryStore?.orderScore, row.score) || '--'"
        ></CustomTag>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'upload'">
          <el-tooltip content="关联合同" placement="top">
            <i class="iconfont2 opera-icon icon-lineicon_upload" @click="openDialog(scope.row, editType.edit)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>

    <!--  新增编辑弹窗  -->
    <add-edit-dialog
      v-model:visible="labelDialogVisible"
      :type="labelDialogParams.type"
      :params="labelDialogParams.params"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-dialog>
    <contract-detail ref="contractDetailRef"></contract-detail>
  </div>
</template>

<script setup lang="tsx" name="orderOther">
import { ref, reactive, onMounted, computed, onActivated } from "vue";
import { useDictLabel } from "@/hooks/useDict";
import { useDicStore } from "@/stores/modules/dictionaty";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getOtherOrderList } from "@/api/modules/order";
import CustomTag from "@/components/CustomTag/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import AddEditDialog from "./components/addOrEditDialog.vue";
import ContractDetail from "./components/contractDetail.vue";

const { BUTTONS } = useAuthButtons();
enum editType {
  "add" = 1,
  "edit" = 2,
  "revoke" = 3,
  "details" = 4
}

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

const dictionaryStore = useDicStore();

onMounted(() => {
  dictionaryStore.getOrderStatus(); // 订单状态
  dictionaryStore.getOrderState();
  dictionaryStore.getOrderScore();
  dictionaryStore.getProdType();
  proTable.value?.getTableList();
});

onActivated(() => {
  proTable.value?.getTableList();
});
const orderStatusDic: any = computed(() => dictionaryStore.orderStatusDic);

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  return getOtherOrderList({
    ...newParams,
    source: "自有"
  });
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  { prop: "orderId", label: "订单号", search: { el: "input" } },
  {
    prop: "productName",
    label: "产品名称"
  },
  {
    prop: "productType",
    label: "产品类型"
  },
  {
    prop: "contactsName",
    label: "客户名称"
  },
  {
    prop: "supplierName",
    label: "供应商名称"
  },
  {
    prop: "orderType",
    label: "订单类型"
  },
  {
    prop: "contractId",
    label: "合同编码"
  },
  {
    prop: "orderAmountStr",
    label: "订单金额（元）"
  },
  {
    prop: "source",
    label: "订单来源"
  },
  {
    prop: "createTime",
    label: "订单时间"
  },
  {
    prop: "state",
    label: "订单状态"
  },
  {
    prop: "score",
    label: "评价"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

interface typeDialog {
  type: number;
  params: Object;
}
// 编辑弹窗
const labelDialogVisible = ref(false);
const labelDialogParams = ref<typeDialog>({
  params: {},
  type: editType.add
});

const onCancel = () => {};
const onConfirm = () => {
  proTable.value?.getTableList();
};

const openDialog = async (row: any, operateType = editType.edit) => {
  //  新增 编辑
  const dialogParams = {
    type: operateType,
    params: {
      ...row
    }
  };
  labelDialogVisible.value = true;
  labelDialogParams.value = dialogParams;
};

// 查看合同
const contractDetailRef = ref();
const showContractDetail = (row: any) => {
  contractDetailRef.value?.show(row);
};
</script>

<style lang="scss" scoped>
:global(.overflow-tooltip-style) {
  max-width: calc(100% - 24px);
  white-space: pre-wrap;
}
</style>
