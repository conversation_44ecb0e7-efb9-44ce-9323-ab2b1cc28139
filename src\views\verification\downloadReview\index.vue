<template>
  <div class="table-box">
    <pro-table ref="proTable" :request-api="getTableList" :columns="columns" :tool-button="false">
      <template #operation="{ row }">
        <el-tooltip content="审核" placement="top" v-if="row.status === '1'">
          <i v-auth="'exportHandle'" class="iconfont2 opera-icon icon-shenhe" @click="verify(row)"></i>
        </el-tooltip>
      </template>
    </pro-table>
    <el-dialog v-model="visible" width="600" title="审核操作" destroy-on-close @close="handleClose">
      <el-form ref="formRef" style="width: 100%" :model="formRefData" label-width="auto" status-icon>
        <el-form-item
          label="审核状态"
          prop="status"
          required
          :rules="[
            {
              required: true,
              message: '请选择审核状态',
              trigger: 'blur'
            }
          ]"
        >
          <el-radio-group v-model="formRefData.status">
            <el-radio v-for="item in selectAudit" :label="item.itemKey" :key="item.itemKey">{{ item.itemValue }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="审核意见"
          prop="advice"
          required
          :rules="[
            {
              required: true,
              message: '请输入审核意见',
              trigger: 'blur'
            }
          ]"
        >
          <el-input
            v-model="formRefData.advice"
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 5 }"
            show-word-limit
            maxlength="200"
            placeholder="驳回时必须填写审核意见，通过默认审核意见为同意"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { queryDownloadAudit, exportAuditHandle } from "@/api/modules/verification";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { ref, computed, reactive } from "vue";
import type { ComponentSize, FormInstance, FormRules } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import CustomTag from "@/components/CustomTag/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { downloadMange } from "@/api/interface/upload";

const { BUTTONS } = useAuthButtons();

const { audit_status, export_type } = useDict("audit_status", "export_type");

const proTable = ref<ProTableInstance>();

const visible = ref();

const formRef = ref<FormInstance>();

const selectAudit = computed(() => {
  return audit_status.value?.filter(el => el.itemKey !== "1");
});

const formData = {
  status: "2",
  advice: "",
  id: null,
  creatorId: null,
  exportType: ""
};

let formRefData = reactive<any>({
  ...formData
});

const columns: ColumnProps[] = [
  {
    prop: "fileOriginalName",
    label: "导出文件"
  },
  {
    prop: "exportType",
    label: "导出操作",
    render: (scope: any) => `${useDictLabel(export_type.value, scope.row.exportType)}`,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {export_type.value.map(item => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "columSize",
    label: "导出数量"
  },

  {
    prop: "status",
    label: "审核状态",
    render: (scope: any) => (
      <CustomTag
        type="audit"
        status={`download_${scope.row.status}`}
        label={useDictLabel(audit_status.value, scope.row.status)}
      ></CustomTag>
    ),
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {audit_status.value.map(item => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "creatorName",
    label: "提交人员"
  },
  {
    prop: "createTime",
    label: "提交时间",
    sortable: true
  },
  {
    prop: "advice",
    label: "审核意见"
  },
  { prop: "auditorName", label: "审核人" },
  {
    prop: "auditTime",
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const getTableList = async (params: downloadMange.IDownLoadSearch) => {
  return Reflect.has(BUTTONS.value, "downloadReviewList") ? await queryDownloadAudit(params) : null;
};

const handleSubmit = () => {
  if (formRefData.status === "2") {
    formRefData.advice = formRefData.advice !== "" ? formRefData.advice : "同意";
  }
  formRef.value?.validate().then(() => {
    exportAuditHandle({ ...formRefData })
      .then(res => {
        ElMessage.success("审核成功");
        proTable.value?.getTableList();
        handleClose();
      })
      .catch(err => {
        ElMessage.success("审核失败");
      });
  });
};

const verify = (data: downloadMange.DownloadReviewType) => {
  formRefData.id = data.id;
  formRefData.creatorId = data.creatorId;
  formRefData.exportType = data.exportType;
  visible.value = true;
};

const handleClose = () => {
  formRef.value?.resetFields();
  // formRefData = { ...formData };
  visible.value = false;
};
</script>

<style scoped></style>
