<template>
  <div class="banner-box table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => toEdit()"> 新增 </el-button>
      </template>

      <template #url="{ row }">
        <span>{{ `${portal_domain[0].itemValue}/portal/themeZone?id=${row.id}` }}</span>
      </template>
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <auth-button :row-data="row" :auth-list="authList"></auth-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="theme">
import { ref, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { bannerStorage } from "@/utils/storage/edit";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { type ProTableInstance, type ColumnProps } from "@/components/ProTable/interface/index";
import { Plus } from "@element-plus/icons-vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { getThemeList, cancel } from "@/api/modules/business/theme";
import type { IAuthButtonItem } from "@/components/AuthButton/typings";
import { useDict } from "@/hooks/useDict";

const { portal_domain } = useDict("portal_domain");
const { BUTTONS } = useAuthButtons();

const router = useRouter();
const route = useRoute();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();
// const bannerItem = ref<any>({});

bannerStorage.clearExpired();

// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: "name",
    label: "专区名称",
    align: "left",
    width: 300,
    search: { el: "input", props: { maxLength: 20 } }
  },
  {
    prop: "creatorName",
    label: "创建人员",
    align: "left",
    width: 200
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "-",
        format: "YYYY/MM/DD",
        valueFormat: "YYYY-MM-DD HH:mm:ss"
      }
    },
    width: 200,
    sortable: true
  },
  {
    prop: "url",
    label: "生成URL",
    align: "left"
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 180 }
];

const authList: IAuthButtonItem<string>[] = [
  {
    contentName: "编辑",
    iconClass: "Edit",
    itemClick: scope => toEdit(scope),
    authName: "themeEdit"
  },
  {
    contentName: "预览",
    iconClass: "Preview",
    itemClick: scope => toPreview(scope),
    authName: "themePreview"
  },
  {
    contentName: "删除",
    iconClass: "Delete",
    itemClick: scope => handleDelete(scope),
    authName: "themeDelete"
  }
];

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const { name, createTime = [], current, size } = search;
  return getThemeList({
    name: name,

    endDate: createTime[1],
    beginDate: createTime[0],
    current,
    size
    // descs: "sort"
  });
};

// 删除用户信息
const handleDelete = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此专区？", "确认操作").then(async () => {
    await cancel(data.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

const toEdit = (data?: any) => {
  if (data) {
    router.push({
      path: "/operation/theme/edit",
      query: {
        id: data.id
      }
    });
  } else {
    router.push({
      path: "/operation/theme/edit"
    });
  }
};

const toPreview = (data: any) => {
  const href = `${portal_domain.value[0].itemValue}/portal/themeZone?id=${data.id}`;
  console.log(href);
  window.open(href, "_blank");
  // if (data) {
  //   const url = `${process.env.VUE_APP_BASE_API}/operation/theme/preview?id=${data.id}`;
  // } else {
  //   return;
  // }
};
watch(
  () => route.path,
  (path: string) => {
    if (path === "/operation/theme") {
      proTable.value?.getTableList();
    }
  }
);
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
