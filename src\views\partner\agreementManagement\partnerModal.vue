<template>
  <el-dialog
    :append-to-body="true"
    :destroy-on-close="true"
    class="custom-partner-modal"
    v-model="dialogVisible"
    :width="1000"
    :before-close="handleClose"
    title="选择合作商"
  >
    <div class="table-box">
      <pro-table
        ref="proTable"
        title="合作协议列表"
        :columns="columns"
        :request-api="getTableList"
        :request-auto="false"
        :tool-button="false"
      >
        <template #radio="scope">
          <el-radio v-model="radio" :label="scope.row['id']" @click="onClick(scope.row)">
            <i></i>
          </el-radio>
        </template>
      </pro-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose"> 取消 </el-button>
        <el-button @click="onSure" type="primary"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="partnerModal">
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { Product } from "@/api/interface/product";
import { nextTick, ref } from "vue";
import { pmsProviderList } from "@/api/modules/product";

const dialogVisible = ref(false);
const proTable = ref<ProTableInstance>();
const selectedProDetail = ref({});

const PROTOCOLTOBEUPLOADED = "4";
const SUCCESSAUDITSTATUS = "8";
// 单选值
const radio = ref("");
// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  { prop: "radio", label: "请选择", width: 80 },
  { prop: "name", label: "合作商名称", search: { el: "input" } },
  { prop: "usciCode", label: "社会信用统一代码", search: { el: "input" } }
];

const emits = defineEmits(["selectedPartner"]);

/* 关闭 */
const handleClose = () => {
  radio.value = "";
  dialogVisible.value = false;
};

/* 打开 */
const handleOpen = (row: any = null) => {
  dialogVisible.value = true;
  if (row) {
    radio.value = row.providerId;
    selectedProDetail.value = {
      id: row.providerId,
      name: row.name,
      code: row.code
    };
  }
  nextTick(() => {
    proTable.value?.getTableList();
  });
};

const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.descs = "createTime";
  // 如果非合作商，点击弹出查询状态为入驻成功的合作商列表
  newParams.auditStatus = `${PROTOCOLTOBEUPLOADED},${SUCCESSAUDITSTATUS}`;
  return pmsProviderList(newParams);
};

const onClick = (row: any) => {
  radio.value = row.id;
  selectedProDetail.value = row;
};

const onSure = () => {
  let infos = {
    providerId: selectedProDetail.value.id,
    name: selectedProDetail.value.name,
    code: selectedProDetail.value.code
  };
  emits("selectedPartner", infos);
  handleClose();
};

defineExpose({
  handleOpen
});
</script>

<style scoped lang="scss">
:global(.custom-partner-modal .el-dialog__header) {
  padding: 22px 24px;
  border-bottom: 1px solid #e1e3e6;
}
:global(.custom-partner-modal .el-dialog__body) {
  padding: 0;
}
:global(.custom-partner-modal .table-box .card) {
  padding: 24px;
  border: none;
  box-shadow: none;
}
:global(.custom-partner-modal .table-box .card.table-search) {
  padding-bottom: 0;
  margin-bottom: 0;
}
:global(.custom-partner-modal .table-box .card.table-main) {
  padding-top: 6px;
}
</style>
