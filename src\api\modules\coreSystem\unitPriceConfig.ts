import http from "@/api";
import { Bill } from "@/api/interface/billManagement";
import { CoreSystem } from "@/api/interface/coreSystem";

/*  实例单价表配置列表 */
export const queryInstancePriceList = async (params?: CoreSystem.InstancePriceSearch) => {
  return await http.get<Array<CoreSystem.InstancePriceItem>>("/cnud-desk/admin/instance/instancePrice/pageList", params);
};

/*  实例单价表配置详情 */
export const queryInstancePriceDetail = async (id: string) => {
  return await http.get<Array<CoreSystem.InstancePriceItem>>("/cnud-desk/admin/instance/instancePrice/detail", { id });
};

/*  核时实例单价表-新增 */
export const queryInstanceSave = async (data: CoreSystem.InstancePriceItem) => {
  return await http.post("/cnud-desk/admin/instance/instancePrice/save", data);
};
/*  核时实例单价表-修改 */
export const queryInstanceUpdate = async (data: CoreSystem.InstancePriceItem) => {
  return await http.post("/cnud-desk/admin/instance/instancePrice/update", data);
};
/*  核时实例单价表-删除 */
export const queryInstanceDelete = async (data: { ids: Array<string> }) => {
  return await http.post("/cnud-desk/admin/instance/instancePrice/delete", data);
};

/*  核时实例单价表-停用 */
export const queryInstanceChangeStatus = async (data: { id: string; status: string }) => {
  return await http.post("/cnud-desk/admin/instance/instancePrice/changeStatus", data);
};

/*  核时实例创建资源下拉选择列表 */
export const queryHardwareCode = async (params: { platformCode: string }) => {
  return await http.get<Array<string>>("/cnud-desk/admin/instance/instancePrice/hardwareCode", params);
};

/*  核时实例创建资源下拉选择列表 */
export const queryGetCluster = async () => {
  return await http.get<Array<Bill.IClusterPlatform>>("/cnud-desk/admin/platform");
};
