<template>
  <div class="table-box">
    <el-tabs v-model="activeName" class="demo-tabs tab-box" @tab-click="handleTabClick">
      <el-tab-pane label="需求发布" :name="TabType.REQUIRE"></el-tab-pane>
      <el-tab-pane label="方案发布" :name="TabType.SCHEME"> </el-tab-pane>
    </el-tabs>
    <div v-show="activeName === TabType.REQUIRE" class="list-box">
      <pro-table
        ref="requireTableRef"
        title="需求发布审核列表"
        :request-auto="false"
        :tool-button="false"
        :columns="columns"
        :request-api="getRequireTableList"
      >
        <template #auditStatus="{ row }">
          <custom-tag
            :type="statusTypeMap[row.auditStatus]"
            :status="row.auditStatus"
            :label="useDictLabel(requireReleaseStatus, row.auditStatus) || '--'"
          ></custom-tag>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span>
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, OperateType.DETAIL)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'review'">
            <el-tooltip v-if="scope.row.auditStatus === AuditStatusEnum.WAIT" content="审核" placement="top">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, OperateType.REVIEW)"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>
    <div v-show="activeName === TabType.SCHEME" class="list-box">
      <pro-table
        ref="schemeTableRef"
        title="方法发布审核列表"
        :request-auto="false"
        :tool-button="false"
        :columns="schemeColumns"
        :request-api="getSchemeTableList"
      >
        <template #auditStatus="{ row }">
          <custom-tag
            :type="statusTypeMap[row.auditStatus]"
            :status="row.auditStatus"
            :label="useDictLabel(requireReleaseStatus, row.auditStatus) || '--'"
          ></custom-tag>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span>
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, OperateType.DETAIL)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'schemeReview'">
            <el-tooltip v-if="scope.row.auditStatus === AuditStatusEnum.WAIT" content="审核" placement="top">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, OperateType.REVIEW)"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>
  </div>
</template>
<script setup lang="tsx" name="requireReleaseJbgsReview">
import { ref, computed, onBeforeMount, onActivated, reactive } from "vue";
import { useRouter } from "vue-router";
import type { TabsPaneContext } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import CustomTag from "@/components/CustomTag/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDictLabel } from "@/hooks/useDict";
import { useDicStore } from "@/stores/modules/dictionaty";
import { getRequireReviewList, getSchemeReviewList } from "@/api/modules/requireReleaseJbgs";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { OperateType, TabType, AuditStatusEnum } from "./type";

const statusTypeMap: { [key: string]: string } = {
  wait: "yellow",
  pass: "blue",
  refuse: "red",
  close: "grey"
};
const dictionaryStore = useDicStore();
const { BUTTONS } = useAuthButtons();
const router = useRouter();
// 审核状态
const requireReleaseStatus: any = computed(() => {
  return dictionaryStore.requireReleaseStatus;
});
// 需求类型
const requireType: any = computed(() => dictionaryStore.jbgsRequireType);

// 实施地址
// const demandConstructionAddress: any = computed(() => dictionaryStore.demandConstructionAddress);

// 表格配置项
const requireTableRef = ref<ProTableInstance>();
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "name",
    label: "需求名称",
    minWidth: 90,
    search: { el: "input", key: "demandName", props: { maxlength: "20" } }
  },
  {
    prop: "demandType",
    minWidth: 90,
    label: "需求类别",
    enum: requireType,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireType.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "companyName",
    minWidth: 110,
    label: "需方企业名称"
  },
  {
    prop: "companyAddress",
    minWidth: 110,
    label: "需方企业地址"
  },
  {
    prop: "constructionAddressName",
    minWidth: 120,
    label: "需求地点"
  },
  {
    prop: "contactName",
    minWidth: 100,
    label: "需方联系人"
  },
  {
    prop: "phone",
    minWidth: 110,
    label: "需方联系电话"
  },
  // {
  //   prop: "demandEndTime",
  //   minWidth: 110,
  //   label: "需求截止时间"
  // },
  // {
  //   prop: "constructionAddress",
  //   minWidth: 110,
  //   label: "需求实施地址",
  //   enum: demandConstructionAddress
  // },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120,
    enum: requireReleaseStatus,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireReleaseStatus.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "createTime",
    minWidth: 110,
    label: "提交时间",
    sortable: true
  },
  {
    prop: "auditorName",
    minWidth: 90,
    label: "审核人"
  },
  {
    prop: "auditTime",
    minWidth: 110,
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];
// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getRequireTableList = (params: any = {}) => {
  console.log(BUTTONS.value?.query, 1);
  if (!BUTTONS.value?.query) {
    return;
  }
  console.log(BUTTONS.value?.query, 2);
  return getRequireReviewList(params);
};

const schemeTableRef = ref<ProTableInstance>();
const schemeColumns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "demandName",
    label: "需求名称",
    minWidth: 90,
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "demandType",
    minWidth: 90,
    label: "需求类别",
    enum: requireType,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireType.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "demandCompanyName",
    minWidth: 110,
    label: "需方企业名称"
  },
  {
    prop: "demandCompanyAddress",
    minWidth: 110,
    label: "需方企业地址"
  },
  {
    prop: "demandContactName",
    minWidth: 100,
    label: "需方联系人"
  },
  {
    prop: "demandPhone",
    minWidth: 110,
    label: "需方联系电话"
  },
  // {
  //   prop: "demandEndTime",
  //   minWidth: 110,
  //   label: "需求截止时间"
  // },
  // {
  //   prop: "constructionAddress",
  //   minWidth: 110,
  //   label: "需求实施地址",
  //   enum: demandConstructionAddress
  // },
  {
    prop: "solutionName",
    minWidth: 90,
    label: "方案名称",
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "solutionCompanyName",
    minWidth: 110,
    label: "供方企业名称"
  },
  {
    prop: "solutionCompanyAddress",
    minWidth: 110,
    label: "供方企业地址"
  },
  {
    prop: "solutionContactName",
    minWidth: 100,
    label: "供方联系人"
  },
  {
    prop: "solutionPhone",
    minWidth: 110,
    label: "供方联系电话"
  },
  {
    prop: "createTime",
    minWidth: 110,
    label: "提交时间",
    sortable: true
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120,
    enum: requireReleaseStatus,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireReleaseStatus.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditorName",
    minWidth: 90,
    label: "审核人"
  },
  {
    prop: "auditTime",
    minWidth: 110,
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];
const getSchemeTableList = (params: any = {}) => {
  console.log(BUTTONS);
  if (!BUTTONS.value?.schemeQuery) {
    return;
  }
  return getSchemeReviewList(params);
};
const toView = (row: any = {}, type = OperateType.DETAIL) => {
  router.push({
    path: "/verification/requireReleaseJbgsReview/detail",
    query: {
      id: row?.id,
      operateType: type,
      pageType: activeName.value
    }
  });
};

const activeName = ref(TabType.REQUIRE);
const handleTabClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name;
  if (tab.props.name === TabType.REQUIRE) {
    requireTableRef.value?.getTableList();
  } else {
    schemeTableRef.value?.getTableList();
  }
};

onBeforeMount(() => {
  dictionaryStore.requireReleaseStatus.length === 0 && dictionaryStore.getRequireReleaseStatus();
  dictionaryStore.jbgsRequireType.length === 0 && dictionaryStore.getJbgsRequireType();
  // dictionaryStore.demandConstructionAddress.length === 0 && dictionaryStore.getDemandConstructionAddress();
});
onActivated(() => {
  console.log("ctivated");
  handleTabClick({ props: { name: activeName.value } });
});
</script>
<style scoped lang="scss">
.tab-box {
  background-color: #ffffff;
}
::v-deep(.el-tabs__item) {
  width: 100px !important;
}
.list-box {
  display: flex;
  flex-direction: column;
  height: calc(100% - 54px);
}
</style>
