<!-- 订单详情-订单详情 -->

<template>
  <div class="info-box card">
    <div class="box-title">
      <div class="title">订单详情</div>
    </div>
    <div class="infos">
      <el-row>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">下单触点：</span>
            <span class="info-text">{{ orderInfo?.channelName }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="1">
          <div class="info-item">
            <span class="info-label">订单编号：</span>
            <span class="info-text">{{ orderInfo?.parentOrderNo }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="2">
          <div class="info-item">
            <span class="info-label">订单类型：</span>
            <span class="info-text">{{ useDictLabel(ord_order_type, orderInfo?.orderType) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">订购时间：</span>
            <span class="info-text">{{ orderInfo?.subscribeTime }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="1">
          <div class="info-item">
            <span class="info-label">订购完成时间：</span>
            <span class="info-text">{{ orderInfo?.finishTime }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="2">
          <div class="info-item">
            <span class="info-label">订购失败时间：</span>
            <span class="info-text">{{ orderInfo?.closeTime }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">支付方式：</span>
            <span class="info-text">{{ useDictLabel(ord_pay_type, orderInfo?.payType) }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="1">
          <div class="info-item">
            <span class="info-label">支付账号：</span>
            <span class="info-text">{{ orderInfo?.purchasePhone }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="2">
          <div class="info-item">
            <span class="info-label">订单支付时间：</span>
            <span class="info-text">{{ orderInfo?.payTime }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">订单创建人：</span>
            <span class="info-text">{{ orderInfo?.creatorName }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="1" v-if="isDisplay">
          <div class="info-item">
            <span class="info-label">退订时间：</span>
            <span class="info-text">{{ orderInfo?.unsubscribeTime }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
const { ord_order_type, ord_pay_type } = useDict("ord_order_type", "ord_pay_type");

const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

const isDisplay = computed(() => {
  if (Reflect.has(props.orderInfo, "unsubscribeTime")) {
    return true;
  } else {
    return false;
  }
});
</script>

<style scoped lang="scss">
@import "../order.module.scss";
</style>
