<template>
  <div class="order-analysis-container">
    <div class="order-header">
      <div class="header-box">
        <el-form :inline="true" ref="orderFormRef" :model="orderForm" :rules="rules">
          <el-form-item label="" prop="btn">
            <div>
              <el-button-group size="large" class="botton-box">
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.day, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.day)"
                >
                  日报表
                </el-button>
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.month, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.month)"
                >
                  月报表
                </el-button>
              </el-button-group>
            </div>
          </el-form-item>
          <el-form-item label="日期" prop="countDate">
            <el-date-picker
              v-if="dateType === DateType.day"
              class="data-picker-style"
              type="daterange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @focus="handleFocus"
              @calendar-change="handleChange"
              :disabled-date="disabledDate"
            />
            <el-date-picker
              v-else
              class="data-picker-style"
              type="monthrange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              @focus="handleFocus1"
              @change="handleChange1"
              :disabled-date="disabledDate1"
            />
          </el-form-item>
          <el-form-item label="渠道名称" prop="channelType">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              v-model="orderForm.channelType"
              placeholder="请选择渠道名称"
              clearable
            >
              <el-option v-for="item in userSorceDict" :key="item.itemKey" :label="item.itemValue" :value="item.itemKey" />
            </el-select>
          </el-form-item>
          <el-form-item label="产品名称" prop="productIdList">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              :multiple="true"
              v-model="orderForm.productIdList"
              placeholder="请选择产品名称"
              clearable
            >
              <el-option v-for="item in productOptions" :key="item.productId" :label="item.productName" :value="item.productId" />
            </el-select>
          </el-form-item>
          <el-form-item label="合作商" prop="providerId">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              v-model="orderForm.providerId"
              placeholder="请选择合作商"
              clearable
            >
              <el-option
                v-for="item in providerOptions"
                :key="item.providerId"
                :label="item.providerName"
                :value="item.providerId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm(orderFormRef)"> 查询 </el-button>
            <el-button @click="resetForm(orderFormRef)"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="analysis-content">
      <div class="content-title">渠道分析</div>
      <channel-chart-content
        :params="commonParams"
        ref="channelChartRef"
        @onChangeIndex="onChangeIndex"
        @onChangeIndicator="onChangeIndicator"
      />
    </div>
    <channel-analysis-list :params="{ ...commonParams }" ref="analysisListRef"></channel-analysis-list>
  </div>
</template>

<script setup lang="ts" name="channelAnalysis">
import { reactive, ref, onMounted, computed } from "vue";
import dayjs from "dayjs";
import type { FormInstance, FormRules } from "element-plus";
import { getProductList, getProviderList } from "@/api/modules/statisticAnalysis";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useDict } from "@/hooks/useDict";
import { startDate, endDate, startMonthDate, endMonthDate } from "../commonAnalysis";
import channelAnalysisList from "./components/channelAnalysisList.vue";
import channelChartContent from "./components/channelChartContent.vue";

const { channel_code } = useDict("channel_code");
const { BUTTONS } = useAuthButtons();

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}

enum IndicatorsType {
  all = ""
}

const analysisListRef = ref();
const channelChartRef = ref();

const dateType = ref<DateType>(DateType.day);
const activeBtn = ref("1");
const indicators = ref<string>(IndicatorsType.all);

const commonParams = reactive({
  countEndDate: endDate,
  countStartDate: startDate,
  dateType: dateType,
  orderAnalysisTypeKey: activeBtn,
  providerId: "",
  productIds: "",
  channelType: indicators,
  productIdList: [],
  productName: ""
});

const userSorceDict = computed(() => {
  const sourceList = channel_code.value;
  if (sourceList && sourceList.length > 0) {
    const newList = sourceList.map((item: any) => {
      return {
        ...item,
        label: item.itemValue,
        value: item.itemKey
      };
    });
    newList.unshift({
      itemValue: "全部",
      label: "全部",
      value: "",
      itemKey: ""
    });
    return newList;
  } else {
    return [];
  }
});

const getAllData = () => {
  if (!BUTTONS.value?.query) {
    return;
  }
  channelChartRef.value.getChannelCharts({ ...commonParams });
  analysisListRef.value.getTableList({ ...commonParams });
};

onMounted(() => {
  getAllData();
  getSearchList();
});

const productOptions = ref([]);
const providerOptions = ref([]);
const getSearchList = async () => {
  getProductList().then((res: any) => {
    productOptions.value = res?.data || [];
  });
  getProviderList().then((res: any) => {
    providerOptions.value = res?.data || [];
  });
};

const changeDate = (type: DateType) => {
  if (type === DateType.month) {
    commonParams.countStartDate = startMonthDate;
    commonParams.countEndDate = endMonthDate;
    orderForm.countDate = [startMonthDate, endMonthDate];
  } else {
    commonParams.countStartDate = startDate;
    commonParams.countEndDate = endDate;
    orderForm.countDate = [startDate, endDate];
  }
};
const onChangeDateType = (type: DateType) => {
  dateType.value = type;
  commonParams.dateType = type;
  changeDate(type);
  getAllData();
};

const orderFormRef = ref<FormInstance>();
const orderForm = reactive({
  channelType: IndicatorsType.all,
  countDate: [startDate, endDate],
  productIdList: [],
  providerId: ""
});

const checkDate = (_: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请选择日期"));
  } else {
    // 定义两个日期字符串
    const dateString1 = value[0];
    const dateString2 = value[1];
    // 将日期字符串转换为 dayjs 对象
    const date1 = dayjs(dateString1);
    const date2 = dayjs(dateString2);

    // 获取两个日期之间的月数差
    const monthDiff = Math.abs(date2.diff(date1, "month"));

    if (monthDiff > 11) {
      callback(new Error("最多只能查询12个月的数据"));
    }
  }
  callback();
};

const dateRule: any = computed(() =>
  dateType.value === DateType.day
    ? [{ required: true, message: "请选择日期", trigger: "blur" }]
    : [
        { required: true, message: "请选择日期", trigger: "blur" },
        { trigger: "blur", validator: checkDate }
      ]
);
const rules = reactive<FormRules>({
  countDate: dateRule
});

const onChangeIndex = (value: string) => {
  commonParams.orderAnalysisTypeKey = value;
};

const onChangeIndicator = (value: string) => {
  commonParams.channelType = value;
};
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      commonParams.countStartDate = orderForm.countDate[0];
      commonParams.countEndDate = orderForm.countDate[1];
      commonParams.productIds = (orderForm.productIdList || []).join(",");
      commonParams.providerId = orderForm.providerId || "";
      commonParams.channelType = orderForm.channelType || IndicatorsType.all;
      indicators.value = orderForm.channelType || IndicatorsType.all;
      getAllData();
    } else {
      console.log("error submit!", fields);
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  // 处理重置日报表与月报表不相同
  changeDate(dateType.value);
  commonParams.productIds = "";
  commonParams.providerId = "";

  getAllData();
};

const chooseDay = ref<any>(null);
const handleChange = (val: Date[]) => {
  const [pointDay] = val;
  chooseDay.value = pointDay;
};
//取值方法
const handleFocus = () => {
  chooseDay.value = null;
};

const disabledDate = (time: number) => {
  if (!chooseDay.value) {
    return false;
  }
  let timeRange = 30;
  const con1 = new Date(chooseDay.value).getTime() - timeRange * 24 * 60 * 60 * 1000;
  const con2 = new Date(chooseDay.value).getTime() + timeRange * 24 * 60 * 60 * 1000;
  return time < con1 || time > con2;
};

const disabledDate1 = (time: number) => {
  if (!chooseDay.value) {
    return false;
  }
  let timeRange = 30;
  const con1 = new Date(chooseDay.value).getTime() - timeRange * 24 * 60 * 60 * 1000;
  const con2 = new Date(chooseDay.value).getTime() + timeRange * 24 * 60 * 60 * 1000;
  return time < con1 || time > con2;
};

const handleChange1 = (val: Date[]) => {
  console.log("🚀 ~ handleChange1 ~ val:", val);
};
//取值方法
const handleFocus1 = () => {};
</script>

<style lang="scss" scoped>
.cust-border {
  border-bottom: 1px solid #e1e3e6;
}
.order-analysis-container {
  background-color: #ffffff;
  .order-header {
    padding: 16px 0 0 24px;
    @extend.cust-border;
    .header-box {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      :deep(.el-form-item--default) {
        margin-bottom: 16px;
      }
      :deep(.el-form--inline .el-form-item) {
        margin-right: 12px;
      }
      :deep(.data-picker-style) {
        max-width: 210px;
      }
      .select-style {
        max-width: 190px;
      }
      .type-box {
        flex: 164px;
      }
    }
    .btn {
      height: 36px;
      &.active {
        color: var(--el-color-primary);
        background-color: var(--el-fill-color-blank);
        border-color: var(--el-color-primary);
      }
    }
  }
  .analysis-content {
    box-sizing: border-box;
    height: 470px;
    padding: 16px 24px 0;
    @extend.cust-border;
    .content-title {
      width: 100%;
      height: 24px;
      margin-bottom: 16px;
      font-family: "PingFangSC, PingFang SC,sans-serif";
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #000000d9;
    }
    .statistical-data {
      display: flex;
      width: 100%;
    }
    .order-main {
      flex: 1;
      .chart-box {
        width: 100%;
        height: 336px;
      }
    }
    .order-aside {
      width: 300px;
      .chart-box {
        height: 390px;
      }
    }
  }
}
</style>
