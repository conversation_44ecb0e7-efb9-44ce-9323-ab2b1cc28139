import type { RouteLocationNormalizedLoaded } from "vue-router";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";

export function closeTabItem(route: RouteLocationNormalizedLoaded) {
  const tabStore = useTabsStore();
  const keepAliveStore = useKeepAliveStore();

  if (route.meta.isAffix) return;
  tabStore.removeTabs(route.fullPath);
  keepAliveStore.removeKeepAliveName(route.name as string);
}
