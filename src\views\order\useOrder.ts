import { TargetCustomer } from "@/config/order";
import { Ref, ref, computed } from "vue";
import { useRouter } from "vue-router";

export const useTargetUser = (targetCustomer: Ref<string>) => {
  /* 只有面对企业用户和所有用户 */
  // const targetVisible = ref(false);
  // if (targetCustomer.value === TargetCustomer.ENTERPRISE || targetCustomer.value === TargetCustomer.ALL) {
  //   targetVisible.value = true;
  //   return targetVisible;
  // } else {
  //   return targetVisible;
  // }
  const targetVisible = computed(() => {
    if (targetCustomer.value === TargetCustomer.ENTERPRISE || targetCustomer.value === TargetCustomer.ALL) {
      return true;
    } else {
      return false;
    }
  });
  return targetVisible;
};

export const useHandleRoute = () => {
  const router = useRouter();

  /* 审核 */
  const verify = (row: { parentOrderNo: string }) => {
    router.push({
      path: "/order/portalOrder/detail",
      query: {
        orderId: row.parentOrderNo,
        auditTag: 1
      }
    });
  };

  /* 处理,跳政企中台 */
  const toVerify = (row: { parentOrderNo: string }) => {
    router.push({
      path: "/order/middleground/detail",
      query: {
        orderId: row.parentOrderNo,
        auditTag: 1
      }
    });
  };

  /**
   * 去详情
   * @param type {parentOrderNo父订单，subOrderNo：子订单}
   * @param item {列表信息}
   */
  const handleToDetail = (type: string, item: any) => {
    if (type === "parentOrderNo") {
      if (item.channelCode === "ZQ") {
        router.push({
          path: "/order/middleground/detail",
          query: {
            orderId: item.parentOrderNo,
            auditTag: 0
          }
        });
      } else {
        router.push({
          path: "/order/portalOrder/detail",
          query: {
            orderId: item.parentOrderNo,
            auditTag: 0
          }
        });
      }
    } else {
      if (item.channelCode === "ZQ") {
        router.push({
          path: "/order/middleground/detail",
          query: {
            orderId: item.parentOrderNo,
            auditTag: 0
          }
        });
      } else {
        router.push({
          path: "/order/portalOrder/detail",
          query: {
            subOrderId: item.id
          }
        });
      }
    }
  };

  /* 新增订单 */
  const createOrder = () => {
    router.push(`/order/portalOrder/add`);
  };

  /**
   * 手动开通 -- 复用详情页，pageType === 'activate' 去订单手动开通
   * @param item
   */
  const handleOrderOpen = (item: any) => {
    router.push({
      path: "/order/portalOrder/detail",
      query: {
        orderId: item.parentOrderNo,
        pageType: "activate",
        auditTag: 1
      }
    });
  };

  return { verify, toVerify, handleToDetail, createOrder, handleOrderOpen };
};
