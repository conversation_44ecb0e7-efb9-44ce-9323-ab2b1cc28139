<template>
  <div class="dataVisualize-box">
    <div class="top-box">
      <div class="top-tab">
        <div class="top-title">平台概况</div>
        <el-tabs v-model="tabActive" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane v-for="item in tab" :key="item.clusterId" :label="item.clusterName" :name="item.clusterId"></el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div>
      <clusterDetail :cluster-id="tabActive"></clusterDetail>
    </div>
  </div>
</template>

<script setup lang="ts" name="dataVisualize">
import { ref, onMounted } from "vue";
// import { getClusterList } from "@/api/modules/coreSystem/dashboard";
import { queryGetCluster } from "@/api/modules/coreSystem/unitPriceConfig";
// import { getCluster } from "@/api/modules/billManagement";
import clusterDetail from "./components/clusterDetail.vue";
import type { TabsPaneContext } from "element-plus";
interface tabCluster {
  clusterId: string;
  clusterName: string;
}
const tabActive = ref("");
const tab = ref<tabCluster[]>([]);

const handleClick = (tab: TabsPaneContext) => {
  tabActive.value = tab.paneName as string;
};

const getTabList = () => {
  queryGetCluster().then(res => {
    tab.value = res.data.map(item => ({ clusterId: item.aiClusterId, clusterName: item.displayName }));
    tabActive.value = tab.value[0].clusterId;
  });
};
onMounted(() => {
  getTabList();
});
</script>

<style lang="scss" scoped>
@import "./index.scss";
::v-deep .el-tabs__item {
  align-items: start !important;
}
</style>
