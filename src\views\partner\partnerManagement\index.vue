<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="合作商管理列表"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
      @selection-change="selectionChange"
    >
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader>
        <!-- 合作商类型账号不可见 -->
        <el-button v-auth="'add'" v-if="!isPartner" plain :icon="Plus" @click="toView({}, editType.add)"> 新增 </el-button>
        <!-- 默认置灰,选择后支持点击，合作商类型账号不可见  -->
        <el-button v-auth="'export'" v-if="!isPartner" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>
      <template #auditStatus="{ row }">
        <CustomTag
          :type="auditStatusColor(row.auditStatus)"
          :status="row.auditStatus"
          :label="useDictLabel(auditStatusDict, row.auditStatus) || '--'"
        ></CustomTag>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <!-- 状态为【资质审核不通过】、【入驻成功】才可操作 -->
          <el-tooltip
            content="资质修改"
            placement="top"
            v-if="
              scope.row.auditStatus == status.qualPendingReview ||
              scope.row.auditStatus == status.noPass ||
              scope.row.auditStatus == status.success
            "
          >
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toView(scope.row, editType.edit)"></i>
          </el-tooltip>
        </span>

        <span v-auth="'agreement'">
          <!-- 状态不为【资质待审核】、【资质审核不通过】时才可操作 -->
          <el-tooltip
            content="协议管理"
            placement="top"
            v-if="!(scope.row.auditStatus == status.qualPendingReview || scope.row.auditStatus == status.noPass)"
          >
            <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, editType.agree)"></i>
          </el-tooltip>
        </span>
        <!-- 上传子商户信息 -->
        <!-- <span v-auth="'setMerchantNumber'">
          <el-tooltip content="上传子商户信息" placement="top">
            <i class="iconfont2 opera-icon icon-lineicon_upload" @click="handleUpload(scope.row)"></i>
          </el-tooltip>
        </span> -->
        <!-- 查看 -->
        <span v-auth="'detailExt'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, editType.view)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <!-- <merchantModal ref="modalRef" @reload="handleReload"></merchantModal> -->
  </div>
</template>

<script setup lang="tsx" name="partnerManagement">
import { ref, reactive, onMounted, computed, onActivated } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { useDictLabel } from "@/hooks/useDict";
import { useDownload } from "@/hooks/useDownload";
import { useDicStore } from "@/stores/modules/dictionaty";
// import merchantModal from "./components/merchantModal.vue";

import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { exportProvider } from "@/api/modules/partner";
import { pmsProviderList as getProviderList } from "@/api/modules/product";

import CustomTag from "@/components/CustomTag/index.vue";

import { useUserStore } from "@/stores/modules/user";
import { UserType } from "@/enums/userInfo";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();
const userStore = useUserStore();
const auditStatusColor = (status: string) => {
  if (!status) {
    return "noBgColor";
  }
  let color = "default";
  /*
    1-"资质待审核"
    2-"引入审核中"
    3-"资质审核不通过"
    4-"协议待上传"
    5-"引入审核不通过"
    6-"协议待审核"
    7-"协议审核不通过"
    8-"入驻成功"
  */
  switch (status) {
    case "1":
      color = "yellow";
      break;
    case "2":
      color = "blue";
      break;
    case "3":
      color = "grey";
      break;
    case "4":
      color = "blue";
      break;
    case "5":
      color = "red";
      break;
    case "6":
      color = "yellow";
      break;
    case "7":
      color = "red";
      break;
    case "8":
      color = "green";
      break;
    default:
      break;
  }
  return color;
};
enum editType {
  "add" = 0,
  "edit" = 1,
  "agree" = 2,
  "view" = 3
}

enum status {
  "qualPendingReview" = 1, // 资质待审核
  "introReviewing" = 2, // 引入审核中
  "noPass" = 3, // 资质审核不通过
  "protocolTobeUploaded" = 4, // 协议待上传
  "introReviewFailed" = 5, // 引入审核不通过
  "agreementPendingReview" = 6, // 协议待审核
  "protocolReviewFailed" = 7, // 协议审核不通过
  "success" = 8 // 入驻成功
}

const router = useRouter();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

const dictionaryStore = useDicStore();

const modalRef = ref<{ handleOpen: (row: { id: string }) => void }>();

onMounted(() => {
  dictionaryStore.getAuditStatusEnterprise();
  dictionaryStore.getIndustryTypeEnterprise();
});
const auditStatusDict: any = computed(() => dictionaryStore.auditStatusEnterprise);
const industryType: any = computed(() => dictionaryStore.industryTypeEnterprise);

const selectedList = ref<{ [key: string]: any }[]>([]);
const selectionChange = (rowArr: { [key: string]: any }[]) => {
  selectedList.value = rowArr;
};

onActivated(() => {
  // 然后
  proTable.value?.getTableList();
});

const getTableList = async (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
  newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
  delete newParams.createTime;
  newParams.descs = "createTime";
  return await getProviderList(newParams);
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "name", label: "合作商名称", search: { el: "input", props: { maxLength: 50 } } },
  {
    prop: "usciCode",
    label: "社会信用统一代码",
    search: { el: "input", props: { maxLength: 20 } }
  },
  // { prop: "merchantNumber", label: "子商户号" },
  { prop: "industryType", label: "行业", enum: industryType },
  {
    prop: "auditStatus",
    label: "状态",
    width: 180,
    enum: auditStatusDict
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY/MM/DD HH:mm:ss",
        valueFormat: "YYYY/MM/DD HH:mm:ss"
      }
    },
    width: 180
  },
  { prop: "operation", label: "操作", fixed: "right", width: 160 }
];

const toView = async (row: any, type = editType.edit) => {
  if (type === editType.edit || type === editType.add || type === editType.view) {
    router.push({
      path: `/partner/partnerManagement/partnerEdit`,
      query: {
        id: row.id,
        type
      }
    });
  } else {
    router.push("/partner/agreementManagement");
  }
};

// 导出合作商列表
const downloadFile = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  delete newParams.createTime;
  ElMessageBox.confirm("确认导出合作商列表?", "提示", { type: "warning" }).then(() => useDownload(exportProvider, newParams));
};

/* 上传子商户号 */
// const handleUpload = (row: { id: string }) => {
//   modalRef.value?.handleOpen(row);
// };

const handleReload = () => {
  proTable.value?.getTableList();
};
</script>

<style lang="scss" scoped>
:global(.overflow-tooltip-style) {
  max-width: calc(100% - 24px);
  white-space: pre-wrap;
}
</style>
