export namespace PmsRights {
  export interface CreateParams {
    name: string;
    pic: string;
    providerId: string;
    providerName: string;
    apiSwitch: string;
    useType: string;
    url: string;
    miniAppsUseType: string;
    miniAppsUrl: string;
    status: string;
    portalQRCodeDescription: string;
    portalUseDescription: string;
    portalQRCodeUrl: string;
    miniAppsUseDescription: string;
    miniAppsQRCodeUrl: string;
    miniAppsQRCodeDescription: string;
    miniAppsUrl: string;
    id?: string;
  }
  export interface EditParams extends CreateParams {
    id: string;
  }
}
