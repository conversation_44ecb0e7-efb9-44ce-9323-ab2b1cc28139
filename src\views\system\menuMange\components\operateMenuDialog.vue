<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="editTypeChinese[props.type] + '菜单'" width="50%" @close="onCancel">
      <el-form ref="menuFormRef" :model="menuForm" :rules="rules" label-position="left" label-width="102px">
        <el-form-item label="上级菜单" prop="parentId">
          <!-- <el-tree-select
            v-model="menuForm.parentId"
            :props="defaultProps"
            :data="menuTreeList"
            node-key="id"
            check-strictly
            :render-after-expand="false"
          /> -->
          <el-cascader v-model="menuForm.parentId" :options="menuTreeList" :props="defaultProps" :show-all-levels="false" />
        </el-form-item>
        <el-form-item label="菜单类型" prop="menuType">
          <el-radio-group v-model="menuForm.menuType">
            <el-radio :label="1">目录</el-radio>
            <el-radio :label="2">菜单</el-radio>
            <el-radio :label="3">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="菜单图标" prop="icon">
          <el-input class="label-name" v-model="menuForm.icon"></el-input>
        </el-form-item> -->
        <el-form-item label="菜单图标" prop="icon" v-if="menuTypeValue != 3">
          <el-popover placement="bottom-start" width="460" trigger="click" @close="resetIconSelect">
            <IconSelect ref="iconSelect" @selected="selected" :active-icon="menuForm.icon" />
            <template #reference>
              <el-input v-model="menuForm.icon" placeholder="点击选择图标" readonly>
                <template #prefix>
                  <SvgIcon v-if="menuForm.icon" :name="menuForm.icon" :style="{ height: '25px', width: '16px' }" />
                  <el-icon v-else><Search /></el-icon>
                </template>
                <template #suffix>
                  <el-icon @click.stop="onResetIconSelect" style="cursor: pointer"><Close /></el-icon>
                </template>
              </el-input>
            </template>
          </el-popover>
        </el-form-item>

        <el-form-item label="接口地址" prop="apiIdList">
          <el-select
            class="api-select"
            collapse-tags
            collapse-tags-tooltip
            filterable
            v-model="menuForm.apiIdList"
            multiple
            placeholder="请选择接口地址"
          >
            <el-option v-for="item in apiOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-button @click="refreshApiData">刷新</el-button>
        </el-form-item>

        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="menuName">
              <el-input class="label-name" v-model="menuForm.menuName" placeholder="请输入菜单名称" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单编码" prop="menuCode">
              <el-input class="label-name" v-model="menuForm.menuCode" placeholder="请输入菜单编码" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源模块" prop="resourceModule">
              <el-input class="label-name" v-model="menuForm.resourceModule" placeholder="请输入资源模块" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源编码" prop="resourceCode">
              <el-select v-model="menuForm.resourceCode" placeholder="请选择资源编码" clearable>
                <el-option v-for="item in resourceCodeList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="显示排序" prop="sort">
              <el-input
                class="label-name"
                type="number"
                v-model="menuForm.sort"
                placeholder="请输入显示排序"
                :min="0"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <template v-if="menuTypeValue != 3">
            <el-col :span="12">
              <el-form-item label="路由地址" prop="path">
                <el-input class="label-name" v-model="menuForm.path" placeholder="请输入路由地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="menuTypeValue == 1">
              <el-form-item label="Redirect" prop="redirect">
                <el-input class="label-name" v-model="menuForm.redirect" placeholder="请输入路由地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示状态" prop="visible">
                <el-radio-group v-model="menuForm.visible">
                  <el-radio label="1">显示</el-radio>
                  <el-radio label="0">隐藏</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="12">
            <el-form-item label="菜单状态" prop="status">
              <el-radio-group v-model="menuForm.status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="menuTypeValue == 2">
            <el-col :span="12">
              <el-form-item label="组件路径" prop="component">
                <el-input class="label-name" v-model="menuForm.component" placeholder="请输入组件路径" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="activeMenu" prop="activeMenu">
                <el-input
                  class="label-name"
                  v-model="menuForm.activeMenu"
                  :maxlength="100"
                  placeholder="请输入组件路径"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否外链" prop="isLink">
                <el-radio-group v-model="menuForm.isLink">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否全屏" prop="isFull">
                <el-radio-group v-model="menuForm.isFull">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否固定标签" prop="isAffix">
                <el-radio-group v-model="menuForm.isAffix">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否keepAlive" prop="isKeepAlive">
                <el-radio-group v-model="menuForm.isKeepAlive">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
        </el-row>

        <el-form-item label="描述" prop="description" v-if="menuTypeValue != 3">
          <el-input class="label-name" v-model="menuForm.description" type="textarea" placeholder="请输入描述"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(menuFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="operateConfigDialog">
import { getTreeList, saveMenu, updateMenu } from "@/api/modules/system";
import IconSelect from "@/components/IconSelect/index.vue";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { editType, editTypeChinese } from "@/enums/dialogTypeEnum";
import { Close, Search } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { computed, reactive, ref, watch } from "vue";
import { fetchApiOptionList, fetchApiIdList, callApiRegister } from "@/api/modules/apiSelect";

interface ApiOption {
  value: string;
  label: string;
}

interface TreeData {
  menuName?: string;
  id: number | string;
  children?: TreeData[];
}

const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: 1
  },
  params: {
    type: Object,
    default: () => {}
  }
});

const iconSelect = ref();
const selected = (name: string) => {
  menuForm.value.icon = name;
};

const resetIconSelect = () => {
  menuForm.value.icon = "";
  iconSelect.value?.reset();
};

const onResetIconSelect = () => {
  resetIconSelect();
};

const menuFormRef = ref();
const defMenu = {
  menuType: 1,
  menuName: "",
  menuCode: "",
  sort: "",
  path: "",
  status: 1,
  visible: "1",
  description: "",
  parentId: "0",
  resourceCode: "",
  icon: ""
};
const resourceCodeList = [
  {
    label: "新增",
    value: "CREATE"
  },
  {
    label: "修改",
    value: "UPDATE"
  },
  {
    label: "删除",
    value: "DELETE"
  },
  {
    label: "查看",
    value: "VIEW"
  },
  {
    label: "导入",
    value: "IMPORT"
  },
  {
    label: "导出",
    value: "EXPORT"
  }
];

const menuForm = ref<any>(defMenu);
const menuTypeValue = computed(() => menuForm.value.menuType);

const apiOptions = ref<ApiOption[]>([]);
const setApi = async () => {
  menuForm.value.apiIdList = await fetchApiIdList(props.params.id);
  apiOptions.value = await fetchApiOptionList();
};
const refreshApiData = async () => {
  callApiRegister();
  apiOptions.value = await fetchApiOptionList();
};

const menuTreeList = ref<Array<TreeData>>([]);
const defaultProps = {
  children: "children",
  label: "menuName",
  value: "id",
  checkStrictly: true,
  emitPath: false
};

const emits = defineEmits(["update:visible", "cancel", "confirm"]);
watch(
  () => props.params,
  () => {
    if (props.visible && props.type !== editType.add) {
      menuForm.value = {
        ...defMenu,
        menuType: props.params.menuType,
        menuName: props.params.menuName,
        menuCode: props.params.menuCode,
        sort: props.params.sort,
        path: props.params.path,
        status: props.params.status,
        visible: props.params.visible,
        description: props.params.description,
        parentId: props.params.parentId,
        isLink: props.params.isLink,
        isFull: props.params.isFull,
        isAffix: props.params.isAffix,
        isKeepAlive: props.params.isKeepAlive,
        resourceCode: props.params.resourceCode,
        resourceModule: props.params.resourceModule,
        component: props.params.component,
        activeMenu: props.params.activeMenu,
        redirect: props.params.redirect,
        icon: props.params.icon
      };
    } else {
      menuForm.value = {
        ...defMenu
      };
    }
  }
);
watch(
  () => props.visible,
  () => {
    if (props.visible) {
      getMenuTree();
      setApi();
    }
  },
  { deep: true, immediate: true }
);

const getMenuTree = () => {
  getTreeList({}).then(res => {
    if (res.code == 200) {
      menuTreeList.value = [
        {
          menuName: "主类目",
          id: "0",
          children: res.data as Array<TreeData>
        }
      ];
    }
  });
};

const onCancel = () => {
  menuFormRef?.value?.clearValidate();
  menuForm.value = defMenu;
  dialogVisible.value = false;
  resetIconSelect();
  emits("cancel");
};

const rules = reactive({
  menuName: [{ required: true, trigger: "change", message: "请输入菜单名称" }],
  menuCode: [{ required: true, trigger: "change", message: "请输入菜单编号" }],
  sort: [{ required: true, trigger: "change", message: "请输入显示排序" }],
  path: [{ required: true, trigger: "change", message: "请输入路由地址" }],
  resourceCode: [{ required: true, trigger: "change", message: "请选择资源编码" }],
  resourceModule: [{ required: true, trigger: "change", message: "请输入资源模块" }],
  component: [{ required: true, trigger: "change", message: "请输入组件路径" }],
  redirect: [{ required: true, trigger: "change", message: "请输入Redirect" }],
  perms: [{ required: true, trigger: "change", message: "请输入权限字符perms" }]
});

const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    console.log("value", value);

    menuForm.value = defMenu;
    emits("update:visible", value);
  }
});

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: saveMenu,
        [editType["edit"]]: updateMenu
      };
      const menuTypeMenu = menuForm.value.menuType == 2;
      const params = menuTypeMenu
        ? {}
        : {
            component: "",
            isLink: false,
            isFull: false,
            isAffix: false,
            isKeepAlive: false
          };
      console.log("params", menuForm.value, params);
      // 按钮

      let reqParams = {};
      if (menuForm.value.menuType == 3) {
        reqParams = {
          parentId: menuForm.value.parentId,
          menuType: menuForm.value.menuType,
          menuName: menuForm.value.menuName,
          menuCode: menuForm.value.menuCode,
          sort: Number(menuForm.value.sort),
          resourceCode: menuForm.value.resourceCode,
          resourceModule: menuForm.value.resourceModule,
          status: menuForm.value.status,
          apiIdList: menuForm.value.apiIdList
        };
      }
      const requestParamsObject = {
        [editType["add"]]: () => {
          if (menuForm.value.menuType == 3) {
            return reqParams;
          }
          return {
            ...menuForm.value,
            redirect: menuForm.value.menuType == 1 ? menuForm.value.redirect : "",
            ...params
          };
        },
        [editType["edit"]]: () => {
          if (menuForm.value.menuType == 3) {
            return {
              ...reqParams,
              id: props.params.id
            };
          }
          return {
            ...menuForm.value,
            sort: Number(menuForm.value.sort),
            id: props.params.id,
            redirect: menuForm.value.menuType == 1 ? menuForm.value.redirect : "",
            ...params
          };
        }
      };
      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        ElMessage.success(res.msg);
        menuForm.value = defMenu;
        menuFormRef?.value?.clearValidate();
        menuFormRef?.value?.resetFields();

        emits("confirm");
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.api-select {
  flex: 1;
  margin-right: 10px;
}
</style>
