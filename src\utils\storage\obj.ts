import { commonKey } from "./storageKey";

class ObjStorage {
  private key: string = "";
  constructor(key: string) {
    this.key = key;
  }
  public set(value: Object) {
    window.localStorage.setItem(this.key, JSON.stringify(value));
  }
  public get(): Object | null {
    const data = localStorage.getItem(this.key);
    if (data) {
      return JSON.parse(data);
    }
    return null;
  }
  public clear() {
    window.localStorage.removeItem(this.key);
  }
}

export const documentStorage = new ObjStorage(commonKey + "DocumentArea");
