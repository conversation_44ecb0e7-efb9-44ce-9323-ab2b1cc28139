<template>
  <BaseDrawer ref="drawerRef" @sure-callback="sureCallback" @handle-close="drawerClose">
    <pro-table
      ref="proTable"
      title="标签列表"
      :columns="columns"
      :request-api="getTableList"
      :request-auto="false"
      :tool-button="false"
    ></pro-table>
  </BaseDrawer>
</template>

<script setup lang="ts" name="stepOne">
import { ref, nextTick } from "vue";
import BaseDrawer from "@/components/BaseDrawer/index.vue";
import { ElMessage } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getTagList } from "@/api/modules/label";
import { Label } from "@/api/interface/label";

let emits = defineEmits(["selectedList", "selectedListIds"]);
const drawerRef = ref(null);
const proTable = ref<ProTableInstance>();
const showLabel = () => {
  const params = {
    title: "选择标签",
    sureText: "选择",
    show: true
    // row: { ...row },
    // api:  undefined,
    // getTableList: null
  };
  drawerRef.value?.acceptParams(params);
  nextTick(() => {
    proTable.value?.getTableList();
  });
};
const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  return getTagList(newParams);
};
// 表格配置项
const columns: ColumnProps<Label.TagItem>[] = [
  { type: "selection", fixed: "left", width: 80 },
  { prop: "name", label: "标签名称", search: { el: "input" } },
  { prop: "typeName", label: "标签分类", search: { el: "input" } },
  { prop: "cnt", label: "关联产品数" }
];
const sureCallback = () => {
  if (proTable.value?.selectedList.length > 3) {
    ElMessage.error("最多只能选择3个标签");
  } else {
    drawerRef.value?.acceptParams({ show: false });
    if (proTable.value?.selectedList.length === 0) return;
    emits("selectedList", proTable.value?.selectedList);
    emits("selectedListIds", proTable.value?.selectedListIds); // tagList 后端需要的是这个
    // oneForm.choiceLabelList = proTable.value?.selectedList;
    // choiceLabelListIds.value = proTable.value?.selectedListIds;
  }
};
defineExpose({
  showLabel
});
</script>

<style scoped lang="scss">
.el-form {
  width: 90%;
  .text-center {
    text-align: center;
  }
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
.step-btn {
  margin: 20px 0;
  text-align: center;
  :deep(.el-button) {
    width: 140px;
    height: 40px;
    margin-right: 15px;
  }
}
.label-select {
  position: relative;
  width: 450px;
  min-height: 34px;
  padding-right: 30px;
  padding-left: 10px;
  cursor: pointer;
  background: #ffffff;
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  .arrow-icon {
    position: absolute;
    top: 50%;
    right: 7px;
    transform: translateY(-50%);
  }
  .label-box {
    padding: 2px 4px 2px 8px;
    margin-right: 10px;
    background: #f4f7fb;
    border: 1px solid #e9edf5;
    border-radius: 4px;
    .close-icon {
      color: #aaaaaa;
      &:hover {
        color: #3a3a3d;
      }
    }
  }
}
.forbid-click {
  color: var(--el-disabled-text-color);
  cursor: not-allowed;
  background: var(--el-fill-color-light);
}
</style>
