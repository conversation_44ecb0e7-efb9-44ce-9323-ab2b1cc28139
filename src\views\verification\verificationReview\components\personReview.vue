<template>
  <div class="card" style="padding-left: 32px">
    <div class="header-title">认证信息</div>
    <el-form ref="detailsFormRef" :model="reviewForm" :rules="rules" label-width="120px" label-position="left">
      <el-form-item label="证件类型" prop="idType">
        {{ idTypeList[0].name || "-" }}
        <!-- <el-select v-model="reviewForm.idType" placeholder="请选择证件类型" clearable style="width: 400px">
          <el-option v-for="(item, index) in idTypeList" :key="index + 'idTypeList'" :label="item.name" :value="item.id" />
        </el-select> -->
      </el-form-item>
      <el-form-item label="真实姓名" prop="realName">
        <el-input :disabled="viewStatus" v-model="reviewForm.realName" placeholder="请输入真实姓名" clearable />
      </el-form-item>
      <el-form-item label="身份证号码" prop="idcardNumber">
        <el-input :disabled="viewStatus" v-model="reviewForm.idcardNumber" placeholder="请输入身份证号码" clearable />
      </el-form-item>
      <!-- <el-form-item label="所属行业" prop="industryType">
        <el-select
          :disabled="viewStatus"
          v-model="reviewForm.industryType"
          placeholder="请选择所属行业"
          clearable
          style="width: 400px"
        >
          <el-option
            v-for="(item, index) in industryList"
            :key="index + 'industryList'"
            :label="item.itemValue"
            :value="item.itemKey"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所在地区" prop="area">
        <el-select
          :disabled="viewStatus"
          v-model="reviewForm.province"
          placeholder="请选择所在地区"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="(item, index) in provinceList"
            :key="index + 'provinceList'"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <span style="margin: 0 10px"> - </span>
        <el-select :disabled="viewStatus" v-model="reviewForm.city" placeholder="请选择所在地区" clearable style="width: 200px">
          <el-option v-for="(item, index) in cityList" :key="index + 'cityList'" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="职业信息" prop="careerType">
        <el-select
          :disabled="viewStatus"
          v-model="reviewForm.careerType"
          placeholder="请选择职业信息"
          clearable
          style="width: 400px"
        >
          <el-option
            v-for="(item, index) in careerTypeList"
            :key="index + 'careerList'"
            :label="item.itemValue"
            :value="item.itemKey"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="手机号码" prop="phone">
        {{ reviewForm.phone || "-" }}
        <!-- <el-input type="text" v-model="reviewForm.phone" placeholder="请输入手机号码" clearable /> -->
      </el-form-item>
      <el-row justify="start">
        <el-form-item label="身份证" prop="certificateFrontUrl">
          <UploadImg
            v-model:image-url="reviewForm.certificateFrontUrl"
            :limit="5"
            :file-size="1"
            :disabled="viewStatus"
            height="140px"
            width="140px"
          >
            <template #tip>身份证人像面</template>
          </UploadImg>
        </el-form-item>
        <el-form-item label-width="20" prop="certificateBackUrl">
          <UploadImg
            v-model:image-url="reviewForm.certificateBackUrl"
            :limit="5"
            :file-size="1"
            :disabled="viewStatus"
            height="140px"
            width="140px"
          >
            <template #tip>身份证国徽面</template>
          </UploadImg>
        </el-form-item>
      </el-row>

      <Examine
        :verification-id="id"
        :audit-msg="reviewForm.auditMsg"
        :is-disabled="isView"
        :is-show-operate-btn="!isView"
        @on-confirm="onConfirm"
        @on-back="backtoList"
      />
    </el-form>
  </div>
</template>

<script setup lang="ts" name="personReview">
import { reactive, ref, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import UploadImg from "@/components/Upload/Img.vue";
import { getPersonalCertDetail, auditPersonalCert, queryPersonalCertDetail } from "@/api/modules/verification";
import Examine from "@/views/verification/examine.vue";
// import { getDictionary } from "@/api/modules/system";
import { TabPaneName } from "element-plus";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { editType } from "@/enums/pageTypeEnum";

import sm2DoDecrypt from "@/utils/sm2DoDecrypt";

const route = useRoute();
const router = useRouter();
const { auditTag } = route.query;
const id = route.query.id as string;

const detailsFormRef = ref<FormInstance>();
const isView = auditTag == "1"; // 是否是详情
const viewStatus = auditTag == "1" || auditTag == "2";

const defFormValue = {
  idType: "0",
  realName: "",
  idcardNumber: "",
  // industryType: "",
  // province: "",
  // city: "",
  careerType: "",
  phone: "",
  certificateFrontUrl: "",
  certificateBackUrl: "",
  auditMsg: ""
};
const reviewForm = ref(defFormValue);
// const industryList = ref<any>([]);
const provinceList = ref<any>([]);
const cityList = ref<any>([]);
const careerTypeList = ref<any>([]);

const tabStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();

// Remove Tab
const tabRemove = (fullPath: TabPaneName) => {
  const name = tabStore.tabsMenuList.filter(item => item.path == fullPath)[0].name || "";
  keepAliveStore.removeKeepAliveName(name);
  tabStore.removeTabs(fullPath as string, fullPath == route.fullPath);
};
/* 请求 */
onMounted(() => {
  // 行业类型字典
  // getDictionary({ dictCode: "dict_industry_type" }).then((res: any) => {
  //   const data = res?.data;
  //   data.forEach((item: any = {}) => {
  //     item.value = item.itemKey;
  //     item.label = item.itemValue;
  //   });
  //   industryList.value = JSON.parse(JSON.stringify(data));
  // });
  // 职业信息字典
  // getDictionary({ dictCode: "dict_career_type" }).then((res: any) => {
  //   const data = res?.data;
  //   data.forEach((item: any = {}) => {
  //     item.value = item.itemKey;
  //     item.label = item.itemValue;
  //   });
  //   careerTypeList.value = JSON.parse(JSON.stringify(data));
  // });

  if (auditTag === String(editType.audit)) {
    queryPersonalCertDetail({ id }).then((res: any) => {
      reviewForm.value = {
        ...defFormValue,
        realName: res.data.realName,
        // idcardNumber: res.data.idcardNumber,
        // phone: res.data.phone,
        certificateFrontUrl: res.data.certificateFrontUrl,
        certificateBackUrl: res.data.certificateBackUrl,
        auditMsg: res.data.auditMsg
      };
      sm2DoDecrypt({
        idcardNumber: res.data.idcardNumber,
        phone: res.data.phone
      })
        .then(data => {
          reviewForm.value.idcardNumber = data?.idcardNumber;
          reviewForm.value.phone = data?.phone;
        })
        .catch(err => {});
    });
  } else {
    getPersonalCertDetail({ id }).then((res: any) => {
      // provinceList.value = res.data.province
      //   ? [
      //       {
      //         label: res.data.province,
      //         value: res.data.province
      //       }
      //     ]
      //   : [];
      // cityList.value = res.data.city
      //   ? [
      //       {
      //         label: res.data.city,
      //         value: res.data.city
      //       }
      //     ]
      //   : [];
      reviewForm.value = {
        ...defFormValue,
        // idType: res.data.idType,
        realName: res.data.realName,
        idcardNumber: res.data.idcardNumber,
        // industryType: res.data.industryType,
        // province: res.data.province,
        // city: res.data.city,
        // careerType: res.data.careerType,
        phone: res.data.phone,
        certificateFrontUrl: res.data.certificateFrontUrl,
        certificateBackUrl: res.data.certificateBackUrl,
        auditMsg: res.data.auditMsg
      };
    });
  }
});

const idTypeList = [{ id: "0", name: "中国大陆身份证" }];

const rules = reactive<FormRules>({
  idType: [{ required: true, message: "请选择证件类型", trigger: "blur" }],
  realName: [{ required: true, message: "请输入真实姓名", trigger: "blur" }],
  idNo: [{ required: true, message: "请输入身份证号码", trigger: "blur" }],
  phone: [{ required: true, message: "请输入手机号码", trigger: "blur" }],
  certificateFrontUrl: [{ required: true, message: "请上传身份证照片", trigger: "blur" }],
  auditMsg: [{ required: true, message: "请输入审核意见", trigger: "blur" }]
});

const backtoList = () => {
  router.push("/verification/verificationReview");
};

const onConfirm = (values: any) => {
  auditPersonalCert({
    id: id,
    msg: values.auditRemark,
    pass: values.type == "pass" ? "1" : "0"
  }).then(res => {
    if (res.code === 200) {
      ElMessage({
        message: values.type == "pass" ? "审核通过成功!" : "审核不通过完成!",
        type: "success"
      });
      tabRemove(route.fullPath);
      backtoList();
    } else {
      ElMessage({
        type: "error",
        message: res.msg
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.el-form {
  width: 60%;
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
</style>
