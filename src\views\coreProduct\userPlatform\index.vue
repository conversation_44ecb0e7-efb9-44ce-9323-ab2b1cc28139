<template>
  <div class="table-box">
    <ProTable :request-auto="false" ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <template #tableHeader>
        <el-button v-auth="'userPlatformAdd'" plain @click="defaultConfig"> 默认权限配置 </el-button>
      </template>
      <template #operation="{ row }">
        <auth-button :row-data="row" :auth-list="authList"></auth-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx">
import type { CoreSystem } from "@/api/interface/coreSystem";
import { queryList } from "@/api/modules/coreSystem/userPlatform";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { onMounted, ref } from "vue";
import { IAuthButtonItem } from "@/components/AuthButton/typings";
import { useRouter } from "vue-router";

const router = useRouter();

const authList: IAuthButtonItem<CoreSystem.userPlatformItem>[] = [
  {
    contentName: "查看",
    iconClass: "View",
    itemClick: scope => toView(scope.userId),
    authName: "userPlatformView"
  }
];

const proTable = ref<ProTableInstance>();

const columns: ColumnProps[] = [
  { prop: "account", label: "用户账号", search: { el: "input", props: { maxlength: 20 } } },
  { prop: "phone", label: "手机号码", search: { el: "input", props: { maxlength: 20 } } },
  { prop: "userName", label: "用户名称" },
  { prop: "operation", label: "操作", fixed: "right" }
];

const getTableList = async (params?: CoreSystem.UserQuotaSearch) => {
  return await queryList({ ...params });
};

const defaultConfig = () => {
  router.push({
    path: `/coreProduct/userPlatform/default`,
    query: {
      pageType: "default"
    }
  });
};
const toView = async (userId: string) => {
  router.push({
    path: `/coreProduct/userPlatform/detail`,
    query: {
      id: userId,
      pageType: "view"
    }
  });
};
onMounted(() => {
  proTable.value?.getTableList();
});
</script>

<style scoped></style>
