.login-container {
  height: 100%;
  background: url("@/assets/images/login_bg.jpg") no-repeat center top;

  // min-height: 550px;
  background-color: #eeeeee;
  background-size: cover;
  .app-name {
    position: absolute;
    top: 56px;
    left: 56px;
    font-size: 48px;
    .app-logo {
      display: block;

      // width: 146px;
      height: 62px;
    }
  }
  .login-box {
    position: relative;

    // padding: 0 120px;
    right: 15%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 100%;

    // background-color: rgb(255 255 255 / 80%);
    border-radius: 10px;
    .el-input__icon {
      width: 20px;
      height: 20px;
    }
    .dark {
      position: absolute;
      top: 13px;
      right: 18px;
    }
    .login-left {
      width: 800px;
      margin-right: 10px;
      .login-left-img {
        width: 100%;
        height: 100%;
      }
    }
    .form-box {
      position: relative;
    }
    .login-form {
      position: relative;
      width: 340px;
      padding: 64px 64px 140px;
      background-color: var(--el-bg-color);
      border-radius: 8px;
      box-shadow: 0 0 20px 0 #c3d4e5;
      .verify-bg {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgb(0 0 0 / 70%);
      }
      :deep(.el-tabs__nav-wrap::after) {
        display: none;
      }
      :deep(.el-tabs__item) {
        font-family: PingFangSC-Semibold, "PingFang SC";
        font-size: 28px;
        font-weight: 600;
        line-height: 40px;
        color: #3a3a3d;
      }
      :deep(.el-tabs__nav) {
        padding-bottom: 12px;
      }
      :deep(.el-tabs__active-bar) {
        left: 47px;
        width: 56px !important;
        height: 4px;
        background: rgb(0 82 217);
        border-radius: 2px;
        transform: translateX(230px);
      }
      :deep(.el-tabs__header) {
        margin-bottom: 42px;
      }
      :deep(.el-input-group) {
        width: 340px;
      }
      :deep(.el-input__wrapper) {
        height: 54px;
        font-family: PingFangSC-Regular, "PingFang SC";
        font-size: 16px;
        font-weight: 400;
        color: #c4c6cc;
        background: #f3f4f9;
        border: none;
        border-radius: 2px;
        box-shadow: none;
      }
      .login-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 45px;
        .login-icon {
          width: 60px;
          height: 52px;
        }
        .logo-text {
          padding: 0 0 0 25px;
          margin: 0;
          font-size: 42px;
          font-weight: bold;
          color: #34495e;
          white-space: nowrap;
        }
      }
      .el-form-item {
        margin-bottom: 24px;

        // &:last-child {
        //   margin-bottom: 0;
        // }
      }
      .login-btn {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 26px;
        margin-bottom: 12px;
        white-space: nowrap;
        .el-button {
          width: 100%;
          height: 54px;
          font-size: 20px;
          color: #ffffff;
          background: linear-gradient(270deg, #22a2ff 0%, #0052d9 100%);
          border-radius: 2px;
          opacity: 0.6;
          &.active {
            opacity: 1;
          }
        }
      }
    }
    .login-tips-box {
      display: flex;
      justify-content: space-between;
      .tips-left {
        display: flex;
        align-items: center;
        font-family: PingFangSC-Regular, "PingFang SC";
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        color: #3a3a3d;
      }
      .el-button {
        padding: 0;
        font-size: 16px;
      }
    }
  }
}

@media screen and (width <= 1250px) {
  .login-left {
    display: none;
  }
}

@media screen and (width <= 600px) {
  .login-form {
    width: 97% !important;
  }
}
