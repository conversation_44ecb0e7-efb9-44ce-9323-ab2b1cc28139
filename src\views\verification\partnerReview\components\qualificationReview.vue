<template>
  <div class="card" style="padding-left: 32px" v-loading="loading">
    <div class="infos">
      <div class="header-title">企业信息</div>
      <el-form ref="formRef" :model="examineForm" label-width="110px" class="demo-reviewForm" :label-position="'left'">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">企业名称： </span>
              <span class="info-text">{{ rowData?.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">社会信用统一代码： </span>
              <span class="info-text">{{ rowData?.usciCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">所属行业： </span>
              <span class="info-text"> {{ useDictLabel(dict_industry_type, rowData?.industryType) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">法定代表人： </span>
              <span class="info-text"> {{ rowData?.legalRepresentative }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">证件类型： </span>
              <span class="info-text"> {{ useDictLabel(dict_personal_idcard_type, rowData?.idcardType) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">证件号码： </span>
              <span class="info-text">{{ rowData?.idcardNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">公司所在地： </span>
              <span class="info-text">{{ rowData?.addressProvince }}-{{ rowData?.addressCity }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">公司注册地： </span>
              <span class="info-text">{{ rowData?.province }}-{{ rowData?.city }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <el-form-item prop="companyCertificateUrl" label="营业执照：">
                <UploadImg
                  :file-size="10"
                  :file-type="fileType"
                  v-model:image-url="rowData.companyCertificateUrl"
                  width="135px"
                  height="135px"
                  :disabled="true"
                >
                  <template #tip>
                    上传的图片格式要求jpg、jpeg、bmp、png，不超过10M。
                    可以添加“华为云备案、云服务申请、云服务认证、云服务备案”等水印；但不能遮挡关键信息，例如企业名称、公司证件号。
                  </template>
                </UploadImg>
              </el-form-item>
            </div>
          </el-col>
          <!-- <el-col :span="8">
            <div class="info-item">
              <span class="info-label">子商户号： </span>
              <span class="info-text">{{ rowData?.merchantNumber }}</span>
            </div>
          </el-col> -->
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">公司注册时间： </span>
              <span class="info-text">{{ rowData?.registeredTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">注册资本（万元）： </span>
              <span class="info-text">{{ rowData?.registeredCapital }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">公司英文名： </span>
              <span class="info-text">{{ rowData?.companyEnglishName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">公司简称： </span>
              <span class="info-text">{{ rowData?.companyShortenName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">纳税人类型： </span>
              <span class="info-text"> {{ useDictLabel(dict_taxpayer_type, rowData?.taxpayerType) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">税务登记号： </span>
              <span class="info-text">{{ rowData?.taxNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">机构地址： </span>
              <span class="info-text">{{ rowData?.companyAddress }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">机构类型： </span>
              <span class="info-text"> {{ useDictLabel(dict_company_type, rowData?.companyType) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">代码证办理日期： </span>
              <span class="info-text">{{ rowData?.certificateRegisteredDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">代码证作废日期： </span>
              <span class="info-text">{{ rowData?.certificateInvalidDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">公司电话： </span>
              <span class="info-text">{{ rowData?.companyPhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">公司传真电话： </span>
              <span class="info-text">{{ rowData?.companyFax }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">主管机构名称： </span>
              <span class="info-text">{{ rowData?.masterOrg }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">主管机构代码： </span>
              <span class="info-text">{{ rowData?.masterOrgCode }}</span>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="header-title mt10">银行信息</div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">户主名： </span>
              <span class="info-text">{{ rowData?.bankAccountUser }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">银行账号： </span>
              <span class="info-text">{{ rowData?.bankAccountNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">开户行名称： </span>
              <span class="info-text">{{ rowData?.registerBankName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">银行名称： </span>
              <span class="info-text">{{ rowData?.bankName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">开户行联行号： </span>
              <span class="info-text">{{ rowData?.interBankNumber }}</span>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="header-title mt10">企业联系人</div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">联系人： </span>
              <span class="info-text">{{ rowData?.companyContactName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">联系人电话： </span>
              <span class="info-text">{{ rowData?.companyContactPhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">电子邮箱： </span>
              <span class="info-text">{{ rowData?.companyContactEmail }}</span>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="header-title mt10">业务联系人</div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">联系人： </span>
              <span class="info-text">{{ rowData?.businessContactName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">联系人电话： </span>
              <span class="info-text">{{ rowData?.businessContactPhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">电子邮箱： </span>
              <span class="info-text">{{ rowData?.businessContactEmail }}</span>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="header-title mt10">合同联系人</div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">联系人： </span>
              <span class="info-text">{{ rowData?.contractContactName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">联系人电话： </span>
              <span class="info-text">{{ rowData?.contractContactPhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">电子邮箱： </span>
              <span class="info-text">{{ rowData?.contractContactEmail }}</span>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="header-title mt10">意向合作产品</div>
          </el-col>
          <!-- 提交多个产品就显示多个 -->
          <template v-if="rowData?.intendCooperateProducts && rowData?.intendCooperateProducts.length">
            <div v-for="(item, index) in rowData?.intendCooperateProducts" :key="index" style="display: flex; width: 100%">
              <el-col :span="8">
                <div class="info-item">
                  <span class="info-label">产品名称： </span>
                  <span class="info-text">{{ item?.name }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="info-label">产品简介： </span>
                  <span class="info-text">{{ item?.value }}</span>
                </div>
              </el-col>
            </div>
          </template>
          <template v-else>
            <div style="display: flex; width: 100%">
              <el-col :span="8">
                <div class="info-item">
                  <span class="info-label">产品名称： </span>
                  <span class="info-text"></span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <span class="info-label">产品简介： </span>
                  <span class="info-text"></span>
                </div>
              </el-col>
            </div>
          </template>

          <el-col :span="24">
            <el-form-item label="产品附件文档：" prop="_productDocs">
              <uploadFiles
                v-model:file-list="rowData._productDocs"
                width="135px"
                :is-private="true"
                height="135px"
                :file-type="'.doc,.docx,.pdf'"
                :limit="10"
                :show-file-list="true"
                :disabled="true"
              >
                <template #tip>
                  <div class="upload-tips">上传的文件格式要求word、pdf，不超过10M。</div>
                </template>
              </uploadFiles>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="header-title mt10">审核信息</div>

        <el-form-item label="审核意见" prop="auditRemark" :rules="[{ required: validRequired, message: '请输入审核意见' }]">
          <el-input
            maxlength="500"
            :placeholder="pageType === PageType.introduce ? '请填写审核意见' : '驳回时必须填写审核意见，通过默认审核意见为同意'"
            show-word-limit
            :rows="5"
            v-model="examineForm.auditRemark"
            type="textarea"
            autocomplete="off"
            :disabled="!isReview"
          />
        </el-form-item>

        <el-form-item
          label="审核文档："
          prop="reviewDocs"
          :rules="[
            {
              required: true,
              message: '请上传审核文档',
              trigger: 'blur'
            }
          ]"
          v-if="pageType === PageType.introduce"
        >
          <uploadFiles
            v-model:file-list="examineForm.reviewDocs"
            width="135px"
            height="135px"
            :file-type="'.doc,.docx,.pdf,.jpg,.jpeg,.bmp,.png'"
            :limit="10"
            :file-size="10"
            :show-file-list="true"
            :disabled="!isReview"
          >
            <template #tip>
              <div class="upload-tips">上传的文件格式要求word、pdf、jpg、jpeg、bmp、png，不超过10M。</div>
            </template>
          </uploadFiles>
        </el-form-item>

        <div class="button" v-if="isReview">
          <el-button type="primary" @click="handleTask('pass')">通过</el-button>
          <el-button type="primary" @click="handleTask('noPass')">驳回</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts" name="qualificationReview">
import { reactive, ref, computed, onMounted, nextTick } from "vue";
import type { Action, FormInstance } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import UploadImg from "@/components/Upload/Img.vue";
import uploadFiles from "@/components/Upload/Files.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
import {
  getProviderApplyDetailExt,
  getProviderIntroduceDetailExt,
  auditProviderApply,
  auditProviderIntroduce,
  getProviderApplyAuditDetailExt,
  queryProviderIntroduceAuditDetailExt
} from "@/api/modules/partner";
import { PageType, operateType } from "../type";
import { closeTabItem } from "@/utils/closeTab";
import sm2DoDecrypt from "@/utils/sm2DoDecrypt";

interface ReqType {
  id: string;
  msg: string;
  pass: string;
  fileInfo?: string;
}
const { dict_industry_type, dict_personal_idcard_type, dict_taxpayer_type, dict_company_type } = useDict(
  "dict_industry_type",
  "dict_personal_idcard_type",
  "dict_taxpayer_type",
  "dict_company_type"
);

const fileType = ["image/jpeg", "image/png", "image/jpg", "image/bmp"];

const route = useRoute();
const router = useRouter();
const { pageType, type, id } = route.query;
const isReview = (type as string) === `${operateType.review}`;
const rowData = ref<any>({ _productDocs: [] });

const loading = ref(true);

const fullData = (apply: any, enterpriseCertificateVO: any, provider: any) => {
  const pageData = {
    ...apply,
    addressProvince: apply.province,
    addressCity: apply.city,
    companyName: enterpriseCertificateVO.companyName,
    usciCode: enterpriseCertificateVO.usciCode,
    industryType: enterpriseCertificateVO.industryType,
    // companyAddress: enterpriseCertificateVO.companyAddress,
    city: enterpriseCertificateVO.city,
    cityCode: enterpriseCertificateVO.cityCode,
    province: enterpriseCertificateVO.province,
    provinceCode: enterpriseCertificateVO.provinceCode,
    registeredCapital: enterpriseCertificateVO.registeredCapital,
    registeredTime: enterpriseCertificateVO.registeredTime,
    companyCertificateUrl: enterpriseCertificateVO.companyCertificateUrl
  };

  rowData.value = { ...apply, ...pageData };
  if (!isReview) {
    examineForm.auditRemark = apply.auditMsg;
  }
  if (provider && Object.keys(provider).length !== 0) {
    examineForm.auditRemark = provider.auditMsg;
    const introduceFile = JSON.parse(provider.introduceFile);
    examineForm.reviewDocs = introduceFile?.map((item: any) => {
      return { ...item, name: item.originalName, url: item.link };
    });
  }
  const _productDocs = JSON.parse(apply.productDocs);
  rowData.value._productDocs = _productDocs.map((item: any) => {
    return { ...item, name: item.originalName, url: item.link };
  });

  rowData.value.intendCooperateProducts = JSON.parse(apply.intendCooperateProducts);
};

const getDetails = () => {
  let api = pageType === PageType.qualification ? getProviderApplyDetailExt : getProviderIntroduceDetailExt;
  if (type === String(operateType.review)) {
    api = pageType === PageType.qualification ? getProviderApplyAuditDetailExt : queryProviderIntroduceAuditDetailExt;
  }
  api({ id: id as string })
    .then((resp: any) => {
      let { apply, enterpriseCertificateVO, provider } = resp.data;
      if (pageType !== PageType.qualification) {
        apply = resp.data.provider;
        enterpriseCertificateVO = resp.data.enterprise;
        provider = resp.data.providerIntroduce;
      }

      /* 审核详情解密 */

      if (type === String(operateType.review)) {
        sm2DoDecrypt({
          idcardNumber: apply.idcardNumber,
          bankAccountNumber: apply.bankAccountNumber,
          companyContactPhone: apply.companyContactPhone,

          interBankNumber: apply.interBankNumber,
          businessContactPhone: apply.businessContactPhone,
          contractContactPhone: apply.contractContactPhone
        })
          .then(data => {
            apply.idcardNumber = data?.idcardNumber;
            apply.bankAccountNumber = data?.bankAccountNumber;
            apply.companyContactPhone = data?.companyContactPhone;
            apply.interBankNumber = data?.interBankNumber;
            apply.businessContactPhone = data?.businessContactPhone;
            apply.contractContactPhone = data?.contractContactPhone;

            fullData(apply, enterpriseCertificateVO, provider);
          })
          .catch(err => {})
          .finally(() => {});
      } else {
        fullData(apply, enterpriseCertificateVO, provider);
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(() => {
  getDetails();
});

const formRef = ref<FormInstance>();
const examineForm = reactive({
  auditRemark: "",
  reviewDocs: []
});
const validType = ref<string>("pass");

const validRequired = computed(() => {
  if (pageType === PageType.introduce) {
    return true;
  }
  return validType.value === "noPass";
});

const handleTask = (index: string) => {
  validType.value = index;
  let tips = "";
  if (index === "pass") {
    tips = "是否确认审核通过";
  } else {
    tips = "是否确认驳回审核";
  }

  nextTick(() => {
    formRef.value?.validate().then(valid => {
      console.log("valid", valid);
      if (valid) {
        ElMessageBox.alert(tips, "操作提示", {
          confirmButtonText: "确定",
          callback: (action: Action) => {
            if (action === "confirm") {
              const auditRemark = examineForm.auditRemark === "" ? "同意" : examineForm.auditRemark;

              const api = pageType === PageType.qualification ? auditProviderApply : auditProviderIntroduce;
              const toPass = index === "pass";
              const params: ReqType = {
                id: id as string,
                msg: toPass ? auditRemark : examineForm.auditRemark,
                pass: toPass ? "1" : "0"
              };
              if (pageType === PageType.introduce) {
                const fileList = examineForm.reviewDocs?.map((item: { url: string; uid: number; name: string; link: string }) => {
                  return { link: item.link, uid: item.uid, originalName: item.name, name: item.name };
                });
                params.fileInfo = JSON.stringify(fileList);
              }
              api(params).then(res => {
                if (res.code == 200) {
                  ElMessage({
                    message: toPass ? "审核通过成功!" : "审核不通过完成!",
                    type: "success"
                  });
                  closeTabItem(route);
                  router.push("/verification/partnerReview");
                } else {
                  ElMessage({
                    message: toPass ? "审核通过失败!" : "审核不通过失败!",
                    type: "error"
                  });
                }
              });
            }
          }
        });
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.mt10 {
  margin-top: 10px;
}
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
.infos {
  margin-left: 10px;
  .info-item {
    margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      margin-right: 10px;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
  .demo-reviewForm.el-form {
    .el-form-item {
      :deep(.el-form-item__label) {
        color: #73787f;
      }
    }
  }
}
</style>
