//枚举类型的数据
export const enumDataType = {
  attrName: "",
  defaultValue: [],
  attrFilter: {
    type: "checkbox",
    content: []
  }
};
//数字格式的数据
export const numberDataType = {
  attrName: "",
  defaultValue: {},
  attrFilter: {
    type: "input",
    children: [
      {
        label: "最小值",
        attrName: "minValue",
        attrCode: "minValue",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "最大值",
        attrName: "maxValue",
        attrCode: "maxValue",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "步长",
        attrName: "step",
        attrCode: "step",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "单位",
        attrName: "unit",
        attrCode: "unit",
        validatePass: { required: true },
        attrFilter: {
          type: "input",
          validatePass: { required: true },
          attr: {
            placeholder: "最长不超过10个字",
            maxlength: 10,
            "show-word-limit": true
          }
        }
      }
    ]
  }
};
//系统盘格式的数据
export const sysDiskDataType = {
  attrName: "系统盘",
  validatePass: { required: true },
  requrie: true,
  defaultValue: {},
  attrFilter: {
    type: "systemDisk",
    children: [
      {
        label: "磁盘类型",
        attrName: "diskTypeList",
        attrCode: "diskTypeList",
        defaultValue: [],
        attrFilter: {
          type: "checkbox",
          content: [
            {
              value: "1",
              name: "通用SSD"
            },
            {
              value: "2",
              name: "高IO"
            },
            {
              value: "3",
              name: "超高IO"
            }
          ]
        }
      },
      {
        label: "磁盘容量最小值",
        attrName: "diskSizeMin",
        attrCode: "diskSizeMin",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "磁盘容量最大值",
        attrName: "diskSizeMax",
        attrCode: "diskSizeMax",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "磁盘容量步长",
        attrName: "diskSizeStep",
        attrCode: "diskSizeStep",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "单位",
        attrName: "diskUnit",
        attrCode: "diskUnit",
        attrFilter: {
          type: "input",
          attr: {
            placeholder: "最长不超过10个字",
            maxlength: 10,
            "show-word-limit": true
          }
        }
      }
    ]
  }
};
//数据盘格式的数据
export const dataDiskDataType = {
  attrName: "数据盘",
  requrie: true,
  defaultValue: {},
  attrFilter: {
    type: "dataDisk",
    children: [
      {
        label: "磁盘类型",
        attrName: "diskTypeList",
        attrCode: "diskTypeList",
        defaultValue: [],
        attrFilter: {
          type: "checkbox",
          content: [
            {
              value: "1",
              name: "通用SSD"
            },
            {
              value: "2",
              name: "高IO"
            },
            {
              value: "3",
              name: "超高IO"
            }
          ]
        }
      },
      {
        label: "磁盘容量最小值",
        attrName: "diskSizeMin",
        attrCode: "diskSizeMin",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "磁盘容量最大值",
        attrName: "diskSizeMax",
        attrCode: "diskSizeMax",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "磁盘容量步长",
        attrName: "diskSizeStep",
        attrCode: "diskSizeStep",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "单位",
        attrName: "diskUnit",
        attrCode: "diskUnit",
        attrFilter: {
          type: "input",
          attr: {
            placeholder: "最长不超过10个字",
            maxlength: 10,
            "show-word-limit": true
          }
        }
      },
      {
        label: "磁盘数量最小值",
        attrName: "diskNumMin",
        attrCode: "diskNumMin",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      },
      {
        label: "磁盘数量最大值",
        attrName: "diskNumMax",
        attrCode: "diskNumMax",
        attrFilter: {
          type: "input",
          attr: {
            type: "number"
          }
        }
      }
    ]
  }
};
//购买时长的数据
export const buyTimeAttr = {
  attrName: "购买时长",
  attrCode: "BUY_DURATION",
  attrType: "1",
  id: "5"
};
//购买数量的数据
export const buyNumberAttr = {
  attrName: "购买数量",
  attrCode: "BUY_COUNT",
  attrType: "1",
  id: "6"
};
