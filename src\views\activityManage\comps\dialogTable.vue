<template>
  <el-dialog v-model="visible" title="选择产品" width="1200" align-center @close="handleCancel" destroy-on-close>
    <div class="table-box">
      <ProTable
        @selection-change="changeSelect"
        ref="ProTableRef"
        :request-api="getTableList"
        :columns="columns"
        :tool-button="false"
        :row-key="'skuId'"
      >
        <template #tableHeader>
          <div class="mb20">
            已选中 <span class="has-choose">{{ diffArray.length }}</span> 项，最多能选<span class="has-choose">20</span>项
          </div>
        </template>
      </ProTable>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button :disabled="diffArray.length === 0" type="primary" @click="handleConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { computed, nextTick, ref, unref, watch } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { queryProdSkuPage } from "@/api/modules/product";
import { Product } from "@/api/interface/product";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getTypeTree } from "@/api/modules/productClassify";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  cList: {
    type: Array,
    default: () => {
      return [];
    }
  },
  pageType: {
    type: String,
    default: "create"
  },
  status: {
    type: Number,
    default: 0
  }
});

const emits = defineEmits(["update:modelValue", "onSelect"]);

const route = useRoute();

const ProTableRef = ref<ProTableInstance>();

const visible = ref(false);

watch(
  () => props.modelValue,
  nVal => {
    visible.value = nVal;

    if (nVal) {
      nextTick(() => {
        props.cList.forEach((row: any) => {
          ProTableRef.value?.element.toggleRowSelection({ skuId: row.skuId, ...row }, true);
        });
      });
    } else {
      // nextTick(() => {
      //   ProTableRef.value?.element!.clearSelection();
      // });
    }
  }
);

/* 新选的产品 */
const diffArray = ref<Array<Product.ProductListParams>>([]);

const columns: ColumnProps<Product.ProductListParams>[] = [
  {
    type: "selection",
    fixed: "left",
    width: 50,
    selectable: (row: any, index: number) => {
      if (diffArray.value.length >= 20) {
        return diffArray.value.some((el: any) => el.skuId === row.skuId);
      } else {
        return true;
      }

      // if (props.pageType === "edit" && props.status === 3) {
      //   return true;
      // }
      // if (props.pageType === "create") {
      //   return true;
      // } else {
      //   if (props.cList.length === 0) {
      //     return true;
      //   } else {
      //     return !props.cList.some(item => item.skuId === row.id && item.marketingId !== null);
      //   }
      // }
    }
  },
  { prop: "productName", label: "产品名称", search: { el: "input" } },
  {
    prop: "categoryName",
    label: "产品分类",

    enum: getTypeTree,
    isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格
    search: { el: "cascader", key: "categoryIdList" },
    fieldNames: {
      value: "id",
      label: "name"
    }
  },
  { prop: "skuName", label: "产品规格" },
  { prop: "skuPrice", label: "规格单价" }
];

const changeSelect = (data: Product.ProductListParams[]) => {
  // if (data.length <= 20) {
  //   diffArray.value = data;
  // } else {
  //   ElMessage.warning("最多选择20个");
  // }

  if (data.length > 20) {
    data.splice(20);
    ProTableRef.value?.element.clearSelection();
    data.forEach(row => {
      ProTableRef.value?.element.toggleRowSelection(row, true);
    });
  }

  diffArray.value = data;
};

const getTableList = async (params: any = {}) => {
  const newParams = JSON.parse(JSON.stringify(params));

  if (Reflect.has(newParams, "categoryIdList")) {
    newParams.categoryId = params.categoryIdList[params.categoryIdList.length - 1];
    Reflect.deleteProperty(newParams, "categoryIdList");
  }

  return await queryProdSkuPage({ ...newParams, publishStatus: 2 });
};

const handleConfirm = () => {
  emits("update:modelValue", false);

  emits("onSelect", diffArray.value);
};

const handleCancel = () => {
  emits("update:modelValue", false);
};
</script>

<style scoped lang="scss">
.has-choose {
  padding: 0 5px;
  color: var(--el-color-primary);
}
</style>
