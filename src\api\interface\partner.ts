export namespace Partner {
  export interface AddAgreementParams {
    agreementName?: string; // 协议名称
    agreementUrl?: string; // 附件url
    auditStatus?: string; // 审核状态[pms_provider_agreement_audit_status 1审核中 2审核不通过 3审核通过 ]
    code?: string; // 合作商编码
    id?: string; // 唯一标识
    isRelated?: string | number; // 是否关联旧协议
    name?: string; // 合作商名称
    relateUuid?: string; // 关联协议,可以逗号分隔
    remark?: string; // 备注
    validEndTime?: string; // 有效时间结束
    validStartTime?: string; // 有效时间开始
  }
  export interface ProviderAgreement {
    agreementName?: string;
    agreementUrl?: string;
    ascs?: string;
    auditStatus?: string;
    code?: string;
    current?: number;
    descs?: string;
    isRelated?: string;
    name?: string;
    relateUuid?: string;
    remark?: string;
    size?: number;
    validEndTime?: string;
    validStartTime?: string;
    id?: string;
  }

  export interface ProviderList {
    auditType: string;
    name: string;
    usciCode?: string;
    agreementName?: string;
    createTime: string;
    auditStatus: string;
    auditTime: string;
    auditUserName: string;
  }

  export interface ProviderAgreementDetail {
    agreementId: string; // 合作商名称
    agreementName: string; //  协议名称
    agreementUrl: string; //  附件url
    auditMsg: string; //  审核信息
    auditStatus: string; //  审核状态[dict_audit_status 1审核中 2审核不通过 3审核通过]
    auditTime: string; //  审核时间
    auditUserName: string; //  审核人名称
    code: string; //  合作商编码
    id: string; //  唯一标识
    isRelated: number; //  是否关联旧协议
    name: string; //  合作商名称
    relateAgreement: string; //  关联协议信息
    remark: string; //  备注
    validEndTime: string; //  有效时间结束
    validStartTime: string; //  有效时间开始
  }

  export interface AgreementUrlType {
    url: string;
    uid: number;
    name?: string;
    originalName: string;
    link: string;
  }

  export interface AuditParams {
    id: string;
    msg: string;
    pass: string;
  }
}
