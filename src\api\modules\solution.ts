import { Soluiton } from "@/api/interface/solution";
import http from "@/api";

/**
 * @name 解决方案管理模块
 */
// 解决方案列表
export const getSolutionList = (params: Soluiton.SolutionListParams) => {
  return http.get(`/cnud-product/pmsSolution/queryPage`, params);
};

// 解决方案上下架
export const solutionPutOn = (params: Soluiton.PutOnItem) => {
  return http.post(`/cnud-product/pmsSolution/publish`, params);
};

// 解决方案列表排序
export const solutionUpdateSort = (params: Object) => {
  return http.post(`/cnud-product/pmsSolution/updateSort`, params);
};

// 解决方案编辑
export const solutionEdit = (params: Object) => {
  return http.post(`/cnud-product/pmsSolution/update`, params);
};

// 解决方案新增
export const solutionSave = (params: Object) => {
  return http.post(`/cnud-product/pmsSolution/save`, params);
};

// 解决方案删除
export const deleteSolution = (params: Object) => {
  return http.post(`/cnud-product/pmsSolution/remove`, params);
};

// 解决方案详情
export const solutionDetail = (params: Soluiton.DetailItem) => {
  return http.post(`/cnud-product/pmsSolution/detail`, params);
};

// 判断解决方案名称是否重复
export const solutioJudgeName = (params: Soluiton.JudgeNameItem) => {
  return http.post(`/cnud-product/pmsSolution/judgeName`, params);
};

// 导出解决列表
export const exportSolution = (params: Object) => {
  return http.get(`/cnud-product/pmsSolution/export`, params, { responseType: "blob" });
};

// 解决方案分类列表
export const solutionCategory = (params: Object) => {
  return http.get(`/cnud-product/pmsSolutionCategory/queryPage`, params);
};

// 解决方案分类树
export const solutionCategoryTree = (params: Object) => {
  return http.post(`/cnud-product/pmsSolutionCategory/list/withChildren`, params);
};

// 解决方案分类排序
export const solutionSortType = (params: Object) => {
  return http.post(`/cnud-product/pmsSolutionCategory/sort`, params);
};

//新增分类
export const solutionAddType = (params: Object) => {
  return http.post(`/cnud-product/pmsSolutionCategory/save`, params);
};
//编辑分类
export const solutionUpdateType = (params: Object) => {
  return http.post(`/cnud-product/pmsSolutionCategory/update`, params);
};
// 删除分类
export const solutionDeleteType = (params: Object) => {
  return http.post(`/cnud-product/pmsSolutionCategory/remove`, params); // 正常 post json 请求  ==>  application/json
};
