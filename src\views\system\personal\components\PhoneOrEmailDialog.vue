<template>
  <el-dialog v-model="dialogVisible" :title="dislogTitle" width="400px" @close="onClose">
    <div class="deplDeploy">
      <el-form ref="modifyFormRef" :model="resetPhoneForm" :rules="rules" label-width="0">
        <div v-if="active === 0">
          <div v-if="isModifyPhone" class="tips">为了保证您的账号安全，请验证身份。认证成功后进行下一步操作。</div>

          <el-form-item label="" prop="verifyType">
            <el-select
              class="formItemStyle"
              v-model="resetPhoneForm.verifyType"
              placeholder="请选择验证方式"
              size="large"
              clearable
            >
              <el-option
                v-for="(item, index) in phoneOptions"
                :key="index + item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="verifyCode">
            <el-input
              class="formItemStyle"
              v-model="resetPhoneForm.verifyCode"
              type=""
              placeholder="请输入验证码"
              size="large"
              maxlength="6"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Message />
                </el-icon>
              </template>
              <template #append>
                <CountDownButton v-show="isModifyPhone" :loading="loading" @click="getVerifyMsg"></CountDownButton>
                <CountDownButton v-show="!isModifyPhone" :loading="loading" @click="getVerifyMsg"></CountDownButton>
                <!-- <el-button text type="primary" @click="getVerifyMsg(modifyFormRef)">获取验证码</el-button> -->
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div v-if="active === 1 && !isModifyPassword">
          <el-form-item v-if="isModifyPhone" label="" prop="newPhone">
            <el-input v-model="resetPhoneForm.newPhone" placeholder="请输入新的手机号码" size="large">
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Iphone />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-else label="" prop="newEmail">
            <el-input v-model="resetPhoneForm.newEmail" placeholder="请输入新的邮箱地址" size="large">
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Iphone />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="newVerifyCode">
            <el-input
              v-model="resetPhoneForm.newVerifyCode"
              type=""
              placeholder="请输入短信验证码"
              autocomplete="new-password"
              size="large"
              maxlength="6"
            >
              <template #prefix>
                <el-icon class="el-input__icon">
                  <Message />
                </el-icon>
              </template>
              <template #append>
                <CountDownButton :loading="loading" @click="getNewVerifyMsg"></CountDownButton>
                <!-- <el-button text type="primary" @click="getNewVerifyMsg(modifyFormRef)">获取验证码</el-button> -->
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div v-if="active === 1 && isModifyPassword">
          <el-form-item label="新密码" prop="certificate" label-width="100">
            <el-input placeholder="请输入新密码" v-model="resetPhoneForm.certificate" type="password" clearable show-password>
            </el-input>
          </el-form-item>
          <el-form-item label="确认新密码" prop="checkCertificate" label-width="100">
            <el-input
              placeholder="请再次输入新密码"
              v-model="resetPhoneForm.checkCertificate"
              type="password"
              clearable
              show-password
            >
            </el-input>
          </el-form-item>
          <div class="tips">密码长度8~20位，大小写字母+数字+字符组合</div>
        </div>
        <div v-if="active === 2">
          <!-- 成功结果页 -->
          <el-result
            v-if="isModifyPhone"
            class="sucessStyle"
            icon="success"
            title="手机号码修改成功"
            sub-title="请在下次使用新的手机号码进行登录"
          >
            <template #extra>
              <el-button type="primary" class="formItemStyle" size="large" @click="onCancel">确定</el-button>
            </template>
          </el-result>
          <el-result
            v-else-if="isModifyPassword"
            class="sucessStyle"
            icon="success"
            title="账号密码修改成功"
            sub-title="修改密码后需要使用新密码重新登录"
          >
            <template #extra>
              <el-button type="primary" class="formItemStyle" size="large" @click="onReLogin()">重新登录</el-button>
            </template>
          </el-result>
          <el-result v-else class="sucessStyle" icon="success" title="邮箱地址绑定成功">
            <template #extra>
              <el-button type="primary" class="formItemStyle" size="large" @click="onCancel">确定</el-button>
            </template>
          </el-result>
        </div>
      </el-form>

      <div class="Btn">
        <el-button
          class="formItemStyle"
          size="large"
          type="primary"
          @click="onConfirm(modifyFormRef)"
          v-if="active == 0 || active == 1"
        >
          下一步
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, computed } from "vue";
import { FormRules, FormInstance, ElMessage } from "element-plus";
import { useUserStore } from "@/stores/modules/user";
import { formatEmail, formatPhone } from "@/utils/tools";
import { checkPhoneNumber } from "@/utils/eleValidate";
import { useRouter } from "vue-router";

import { LOGIN_URL } from "@/config";
import { logoutApi } from "@/api/modules/login";
import { ModifyType, VerifyTypeType } from "../personalEnum";
import { System } from "@/api/interface/system";
import { getVerifyCode, getNewVerifyCode, verifyChecked, modifyVerifyInfo } from "@/api/modules/system";
import CountDownButton from "./CountDownButton.vue";
import { sm2 } from "sm-crypto";

let verifyToken: string = "";

class FormState {
  verifyType = "";
  verifyCode = "";
  newPhone = "";
  newEmail = "";
  newVerifyCode = "";
  certificate = "";
  checkCertificate = "";
}
const props = defineProps({
  modifyInfo: {
    type: Object,
    default: () => {}
  }
});

const router = useRouter();
const modifyFormRef = ref<FormInstance>();
const resetPhoneForm = reactive(new FormState());
const loading = ref(false);

const rules = reactive<FormRules>({
  verifyType: [{ required: true, message: "请选择验证方式" }],
  verifyCode: [{ required: true, message: "请输入验证码" }],
  newPhone: [{ required: true, validator: checkPhoneNumber }],
  newEmail: [
    { required: true, message: "请输入邮箱地址" },
    {
      type: "email",
      message: "请输入正确的邮箱地址"
    }
  ],
  newVerifyCode: [{ required: true, message: "请输入验证码", trigger: "blur" }],
  certificate: [
    {
      validator(_, value, cb) {
        if (!value) {
          cb(new Error("请输入新密码"));
        }
        const reg = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*,\._])[0-9a-zA-Z!@#$%^&*,\\._]{8,20}$/;
        if (!reg.test(value)) {
          cb(new Error("请输入8-20位大小写字母+数字+特殊字符组合。"));
        } else {
          cb();
        }
      }
    }
  ],
  checkCertificate: [
    {
      validator: (_, value, cb) => {
        if (!value) {
          cb(new Error("请再次输入新密码"));
        }
        if (value !== resetPhoneForm.certificate) {
          cb(new Error("两次密码输入不一致"));
        }
        cb();
      }
    }
  ]
});
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const isModifyPhone = computed(() => props.modifyInfo.modifyType === ModifyType.phone);
const isModifyPassword = computed(() => props.modifyInfo.modifyType === ModifyType.password);
const titleMap = {
  [ModifyType.password]: "修改登录密码",
  [ModifyType.phone]: "修改手机号码",
  [ModifyType.email]: userInfo.value?.email ? "修改邮箱地址" : "绑定邮箱地址"
};

const dislogTitle = computed(() => titleMap[props.modifyInfo.modifyType as ModifyType]);

const phoneOptions = computed(() => {
  const options = [];
  const { phone = "", email = "" } = userInfo.value || {};
  if (phone) {
    options.push({
      label: `使用手机号码 ${formatPhone(phone)} 验证`,
      value: VerifyTypeType.phone
    });
  }
  if (email) {
    options.push({
      label: `使用邮箱 ${formatEmail(email) || email} 验证`,
      value: VerifyTypeType.email
    });
  }
  return options;
});

const active = ref(0);
// const prev = () => {
//   --active.value;
//   if (active.value < 0) active.value = 0;
// };
const next = () => {
  if (active.value++ > 2) active.value = 0;
};

const dialogVisible = ref(false);

const onClose = () => {
  verifyToken = "";
  if (isModifyPassword.value && active.value === 2) {
    onReLogin(0);
  } else {
    onCancel();
  }
};
const onCancel = () => {
  active.value = 0;
  modifyFormRef.value?.resetFields();
  Object.assign(resetPhoneForm, new FormState());
  dialogVisible.value = false;
};

const onReLogin = (time = 3000) => {
  setTimeout(async () => {
    // 1.执行退出登录接口
    await logoutApi();

    // 2.清除 Token
    userStore.setToken("");

    // 3.重定向到登陆页
    router.replace(LOGIN_URL);
    ElMessage.success("退出登录成功！");
  }, time);
};
const openDialog = () => {
  Object.assign(resetPhoneForm, new FormState());
  modifyFormRef.value?.resetFields();
  active.value = 0;
  dialogVisible.value = true;
};

const getCode = async (params: System.UserVerifyCode, isNew = false) => {
  loading.value = true;
  if (!isNew) {
    Reflect.deleteProperty(params, "verifyToken");
  }
  const api = isNew ? getNewVerifyCode : getVerifyCode;
  return api(params)
    .then(res => {
      verifyToken = res.data as string;
      return res;
    })
    .finally(() => {
      loading.value = false;
    });
  // const { code } = await (isNew ? getNewVerifyCode(params) : getVerifyCode(params));
  // console.log("🚀 ~ file: PhoneOrEmailDialog.vue:213 ~ getCode ~ code:", code);
  // loading.value = false;
  // return code;
  // if (code == 200) {
  //   callBack?.();
  //   ElMessage.success("验证码已下发，请注意查收");
  // }
};
const checkedVerifyCode = async (params: System.UserVerifyCode) => {
  const { code, data } = await verifyChecked(params);
  verifyToken = data as string;
  return code == 200;
};

const getVerifyMsg = (successCallback: () => void) => {
  const formEl: FormInstance | undefined = modifyFormRef.value;
  if (!formEl) return;
  formEl.validateField("verifyType").then(data => {
    if (data) {
      const params: System.UserVerifyCode = {
        verifyContent: Number(VerifyTypeType[props.modifyInfo.modifyType]),
        verifyType: Number(resetPhoneForm.verifyType),
        verifyToken: verifyToken
      };

      if (Number(resetPhoneForm.verifyType) === VerifyTypeType.phone) {
        params.phone = userInfo.value.phone;
      } else {
        params.email = userInfo.value.email;
      }
      getCode(params, false).then(data => {
        successCallback?.();
        if (data.code == 200) {
          verifyToken = data.data as string;
          ElMessage.success("验证码已下发，请注意查收");
        }
      });
    }
  });
};

const getNewVerifyMsg = (successCallback: () => void) => {
  const formEl: FormInstance | undefined = modifyFormRef.value;

  if (!formEl) return;
  formEl.validateField("newPhone").then(data => {
    if (data) {
      const params: System.UserVerifyCode = {
        verifyContent: Number(VerifyTypeType[props.modifyInfo.modifyType]),
        verifyType: Number(resetPhoneForm.verifyType),
        verifyToken
      };
      if (isModifyPhone.value) {
        params.phone = resetPhoneForm.newPhone;
      } else {
        params.email = resetPhoneForm.newEmail;
      }
      getCode(params, true).then(data => {
        successCallback?.();
        if (data.code == 200) {
          verifyToken = data.data as string;
          ElMessage.success("验证码已下发，请注意查收");
        }
      });
    }
  });
};

const publicKey: any = computed(() => userStore.publicKey);

const onConfirm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      if (active.value === 0) {
        const params: System.UserVerifyCode = {
          verifyContent: Number(VerifyTypeType[props.modifyInfo.modifyType]),
          verifyType: Number(resetPhoneForm.verifyType),
          verifyCode: resetPhoneForm.verifyCode,
          verifyToken
        };
        if (Number(resetPhoneForm.verifyType) === VerifyTypeType.phone) {
          params.phone = userInfo.value.phone;
        } else {
          params.email = userInfo.value.email;
        }
        checkedVerifyCode(params).then(isSucess => {
          if (isSucess) {
            next();
            if (isModifyPassword.value) {
              // 获取publicKey
              userStore.setPublicKey();
            }
          }
        });
      } else {
        const params: System.UserVerifyCode = {
          verifyContent: Number(VerifyTypeType[props.modifyInfo.modifyType]),
          verifyType: Number(resetPhoneForm.verifyType),
          verifyCode: resetPhoneForm.newVerifyCode,
          verifyToken
        };
        if (isModifyPhone.value) {
          params.phone = resetPhoneForm.newPhone;
        } else if (isModifyPassword.value) {
          params.password = sm2.doEncrypt(resetPhoneForm.certificate, publicKey.value, 1);
          params.verifyCode = resetPhoneForm.verifyCode;
        } else {
          params.email = resetPhoneForm.newEmail;
        }
        modifyVerifyInfo(params).then(isSucess => {
          if (isSucess) {
            userStore.setUserInfo({
              ...userInfo.value,
              ...params
            });
            next();
            if (isModifyPassword.value) {
              onReLogin();
            }
          }
        });
      }
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
defineExpose({ openDialog });
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item--default {
    margin-bottom: 38px;
  }
}
.tips {
  margin-bottom: 18px;
  color: rgb(153 153 153);
  text-align: center;
}
.formItemStyle,
.nextBtn {
  width: 100%;
}
.sucessStyle {
  :deep(.el-result__extra) {
    width: 100%;
  }
}
</style>
