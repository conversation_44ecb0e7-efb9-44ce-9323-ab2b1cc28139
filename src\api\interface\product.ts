import { getChannelList, judgeName } from "@/api/modules/product";
import { extend } from "dayjs";

export namespace Product {
  export interface ProductListParams {
    ascs?: string; // 根据该字段排正序
    descs?: string; // 根据该字段排倒序
    categoryId?: string; // 产品分类ID
    cooperateType?: string; // 产品合作类型
    createTimeStart?: string;
    createTimeEnd?: string;
    createTime?: string;
    current?: number; // 当前页
    size?: number; // 每页的数量
    updateTime?: string;
    productCode?: string; // 产品编码
    productName?: string; // 产品名
    providerId?: string; // 产品服务商id
    publishStatus?: number; // 上架状态，上架2 下架1
    status?: string;
    skuType?: string;
  }
  export interface PutOnItem {
    id?: string;
    publishStatus?: number;
  }
  export interface DetailItem {
    id?: string;
  }
  export interface JudgeNameItem {
    productName?: string;
    id?: string;
  }
  export interface LabelAddItem {
    ids?: string[];
    tagIds?: string[];
  }

  export interface pmsRightsListItem {
    code?: string;
    description?: string;
    effectTimeEnd?: string;
    effectTimeStart?: string;
    id: string;
    maxCount?: number;
    name?: string;
    providerCode?: string;
    status?: number;
    url?: string;
  }

  export interface ChannelListParams {
    /**
     * 根据该字段排正序
     */
    ascs?: string;
    channelId?: string;
    /**
     * 创建时间
     */
    createTime?: string;
    creatorId?: string;
    /**
     * 当前页
     */
    current?: number;
    /**
     * 根据该字段排倒序
     */
    descs?: string;
    id?: string;
    productId?: string;
    /**
     * 每页的数量
     */
    size?: number;
    updaterId?: string;
    /**
     * 更新时间
     */
    updateTime?: string;
    [property: string]: any;
  }

  export interface CreateParams {
    attrCode: string;
    attrEnumType: string;
    attrName: string;
    attrType: string;
    attrValueList: string;
    _attrList?: Array<{ value: any; name: string }>;
  }
  export interface ProductAttrListItem extends CreateParams {
    id: string;
  }
  export interface ProductAttrSearch {
    ascs?: string;
    attrCode?: string;
    attrEnumType?: string;
    attrName?: string;
    attrType?: string;
    attrValueList?: string;
    current?: number;
    descs?: string;
    size?: number;
    id?: string;
    [key: string]: any;
  }
  export interface ReqPageInfo {
    asc?: string; // 根据该字段排正序
    current?: number; // 当前页
    descs?: string; // 根据该字段排倒序
    size?: number; // 每页的数量
  }
  export interface QueryGiftReq extends ReqPageInfo {
    productName?: string;
    categoryId?: string;
  }
}
