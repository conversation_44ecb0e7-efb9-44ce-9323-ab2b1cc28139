<template>
  <div class="status-type" :class="`order__${orderStatus}`">
    {{ useDictLabel(orderStatusDic, orderStatus) }}
    <span>{{ statusRemark }}</span>
  </div>
</template>

<script setup lang="ts">
import { useDict, useDictLabel } from "@/hooks/useDict";
import { computed } from "vue";

// const { ord_order_status } = useDict("ord_order_status");
import { useDicStore } from "@/stores/modules/dictionaty";
import { storeToRefs } from "pinia";

const dictionaryStore = useDicStore();
const { orderStatusDic } = storeToRefs(dictionaryStore);

const subTitle = computed(() => {
  switch (Number(props.orderStatus)) {
    case 1:
      return "请耐心等待管理员审核";
    case 2:
      return "请您尽快完成支付";
    case 3:
      return "已提交支付，待返回支付结果";
    case 9:
      return "产品/服务正在开通中";
    case 4:
      return "取消订单/审核不通过/支付超时";
    case 5:
      return "";
    case 7:
      return "";
    default:
      return "";
  }
});

const props = defineProps({
  orderStatus: {
    type: String,
    request: true
  },
  statusRemark: {
    type: String,
    default: ""
  }
});
</script>

<style scoped lang="scss">
.status-type {
  height: 84px;
  padding-left: 84px;
  margin-bottom: 10px;
  line-height: 84px;
  span {
    margin-left: 16px;
    font-size: 14px;
    color: rgb(115 120 127 / 100%);
  }
  &.order {
    color: #3a3a3d;

    /* 待审核 */
    &__1 {
      background-color: rgb(255 170 0 / 10%);
      background-image: url("@/assets/images/status_icon_5.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }

    /* 待支付 */
    &__2 {
      background-color: rgb(229 48 47 / 10%);
      background-image: url("@/assets/images/status_icon_3.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }

    /* 支付中 */
    &__3 {
      background-color: rgb(0 82 217 / 10%);
      background-image: url("@/assets/images/status_icon_1.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }

    /* 订单关闭 */
    &__4 {
      background-color: rgb(115 120 127 / 10%);
      background-image: url("@/assets/images/status_icon_2.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }

    /* 单订完成 */
    &__5 {
      background-color: rgb(25 190 107 / 10%);
      background-image: url("@/assets/images/status_icon_6.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }

    /* 退订完成 */
    &__7 {
      background-color: rgb(25 190 107 / 10%);
      background-image: url("@/assets/images/status_icon_6.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }

    /* 待开通 */
    &__9 {
      background-color: rgb(0 82 217 / 10%);
      background-image: url("@/assets/images/status_icon_4.png");
      background-repeat: no-repeat;
      background-position: 24px center;
      background-size: 36px 36px;
    }
  }
}
</style>
