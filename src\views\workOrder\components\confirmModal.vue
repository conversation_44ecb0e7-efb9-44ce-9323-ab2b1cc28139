<template>
  <el-dialog v-model="dialogVisible" :title="modalTitle" width="800" :before-close="handleClose" :destroy-on-close="true">
    <div class="content">
      <el-form ref="applyRef" :model="applyForm" label-width="120px">
        <template v-if="modalAction === 'confirm'">
          <el-form-item
            label="解决方案："
            prop="applicationMessage"
            :rules="[{ trigger: 'blur', required: true, message: '请输入解决方案' }]"
          >
            <el-input
              v-model="applyForm.applicationMessage"
              type="textarea"
              placeholder="描述出现原因和解决方案"
              maxlength="1200"
              rows="15"
              show-word-limit
            ></el-input>
          </el-form-item>
        </template>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, unref, computed, nextTick } from "vue";
import { Product } from "@/api/interface/product";
type MODAL_ACTION = "confirm" | "";

const modalTitle = computed(() => {
  switch (unref(modalAction)) {
    case "confirm":
      return "确认解决";
    default:
      return "";
  }
});

const dialogVisible = ref(false);
const modalAction = ref("on");
const applyRef = ref();

const applyForm = ref<any>({
  productName: "",
  categoryName: "",
  applicationMessage: ""
});

const emit = defineEmits(["on-submit"]);

const handleOpen = (action: MODAL_ACTION, rowData?: Product.ProductAttrListItem) => {
  modalAction.value = action;
  dialogVisible.value = true;
  nextTick(() => {
    if (action === "confirm") {
      applyForm.value = {
        ...rowData
      };
    }
  });
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleSubmit = () => {
  applyRef.value
    ?.validate()
    .then(() => {
      // dialogVisible.value = false;
      // eslint-disable-next-line vue/custom-event-name-casing
      emit("on-submit", applyForm.value.applicationMessage);
    })
    .catch((err: string) => {
      console.log(err);
    });
};

defineExpose({
  handleOpen,
  handleClose
});
</script>

<style scoped lang="scss">
// .content {
//   margin-top: 20px;
// }
</style>
