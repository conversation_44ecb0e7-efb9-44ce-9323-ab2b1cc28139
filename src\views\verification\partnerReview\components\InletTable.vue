<template>
  <div>
    <ProTable
      ref="inletTable"
      title="协议管理列表"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
    >
      <template #auditStatus="{ row }">
        <CustomTag
          :type="activeName === PageType.bill ? 'bill' : 'partner'"
          :status="row.auditStatus"
          :label="useDictLabel(auditStatusDic, row.auditStatus)"
        ></CustomTag>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <template v-if="activeName === PageType.qualification">
          <span v-auth="'qualificationView'">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, operateType.detail)"></i>
            </el-tooltip>
          </span>
          <span
            v-auth="'qualificationReview'"
            v-if="!(scope.row.auditStatus === auditStatusType.pass || scope.row.auditStatus === auditStatusType.noPass)"
          >
            <el-tooltip content="审核" placement="top">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, operateType.review)"></i>
            </el-tooltip>
          </span>
        </template>
        <template v-if="activeName === PageType.introduce">
          <span v-auth="'introduceView'">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, operateType.detail)"></i>
            </el-tooltip>
          </span>
          <span
            v-auth="'introduceReview'"
            v-if="!(scope.row.auditStatus === auditStatusType.pass || scope.row.auditStatus === auditStatusType.noPass)"
          >
            <el-tooltip content="审核" placement="top">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, operateType.review)"></i>
            </el-tooltip>
          </span>
        </template>
        <template v-if="activeName === PageType.enterprise">
          <span v-auth="'agreementView'">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, operateType.detail)"></i>
            </el-tooltip>
          </span>
          <span
            v-auth="'agreementReview'"
            v-if="!(scope.row.auditStatus === auditStatusType.pass || scope.row.auditStatus === auditStatusType.noPass)"
          >
            <el-tooltip content="审核" placement="top">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, operateType.review)"></i>
            </el-tooltip>
          </span>
        </template>
        <template v-if="activeName === PageType.bill">
          <span v-auth="'billPaymentView'">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, operateType.detail)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'billPaymentReview1'" v-if="scope.row.auditStatus === auditStatusType.padding && !scope.row.auditorName">
            <el-tooltip content="一级审核" placement="top">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, operateType.review)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'billPaymentReview2'" v-if="scope.row.auditStatus === auditStatusType.padding && scope.row.auditorName">
            <el-tooltip content="二级审核" placement="top">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, operateType.review)"></i>
            </el-tooltip>
          </span>
        </template>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onActivated, computed } from "vue";
import { useRouter } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import { useDictLabel } from "@/hooks/useDict";

import CustomTag from "@/components/CustomTag/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { PageType, operateType, auditStatusType } from "../type";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();
//  资质审核-合作商申请表-列表查询(分页) /cnud-product/pmsProviderApply-pageList
// /cnud-product/pmsProviderApply-detail
//  引入管理-合作商引入管理表-列表查询(分页) /cnud-product/pmsProviderIntroduce-pageList
// /cnud-product/pmsProviderIntroduce-detail
//  合作方协议管理--合作商协议审核表-列表查询(分页) /cnud-product/pmsProviderAgreementAudit-pageList
// /cnud-product/pmsProviderAgreement-detail

interface TableProps {
  columns: ColumnProps[]; // 列配置项  ==> 必传
  statusDict: any[];
  activeName: PageType;
  api: Function;
  extraParams?: any;
}
const props = withDefaults(defineProps<TableProps>(), {
  columns: () => [],
  statusDict: () => [],
  activeName: PageType.qualification,
  api: () => {},
  extraParams: {}
});

const router = useRouter();

const initParam = reactive({});
const inletTable = ref<ProTableInstance>();

const auditStatusDic = computed(() => props.statusDict);
const columns = ref<ColumnProps[]>(props.columns);

onActivated(() => {
  inletTable.value?.getTableList();
});

const getTableList = (params: any = {}) => {
  if (props.activeName === PageType.qualification && !BUTTONS.value?.qualificationQuery) {
    return;
  }
  if (props.activeName === PageType.introduce && !BUTTONS.value?.introduceQuery) {
    return;
  }
  if (props.activeName === PageType.enterprise && !BUTTONS.value?.agreementQuery) {
    return;
  }
  if (props.activeName === PageType.bill && !BUTTONS.value?.billQuery) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.descs = "createTime";
  newParams = {
    ...newParams,
    ...props.extraParams
  };
  return props.api?.(newParams);
};

const toView = async (row: any, type = operateType.detail) => {
  if (props.activeName === PageType.qualification || props.activeName === PageType.introduce) {
    router.push({
      path: `/verification/partnerReview/qualificationReview`,
      query: {
        id: row.id,
        type,
        pageType: props.activeName
      }
    });
  } else if (props.activeName === PageType.bill) {
    router.push({
      path: `/verification/partnerReview/billReview`,
      query: {
        id: row.id,
        type,
        pageType: props.activeName
      }
    });
  } else {
    router.push({
      path: "/verification/partnerReview/agreementReview",
      query: {
        id: row.id,
        type,
        pageType: props.activeName
      }
    });
  }
};
</script>

<style lang="scss" scoped></style>
