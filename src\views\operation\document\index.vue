<template>
  <div class="banner-box table-box">
    <ProTable ref="proTable" title="用户列表" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => toEdit()"> 新增 </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <el-tooltip effect="dark" content="编辑" placement="top">
            <i class="iconfont2 icon-a-lineicon_edit opera-icon" @click="() => toEdit(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip effect="dark" content="删除" placement="top">
            <i class="iconfont2 icon-lajitong opera-icon" @click="del(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <Edit v-if="visible" v-model:visible="visible" @refresh="refresh"></Edit>
  </div>
</template>

<script setup lang="tsx" name="document">
import { ref, reactive } from "vue";
import { documentStorage } from "@/utils/storage/obj";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { deleteItem, getPageList, change, add } from "@/api/modules/business/document";
import { type ProTableInstance, type ColumnProps } from "@/components/ProTable/interface/index";
import { Plus } from "@element-plus/icons-vue";
import Edit from "./edit/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();
const visible = ref<boolean>(false);

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const { docName, current, size } = search;
  return getPageList({
    search: docName,
    current,
    size
  });
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: "docName",
    label: "文档名称",
    align: "left",
    search: { el: "input", props: { maxLength: 20 } }
  },
  {
    prop: "creatorName",
    align: "left",
    label: "创建人员"
  },
  {
    prop: "createTime",
    align: "left",
    label: "创建时间"
  },
  { prop: "operation", align: "left", label: "操作", width: 95 }
];

// 删除用户信息
const del = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此项文档配置？", "确认操作").then(async () => {
    await deleteItem(data.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};
const refresh = () => {
  proTable.value?.getTableList();
};

const toEdit = (data?: any) => {
  documentStorage.clear();
  if (data) {
    documentStorage.set(data);
  }
  visible.value = true;
};
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
</style>
