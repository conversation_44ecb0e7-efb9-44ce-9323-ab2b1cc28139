<template>
  <el-dialog v-model="dialogVisible" :title="`${parameter.title}`" :destroy-on-close="true" width="80%">
    <div class="pay-status">
      支付状态：
      <CustomTag
        :type="statusTypeMap[parameter.payStatus]"
        :status="parameter.payStatus"
        :label="useDictLabel(dsk_pay_status, parameter.payStatus)"
      ></CustomTag>
    </div>

    <div class="table-box">
      <pro-table
        ref="proTable"
        :columns="columns"
        :data="tableData"
        :request-api="getTableList"
        :request-auto="false"
        :tool-button="false"
        :pagination="false"
        :summary-method="getSummaries"
        show-summary
      >
      </pro-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose"> 取消 </el-button>
        <!-- <el-button @click="onSure" type="primary"> 确认 </el-button> -->
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="relatedModal">
import { onMounted, ref } from "vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { relativeOrder } from "@/api/modules/billManagement";
import { useDict, useDictLabel } from "@/hooks/useDict";
import CustomTag from "@/components/CustomTag/index.vue";
import { statusTypeMap } from "./type";

const { dsk_pay_status } = useDict("dsk_pay_status");

export interface ExcelParameterProps {
  title: string; // 标题
  id: string; // 明细账单Id
  payStatus: string; // 支付状态
  getTableList?: () => void; // 获取表格数据的Api
}

const proTable = ref<ProTableInstance>();
const tableData = ref([]);

// dialog状态
const dialogVisible = ref(false);
// 父组件传过来的参数
const parameter = ref<any>({
  title: "关联订单"
});

const getTableList = async (params: any = {}) => {
  let newParams = { ...params, jobBillId: parameter.value.id };
  // return relativeOrder(newParams);
  const res: any = await relativeOrder(newParams);
  tableData.value = res?.data || [];
  return res;
};

// 接收父组件参数
const acceptParams = (params: ExcelParameterProps) => {
  parameter.value = { ...parameter.value, ...params };
  dialogVisible.value = true;
  getTableList();
};

const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = "总计";
      return;
    }
    if (["changeQuantity", "costPrice"].includes(column.property)) {
      const values = tableData.value?.map(item => Number(item[column.property]));
      sums[index] = values.reduce((prev, curr) => prev + curr, 0).toFixed(2);
      return;
    }
    sums[index] = "";
    return;
  });

  return sums;
};

/* 关闭 */
const handleClose = () => {
  dialogVisible.value = false;
};

// const onSure = () => {
//   handleClose();
// };

// 表格配置项
const columns: ColumnProps[] = [
  { prop: "jobBillId", label: "明细账单ID", minWidth: 120 },
  { prop: "happenTime", label: "核时交易时间", minWidth: 120 },
  { prop: "subOrderNo", label: "订单编码", search: { el: "input" }, minWidth: 120 },
  { prop: "productName", label: "产品名称", minWidth: 120 },
  { prop: "skuName", label: "规格" },
  { prop: "orderFee", label: "订单金额（元）", minWidth: 150 },
  { prop: "orderTotalQuantity", label: "订单核时", minWidth: 120 },
  { prop: "skuPrice", label: "核时单价（元/核时）", minWidth: 180 },
  { prop: "beforeOrderBalance", label: "结算前订单余额", minWidth: 150 },
  { prop: "changeQuantity", label: "订单关联核时", minWidth: 160 },
  { prop: "settleTime", label: "支付时间", minWidth: 120 },
  { prop: "costPrice", label: "本次结算金额（元）", minWidth: 160 },
  { prop: "orderRemainQuantity", label: "结算后订单余额", minWidth: 150 }
];
defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.pay-status {
  margin-bottom: 16px;
}
</style>
