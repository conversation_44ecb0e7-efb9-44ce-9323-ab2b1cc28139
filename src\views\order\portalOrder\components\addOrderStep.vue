<template>
  <div class="order-step">
    <div class="step-comps">
      <div class="step-comps--item" v-for="item in stepLength" :key="item.value" :class="step > item.value ? 'active' : ''">
        <div class="step-comps--item--box">
          <div class="step-comps--item--index">{{ item.value + 1 }}</div>
          <div class="step-comps--item--text">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

defineProps({
  step: {
    type: Number,
    default: 0
  }
});

const stepLength = ref<{ value: number; label: string }[]>([
  {
    value: 0,
    label: "选择客户"
  },
  {
    value: 1,
    label: "选择产品"
  },
  {
    value: 2,
    label: "订单录入"
  },
  {
    value: 3,
    label: "订单预览"
  }
]);
</script>

<style scoped lang="scss">
.step-comps {
  display: flex;
  justify-content: center;
  width: 100%;
  &--item {
    &--box {
      cursor: pointer;
    }

    display: flex;
    flex: 1;
    &--index {
      width: 28px;
      height: 28px;
      margin: 0 auto;
      overflow: hidden;
      line-height: 28px;
      color: #ffffff;
      text-align: center;
      background: #f4f5f7;
      background-color: var(--el-color-primary-light-7);
      border-radius: 50%;
    }
    &--text {
      margin-top: 10px;
      font-size: 14px;
    }
    &:last-child {
      flex: none;
    }
    &::after {
      display: block;
      flex: 1;
      height: 2px;
      margin-top: 15px;
      content: "";
      background-color: var(--el-color-primary-light-3);
    }
    &.active {
      .step-comps--item--index {
        background-color: var(--el-color-primary-dark-2);
      }
      .step-comps--item--text {
        color: var(--el-color-primary);
      }
    }
  }
}
</style>
