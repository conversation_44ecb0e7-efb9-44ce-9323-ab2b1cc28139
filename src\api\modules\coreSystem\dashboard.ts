import http from "@/api";
import type { CoreSystem } from "@/api/interface/coreSystem";

/*  获取集群分区列表 */
export const getClusterList = async () => {
  return await http.get("/cnud-desk/admin/dashboard/cluster/clusterList");
};

/*  获取集训详情信息 */
export const getClusterInfo = async (id: string) => {
  return await http.get<CoreSystem.dashboardClusterItem>("/cnud-desk/admin/dashboard/cluster/clusterInfo", { clusterId: id });
};
