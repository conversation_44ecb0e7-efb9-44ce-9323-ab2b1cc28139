<template>
  <div class="table-box">
    <pro-table :request-auto="false" ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <template #tableHeader>
        <el-button v-auth="'unitPriceConfigAdd'" plain :icon="Plus" @click="handleCreate"> 新增 </el-button>
      </template>
      <template #operation="{ row }">
        <div class="operation-btn">
          <auth-button :row-data="row" :auth-list="authList"></auth-button>
        </div>
      </template>
    </pro-table>
    <!-- 增加／修改 -->
    <el-dialog
      v-model="dialogVisible"
      :title="rowData?.id ? '修改实例单价' : '创建实例单价'"
      width="600"
      @close="handleClose"
      destroy-on-close
      append-to-body
    >
      <el-form ref="formRef" :model="form" label-width="auto">
        <!-- 集群名称 -->
        <el-form-item label="集群名称:" prop="platformCode" :rules="[{ required: true, message: '请选择集群名称' }]">
          <el-select
            :disabled="isDisabled"
            v-model="form.platformCode"
            clearable
            placeholder="请选择集群名称"
            style="width: 100%"
            @change="handleChange"
          >
            <el-option
              v-for="item in clusterList"
              :key="item.platformCode"
              :label="item.displayName"
              :value="item.platformCode"
            />
          </el-select>
        </el-form-item>
        <!-- 实例名称 -->
        <el-form-item
          label=" 实例名称:"
          prop="instanceName"
          :rules="[
            { required: true, message: '请输入实例名称' },
            { max: 20, required: true, message: '名称长度为20' }
          ]"
        >
          <el-input v-model.trim="form.instanceName" placeholder="请输入实例名称" :disabled="isDisabled" />
        </el-form-item>
        <!-- 硬件资源 -->
        <el-form-item label=" 硬件资源:" prop="hardwareCode" :rules="[{ required: true, message: '请输入硬件资源' }]">
          <!-- <select-dict
            :disabled="rowData?.id"
            v-model="form.hardwareCode"
            dict-key="hardware_code"
            label="硬件资源"
          ></select-dict> -->
          <el-select :disabled="isDisabled" v-model="form.hardwareCode" clearable placeholder="硬件资源" style="width: 100%">
            <el-option v-for="item in hardwareCode" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- cpu -->
        <el-form-item label="cpu:" prop="cpu" :rules="[{ required: true, message: '请输入cpu' }]">
          <el-input :disabled="isDisabled" v-model.trim="form.cpu" v-d-input-max="100000" v-d-input-int placeholder="请输入cpu">
            <template #append>核</template>
          </el-input>
        </el-form-item>
        <!-- gpu -->
        <el-form-item label="gpu:" prop="gpu" :rules="[{ required: true, message: '请输入gpu' }]">
          <el-input :disabled="isDisabled" v-model.trim="form.gpu" v-d-input-max="100000" v-d-input-int placeholder="请输入gpu">
            <template #append>卡</template>
          </el-input>
        </el-form-item>
        <!-- 显存 -->
        <el-form-item label="显存:" prop="graphics" :rules="[{ required: true, message: '请输入显存(GB)' }]">
          <el-input :disabled="isDisabled" v-model="form.graphics" v-d-input-max="100000" v-d-input-int placeholder="请输入显存">
            <template #append>GB</template>
          </el-input>
        </el-form-item>
        <!-- 内存  -->
        <el-form-item label="内存:" prop="memory" :rules="[{ required: true, message: '请输入内存(GB)' }]">
          <el-input v-model="form.memory" v-d-input-max="100000" v-d-input-int placeholder="请输入内存">
            <template #append>GB</template>
          </el-input>
        </el-form-item>
        <!-- 存储 -->
        <el-form-item label="存储:">
          <el-input :disabled="isDisabled" v-model="form.storage" v-d-input-max="100000" v-d-input-int placeholder="请输入存储" />
        </el-form-item>
      </el-form>
      <!-- 标准价格（核时/小时） -->
      <el-descriptions v-show="rowData?.id">
        <el-descriptions-item label="标准价格（核时/小时）:">{{ rowData.price }}</el-descriptions-item>
      </el-descriptions>

      <template #footer>
        <el-button type="primary" @click="submitForm(formRef)"> 确认 </el-button>
        <el-button @click="resetForm(formRef)">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { Plus } from "@element-plus/icons-vue";
import type { CoreSystem } from "@/api/interface/coreSystem";
import {
  queryInstancePriceList,
  queryInstancePriceDetail,
  queryInstanceSave,
  queryInstanceUpdate,
  queryInstanceDelete,
  queryInstanceChangeStatus,
  queryHardwareCode,
  queryGetCluster
} from "@/api/modules/coreSystem/unitPriceConfig";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { onMounted, reactive, ref } from "vue";
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from "element-plus";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
import type { IAuthButtonItem } from "@/components/AuthButton/typings";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { getCluster } from "@/api/modules/billManagement";
import { Bill } from "@/api/interface/billManagement";
import { computed } from "vue";

const { BUTTONS } = useAuthButtons();

const { instance_status_type } = useDict("instance_status_type");

const formRef = ref<FormInstance>();

const proTable = ref<ProTableInstance>();

const dialogVisible = ref(false);

const isDisabled = computed(() => Reflect.has(rowData, "id") && Reflect.get(rowData, "id") !== null);

/* 硬件资源 */
const hardwareCode = ref<Array<{ label: string; value: string }>>([]);

const findCluster = (rowData: CoreSystem.InstancePriceItemDetail) => {
  const itemData = clusterList.value.find(el => el.platformCode === rowData.platformCode);

  if (itemData) {
    return itemData.displayName;
  }

  return rowData.platformCode;
};

const handleChange = (value: string) => {
  hardwareCode.value = [];
  queryHardwareCode({ platformCode: value }).then(res => {
    hardwareCode.value = res.data.map(el => {
      return { value: el, label: el };
    });
  });
  const clusterItem = clusterList.value.find(item => item.clusterId === value);

  if (clusterItem) {
    form.platformCode = clusterItem.platformCode;
  }
};

const columns: ColumnProps<CoreSystem.InstancePriceItemDetail>[] = [
  { prop: "instanceName", label: "实例名称", search: { el: "input", props: { maxlength: 20 } } },
  {
    prop: "platformCode",
    label: "集群名称	",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable placeholder="请选择集群名称">
            {clusterList.value.map(item => (
              <el-option key={item.platformCode} label={item.displayName} value={item.platformCode} />
            ))}
          </el-select>
        );
      }
    },
    render: ({ row }) => <>{findCluster(row)}</>
  },

  {
    prop: "hardwareCode",
    label: "硬件资源",
    search: {
      render: (scope: any) => {
        return <select-dict model-value={scope.modelValue} dictKey="hardware_code" label="硬件资源"></select-dict>;
      }
    }
  },
  { prop: "cpu", label: "CPU", render: ({ row }) => <>{row.cpu}核</> },
  {
    prop: "gpu",
    label: "GPU",
    render: ({ row }) => <>{row.gpu}卡</>
  },
  {
    prop: "graphics",
    label: "显存",
    render: ({ row }) => <>{row.graphics}GB</>
  },
  {
    prop: "memory",
    label: "内存",
    render: ({ row }) => <>{row.memory}GB</>
  },
  {
    prop: "status",
    label: "状态",
    render: scope => {
      return (
        <CustomTag
          type={scope.row.status === "1" ? "success" : ""}
          status={String(scope.row.status)}
          label={useDictLabel(instance_status_type.value, scope.row.status)}
        ></CustomTag>
      );
    }
  },
  {
    prop: "price",
    label: "标准价格（核时/小时）"
  },
  {
    prop: "updateTime",
    label: "更新时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

const authList: IAuthButtonItem<CoreSystem.InstancePriceItemDetail>[] = [
  {
    contentName: "编辑",
    iconClass: "Edit",
    itemClick: scope => handleOpen(scope),
    authName: "unitPriceConfigEdit"
  },
  {
    contentName: "删除",
    iconClass: "Delete",
    itemClick: scope => handleDelete(scope),
    authName: "unitPriceConfigDelete"
  },
  {
    contentName: "停用",
    iconClass: "Stop",
    itemClick: scope => handleStart(scope),
    show: scope => scope.status === "1",
    authName: "unitPriceConfigStart"
  },
  {
    contentName: "启用",
    iconClass: "Start",
    itemClick: scope => handleStart(scope),
    show: scope => scope.status === "0",
    authName: "unitPriceConfigStart"
  }
];

/* 多集群 */
const clusterList = ref<Array<Bill.IClusterPlatform>>([]);

/* 平台 */

const formData = {
  clusterId: null,
  cpu: null,
  gpu: null,
  graphics: null,
  hardwareCode: "",
  instanceName: "",
  memory: null,
  storage: "",
  price: 0,
  status: "",
  platformCode: ""
};

const rowData = reactive<CoreSystem.InstancePriceItemDetail>({
  ...formData,
  id: null
});

const form = reactive<CoreSystem.InstancePriceItem>({
  ...formData
});

const getTableList = async (params?: CoreSystem.InstancePriceSearch) => {
  return Reflect.has(BUTTONS.value, "unitPriceConfigList") ? await queryInstancePriceList({ ...params }) : null;
};

/* 关闭 */
const handleClose = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
  Object.assign(form, formData);
  Object.assign(rowData, formData, { id: null });

  Reflect.deleteProperty(form, "id");
  Reflect.deleteProperty(rowData, "id");
};

/* 提交 */
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl
    .validate()
    .then(res => {
      if (!Reflect.has(form, "id")) {
        queryInstanceSave(form).then(() => {
          ElMessage({
            message: "新增成功",
            type: "success"
          });
          handleClose();
          proTable.value?.getTableList();
        });
      } else {
        queryInstanceUpdate(form).then(() => {
          ElMessage({
            message: "修改成功",
            type: "success"
          });
          handleClose();
          proTable.value?.getTableList();
        });
      }
    })
    .catch(error => {});
};

/* 删除 */
const handleDelete = (row: CoreSystem.InstancePriceItemDetail) => {
  const ids = [row.id];
  if (ids.length === 0) return;
  ElMessageBox.confirm(`是否确认删除实例名称：${row.instanceName}`, "操作提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(result => {
      queryInstanceDelete({ ids: ids }).then(() => {
        ElMessage({
          message: "删除成功",
          type: "success"
        });
        proTable.value?.getTableList();
      });
    })
    .catch(err => {});
};

/* 重置 */
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  dialogVisible.value = false;
};

/* 新增修改 */
const handleCreate = () => {
  rowData.id = null;
  dialogVisible.value = true;
};

/* 打开弹窗 */
const handleOpen = (item: CoreSystem.InstancePriceItemDetail) => {
  queryInstancePriceDetail(item.id as string).then(res => {
    Object.assign(form, res.data);
    Object.assign(rowData, res.data);
    dialogVisible.value = true;
  });
};

/* 停启用 */
const handleStart = (item: CoreSystem.InstancePriceItemDetail) => {
  const status = item.status === "0" ? "1" : "0";
  const text = item.status === "0" ? "启用" : "停用";

  ElMessageBox.confirm(`是否确认${text}实例名称：${item.instanceName}`, "操作提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(result => {
      queryInstanceChangeStatus({ id: item.id, status }).then(() => {
        ElMessage({
          message: "停用成功",
          type: "success"
        });
        proTable.value?.getTableList();
      });
    })
    .catch(err => {});
};

onMounted(() => {
  proTable.value?.getTableList();

  queryGetCluster()
    .then(result => {
      clusterList.value = result.data;
    })
    .catch(err => {});
});
</script>

<style scoped lang="scss">
.operation-btn {
  display: flex;
  justify-content: center;
  .btn {
    color: var(--el-color-primary);
    cursor: pointer;
    &:nth-child(2n) {
      margin: 0 20px;
    }
  }
}
</style>
