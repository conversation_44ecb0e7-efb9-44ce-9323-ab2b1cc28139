<template>
  <div class="table-box">
    <!--request-auto="false" 不自动调接口-->
    <ProTable
      ref="proTable"
      title="产品列表"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
    >
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader="scope">
        <el-button v-auth="'add'" plain :icon="Plus" @click="toView({}, 'add')"> 新增 </el-button>
        <el-button
          v-auth="'delete'"
          plain
          :icon="Delete"
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          删除
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, 'details')"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'" v-show="!(scope.row.initStatus == 1)">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toView(scope.row, 'edit')"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'" v-show="!(scope.row.initStatus == 1)">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="deleteRoles(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <OperateRoleDialog
      ref="roleRef"
      :dialog-type="dialogInfo.dialogType"
      :dialog-data="dialogInfo.dialogData"
      @cancel="onCancel"
      @confirm="onComfirm"
    ></OperateRoleDialog>
  </div>
</template>

<script setup lang="tsx" name="roleManage">
import { ref, reactive, onMounted } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Plus, Delete } from "@element-plus/icons-vue";
import { getRoleList, getRoleInfo, deleteRole } from "@/api/modules/system";
import { Product } from "@/api/interface/product";
import OperateRoleDialog from "./components/OperateRoleDialog.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

const roleRef = ref();
const defaultDialogInfo = { dialogType: "add", dialogData: {} };
const dialogInfo = reactive({ ...defaultDialogInfo });

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

onMounted(() => {
  // 然后
  proTable.value?.getTableList();
});

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  return getRoleList(newParams);
};

// 页面按钮权限（按钮权限既可以使用 hooks，也可以直接使用 v-auth 指令，指令适合直接绑定在按钮上，hooks 适合根据按钮权限显示不同的内容）
// const { BUTTONS } = useAuthButtons();

// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "roleName", label: "角色名称", search: { el: "input" } },
  { prop: "perms", label: "权限", search: { el: "input" } },
  { prop: "updaterName", label: "更新人" },
  {
    prop: "updateTime",
    label: "更新时间",
    width: 170
  },
  { prop: "description", label: "描述", width: 180 },
  { prop: "operation", label: "操作", fixed: "right", width: 152 }
];

// 批量删除用户信息
const batchDelete = async (ids: string[]) => {
  await useHandleData(deleteRole, { roleId: ids }, { msg: `删除后不能恢复，是否确认删除所选记录？`, successMsg: "删除" });
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
// 删除
const deleteRoles = async (row: any) => {
  if (!row.id) {
    return;
  }
  await useHandleData(deleteRole, { roleId: [row.id] }, `删除<${row.roleName}>`);
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const getInfo = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  return getRoleInfo(newParams);
};
const toView = async (row: any, type = "add") => {
  dialogInfo.dialogType = type;
  roleRef.value?.openDialog();

  if (row.id) {
    // 查看角色信息
    await getInfo({ id: row.id }).then(res => {
      dialogInfo.dialogData = res.data as Object;
    });
  }
};

const onCancel = () => {
  dialogInfo.dialogType = "add";
  dialogInfo.dialogData = {};
};

const onComfirm = () => {
  onCancel();
  proTable.value?.getTableList();
};
</script>
