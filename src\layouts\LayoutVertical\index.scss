.el-container {
  width: 100%;
  height: 100%;
  :deep(.el-aside) {
    width: auto;
    background-color: var(--el-menu-bg-color);

    // border-right: 1px solid var(--el-aside-border-color);
    .aside-box {
      display: flex;
      flex-direction: column;
      height: 100%;
      transition: width 0.3s ease;
      .el-scrollbar {
        // height: calc(100% - 55px);
        height: calc(100% - 60px);
        .el-menu {
          width: 100%;
          overflow-x: hidden;
          background-color: #203c69;
          border-right: none;
          .el-menu-item {
            color: #c4c6cc;
          }
          .el-menu-item:hover {
            color: #ffffff;
            background: #385a90;
          }
          .el-menu-item.is-active {
            color: #ffffff !important;
            background: linear-gradient(270deg, #1c65f5 0%, #0052d9 100%);
          }
          .el-sub-menu__title {
            color: #c4c6cc;
          }
          .el-sub-menu__title:hover {
            color: #ffffff !important;
            background: #385a90 !important;
          }
        }
        .el-menu--inline {
          background: #0e2549;
        }
      }
      .my-menu {
        background-color: #203c69;
        &::before {
          position: absolute;
          inset: 0;
          z-index: 0;
          display: block;
          content: "";
          background-image: url("@/assets/images/nav-bg.png");
          background-repeat: no-repeat;
          background-position: center 95%;
          background-size: contain;
        }
      }
      .logo {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        height: 60px;
        padding: 0 8px;

        // background: var(--el-color-primary);
        background: #203c69;
        border-bottom: 1px solid rgb(255 255 255 / 10%);
        .logo-img {
          width: 28px;
          object-fit: contain;
          margin-right: 6px;
        }
        .logo-text {
          height: 22px;
          font-family: PingFangSC-Semibold, "PingFang SC";
          font-size: 14px;
          font-weight: 600;
          line-height: 22px;

          // color: #3a3a3d;
          color: #ffffff;
        }
      }
    }
  }
  .el-header {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    padding: 0 15px;

    // background-color: var(--el-header-bg-color);
    background-color: #203c69;

    // border-bottom: 1px solid var(--el-header-border-color);
  }
}
