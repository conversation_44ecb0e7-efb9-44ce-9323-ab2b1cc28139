import http from "@/api";

export const getPageList = (data: any) => {
  return http.get("/cnud-operation/admin/document-pageList", data);
};

export const deleteItem = (id: string) => {
  return http.post("/cnud-operation/admin/document-delete", { ids: [id] });
};

export const change = (data: any) => {
  return http.post("/cnud-operation/admin/document-update", data);
};

export const add = (data: any) => {
  return http.post("/cnud-operation/admin/document-save", data);
};
