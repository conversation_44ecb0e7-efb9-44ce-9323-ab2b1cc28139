<template>
  <div class="page-order-detail">
    <orderStatus :order-status="orderInfo.status" :status-remark="orderInfo.statusRemark" v-if="orderInfo.status"> </orderStatus>

    <div class="info-box card">
      <div class="box-title">
        <div class="title">客户信息</div>
      </div>
      <div class="infos">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">客户账号：</span>
              <span class="info-text">{{ orderInfo?.customerAccount }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">客户名称：</span>
              <span class="info-text">{{ orderInfo?.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">地市：</span>
              <span class="info-text">{{ orderInfo?.city }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">手机号码：</span>
              <span class="info-text">{{ orderInfo?.purchasePhone }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">用户邮箱：</span>
              <span class="info-text">{{ orderInfo?.customerEmail }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">认证状态：</span>
              <span class="info-text">{{ orderInfo?.authStatus }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-box card">
      <div class="box-title">
        <div class="title">订单信息</div>
      </div>
      <div class="infos">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">下单触点：</span>
              <span class="info-text">{{ orderInfo?.channelName }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">订单编号：</span>
              <span class="info-text">{{ orderInfo?.parentOrderNo }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">订单类型：</span>
              <span class="info-text">{{ useDictLabel(orderTypeDic, orderInfo?.orderType) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">订购时间：</span>
              <span class="info-text">{{ orderInfo?.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">订购完成时间：</span>
              <span class="info-text">{{ orderInfo?.finishTime }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">订购失败时间：</span>
              <span class="info-text">{{ orderInfo?.closeTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">支付方式：</span>
              <span class="info-text">{{ useDictLabel(orderPayWayDic, orderInfo?.payType) }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">支付账号：</span>
              <span class="info-text">{{ orderInfo?.purchasePhone }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">订单支付时间：</span>
              <span class="info-text">{{ orderInfo?.payTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">订单创建人：</span>
              <span class="info-text">{{ orderInfo?.creatorName }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="info-box card">
      <div class="box-title">
        <div class="title">产品信息</div>
        <div class="sub-tips" v-show="tableData?.length > 1 && auditTagType === 1">
          该订单为组合交易订单，订单内产品需一同审核
        </div>
      </div>
      <!-- <pro-table
        ref="proTable"
        title="产品信息列表"
        :columns="columns"
        :data="tableData"
        :tool-button="false"
        :request-auto="false"
        :pagination="false"
        :has-padding="false"
      >
        <template #orderType="{ row }">
          {{ useDictLabel(orderTypeDic, row?.orderType) }}
        </template>
        <template #count="{ row }">
          <div v-if="row?.count">
            {{ row?.count }}
            {{ useDictLabel(quantityDic, row?.countUnit) }}
          </div>
          <div v-else>/</div>
        </template>
        <template #buyDuration="{ row }">
          <div v-if="row?.buyDuration">
            {{ row?.buyDuration }}
            {{ useDictLabel(timeDic, row?.buyDurationUnit) }}
          </div>
          <div v-else>/</div>
        </template>
        <template #effective="{ row }"> {{ row.effectTime }} - {{ row.endTime }} </template>
        <template #payFee="{ row }">
          <div class="pay-fee" style="font-size: 16px; color: #0052d9">{{ row.payFee }}</div>
        </template>
      </pro-table> -->
      <orderTable :data="tableData"></orderTable>
      <div class="total-price"><span>订单总金额：</span>¥{{ toPrice(orderInfo.payFee) }}</div>
    </div>
    <div class="info-box card">
      <div class="box-title">
        <div class="title">其它信息</div>
      </div>
      <el-col :span="24">
        <div class="info-item">
          <span class="info-label default">备注：</span>
          <span class="info-text">{{ orderInfo?.otherRemark }}</span>
        </div>
      </el-col>
    </div>
    <div class="info-box card" v-if="orderInfo.auditStatus === '3' || orderInfo.auditStatus === '4'">
      <div class="box-title">
        <div class="title">审核信息</div>
      </div>
      <div class="infos">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">审核状态：</span>
              <span class="info-text">{{ useDictLabel(auditStatusDic, orderInfo?.auditStatus) }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">审核人：</span>
              <span class="info-text">{{ orderInfo?.auditor }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">审核时间：</span>
              <span class="info-text">{{ orderInfo?.auditTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">审核意见：</span>
              <span class="info-text">{{ orderInfo?.auditRemark }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <template v-if="auditTagType === 1 && orderInfo.auditStatus === '2'">
      <examine :order-id="orderInfo.parentOrderNo" @on-change="handleChange"></examine>
    </template>
    <div class="order-info" v-if="auditTagType !== 1 || orderInfo.auditStatus !== '2'">
      <el-button plain @click="handleBack"> 返回订单列表 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, ref, toRefs } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import orderStatus from "./components/orderStatus.vue";
import Examine from "./components/examine.vue";
import { parentOrderDetail, subOrderDetail } from "@/api/modules/order";
import { useRoute, useRouter } from "vue-router";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { useDicStore } from "@/stores/modules/dictionaty";
import { storeToRefs } from "pinia";
import orderTable from "../portalOrder/components/orderTable.vue";
import { toPrice } from "@/utils/index";
const dictionaryStore = useDicStore();
/* 获取字典 */
const { orderTypeDic, orderPayWayDic, auditStatusDic } = storeToRefs(dictionaryStore);

// const { ord_pay_type, ord_order_type, ord_audit_status } = useDict("ord_pay_type", "ord_order_type", "ord_audit_status");

const route = useRoute();
const router = useRouter();
const orderInfo = ref<Record<string, any>>({});
const auditTagType = ref<number>(0);
const columns: ColumnProps<any>[] = [
  { prop: "subOrderNo", label: "子订单编号" },
  { prop: "providerSkuId", label: "外系统订单编号" },
  { prop: "productName", label: "产品名称" },
  { prop: "skuName", label: "产品规格" },
  { prop: "orderType", label: "订单类型" },
  { prop: "count", label: "购买数量" },
  { prop: "buyDuration", label: "购买时长" },
  { prop: "skuPrice", label: "单价（元）" },
  { prop: "effective", label: "有效期" },
  { prop: "productProviderName", label: "产品服务商" },
  { prop: "payFee", label: "订单金额（元）", width: 180 }
];
const handleChange = () => {
  parentOrderDetail({
    parentOrderNo: orderInfo.value.parentOrderNo,
    auditTag: 1
  }).then(res => {
    orderInfo.value = res.data;
  });
};

const tableData = computed(() => {
  if (Reflect.has(route.query, "orderId") && Reflect.has(route.query, "auditTag")) {
    return orderInfo.value.subOrderDetailList;
  } else {
    return [
      {
        subOrderNo: orderInfo.value.subOrderNo,
        providerSkuId: orderInfo.value.providerSkuId,
        orderType: orderInfo.value.orderType,
        productName: orderInfo.value.productName,
        skuName: orderInfo.value.skuName,
        skuConfig: orderInfo.value.skuConfig,
        productProviderName: orderInfo.value.productProviderName,
        skuPrice: orderInfo.value.skuPrice,
        count: orderInfo.value.count,
        buyDuration: orderInfo.value.buyDuration,
        payFee: orderInfo.value.payFee,
        skuPriceUnit: orderInfo.value.skuPriceUnit,
        countUnit: orderInfo.value.countUnit,
        buyDurationUnit: orderInfo.value.buyDurationUnit,
        effectTime: orderInfo.value.effectTime,
        endTime: orderInfo.value.endTime
      }
    ];
  }
});
const handleBack = () => {
  router.back();
};
onMounted(() => {
  const { orderId, auditTag, subOrderId } = route.query;
  auditTagType.value = Number(auditTag);
  if (Reflect.has(route.query, "orderId") && Reflect.has(route.query, "auditTag")) {
    parentOrderDetail({
      parentOrderNo: orderId,
      auditTag: 1
    }).then(res => {
      orderInfo.value = res.data;
    });
  }
  if (Reflect.has(route.query, "subOrderId")) {
    subOrderDetail({
      subOrderId: subOrderId
    }).then(res => {
      orderInfo.value = res.data;
    });
  }
});
</script>

<style scoped lang="scss">
.order-info {
  margin-top: 30px;
  margin-bottom: 10px;
  text-align: left;
  :deep(.el-button) {
    height: 36px;
    margin-right: 16px;
  }
}
.page-title {
  padding: 26px 23px;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}
.table {
  padding: 25px;
}
.info-box {
  margin-bottom: 15px;
  .box-title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-weight: 600;
    color: #3a3a3d;
    border-bottom: 1px solid #e4e7ed;
    .title {
      font-size: 18px;
      .card-btn {
        float: right;
        margin-top: -5px;
        margin-right: 10px;
      }
    }
    .sub-tips {
      color: red;
    }
  }
  .infos {
    margin-left: 16px;
  }
  .info-item {
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      color: #73787f;
      text-align: right;
      &.default {
        margin-left: 16px;
      }
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
}
.total-price {
  margin-top: 36px;
  font-size: 24px;
  font-weight: 600;
  color: #0052d9;
  text-align: right;
  span {
    font-size: 14px;
    font-weight: 400;
    color: #3a3a3d;
  }
}
</style>
