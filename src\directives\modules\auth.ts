/*
 * @Author: <EMAIL>
 * @Date: 2023-09-08 09:49:57
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-11-23 16:36:41
 * @Description: file content
 */
/**
 * v-auth
 * 按钮权限指令
 */
import { useAuthStore } from "@/stores/modules/auth";
import type { Directive, DirectiveBinding } from "vue";

const auth: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value, arg } = binding;

    const authStore = useAuthStore();
    const routeName = arg || authStore.routeName;
    console.log(routeName);

    const currentPageRoles = authStore.authButtonListGet[routeName] ?? [];

    if (value instanceof Array && value.length) {
      const hasPermission = value.every(item => currentPageRoles.includes(item));
      if (!hasPermission) el.remove();
    } else {
      if (!currentPageRoles.includes(value)) el.remove();
    }
  }
};

export default auth;
