<template>
  <div class="info-box card">
    <div class="box-title">
      <div class="title">折扣信息</div>
      <div class="card-btn">
        <slot name="button"> </slot>
      </div>
    </div>
    <div class="discount-info">
      <el-table
        :data="tableData"
        style="width: 100%"
        :border="true"
        :header-cell-style="{ fontWeight: 'bold', color: '#303133', background: '#f5f7fa' }"
      >
        <el-table-column prop="attrValueName" label="产品名称" />
        <el-table-column prop="attrValue" label="协议价（元）">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="show === 'edit'"
              :prop="'_tableData.' + $index + '.attrValue'"
              :rules="[{ required: true, validator: validateValue, trigger: 'blur' }]"
            >
              <el-input maxlength="11" v-model="row.attrValue" size="large" placeholder="输入协议价" />
            </el-form-item>
            <span v-else>{{ row.attrValue }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="count">
        <div class="count-item">
          <span>订单总金额：</span>
          ¥{{ toPrice(totalPrice) }}
        </div>
        <div class="count-item" v-show="show === 'edit'">
          <span>应付金额：</span>
          ¥{{ toPrice(totalCount) }}
        </div>
        <div class="count-item" v-show="show === 'view'">
          <span>应付金额：</span>
          ¥{{ toPrice(payFee) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { toPrice } from "@/utils/index";
const props = defineProps({
  tableData: {
    type: Array,
    default: () => {
      return [];
    }
  },
  show: {
    type: String,
    default: "edit"
  },
  totalPrice: {
    type: [String, Number],
    default: "0"
  },
  payFee: {
    type: [String, Number],
    default: "0"
  }
});
const validateValue = (rule: any, value: any, callback: any) => {
  const regex = /^(?!0+(\.00)?$)[0-9]+(\.[0-9]{2})?$/;
  if (regex.test(value)) {
    callback();
  } else {
    callback(new Error(`必须是数字,大于0且只能保留两位小数`));
  }
};
const totalCount = computed(() => {
  const data = props.tableData[0] || {};
  if (Reflect.has(data, "attrValue")) {
    return props.tableData[0].attrValue;
  } else {
    return 0;
  }
});
</script>

<style scoped lang="scss">
@import "../order.module.scss";
.count {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  font-size: 20px;
  font-weight: bold;
  color: #de0c0c;
  text-align: right;
  .count-item {
    margin-left: 50px;
    span {
      font-size: 14px;
      font-weight: 400;
      color: #3a3a3d;
    }
  }
}
</style>
