<!--
 * @Author: <EMAIL>
 * @Date: 2023-09-08 10:11:24
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-19 11:22:54
 * @descriptionription: file content
-->
<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="editTypeChinese[props.type] + '分类'" top="0" width="30%">
      <el-form ref="ruleFormRef" :model="labelForm" :rules="rules" label-width="120px" status-icon label-position="top">
        <el-form-item label="父级分类" class="horizontal">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="(item, index) in params.parentList" :key="index">{{ item }}</el-breadcrumb-item>
          </el-breadcrumb>
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input class="label-name" v-model="labelForm.name" maxlength="10" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input class="label-name" v-model="labelForm.description" type="textarea" maxlength="100" show-word-limit></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import { computed, ref, reactive, watch } from "vue";
import type { FormInstance } from "element-plus";
import { addType as addTypeApi, updateType as updateTypeApi } from "@/api/modules/productClassify";
import { ElMessage } from "element-plus";
// import { Label } from "@/api/interface/label";
let props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: 1
  },
  params: {
    type: Object,
    default: () => {}
  }
});
const ruleFormRef = ref();
const labelForm = ref<any>({
  name: "",
  description: "",
  parentId: ""
});
const emits = defineEmits(["update:visible", "cancel", "confirm"]);
watch(
  () => props.params,
  () => {
    labelForm.value.name = props.params.name || "";
    labelForm.value.description = props.params.description || "";
    labelForm.value.parentId = props.params.parentId;
  }
);
const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
};

const rules = reactive({
  name: [{ trigger: "blur", required: true, message: "名称不能为空" }]
});
const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    labelForm.value = {
      name: "",
      list: []
    };
    emits("update:visible", value);
  }
});

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: addTypeApi,
        [editType["edit"]]: updateTypeApi
      };
      const requestParamsObject = {
        [editType["add"]]: () => {
          return {
            parentId: labelForm.value.parentId,
            name: labelForm.value.name,
            description: labelForm.value.description
          };
        },
        [editType["edit"]]: () => {
          return {
            id: props.params.id,
            name: labelForm.value.name,
            description: labelForm.value.description
          };
        }
      };
      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        emits("confirm");
        ElMessage.success(res.msg);
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
.horizontal.el-form-item {
  display: flex;
  align-items: center;
  :deep(.el-form-item__label) {
    margin-bottom: 0;
  }
}
</style>
