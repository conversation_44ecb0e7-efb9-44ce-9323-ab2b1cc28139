<template>
  <div class="table-box custom-list">
    <pro-table
      ref="proTable"
      title="产品使用分析总列表"
      :request-auto="false"
      :columns="columns"
      :init-param="initParam"
      :tool-button="false"
      :pagination="false"
      :data="tableData"
      :summary-method="getSummaries"
      show-summary
      :height="800"
    >
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <template #accumulateHB="{ row }">
        {{ row.accumulateHB }} <HBArrow v-if="row.accumulateHBArrow" :value="row.accumulateHBArrow" />
      </template>
      <template #gmvHB="{ row }"> {{ row.gmvHB }} <HBArrow v-if="row.gmvHBArrow" :value="row.gmvHBArrow" /> </template>
      <template #accumulateGmvHB="{ row }">
        {{ row.accumulateGmvHB }} <HBArrow v-if="row.accumulateGmvHBArrow" :value="row.accumulateGmvHBArrow" />
      </template>
      <template #dataType="{ row }">
        {{ useDictLabel(pm_data_type, row?.dataType) }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span>
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="orderAnalysisList">
import { ref, reactive, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { exportProductUse, queryProductUseList } from "@/api/modules/statisticAnalysis";
import HBArrow from "@/components/HBArrow/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { useDownload } from "@/hooks/useDownload";
import ProTable from "@/components/ProTable/index.vue";

const { pm_data_type } = useDict("pm_data_type");

const { BUTTONS } = useAuthButtons();
const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
});

const router = useRouter();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive(props.params);
const tableData = ref([]);
const totalData = ref([]);
const tableParams = ref(props.params);

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const reqParams = { ...initParam, ...params };
  tableParams.value = reqParams;
  queryProductUseList(reqParams).then((res: any) => {
    const { list = [], total = [] } = res.data || {};
    tableData.value = list;
    totalData.value = total;
  });
};

const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = "总计";
      return;
    }
    sums[index] = totalData.value[column.property];
  });

  return sums;
};

// 表格配置项
const columns: ColumnProps<Statistic.OrderDateItem>[] = [
  { prop: "indexDate", label: "统计时间", minWidth: 100 },
  { prop: "dataName", label: "产品名称/权益名称", minWidth: 150 },
  { prop: "dataType", label: "数据维度", minWidth: 100 },
  { prop: "providerName", label: "合作商", minWidth: 100 },
  { prop: "userNa", label: "新增使用用户数", minWidth: 130 },
  { prop: "userCum", label: "累计使用用户数", minWidth: 130 },
  { prop: "effectUserCum", label: "产品累计生效用户数", minWidth: 155 },
  { prop: "orderUserRatio", label: "占产品订购用户数", minWidth: 150 },
  { prop: "useNumNa", label: "新增使用次数", minWidth: 110 },
  { prop: "useNumCum", label: "累计使用次数", minWidth: 110 }
];

const toView = async (row: any) => {
  router.push({
    path: "/statisticAnalysis/orderAnalysis/orderDimension",
    query: {
      dateType: props.params.dateType,
      countDate: [row.countDate, row.countDate]
    }
  });
};

const downloadFile = () => {
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() => {
    const queryData = {
      ...tableParams.value
    };
    useDownload(exportProductUse, queryData);
  });
};
defineExpose({
  getTableList
});
</script>
<style lang="scss" scoped>
.custom-list {
  :deep(.card.table-main) {
    border: none !important;
    box-shadow: none !important;
  }
}
:deep(.el-table .el-table__header .el-table__cell > .cell, .el-table .el-table__header .el-table__cell > .cell) {
  white-space: wrap;
}
:deep(.el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell) {
  color: var(--el-color-primary);
  background-color: #ffffff;
}
</style>
