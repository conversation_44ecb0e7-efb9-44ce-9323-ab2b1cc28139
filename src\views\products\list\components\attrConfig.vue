<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-08 16:45:21
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-19 11:45:06
 * @Description: file content
-->
<template>
  <el-form
    :model="formData"
    :rules="formRules"
    label-width="160px"
    class="el-form"
    ref="diskRef"
    :validate-on-rule-change="false"
  >
    <el-form-item
      :label="item.label"
      v-for="(item, index) in attrList"
      :key="'attr' + index"
      :style="item.attrFilter.type === 'checkbox' ? 'width:100%' : ''"
      :prop="item.attrCode"
    >
      <el-checkbox-group v-model="formData[item.attrCode]" v-if="item.attrFilter.type === 'checkbox'">
        <el-checkbox v-for="(item2, index2) in item.attrFilter.content" :label="item2" :key="index2" :disabled="disabled">{{
          item2.name
        }}</el-checkbox>
      </el-checkbox-group>
      <div v-if="item.attrFilter?.type === 'input'">
        <el-input-number
          v-model="formData[item.attrCode]"
          type="text"
          :disabled="disabled"
          v-bind="item.attrFilter.attr"
          :min="1"
          v-if="item.attrFilter.attr.type === 'number'"
        ></el-input-number>
        <el-input
          v-model="formData[item.attrCode]"
          type="text"
          v-bind="item.attrFilter.attr"
          :disabled="disabled"
          v-else
        ></el-input>
      </div>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { computed, ref } from "vue";
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  },
  attrList: {
    type: Object,
    default: () => {}
  },
  rules: {
    type: Object,
    default: () => {}
  },
  disabled: {
    type: Boolean,
    default: false
  }
});
const diskRef = ref();
const emits = defineEmits(["update:data"]);
const formData = computed({
  get: () => {
    return props.data || {};
  },
  set: value => {
    emits("update:data", value);
  }
});
const formRules = computed(() => {
  if (!props.rules) {
    return;
  }
  const result: any = {};
  Object.keys(props.rules).forEach(item => {
    if (item === "maxValue" || item === "minValue") {
      result[item] = [
        ...props.rules[item],
        { validator: checkNumber("maxValue", "minValue"), trigger: "blur", message: "数据验证不通过" }
      ];
    } else if (item === "diskSizeMin" || item === "diskSizeMax") {
      result[item] = [
        ...props.rules[item],
        { validator: checkNumber("diskSizeMax", "diskSizeMin"), trigger: "blur", message: "数据验证不通过" }
      ];
    } else if (item === "diskNumMin" || item === "diskNumMax") {
      result[item] = [
        ...props.rules[item],
        { validator: checkNumber("diskNumMax", "diskNumMin"), trigger: "blur", message: "数据验证不通过" }
      ];
    } else {
      result[item] = props.rules[item];
    }
  });
  return result;
  // return props.rules;
});

//校验最大值，最小值
const checkNumber = (max: string, min: string) => {
  return function () {
    if (formData.value[max] && formData.value[min]) {
      return formData.value[max] > formData.value[min];
    }
    return true;
  };
};

const validateData = () => {
  return new Promise((resolve, reject) => {
    diskRef.value.validate((valid: any, fields: any) => {
      if (valid) {
        resolve("校验成功");
      } else {
        let obj = Object.keys(fields);
        diskRef.value.scrollToField(obj[0]);
        reject("校验失败");
      }
    });
  });
};
defineExpose({ validateData });
</script>
<style lang="scss" scoped>
.el-form {
  display: flex;
  flex-wrap: wrap;
  :deep(.el-form-item) {
    min-width: 50%;
    margin-bottom: 24px;
  }
}
</style>
