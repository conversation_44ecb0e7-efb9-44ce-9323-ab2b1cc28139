<template>
  <div class="table-box">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="item in tabs" :label="item.label" :name="item.name" :key="item.name"></el-tab-pane>
    </el-tabs>

    <div v-show="activeName === PageType.banner">
      <banner-list />
    </div>
    <div v-show="activeName === PageType.news">
      <news-list />
    </div>
    <div v-show="activeName === PageType.message">
      <message-list />
    </div>
    <div v-show="activeName === PageType.policyNews">
      <policy-list />
    </div>
  </div>
</template>

<script setup lang="tsx" name="partnerReview">
import { ref, onMounted } from "vue";
import type { TabsPaneContext } from "element-plus";
import bannerList from "./components/bannerList.vue";
import newsList from "./components/newsList.vue";
import messageList from "./components/messageList.vue";
import policyList from "./components/policyList.vue";
import { PageType } from "./type";
import { useDicStore } from "@/stores/modules/dictionaty";

const dictionaryStore = useDicStore();

onMounted(() => {
  dictionaryStore.getBannerAuditAction();
  dictionaryStore.getProductAuditStatus();
});

const tabs = [
  {
    label: "Banner位",
    name: "banner"
  },
  {
    label: "最新动态",
    name: "news"
  },
  {
    label: "消息中心",
    name: "message"
  },
  {
    label: "政策消息",
    name: "policyNews"
  }
];

const activeName = ref<any>(PageType.banner);

const handleClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name;
};
</script>
