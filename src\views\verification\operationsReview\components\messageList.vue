<template>
  <div class="banner-box table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <template #status="{ row }">
        <CustomTag
          :type="statusTypeMap[row.status]"
          :status="row.status"
          :label="useDictLabel(dictStore.productAuditStatus, row.status)"
        ></CustomTag>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'messageView'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="() => toEdit(scope.row, OperateType.detail)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'messageReview'">
          <el-tooltip content="审核" placement="top" v-if="scope.row.auditStatus == '1'">
            <i class="iconfont2 icon-shenhe opera-icon" @click="() => toEdit(scope.row, OperateType.review)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="messageList">
import { effect, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import type { ProTableInstance, ColumnProps } from "@/components/ProTable/interface/index";
import { getPageList, messageAuditDetail } from "@/api/modules/business/message";
import { useDicStore } from "@/stores/modules/dictionaty";
import { messageReviewStorage } from "@/utils/storage/edit";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDictLabel } from "@/hooks/useDict";
import { OperateType } from "../type";

const statusTypeMap: { [key: string]: string } = {
  "1": "blue",
  "2": "yellow",
  "3": "green",
  "4": "grey",
  "5": "red"
};
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { messageTypes } from "element-plus";

const { BUTTONS } = useAuthButtons();

const route = useRoute();

const dictStore = useDicStore();

const router = useRouter();

const proTable = ref<ProTableInstance>();
const newsStatus = ref([]);

const getTableList = async (search: any) => {
  if (!BUTTONS.value?.newsQuery) {
    return;
  }

  let newParams = JSON.parse(JSON.stringify(search));
  return getPageList(newParams, 1);
};

const columns = ref<ColumnProps[]>([
  {
    prop: "title",
    label: "消息标题",
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "bizTypeId",
    label: "消息类型",
    align: "left",
    // enum:dictStore.;
    enum: [
      { label: "全部", value: "-1" },
      { label: "系统通知", value: "1" },
      { label: "活动通知", value: "2" },
      { label: "其他", value: "4" }
    ],
    search: { el: "select" }
  },
  {
    prop: "auditAction",
    label: "审核操作",
    align: "left",
    // enum: dictStore.bannerAuditAction,
    enum: [
      { label: "新增", value: 0 },
      { label: "编辑", value: 1 }
    ],
    search: { el: "select" }
  },

  {
    prop: "creatorName",
    align: "left",
    label: "提交账号"
  },
  {
    prop: "createTime",
    label: "提交时间",
    align: "left",
    width: 180,
    sortable: true
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    align: "left",
    enum: [
      { label: "未提交", value: "0" },
      { label: "待审核", value: "1" },
      { label: "审核通过", value: "2" },
      { label: "审核不通过", value: "3" }
    ],
    search: { el: "select" }
  },
  {
    prop: "auditTime",
    label: "审核时间",
    align: "left",
    width: 180
  },
  {
    prop: "auditName",
    label: "审核人",
    align: "left"
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 130 }
]);

dictStore.getNewsStatus().then((data: any) => {
  newsStatus.value = data.map((item: any) => ({ value: item.itemKey, label: item.itemValue }));
});

messageReviewStorage.clearExpired();

const toEdit = (data: any, type = OperateType.detail) => {
  const auditMsg = data.auditRemark;
  // console.log(auditMsg);
  messageAuditDetail(data.id, 1).then(resp => {
    const respData = resp.data || {};
    messageReviewStorage.set(data.id, data);
    // console.log(data.id, data.effectTime);
    router.push({
      path: "/verification/operationsReview/messageReview",
      query: {
        id: data.id,
        type,
        auditMsg
      }
    });
  });
};

// watch(
//   () => route.path,
//   (path: string) => {
//     if (path === "/operation/news") {
//       proTable.value?.getTableList();
//     }
//   }
// );
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
</style>
