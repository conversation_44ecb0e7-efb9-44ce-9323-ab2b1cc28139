export namespace downloadMange {
  export interface IDownLoadSearch extends ReqPage {
    status: string;
  }

  export interface IDownLoadItem {
    advice: null;
    auditTime: null;
    auditorId: null;
    auditorName: null;
    columSize: number;
    createTime: string;
    creatorId: null;
    creatorName: null;
    exportType: string;
    fileOriginalName: string;
    id: string | null;
    status: string;
    updateTime: null;
    updaterId: null;
    updaterName: null;
  }

  export type DownloadReviewType = Pick<IDownLoadItem, "advice" | "id" | "status" | "creatorId" | "exportType">;

  export interface IExportFile {
    advice: string;
    creatorId: string;
    exportType: string;
    id: string;
    status: string;
  }
}
