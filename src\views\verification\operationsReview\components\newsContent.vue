<template>
  <div class="content-box">
    <el-form ref="ruleFormRef" :model="ruleForm" label-position="left" :rules="rules" label-width="110px" :disabled="!isEdit">
      <el-form-item label="最新动态标题" prop="title" required>
        <el-input :disabled="disabled" v-model="ruleForm.title" :max="50" placeholder="输入内容" />
      </el-form-item>
      <el-form-item label="发布形式" prop="publishType" required>
        <el-radio-group v-model="ruleForm.publishType" :disabled="disabled">
          <el-radio label="1">立即发布</el-radio>
          <el-radio label="2">定时发布</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="定时发布" prop="publishStartTime" v-if="ruleForm.publishType === '2'">
        <el-date-picker
          :disabled="disabled"
          v-model="ruleForm.publishStartTime"
          type="datetime"
          placeholder="请选择发布时间"
          :disabled-date="disabledDate"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="最新动态内容" prop="detail" placeholder="请输入内容" required>
        <WangEditor :disabled="disabled" :max="20000" v-model:value="ruleForm.detail" height="200px" />
      </el-form-item>
      <el-form-item label="最新动态摘要" prop="summary" placeholder="请输入内容" required>
        <el-input :disabled="disabled" :max="200" v-model="ruleForm.summary" type="textarea" />
      </el-form-item>
      <el-form-item label="封面" prop="coverUrl" required>
        <UploadImg :disabled="disabled" :file-size="2" v-model:image-url="ruleForm.coverUrl" width="135px" height="135px">
          <template #tip>建议图片宽度为750px,高度为750px，支持图片格式jpg/jpeg/png</template>
        </UploadImg>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="newsEdit">
import { reactive, ref } from "vue";
import { useRoute } from "vue-router";
import type { FormInstance, FormRules } from "element-plus";
import { newsReviewStorage } from "@/utils/storage/edit";
import UploadImg from "@/components/Upload/Img.vue";
import WangEditor from "@/components/WangEditor/index.vue";

const route = useRoute();
const id: string = (route.query.id as string) || "";

let isEdit = false;
let disabled = false;

const getInitForm = () => {
  const item = newsReviewStorage.get(id);
  console.log("🚀 ~ file: newsContent.vue:55 ~ getInitForm ~ item:", item);
  if (item) {
    return {
      ...item
    };
  } else {
    return {
      title: "",
      publishType: "",
      publishStartTime: "",
      detail: "",
      summary: "",
      coverUrl: ""
    };
  }
};

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive(getInitForm());

const disabledDate = (time: Date) => time.getTime() < Date.now() - 60 * 60 * 24 * 1000;

const validatePublishStartTime = (rule: any, value: any, callback: any) => {
  if (ruleForm.publishType == "2" && value === "") {
    callback(new Error("请输入定时发布时间"));
  }
  callback();
};

const rules = reactive<FormRules>({
  title: [
    { required: true, message: "请输入最新动态标题", trigger: "blur" },
    { max: 50, message: "最多输入50个字符", trigger: "blur" }
  ],
  publishType: [{ required: true, message: "请选择发布形式", trigger: "blur" }],
  publishStartTime: [{ validator: validatePublishStartTime, trigger: "blur" }],
  detail: [
    { required: true, message: "请输入最新动态内容", trigger: "blur" },
    { max: 20000, message: "最多输入20000个字符", trigger: "blur" }
  ],
  summary: [
    { required: true, message: "请输入最新动态摘要", trigger: "blur" },
    { max: 200, message: "最多输入200个字符", trigger: "blur" }
  ],
  coverUrl: [{ required: true, message: "请上传封面", trigger: "blur" }]
});
</script>

<style scoped lang="scss">
.content-box {
  align-items: start;
}
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
</style>
