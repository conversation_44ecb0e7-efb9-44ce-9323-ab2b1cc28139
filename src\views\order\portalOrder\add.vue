<template>
  <div class="add-order">
    <div class="info-box card" v-show="!(step === 4)">
      <addOrderStep v-model:step="step"></addOrderStep>
    </div>
    <!-- 客户信息 -->
    <CustomerInfo :customer-info="choiceAccountInfo" :phone="choiceAccountInfo.enterpriseCertificate?.phone">
      <template #button>
        <el-button v-show="orderStatus === 'edit'" class="card-btn" plain :icon="CircleCheck" @click="choiceCustomer">
          选择客户
        </el-button>
        <span v-auth="'accountOpen'">
          <el-button v-show="orderStatus === 'edit'" class="card-btn" plain :icon="CirclePlus" @click="createOpen">
            客户开户
          </el-button>
        </span>
      </template>
    </CustomerInfo>
    <!-- 产品信息 -->
    <div class="info-box card">
      <div class="box-title">
        <div class="title">产品信息</div>
        <el-button v-show="orderStatus === 'edit'" class="card-btn" plain :icon="CirclePlus" @click="orderProduct">
          订购产品
        </el-button>
      </div>
      <pro-table
        ref="proTable"
        title="产品信息列表"
        :columns="columns"
        :data="proChoiceList"
        :tool-button="false"
        :request-auto="false"
        :pagination="false"
        :has-padding="false"
        :border="false"
      >
        <template #deliveryMethod="{ row }">{{ useDictLabel(pms_delivery_method, row.deliveryMethod) }}</template>
        <template #quantityNum="{ row }">
          {{ row.quantityNum > 0 ? `${row.quantityNum}` : "/" }}
        </template>
        <template #timeNum="{ row }">
          {{ row.timeNum > 0 ? `${row.timeNum}${useDictLabel(timeDic, row.timeUnit)}` : "/" }}
        </template>
        <template #skuName="{ row }">
          <div v-html="row.skuName"></div>
        </template>
      </pro-table>
    </div>

    <el-form ref="formRef" :model="form" :disabled="orderStatus === 'view'">
      <!-- 客户经理信息 -->
      <info-template
        :title="'客户经理信息'"
        :type="'edit'"
        v-model:customer-manager-name="form.customerManagerName"
        v-model:customerManagerDept="form.customerManagerDept"
        v-model:customerManagerPhone="form.customerManagerPhone"
      ></info-template>

      <div class="info-box card" v-show="orderTime">
        <div class="title">生效模式</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="生效模式：">
              <el-select v-model="form.mode" placeholder="请选择生效模式" style="width: 100%" @change="handleChange">
                <el-option label="立即生效" value="0" />
                <el-option label="自定义有效期" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="orderTime && form.mode === '1'">
            <el-form-item
              prop="effectTime"
              label="选择开始生效时间："
              :rules="[{ required: true, message: '选择开始生效时间', trigger: 'blur' }]"
            >
              <el-date-picker
                @change="handleDateChange"
                style="width: 100%"
                v-model="form.effectTime"
                type="datetime"
                :value-format="'YYYY-MM-DD HH:mm:ss'"
                placeholder="选择开始生效时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="orderTime && form.mode === '1'">
            <el-form-item label="到期时间：">
              <el-date-picker
                :disabled="true"
                style="width: 100%"
                v-model="form.endTime"
                type="datetime"
                placeholder="到期时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <DiscountInfo :table-data="form._tableData" :total-price="selectProductInfo.wholeMoney">
        <template #button>
          <el-button class="card-btn" plain :icon="CirclePlus" @click="handleUpload">审批单上传</el-button>
        </template>
      </DiscountInfo>
      <div class="info-box card">
        <div class="title">合同及其他附件</div>
        <div class="infos">
          <el-form-item
            label="附件："
            prop="_productDocs"
            :rules="[{ required: true, message: '请上传附件', trigger: 'change' }]"
          >
            <uploadFile
              :is-private="true"
              class="upload"
              v-model:file-list="form._productDocs"
              :file-type="'.doc,.xls,.docx,.xlsx,.rar,.zip,.jpg,.gif,.jpeg,.png,.pdf'"
              :limit="5"
              :file-size="30"
              :show-file-list="true"
              :drag="false"
              :width="'auto'"
              :height="'auto'"
              ><template #trigger>
                <el-button type="primary" ref="uploadBtnRef">上传附件</el-button>
              </template>

              <template #tip>
                <div class="upload-tips">
                  文件类型：.doc .xls .docx .xlsx .rar .zip .jpg .gif .jpeg .png .pdf，支持5个附件上传，文件大小不超过30M
                </div>
              </template>
            </uploadFile>
          </el-form-item>
        </div>
      </div>
      <div class="info-box card">
        <div class="title">其他信息</div>
        <div class="infos">
          <div class="info-item">
            <el-form-item label="备注：">
              <el-input v-model="otherRemark" type="textarea" placeholder="请输入备注信息" maxlength="200" show-word-limit />
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
    <div class="order-btn" v-show="orderStatus === 'edit'">
      <el-button plain @click="backto"> 返回 </el-button>
      <el-button type="primary" @click="handelView"> 订单预览 </el-button>
    </div>
    <div class="order-btn" v-show="orderStatus === 'view'">
      <el-button plain @click="backto"> 返回 </el-button>
      <el-button plain @click="handlePerv"> 上一步 </el-button>
      <el-button type="primary" @click="submit"> 提交订购 </el-button>
    </div>
    <account-modal ref="accountModalRef" @selected-list="getSelectedList"></account-modal>
    <OrderModal ref="orderModal" @selected-product-info="getProductInfo"></OrderModal>
    <createAccount ref="ModalRef"></createAccount>
  </div>
</template>

<script setup lang="ts" name="addPortalOrder">
type OrderStatus = "edit" | "view";
import CustomerInfo from "./components/customerInfo.vue";
import infoTemplate from "./components/infoTemplate.vue";
import uploadFile from "@/components/Upload/Files.vue";
import addOrderStep from "./components/addOrderStep.vue";
import DiscountInfo from "./components/discountInfo.vue";
import { computed, ref, reactive, watch, nextTick, onMounted } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { CirclePlus, CircleCheck, Download } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import AccountModal from "./components/accountModal.vue";
import OrderModal from "./components/orderModal.vue";
import { addOrder } from "@/api/modules/order";
import { ElMessage } from "element-plus";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useUserStore } from "@/stores/modules/user";
import { storeToRefs } from "pinia";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { formatDate, futureTime, timeUnit } from "@/utils";
import type { FormInstance, FormRules } from "element-plus";

import createAccount from "./createAccount/index.vue";

import { userCertificateInfo } from "@/api/modules/system";

const { pms_delivery_method } = useDict("pms_delivery_method");

const ModalRef = ref<{ handleOpen: () => void }>();

const router = useRouter();
const accountModalRef = ref();
const uploadBtnRef = ref();
/* 弹窗 */
const orderModal = ref();
const proTable = ref<ProTableInstance>();
const otherRemark = ref("");
const choiceAccountInfo = ref({
  enterpriseCertificate: {},
  personalCertificate: null,
  portalUser: null,
  userId: ""
});
const selectProductInfo = ref({});
const proChoiceList = ref([{}]);
const dictionaryStore = useDicStore();

const { timeDic } = storeToRefs(dictionaryStore);
/* 步聚 */
const step = ref<number>(0);

const form = reactive({
  mode: "0",
  effectTime: "",
  endTime: "",
  _productDocs: [],
  _tableData: [],
  customerManagerName: "",
  customerManagerDept: "",
  customerManagerPhone: ""
});
const orderTime = ref(false);

const orderStatus = ref<OrderStatus>("edit");
const formRef = ref<FormInstance>();
// 表格配置项
const columns: ColumnProps<any>[] = [
  // { prop: "productCode", label: "产品编码" },
  { prop: "productName", label: "产品名称" },
  { prop: "skuName", width: 240, label: "产品规格" },
  { prop: "skuConfig", width: 240, label: "规格配置" },
  {
    prop: "probationFlag",
    label: "试用产品",
    enum: [
      { label: "是", value: "1" },
      { label: "否", value: "0" }
    ]
  },
  { prop: "quantityNum", label: "购买数量" },
  { prop: "timeNum", label: "购买时长" },
  { prop: "price", label: "单价（元）" },
  { prop: "deliveryMethod", label: "交付方式" },

  { prop: "wholeMoney", label: "订单总金额（元）", width: 180 }
];

const choiceCustomer = () => {
  accountModalRef.value?.showDialog();
};

const orderProduct = () => {
  if (step.value >= 1) {
    orderModal.value.handleOpen();
  } else {
    ElMessage.warning("请先选择客户");
  }
};

watch(form, nVal => {
  if (nVal._productDocs.length > 0 && nVal._tableData[0]?.attrValue > 0) {
    step.value = 3;
  } else {
    step.value = 2;
  }
});

const getSelectedList = (info: any[]) => {
  userCertificateInfo({ id: info[0].id })
    .then(result => {
      const { data } = result;
      choiceAccountInfo.value = data;
      choiceAccountInfo.value.userId = info[0].id;
      step.value = 1;
    })
    .catch(err => {});
};
const getProductInfo = (data: object) => {
  selectProductInfo.value = data;
  proChoiceList.value = [selectProductInfo.value];

  if (Object.keys(selectProductInfo.value).length > 0) {
    step.value = 2;
    form._tableData = proChoiceList.value.map((item: any) => {
      return { attrValueName: item.productName, attrValue: "" };
    });
    let buyTime = 0;
    proChoiceList.value.forEach((el: any) => {
      if (el.timeNum === "") {
        buyTime += 0;
      } else {
        buyTime += Number(el.timeNum);
      }
    });

    /* 产品时间大于等于1或skuType ===1 时 */
    if (buyTime >= 1 && proChoiceList.value[0].skuType === "1") {
      orderTime.value = true;
    } else {
      orderTime.value = false;
    }
  }
};
/* 提交订单 */
const submit = () => {
  const fileList = form._productDocs.map((item: any) => {
    return {
      attrCode: "",
      attrName: item.name,
      attrValue: item.link,
      uid: item.uid,
      isVirtual: true
    };
  });

  let params = {
    customerId: choiceAccountInfo.value.userId,
    payType: selectProductInfo.value.payType,
    customerManagerDept: form.customerManagerDept,
    customerManagerName: form.customerManagerName,
    customerManagerPhone: form.customerManagerPhone,
    subOrderProductList: {
      buyDuration: selectProductInfo.value.timeNum,
      count: selectProductInfo.value.quantityNum,
      productId: selectProductInfo.value.productId,
      skuId: selectProductInfo.value.skuId,
      effectTime: form.effectTime,
      endTime: form.endTime,
      attributes: [
        {
          attrCode: "discountInfo",
          attrName: "折扣信息",
          attrValue: "",
          attrValueName: "",
          children: [
            {
              attrCode: "agreementPrice",
              attrName: "协议价",
              attrValue: form._tableData[0].attrValue,
              attrValueName: form._tableData[0].attrValueName
            }
          ]
        },
        {
          attrCode: "contractFiles",
          attrName: "合同及其他附件列表",
          attrValue: "contractFiles",
          attrValueName: "合同及其他附件列表",
          isVirtual: true,
          children: fileList
        }
      ]
    },
    otherRemark: otherRemark.value
  };

  addOrder(params).then(res => {
    if (res.code === 200) {
      ElMessage.success(res.msg);
      // router.push("/order/portalOrder");
      router.back();
    }
  });
};
const backto = () => {
  // router.push("/order/portalOrder");
  router.back();
};
const handleUpload = () => {
  if (step.value >= 2) {
    uploadBtnRef.value?.$.vnode.el?.click();
  } else {
    ElMessage.warning("请先选择产品");
  }
};

const handleDateChange = (value: string) => {
  if (orderTime.value) {
    const time = timeUnit(proChoiceList.value[0].timeNum, proChoiceList.value[0].timeUnit);
    form.endTime = formatDate(futureTime(value, time));
  }
};

const handleChange = (value: string) => {
  if (step.value >= 2) {
    form.mode = value;
    if (form.mode === "0") {
      form.effectTime = "";
      form.endTime = "";
    }
  } else {
    ElMessage.warning("请先选择产品");
  }
};

const handelView = () => {
  if (!choiceAccountInfo.value.enterpriseCertificate?.id) {
    ElMessage.error("请选择客户信息");
    return;
  }
  if (!selectProductInfo.value.productId || (selectProductInfo.value.skuType == "1" && !selectProductInfo.value.skuId)) {
    ElMessage.error("请先订购产品");
    return;
  }
  formRef.value
    ?.validate()
    .then(result => {
      orderStatus.value = "view";
      step.value = 4;
      nextTick(() => {
        const elMain = document.querySelector(".el-main") as HTMLDivElement;
        elMain.scrollTo({ left: 0, top: 0, behavior: "smooth" });
      });
    })
    .catch(err => {
      ElMessage.error("请输入完整信息");
    });
};

const handlePerv = () => {
  orderStatus.value = "edit";
  step.value = 3;
};
/* 客户开户 */
const createOpen = () => {
  ModalRef.value?.handleOpen();
};
</script>

<style lang="scss" scoped>
.card-btn {
  margin-right: 10px;
}
.info-box {
  margin-bottom: 15px;
  .title {
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #3a3a3d;
    border-bottom: 1px solid #e4e7ed;
  }
  .infos {
    margin-left: 16px;
  }
  .info-item {
    margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;

      // width: 80px;
      // margin-right: 10px;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
}
.order-btn {
  margin-top: 30px;
  margin-bottom: 10px;
  text-align: left;
  :deep(.el-button) {
    height: 36px;
    margin-right: 16px;
  }
}
.box-title {
  display: flex;
  justify-content: space-between;
}
</style>
