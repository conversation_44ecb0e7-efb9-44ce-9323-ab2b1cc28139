<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="title" width="600" @close="onCancel">
      <el-form name="test" ref="ruleFormRef" :model="dialogForm" :rules="rules" :label-width="80" label-position="right">
        <el-form-item label="订单号" prop="orderId">
          {{ dialogForm.orderId }}
        </el-form-item>
        <el-form-item label="合同编码" prop="contractId">
          <el-input v-model="dialogForm.contractId" maxlength="50" :disabled="isDisable" clearable></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { editType, editTypeChinese } from "@/enums/dialogTypeEnum";
import { computed, ref, reactive, watch } from "vue";
import type { FormInstance } from "element-plus";
import { FormRules } from "element-plus";
import { otherOrderUpload } from "@/api/modules/order";

const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: editType.add
  },
  params: {
    type: Object,
    default: () => {}
  }
});

const ruleFormRef = ref();
const isShowSelectDepart = ref(false);

const dialogForm = ref<any>({
  id: "", //	订单ID
  orderId: "", //	订单ID
  contractId: "" //	合同编码
});

const title = computed(() => "关联合同");
const isDisable = computed(() => !editTypeChinese[props.type]);

const emits = defineEmits(["update:visible", "cancel", "confirm"]);

watch(
  () => props.params,
  () => {
    if (props.type !== editType.add) {
      dialogForm.value = {
        id: props.params.id,
        orderId: props.params.orderId,
        contractId: props.params.contractId
      };
    } else {
      dialogForm.value.id = "";
    }
  },
  {
    immediate: true
  }
);

const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
  isShowSelectDepart.value = false;
};

const rules = reactive<FormRules>({
  contractId: [{ trigger: "blur", required: true, message: "合同编码不能为空" }]
});

// 监听弹窗
const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    dialogForm.value = {
      id: "",
      orderId: "",
      contractId: ""
    };
    emits("update:visible", value);
  }
});

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const params = {
        id: dialogForm.value.id,
        contractId: dialogForm.value.contractId
      };

      otherOrderUpload(params).then((res: any) => {
        emits("confirm");

        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
.horizontal.el-form-item {
  display: flex;
  align-items: center;
  :deep(.el-form-item__label) {
    margin-bottom: 0;
  }
}
.detail-title {
  margin-bottom: 18px;
  font-size: 16px;
  font-weight: 500;
  color: #3a3a3d;
  &.mt8 {
    margin-top: 8;
  }
}
.select-new-depart {
  margin-left: 10px;
  cursor: pointer;
}
</style>
