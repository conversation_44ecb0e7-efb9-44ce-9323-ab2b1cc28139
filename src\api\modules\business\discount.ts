import http from "@/api";
import { array } from "@amcharts/amcharts4/core";

// 获取折扣管理列表
export const getPageList = (data: any) => {
  return http.get("/cnud-product/admin/productDiscount/queryDiscountPage", data);
};

// 获取折扣产品列表
export const getProductList = (data: any) => {
  return http.get("/cnud-product/admin/pmsProduct/queryDiscountPage", data);
};

//删除折扣产品
export const deleteItem = (id: string) => {
  return http.post("/cnud-product/admin/productDiscount/delDiscount", { id });
};

//增加折扣产品
export const add = (ids: Array<string>) => {
  return http.post("/cnud-product/admin/productDiscount/addDiscount", { ids: ids });
};

//排序
export const sortDiscount = (ids: string, sort: number) => {
  return http.post("/cnud-product/admin/productDiscount/sortDiscount", { id: ids, sort: sort });
};

// //启用、禁用折扣产品
// export const updateDiscountStatus = (ids: string, status: string) => {
//   return http.post("/cnud-product/admin/productDiscount/updateDiscountStatus", { id: ids, status: status });
// };
