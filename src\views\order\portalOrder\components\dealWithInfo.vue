<template>
  <div class="page-order-detail">
    <div class="order-info">
      <div class="page-title">处理信息</div>
      <div class="info-box card">
        <div class="form">
          <el-form ref="formRef" :model="examineForm" label-width="100px" class="demo-ruleForm">
            <div class="infos" v-if="examineForm.effectTime">
              <el-row algin="center">
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">生效模式：</span>
                    <span class="info-text">{{ orderInfo.effectType === "0" ? "立即生效" : "自定义有效期" }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-text">
                      <el-form-item
                        prop="effectTime"
                        label="生效时间："
                        :rules="[{ required: true, message: '选择开始生效时间', trigger: 'blur' }]"
                      >
                        <el-date-picker
                          @change="handleDateChange"
                          style="width: 100%"
                          v-model="examineForm.effectTime"
                          type="datetime"
                          :value-format="'YYYY-MM-DD HH:mm:ss'"
                          placeholder="选择开始生效时间"
                        />
                      </el-form-item>
                    </span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-text">
                      <el-form-item label="到期时间：">
                        <el-date-picker
                          :disabled="true"
                          style="width: 100%"
                          v-model="examineForm.endTime"
                          type="datetime"
                          placeholder="到期时间"
                        />
                      </el-form-item>
                    </span>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-form-item label="处理意见：" prop="auditRemark" :rules="[{ required: validRequired, message: '请输入处理意见' }]">
              <el-input
                rows="5"
                v-model="examineForm.auditRemark"
                placeholder="请输入处理意见"
                type="textarea"
                maxlength="500"
                show-word-limit
                autocomplete="off"
              />
            </el-form-item>
            <el-form-item>
              <!-- <div class="tips">驳回时必须填写审核意见，通过默认审核意见为同意</div> -->
            </el-form-item>
            <div class="button">
              <el-button type="primary" @click="handleTask('FINISH')">报竣</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from "vue";
import type { Action, FormInstance, FormRules } from "element-plus";

import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { formatDate, futureTime, timeUnit } from "@/utils";
import { manualOrderHandle } from "@/api/modules/order";
import { getPlatformAccountList } from "@/api/modules/platform";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { useTabsStore } from "@/stores/modules/tabs";

const route = useRoute();
const router = useRouter();
const formRef = ref<FormInstance>();
const validType = ref<string>("3");

const validRequired = computed(() => {
  return validType.value === "4";
});

const examineForm = reactive({
  auditRemark: "",
  effectTime: null,
  endTime: null
});

const emit = defineEmits(["onChange"]);

const handleTask = (result: string) => {
  validType.value = result;
  nextTick(() => {
    getPlatformAccountList({
      subOrderNo: props.orderInfo.parentOrderNo,
      userAccount: props.orderInfo.customerAccount,
      phoneList: [props.orderInfo.purchasePhone]
    }).then(res => {
      if (res.data.records.length > 0) {
        formRef.value?.validate().then(valid => {
          let tips = "";
          if (result === "FINISH") {
            tips = "是否确认报竣";
          } else {
            tips = "是否确认退单";
          }
          ElMessageBox.alert(tips, "操作提示", {
            confirmButtonText: "确定",
            callback: (action: Action) => {
              if (action === "confirm") {
                manualOrderHandle({
                  operateRemark: examineForm.auditRemark,
                  effectTime: examineForm.effectTime,
                  effectType: 1,
                  parentOrderNo: props.orderInfo.parentOrderNo || 0
                }).then(res => {
                  // emit("onChange");
                  ElMessage({
                    message: result === "FINISH" ? "报俊成功" : "退单成功",
                    type: "success"
                  });
                  handleBack();
                });
              }
            }
          });
        });
      } else {
        ElMessage({
          message: "请绑它账号信息",
          type: "error"
        });
      }
    });
  });
};
function closeTabItem() {
  const tabStore = useTabsStore();
  const keepAliveStore = useKeepAliveStore();
  if (route.meta.isAffix) return;
  tabStore.removeTabs(route.fullPath);
  keepAliveStore.removeKeepAliveName(route.name as string);
}
const handleBack = () => {
  closeTabItem();
  router.push("/order/portalOrder");
};

const handleDateChange = (value: string) => {
  const time = timeUnit(props.orderInfo.buyDuration, props.orderInfo.buyDurationUnit);
  examineForm.endTime = formatDate(futureTime(value, time));
};

const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

watch(
  () => props.orderInfo,
  nVal => {
    examineForm.effectTime = nVal.effectTime;
    examineForm.endTime = nVal.endTime;
  }
);
</script>

<style scoped lang="scss">
@import "../order.module.scss";
.button {
  display: flex;
  justify-content: center;
}
.order-info {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}
.page-title {
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #3a3a3d;
  border-bottom: 1px solid #e4e7ed;
}
.form {
  margin-top: 20px;
}
.tips {
  color: rgb(156 163 172 / 100%);
}
</style>
