<template>
  <div class="table-box">
    <ProTable ref="proTable" title="折扣专区列表" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => handelAdd()"> 新增产品 </el-button>
      </template>

      <!-- <template #sort="{ row }">
        <div style="cursor: pointer" @click="sortDialog(row)">{{ row.sort }}</div>
      </template> -->

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="handelDelete(scope.row)"></i>
          </el-tooltip>
        </span>
        <!-- <span v-auth="'edit'">
          <el-tooltip :content="scope.row.status === '1' ? '停用' : '启用'" placement="top">
            <i v-if="scope.row.status === '1'" class="iconfont2 opera-icon icon-tingyong" @click="handleStatus(scope.row)"></i>
            <i v-else class="iconfont2 opera-icon icon-shenhe" @click="handleStatus(scope.row)"></i>
          </el-tooltip>
        </span> -->
      </template>
    </ProTable>

    <!-- <el-dialog title="修改排序" width="500px" v-model="dialogVisible" :destroy-on-close="true" :before-close="handleClose">
      <el-form ref="form" :rules="rules" :model="confirmForm">
        <el-form-item label="排序" prop="sortNumber" required>
          <el-input :max="50" v-model="confirmForm.sortNumber" v-d-input-int v-d-input-max="10000000" clearable size="large" />
        </el-form-item>
        <el-form-item>
          <span style="margin-left: 50px"> 数字越大，排序越靠前。数字重复时，则最新上架的靠前。 </span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="sort"> 确认 </el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog> -->
    <EditSortDialog v-if="dialogVisible" v-model:show-dialog="dialogVisible" :sort="sortNumber" @save="sort" />
    <productDrawer ref="drawerRef" @refresh="refresh" />
  </div>
</template>

<script setup lang="tsx" name="discount">
import { ref, reactive } from "vue";
import { Plus, Delete } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, FormRules } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
// import Edit, { IAction } from "./edit/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { getPageList, deleteItem, sortDiscount, updateDiscountStatus } from "@/api/modules/business/discount";
import productDrawer from "./components/productDrawer.vue";
import EditSortDialog from "@/components/EditSortDialog/index.vue";
import { useDiscountProductStore } from "@/stores/modules/productArea";

const { BUTTONS } = useAuthButtons();
const productStore = useDiscountProductStore();

let confirmForm = reactive({
  sortNumber: 0
});
const proTable = ref<ProTableInstance>();
const dialogVisible = ref(false);
// const editRef = ref<{ handleOpen: (active: IAction, rowData?: PmsRights.EditParams) => void } | null>(null);
const drawerRef = ref<InstanceType<typeof productDrawer> | null>(null);
// const isMoreSelect = ref(true);

const getTableList = async (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }

  let newParams = JSON.parse(JSON.stringify(search));
  getPageList(newParams).then(res => {
    if (productStore.productSkuIdList.length > 0) {
      productStore.productSkuIdList = [];
    }
    res.data.records.forEach((item: any) => productStore.productSkuIdList.push(item.skuId));
  });
  return getPageList(newParams);
};

const rules = reactive<FormRules>({
  sortNumber: { required: true, message: "请输入排序", trigger: "blur" }
});
// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "productName", label: "产品名称", search: { el: "input", props: { maxLength: 20 } } },
  { prop: "categoryName", label: "产品分类" },
  {
    prop: "skuName",
    label: "商品规格"
  },
  {
    prop: "skuPrice",
    label: "规格单价"
  },
  {
    prop: "skuDiscountPrice",
    label: "折扣价"
  },
  {
    prop: "sort",
    label: "排序",
    render: (scope: any) => {
      return (
        <span class={"sort-btn"} onClick={() => setShowEditDialog(scope.row)}>
          {scope.row.sort}
        </span>
      );
    }
    // render: (row: any) => {
    //   return (
    //     <el-table-column @onclick>
    //     </>
    //   )
    // }
    // enum: [
    //   { label: "不支持", value: "0" },
    //   { label: "打开", value: "1" },
    //   { label: "关闭", value: "2" }
    // ]
    // enum: industryType
  },
  {
    prop: "creatorName",
    label: "创建人员",
    width: 180
    // enum: auditStatusDict
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    sortable: true,
    width: 170
  },
  { prop: "operation", label: "操作", fixed: "right", width: 130 }
];

const handelAdd = () => {
  if (drawerRef.value) {
    drawerRef.value.visible = true;
  }
};
// const handleStatus = (data: any) => {
//   ElMessageBox.confirm("请确认是否删除此折扣产品？", "确认操作")
//   updateDiscountStatus(data.id, data.status === "1" ? "0" : "1")
//     .then(res => {
//       if (res.code === 200) {
//         ElMessage.success("修改成功");
//         proTable.value?.getTableList();
//       }
//     })
//     .catch(() => {});
// };

const handelDelete = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此折扣产品？", "确认操作")
    .then(async () => {
      await deleteItem(data.id);
      ElMessage.success("操作成功！");
      proTable.value?.getTableList();
    })
    .catch(() => {});
};
let id = ref("");

const setShowEditDialog = (data: any) => {
  id.value = data.id;
  dialogVisible.value = true;
};

const sort = async (sort: number) => {
  await sortDiscount(id.value, sort);
  ElMessage.success("操作成功！");
  // dialogVisible.value = false;
  proTable.value?.getTableList();
};
const refresh = () => {
  proTable.value?.getTableList();
};
</script>

<style lang="scss">
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
