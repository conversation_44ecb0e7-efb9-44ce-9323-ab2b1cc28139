<template>
  <el-dialog v-model="dialogVisible" title="关联合同" align-center width="600px">
    <div class="dialog-content">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px" label-position="left" status-icon>
        <el-form-item label="订单号" prop="orderId">
          <el-input v-model="ruleForm.orderId" placeholder="请输入" minlength="1" maxlength="50" clearable disabled> </el-input>
        </el-form-item>
        <el-form-item label="合同编码" prop="contractId">
          <el-input v-model="ruleForm.contractId" placeholder="请输入" minlength="1" maxlength="50" clearable> </el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="confirmLoading" @click="confirmClick">关联合同</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { relContract } from "@/api/modules/transactionVolumeFiling";

const emit = defineEmits(["updateSuccess"]);

let currentId = "";

const dialogVisible = ref(false);

const title = ref("");

const confirmLoading = ref<boolean>(false);

const ruleFormRef = ref();

const ruleForm = reactive({
  orderId: [],
  contractId: ""
});

const rules = reactive({
  orderId: [],
  contractId: [
    {
      required: true,
      message: "请输入合同编码",
      trigger: "blur"
    }
  ]
});

const show = (row: any) => {
  if (!row) {
    return;
  }
  currentId = row.id;
  dialogVisible.value = true;
  nextTick(() => {
    ruleFormRef.value?.resetFields();
    nextTick(() => {
      ruleForm.orderId = row.orderId;
    });
  });
  confirmLoading.value = false;
};

const close = () => {
  dialogVisible.value = false;
};

const save = () => {
  confirmLoading.value = true;
  const queryData = {
    id: currentId,
    contractId: ruleForm.contractId
  };
  relContract(queryData)
    .then(res => {
      confirmLoading.value = false;
      ElMessage({
        message: "关联合同成功",
        type: "success"
      });
      emit("updateSuccess");
      close();
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const confirmClick = () => {
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      save();
    }
  });
};

defineExpose({
  show
});
</script>
