<!--
 * @Author: <EMAIL>
 * @Date: 2023-10-25 17:42:13
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-11-13 17:10:34
 * @Description: file content
-->
<template>
  <div class="table-box table-height">
    <ProTable :tool-button="false" :request-auto="false" ref="proTable" :columns="columns" :request-api="getTableList">
      <template #effective="{ row }">
        <div class="time-text">
          <span>{{ row.effectTime }}</span> <span> -</span> <span>{{ row.endTime }}</span>
        </div>
      </template>
      <template #skuName="{ row }">
        <div v-html="row.skuName"></div>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance } from "@/components/ProTable/interface";
import { queryCustomerProducts } from "@/api/modules/customerManage";
const props = defineProps<{ id?: string }>();
const proTable = ref<ProTableInstance>();
const columns = [
  { type: "index", label: "序号", width: 80 },
  { prop: "channelName	", label: "下单触点" },
  { prop: "productName", label: "产品名称" },
  { prop: "skuName", width: 240, label: "产品规格" },
  { prop: "status", label: "订单状态" },
  { prop: "parentOrderNo", label: "订单编号" },
  { prop: "count", label: "购买数量" },
  { prop: "buyDuration", label: "购买时长" },
  { prop: "skuPrice", label: "单价（元）" },
  { prop: "effective", label: "有效期" },
  { prop: "productProviderName", label: "产品服务商" },
  { prop: "payFee", label: "订单总金额（元）" }
];
const getTableList = async (params: { current: number; size: number }) => {
  return await queryCustomerProducts({ userId: props.id, ...params });
};

watch(
  () => props.id,
  () => {
    proTable.value?.getTableList();
  }
);

onMounted(() => {});
</script>

<style lang="scss" scoped>
::v-deep(.el-scrollbar) {
  min-height: 120px;
}
</style>
