<template>
  <div class="banner-box table-box">
    <ProTable
      ref="proTable"
      title="用户列表"
      :columns="columns"
      :request-api="getTableList"
      :tool-button="false"
      :request-auto="false"
    >
      <template #status="{ row }">
        <CustomTag
          :type="statusTypeMap[row.status]"
          :status="row.status"
          :label="useDictLabel(audit_status, row.status) || '--'"
        ></CustomTag>
      </template>
      <template #auditAction="{ row }">
        {{ useDictLabel(AUDIT_ACTION, row?.auditAction) }}
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'bannerView'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toEdit(scope.row, OperateType.detail)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'bannerReview'">
          <el-tooltip content="审核" placement="top" v-if="scope.row.status == PENDING_AUDIT">
            <i class="iconfont2 icon-shenhe opera-icon" @click="toEdit(scope.row, OperateType.review)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="bannerList">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
// import { useDicStore } from "@/stores/modules/dictionaty";
import { bannerReviewStorage } from "@/utils/storage/edit";
import ProTable from "@/components/ProTable/index.vue";
import { bannerAuditDetail, bannerAuditList } from "@/api/modules/business/banner";
import { type ProTableInstance, type ColumnProps } from "@/components/ProTable/interface/index";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { OperateType } from "../type";
import { useDict, useDictLabel } from "@/hooks/useDict";
import CustomTag from "@/components/CustomTag/index.vue";

const { AUDIT_ACTION, audit_status } = useDict("AUDIT_ACTION", "audit_status");

const PENDING_AUDIT = 1; // 待审核
const statusTypeMap: { [key: string]: string } = {
  "0": "yellow",
  "2": "green",
  "3": "red"
};

// const dictionaryStore = useDicStore();
const { BUTTONS } = useAuthButtons();

const router = useRouter();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

bannerReviewStorage.clearExpired();

const getTableList = async (search: any) => {
  if (!BUTTONS.value?.bannerQuery) {
    return;
  }
  return await bannerAuditList({
    ...search,
    descs: "createTime",
    bizType: "banner"
  });
};

onMounted(() => {
  proTable.value?.getTableList();
});
// const auditActionDict: any = computed(() => dictionaryStore.bannerAuditAction);
// const statusDict: any = computed(() => dictionaryStore.productAuditStatus);

// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: "bizInfo",
    label: "项目名称",
    align: "left",
    search: { el: "input", props: { maxLength: 10 } }
  },
  {
    prop: "auditAction",
    label: "审核操作",
    align: "left",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {AUDIT_ACTION.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "creatorName",
    label: "提交账号",
    align: "left"
  },
  {
    prop: "createTime",
    label: "提交时间",
    align: "left",
    sortable: true
  },
  {
    prop: "status",
    label: "审核状态",
    align: "left",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {audit_status.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditTime",
    label: "审核时间",
    align: "left",
    width: 180
  },
  {
    prop: "auditorName",
    label: "审核人",
    align: "left"
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 95 }
];

const toEdit = (data: any, type = OperateType.detail) => {
  const auditMsg = data.status != 0 ? data.auditMsg : undefined;

  bannerAuditDetail({ bizId: data.bizId }).then(resp => {
    const respData = resp.data || {};
    bannerReviewStorage.set(data.bizId, { ...respData, listId: data.id });
  });
  router.push({
    path: "/verification/operationsReview/bannerReview",
    query: {
      id: data.bizId,
      type,
      auditMsg
    }
  });
};
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
