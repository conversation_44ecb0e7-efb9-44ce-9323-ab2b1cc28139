<!--账单管理（合作商）-->
<template>
  <div class="table-box">
    <ProTable ref="proTable" title="账单列表" :columns="columns" :tool-button="false" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <!-- <el-button v-auth="'add'" plain :icon="Plus" @click="creatOrder"> 新增 </el-button> -->
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看明细" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>

        <span v-auth="'confirm'">
          <el-tooltip content="确认账单" placement="top" v-if="scope.row.status == '1' || scope.row.status == '4'">
            <i class="iconfont2 icon-shenhe opera-icon" @click="confirmControl(scope.row)"></i>
          </el-tooltip>
        </span>

        <span v-auth="'overruler'">
          <el-tooltip content="驳回" placement="top" v-if="scope.row.status == '1' || scope.row.status == '4'">
            <i class="iconfont2 opera-icon icon-lajitong" @click="rejectControl(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>

    <el-dialog v-model="confirmVisible" title="确认账单" width="800">
      <el-form :disabled="true">
        <el-row>
          <el-col :span="12">
            <el-form-item label="合作商名称">
              <el-input v-model="confirmForm.name" autocomplete="off" />
            </el-form-item>
            <el-form-item label="子商户号">
              <el-input v-model="confirmForm.number" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账单日期">
              <el-input v-model="confirmForm.date" autocomplete="off" />
            </el-form-item>
            <el-form-item label="账单总金额(元)">
              <el-input v-model="confirmForm.totalFee" autocomplete="off" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirm"> 账单确认并申请拨付 </el-button>
          <el-button @click="confirmVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="rejectVisible" title="驳回账单" width="800" :destroy-on-close="true" :before-close="handleClose">
      <el-form ref="form" :rules="rules" :model="confirmForm">
        <el-form-item label="驳回意见" prop="rejectReason" required>
          <el-input :max="50" v-model="confirmForm.rejectReason" autocomplete="off" clearable size="large" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="reject"> 确认 </el-button>
          <el-button @click="confirmVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="partnerBillManage">
import { ref, reactive, onMounted, computed, onActivated } from "vue";
import ProTable from "@/components/ProTable/index.vue";

import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { System } from "@/api/interface/system";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useRouter } from "vue-router";
import { getPartnerBillPageList, billConfirm, billReject, exportPartnerBillList } from "@/api/modules/billManage";
import { getUserProvider } from "@/api/modules/partner";
import { ElMessageBox, FormRules, ElMessage, FormInstance } from "element-plus";
import { useUserStore } from "@/stores/modules/user";
import { UserType } from "@/enums/userInfo";
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const isPartner = computed(() => userStore.userInfo.userType == UserType.partnerUsers);

const { BUTTONS } = useAuthButtons();
// const router = useRouter();
// const userStore = useUserStore();
const form = ref<FormInstance>();
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();
// const ispartner = userStore.isPartner;

const router = useRouter();

const confirmVisible = ref(false);
const rejectVisible = ref(false);
let confirmForm = reactive({
  name: "",
  number: "",
  date: "",
  totalFee: "",
  rejectReason: "",
  id: ""
});

const isInit = ref(false); //是否初次获取合作商名称
const getTableList = async (params: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  if (!isInit.value) {
    getPartnerName();
    isInit.value = true;
  }
  return getPartnerBillPageList(newParams);
};

const getPartnerName = async () => {
  await getUserProvider().then(resp => {
    const data: any = resp.data;
    proTable.value.searchParam.providerName = data.name;
  });
};

// onMounted(() => {
//   getPartnerName();
//   let partnerName = getPartnerName();
// });

const downloadFile = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  delete newParams.createTime;
  ElMessageBox.confirm("确认导出合作商账单列表?", "提示", { type: "warning" }).then(() =>
    useDownload(exportPartnerBillList, newParams)
  );
};

const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "providerName",
    label: "合作商名称",
    search: {
      el: "input",
      props: { disabled: isPartner.value }
      // defaultValue: partnerName.value
    }
  },
  {
    prop: "billDate",
    label: "账单日期",
    search: {
      el: "date-picker",
      props: {
        type: "month",
        // "range-separator": "-",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM"
      }
    }
  },
  { prop: "totalFee", label: "账单金额（元）" },
  {
    prop: "status",
    label: "状态",
    width: 130,
    enum: [
      { label: "账单待确认", value: "1" },
      { label: "拨付审核中", value: "2" },
      { label: "拨付处理中", value: "3" },
      { label: "拨付审核不通过", value: "4" },
      { label: "已拨付", value: "5" },
      { label: "账单驳回", value: "6" },
      { label: "账单已撤回", value: "7" }
    ],
    search: { el: "select" }
  },
  {
    prop: "auditMsg",
    label: "拨付审核意见",
    align: "left"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 200 }
];

const rules = reactive<FormRules>({
  rejectReason: [
    { required: true, message: "请输入驳回意见", trigger: "blur" },
    // { required: true, validator: validatorRejectReason, trigger: "blur" },
    { max: 50, message: "最多输入50个字符", trigger: "blur" }
  ]
});

const toView = async (row: any) => {
  router.push({ path: `/partner/partnerBillManage/billDetail`, query: { id: row.id } });
};

const confirm = async () => {
  await billConfirm(confirmForm.id).then(res => {
    if (res.code === 200) {
      ElMessage.success("账单确认成功");
      confirmVisible.value = false;
      proTable.value?.getTableList();
    }
  });
};

const reject = async () => {
  form.value?.validate().then(() => {
    billReject(confirmForm.id, confirmForm.rejectReason).then(res => {
      if (res.code === 200) {
        ElMessage.success("驳回成功");
        rejectVisible.value = false;
        proTable.value?.getTableList();
      }
    });
    // }
  });
};

const confirmControl = async (row: any) => {
  confirmForm.name = row.providerName;
  confirmForm.number = row.merchantNumber;
  confirmForm.date = row.billDate;
  confirmForm.totalFee = row.totalFee;
  confirmForm.id = row.id;
  confirmVisible.value = true;
};

const rejectControl = async (row: any) => {
  confirmForm.id = row.id;
  rejectVisible.value = true;
  confirmForm.rejectReason = "";
};

const handleClose = () => {
  rejectVisible.value = false;
};
</script>

<style scoped></style>
