export const disabledHours = () => {
  const currentHour = new Date().getHours();
  return Array.from({ length: currentHour }, (_, index) => index);
};
export const disabledMinutes = (hour: any) => {
  if (hour === new Date().getHours()) {
    const currentMinute = new Date().getMinutes();
    return Array.from({ length: currentMinute }, (_, index) => index);
  }
  return [];
};
export const disabledSeconds = (hour: any, minute: any) => {
  if (hour === new Date().getHours() && minute === new Date().getMinutes()) {
    const currentSecond = new Date().getSeconds();
    return Array.from({ length: currentSecond }, (_, index) => index);
  }
  return [];
};

/* 结束时间不能早于开始时间 */
export function validateTimestamps(startTime: string, endTime: string): boolean {
  const timestampRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;

  if (!timestampRegex.test(startTime) || !timestampRegex.test(endTime)) {
    return false;
  }

  const startTimestamp = new Date(startTime);
  const endTimestamp = new Date(endTime);

  return startTimestamp <= endTimestamp;
}
/* 开始时间必须大于当前时间 */
export function compareTime(currentTime: Date, futureTime: Date): boolean {
  if (currentTime < futureTime) {
    return true;
  } else if (currentTime > futureTime) {
    return false;
  } else {
    return false;
  }
}

interface Product {
  skuId: number;
}

export function replaceProducts(target: Product[], source: Product[]): Product[] {
  const map = new Map<number, Product>();

  for (const b of source) {
    map.set(b.skuId, b);
  }

  for (let i = 0; i < target.length; i++) {
    const product = map.get(target[i].skuId);
    if (product) {
      target[i] = product;
    }
  }

  return target;
}
