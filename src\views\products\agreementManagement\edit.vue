<template>
  <div class="card content-box">
    <div class="edit-title">{{ route.meta.title }}</div>
    <el-form ref="ruleFormRef" :model="ruleForm" label-position="left" :rules="rules" label-width="150px">
      <el-form-item label="产品服务协议名称" prop="protocolTitle" placeholder="请入产品服务协议名称" required>
        <el-input v-model="ruleForm.protocolTitle" :max="50" />
      </el-form-item>
      <el-form-item label="关联产品" prop="productIds">
        <el-button type="primary" @click="selectProduct()"> 选择产品 </el-button>
        <product-table v-model:productIdList="ruleForm.productIds"></product-table>
      </el-form-item>
      <el-form-item label="协议内容" prop="content" placeholder="请输入内容" required>
        <WangEditor v-model:value="ruleForm.content" height="200px" />
      </el-form-item>
      <!-- <div v-if="!disabled"> -->
      <el-button type="primary" @click="submitForm(ruleFormRef)"> 确定 </el-button>
      <!-- <el-button type="primary" @click="submitForm(ruleFormRef, true)"> 提交发布</el-button> -->
      <el-button @click="goBack()"> 取消 </el-button>
      <!-- </div> -->
    </el-form>
    <ProductDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="ts" name="productAgreementEdit">
import { reactive, ref, onMounted, onBeforeMount } from "vue";
import { FormInstance, FormRules } from "element-plus";
import WangEditor from "@/components/WangEditor/index.vue";
import { useRoute, useRouter } from "vue-router";
import { productAgreementQuery, productAgreementEdit, productAgreementAdd } from "@/api/modules/product";
import { closeTabItem } from "@/utils/closeTab";
import ProductDrawer from "./components/productDrawer.vue";
import ProductTable from "./components/productTable.vue";
import { useProductAgreementStore } from "@/stores/modules/productAgreement";
import { productAgreementStorage } from "@/utils/storage/edit";

const drawerRef = ref<InstanceType<typeof ProductDrawer> | null>(null);
const route = useRoute();
const productStore = useProductAgreementStore();
const id: string = (route.query.id as string) || "";
const type: string = (route.query.type as string) || "";
const protocolTitle: string = (route.query.protocolTitle as string) || "";
const productIds: Array<string> = (route.query.productIds as Array<string>) || "";
const content: string = (route.query.content as string) || "";
// const data: any = (route.query.data as any) || "";

// productAreaStorage.clearExpired();
const getInitForm = () => {
  if (type === "edit") {
    // console.log("edi11111111t");
    // console.log(id);
    return {
      protocolTitle: protocolTitle,
      productIds: productIds,
      content: content
    };
  } else {
    // console.log("init is empty");
    return {
      protocolTitle: "",
      productIds: [],
      content: ""
    };
  }
};
const router = useRouter();

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive(getInitForm());

const validateProductIds = (rule: any, value: any, callback: any) => {
  if (!productStore.productDetailList.length) {
    callback(new Error("产品不能为空"));
  } else if (productStore.productDetailList.length > 1) {
    callback(new Error("关联产品不能超过1个"));
  }
  callback();
};

const rules = reactive<FormRules>({
  protocolTitle: [
    { required: true, message: "请输入产品服务协议名称", trigger: "blur" },
    { max: 30, message: "最多输入30个字符", trigger: "blur" }
  ],
  productIds: [{ validator: validateProductIds, trigger: "blur" }],
  content: [{ required: true, message: "请输入消息内容", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;

  if (type === "edit") {
    const data = { ...ruleForm, productIds: productStore.productDetailList.map((item: any) => item.id), id: id };

    await formEl.validate(async (valid, fields) => {
      // data.productIds = JSON.stringify(data.productIds);
      if (valid) {
        await productAgreementEdit(data);
        goBack();
      }
    });
  } else {
    const data = { ...ruleForm, productIds: productStore.productDetailList.map((item: any) => item.id) };
    await formEl.validate(async valid => {
      if (valid) {
        await productAgreementAdd(data);
        goBack();
      }
    });
  }
};

const selectProduct = () => {
  if (drawerRef.value) {
    drawerRef.value.visible = true;
  }
};
// productAreaStorage.clearExpired();
const goBack = () => {
  productAgreementStorage.clearExpired();
  closeTabItem(route);
  router.push("/productManage/agreementManagement");
};
</script>

<style scoped lang="scss">
.content-box {
  align-items: start;
}
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
</style>
