<template>
  <div class="card">
    <div class="header-title">审核内容</div>
    <banner-content :id="id" />

    <Examine
      :verification-id="'id'"
      :audit-msg="reviewForm.auditMsg"
      :is-disabled="viewStatus"
      :is-show-operate-btn="!viewStatus"
      :is-show-backed-btn="false"
      @on-confirm="onConfirm"
    >
    </Examine>
  </div>
</template>

<script setup lang="ts" name="bannerReview">
import { reactive } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { closeTabItem } from "@/utils/closeTab";
import { backtoOperationList, OperateType } from "../type";
import bannerContent from "./bannerContent.vue";
import Examine from "@/views/verification/examine.vue";
import { bannerDoAudit } from "@/api/modules/business/banner";
import { bannerReviewStorage } from "@/utils/storage/edit";

const route = useRoute();
const id: string = (route.query.id as string) || "";
const type = (route.query.type as string) || OperateType.detail;
const auditMsg = (route.query.auditMsg as string) || "";
const viewStatus = type == OperateType.detail;

const reviewForm = reactive({
  auditMsg: auditMsg
});

const onConfirm = (values: any) => {
  const banner = bannerReviewStorage.get(id);
  bannerDoAudit({
    id: banner?.listId,
    auditMsg: values.auditRemark,
    status: values.type == "pass" ? 2 : 3,
    bizType: "banner"
  }).then(res => {
    if (res.code === 200) {
      ElMessage({
        message: values.type == "pass" ? "审核通过成功!" : "审核不通过完成!",
        type: "success"
      });
      closeTabItem(route);
      backtoOperationList();
    } else {
      ElMessage({
        type: "error",
        message: res.msg
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
</style>
