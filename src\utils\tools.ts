/**
 *  手机号打码
 */
export function formatPhone(value: any) {
  if (!value) return "";
  let str = value;
  str = str.toString().replace(/^(\d{3})(\d{4})(\d{4})/g, "$1****$3");
  return str;
}

/**
 *  身份证打码
 */
export function formatIDcard(value: any) {
  if (!value) return "";
  let str = value;
  str = str.toString().replace(/^(.{6})(?:\w+)(.{4})$/, "$1********$2");
  return str;
}

/**
 *  邮箱打码
 */
export function formatEmail(email: any) {
  if (!email) return "";
  if (String(email).indexOf("@") > 0) {
    let str = email.split("@"),
      _s = "";
    if (str[0].length > 3) {
      for (let i = 0; i < str[0].length - 3; i++) {
        _s += "*";
      }
    }
    let new_email = str[0].substr(0, 3) + _s + "@" + str[1];
    return new_email;
  }
}

/**
 * 将数字进行千分位格式化
 * @param num
 * @returns {string}
 */
export function toThousandFilter(num: any) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ","));
}
