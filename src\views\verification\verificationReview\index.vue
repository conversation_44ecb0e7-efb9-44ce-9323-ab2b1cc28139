<template>
  <div class="table-box">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" style="background-color: #ffffff">
      <el-tab-pane label="个人认证" name="personal"> </el-tab-pane>
      <el-tab-pane label="企业认证" name="enterprise"> </el-tab-pane>
    </el-tabs>

    <div v-show="activeName === 'personal'">
      <ProTable
        ref="personalTable"
        title="个人认证列表"
        :request-auto="false"
        :tool-button="false"
        :columns="tableColumnsPerson"
        :request-api="getTableList"
        @sort-change="sortChange"
      >
        <template #applyType="{ row }">
          {{ useDictLabel(dict_certificate_apply_type, row?.applyType) }}
        </template>
        <template #auditStatus="{ row }">
          <CustomTag
            :type="'audit'"
            :status="row.auditStatus"
            :label="useDictLabel(dict_audit_status, row.auditStatus)"
          ></CustomTag>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span>
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, editType.details)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'personalReview'">
            <el-tooltip content="审核" placement="top" v-if="scope.row.auditStatus == AuditStatus.inReview">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, editType.audit)"></i>
            </el-tooltip>
          </span>
        </template>
      </ProTable>
    </div>

    <div v-show="activeName === 'enterprise'">
      <ProTable
        ref="enterpriseTable"
        title="企业认证列表"
        :request-auto="false"
        :columns="tableColumnsEnterprise"
        :request-api="getEnterpriseInfo"
        :tool-button="false"
      >
        <template #applyType="{ row }">
          {{ useDictLabel(dict_certificate_apply_type, row?.applyType) }}
        </template>
        <template #auditStatus="{ row }">
          <CustomTag
            :type="'audit'"
            :status="row.auditStatus"
            :label="useDictLabel(dict_audit_status, row.auditStatus)"
          ></CustomTag>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span>
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, editType.details)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'enterpriseReview'">
            <el-tooltip content="审核" placement="top" v-if="scope.row.auditStatus == AuditStatus.inReview">
              <i class="iconfont2 opera-icon icon-shenhe" @click="toView(scope.row, editType.audit)"></i>
            </el-tooltip>
          </span>
        </template>
      </ProTable>
    </div>
  </div>
</template>

<script setup lang="tsx" name="verificationReview">
import { ref, onMounted, computed } from "vue";
import type { TabsPaneContext } from "element-plus";
import { useRouter } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDict, useDictLabel } from "@/hooks/useDict";
import CustomTag from "@/components/CustomTag/index.vue";
import { getPersonalCertificate, getEnterpriseCertificate } from "@/api/modules/verification";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { editType } from "@/enums/pageTypeEnum";

const { BUTTONS } = useAuthButtons();

enum AuditStatus {
  inReview = 1, // 1审核中
  failed = 2, // 2审核不通过
  passed = 3 // 3审核通过
}
const sortType = {
  descending: "descs",
  ascending: "ascs"
};
const sortCreateTime = ref(sortType.descending);

const { dict_certificate_apply_type, dict_audit_status } = useDict("dict_certificate_apply_type", "dict_audit_status");

const router = useRouter();

type TabsActiveType = "personal" | "enterprise";
const activeName = ref<TabsActiveType>("personal");

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const personalTable = ref<ProTableInstance>();
const enterpriseTable = ref<ProTableInstance>();

onMounted(() => {
  personalTable.value?.getTableList();
});

const sortChange = (e: any) => {
  if (e.order === "descending") {
    sortCreateTime.value = sortType.descending;
  } else if (e.order === "ascending") {
    sortCreateTime.value = sortType.ascending;
  } else {
    sortCreateTime.value = e.order;
  }
  personalTable.value?.getTableList();
};

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.personalQuery) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  return getPersonalCertificate(newParams);
};

const getEnterpriseInfo = (params: any = {}) => {
  if (!BUTTONS.value?.enterpriseQuery) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  return getEnterpriseCertificate(newParams);
};

// 表格配置项
const tableColumnsPerson: ColumnProps<any>[] = [
  { prop: "realName", label: "认证用户", search: { el: "input" } },
  { prop: "phone", label: "手机号码" },
  {
    prop: "applyType",
    label: "审核操作",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {dict_certificate_apply_type.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "creatorName",
    label: "提交账号"
  },
  {
    prop: "createTime",
    label: "提交时间",
    width: 170,
    sortable: true
  },
  {
    prop: "auditStatus",
    label: "状态",
    width: 120,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {dict_audit_status.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditTime",
    label: "审核时间",
    width: 170
  },
  { prop: "auditUserName", label: "审核人员", width: 180 },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];
const tableColumnsEnterprise: ColumnProps<any>[] = [
  { prop: "companyName", label: "认证企业", search: { el: "input" } },
  { prop: "usciCode", label: "统一社会信用代码" },
  {
    prop: "applyType",
    label: "审核操作",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {dict_certificate_apply_type.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "creatorName",
    label: "提交账号"
  },
  {
    prop: "createTime",
    label: "提交时间",
    width: 170,
    sortable: true
  },
  {
    prop: "auditStatus",
    label: "状态",
    width: 120,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {dict_audit_status.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditTime",
    label: "审核时间",
    width: 170
  },
  { prop: "auditUserName", label: "审核人员", width: 180 },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const handleClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name as TabsActiveType;
  if (tab.props.name === "personal") {
    personalTable.value?.getTableList();
  } else {
    enterpriseTable.value?.getTableList();
  }
};

const toView = async (row: any, type = editType.details) => {
  const toPath =
    activeName.value == "personal"
      ? "/verification/verificationReview/personReview"
      : "/verification/verificationReview/busiReview";
  router.push({
    path: toPath,
    query: {
      id: row.id,
      auditTag: type
    }
  });
};
</script>

<style lang="scss" scoped>
::v-deep(.el-tabs__item) {
  width: 100px !important;

  // left: 52px !important;
}
</style>
