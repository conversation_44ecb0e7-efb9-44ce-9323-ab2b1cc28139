<template>
  <el-form
    ref="threeFormRef"
    :model="threeForm"
    :rules="rules"
    label-width="122px"
    label-position="left"
    :validate-on-rule-change="false"
  >
    <el-form-item label="规格类型" prop="skus">
      <el-radio-group v-model="threeForm.skuType" :disabled="viewStatus" @change="changeSkuType">
        <el-radio v-for="(item, index) in skuTypeDic" :key="index + 'jumpType'" :label="item.itemKey">
          {{ item.itemValue }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <!-- <el-form-item label="规格类型" prop="skus">
      <el-radio-group v-model="threeForm.skuType" :disabled="viewStatus">
        <el-radio :label="productSkuTypeEnum['应用类产品']" size="large">应用类产品</el-radio>
        <el-radio :label="productSkuTypeEnum['计算类产品']" size="large">计算类产品</el-radio>
      </el-radio-group>
    </el-form-item> -->
    <el-form-item label="产品规格" prop="skus" v-if="threeForm.skuType === productSkuTypeEnum['应用类产品']">
      <specification-content
        ref="skuRef"
        prefixion="产品"
        :view-control="viewStatus"
        :product-id="productId"
        :pay-type="passdata?.payType"
        :is-gift="isGift"
        v-model:dataList="threeForm.skus"
        @add-item="addSpecification"
        @delete-item="deleteSpecification"
      >
        <template #nameTips>最多可添加10个</template>
      </specification-content>
    </el-form-item>

    <el-form-item
      label="规格价格"
      prop="price"
      v-if="threeForm.skuType === productSkuTypeEnum['计算类产品'] || threeForm.skuType === productSkuTypeEnum['云主机产品']"
    >
      <el-input
        v-model="threeForm.price"
        type="number"
        :disabled="viewStatus"
        maxlength="20"
        placeholder="请输入"
        show-word-limit
      />
    </el-form-item>
    <el-form-item label="产品属性" prop="productAttr" v-if="threeForm.skuType === productSkuTypeEnum['计算类产品']">
      <product-attr ref="productAttrRef" v-model:productAttr="initProductData" :disabled="viewStatus"></product-attr>
    </el-form-item>
    <el-form-item
      label="产品资费说明"
      v-if="threeForm.skuType === productSkuTypeEnum['计算类产品'] || threeForm.skuType === productSkuTypeEnum['应用类产品']"
    >
      <el-input
        v-model="threeForm.expensesDesc"
        type="textarea"
        :disabled="viewStatus"
        maxlength="500"
        placeholder="请输入"
        show-word-limit
      />
    </el-form-item>
    <template v-if="threeForm.skuType === productSkuTypeEnum['外部对接类产品']">
      <el-form-item label="产品规格" prop="skus">
        <specification-content
          ref="externalSkuRef"
          prefixion="产品"
          :view-control="viewStatus"
          :product-id="productId"
          :pay-type="passdata?.payType"
          :is-gift="isGift"
          v-model:dataList="threeForm.skus"
          :is-external="true"
          @add-item="addSpecification"
          @delete-item="deleteSpecification"
        >
          <template #nameTips>最多可添加10个</template>
        </specification-content>
      </el-form-item>

      <!--
      2024-10-25 修改：外部对接类产品
       <el-form-item
        label="是否支持子账号"
        class="jf-item"
        prop="skus[0].isSubAccountSharing"
        :rules="[{ required: true, message: '请选择是否支持子账号', trigger: 'blur' }]"
      >
        <el-radio-group v-model="externalProduct.isSubAccountSharing" :disabled="viewStatus">
          <el-radio :label="true"> 是 </el-radio>
          <el-radio :label="false"> 否 </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" class="jf-item" v-if="externalProduct.isSubAccountSharing === true" prop="subAccountQuantity">
        <div class="jf-box" v-if="externalProduct.isSubAccountSharing === true">
          <span class="jf-label">子账号数：</span>
          <el-input
            v-d-input-int
            v-d-input-max="100"
            style="width: 80px; margin-right: 20px"
            v-model="externalProduct.subAccountQuantity"
            :disabled="viewStatus"
            placeholder="请输入"
          />
          <span>个</span>
        </div>
      </el-form-item> -->
    </template>
  </el-form>
  <div class="step-btn" v-if="isShowOperateBtn">
    <el-button plain @click="backtoList"> 返回 </el-button>
    <el-button plain @click="backto"> 上一步 </el-button>
    <el-button type="primary" v-if="!viewStatus" @click="onUpdate"> 提交 </el-button>
  </div>
  <applyModal ref="applyModalRef" @on-submit="handleSubmit" />
</template>

<script setup lang="ts" name="stepThree">
import { nextTick, onMounted, reactive, ref, computed } from "vue";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import specificationContent from "./specificationContent.vue";
import externalDockingContent from "./externalDockingContent.vue";
import { useProductStore } from "@/stores/modules/product";
import { productSave, productEdit } from "@/api/modules/product";
import { productSkuType as productSkuTypeEnum } from "@/enums/productEnum";
import productAttr from "./productAttr.vue";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { useDicStore } from "@/stores/modules/dictionaty";
import applyModal from "./applyModal.vue";
import ExternalDocking from "../validator";

const props = defineProps({
  passdata: {
    type: Object,
    default: () => {}
  },
  pageType: {
    type: String,
    default: ""
  },
  viewStatus: {
    type: Boolean,
    default: false
  },
  isShowOperateBtn: {
    type: Boolean,
    default: () => true
  },
  isAudit: {
    type: Boolean,
    default: () => false // 产品审核页面id是通过query传递的
  }
});

const applyModalRef = ref();
const dictionaryStore = useDicStore();
let emits = defineEmits(["getStepValue"]);
const skuTypeDic = computed(() => dictionaryStore.skuTypeDic);
const productStore = useProductStore();
const router = useRouter();
const route = useRoute();
const id = props.isAudit ? (route.query.id as string) : (route.params.id as string);
const productId = props.pageType !== "add" ? id : "add_product";
// const isPhonePay = props.passType !== "add" ? props.passdata.payType.indexOf("1") != -1 : false;
// console.log("111111111111111111");
// console.log(isPhonePay);
// console.log(props.passdata.payType.indexOf("1") != -1 ? true : false);

const threeFormRef = ref<FormInstance>();
const skuRef = ref();
const externalSkuRef = ref();
const productAttrRef = ref();
const initProductData = ref("[]");
const threeForm: any = reactive({
  skuType: productSkuTypeEnum["应用类产品"],
  skus: [
    {
      skuName: "",
      skuConfig: "",
      skuPrice: "",
      skuDiscountPrice: undefined,
      phoneBillPayCode: "",
      providerSkuId: "",
      creatJiFeiList: [],
      minQuantity: "",
      maxQuantity: "",
      quantityStep: "",
      skuUnit: "",
      minTime: "",
      maxTime: "",
      timeStep: "",
      timeUnit: "",
      probationFlag: "",
      probationDate: "7",
      probationTime: "",
      skuContent: "",
      isSubAccountSharing: undefined,
      subAccountQuantity: undefined
    }
  ],
  productAttr: "",
  expensesDesc: "",
  price: "",
  id: ""
});
const externalProduct = computed(() => threeForm.skus[0] || [{ isSubAccountSharing: undefined, subAccountQuantity: undefined }]);

const MAIN_PRODUCT = "1";
const isGift = computed(() => productStore.sendProductDetailParam?.[productId]?.productType !== MAIN_PRODUCT);

const checkProductAttr = (rule: any, value: any) => {
  if (!value || JSON.parse(value).length === 0) {
    return false;
  }
  return true;
};
const rules = computed(() => {
  const rulesObject: any = {
    [productSkuTypeEnum["应用类产品"]]: {
      skus: [{ required: true, message: "请添加产品规格", trigger: "blur" }]
    },
    [productSkuTypeEnum["计算类产品"]]: {
      productAttr: [
        { required: true, message: "请添加产品规格", trigger: "blur" },
        { validator: checkProductAttr, trigger: "blur" }
      ],
      price: [{ required: true, message: "请添加规格价格", trigger: "blur" }]
    },
    [productSkuTypeEnum["外部对接类产品"]]: {
      skus: [{ required: true, message: "请添加产品规格", trigger: "blur" }]
    }
  };
  return rulesObject[threeForm.skuType];
});
const changeSkuType = () => {
  threeForm.price = "0";
};
// const rules = reactive<FormRules>({

// });

onMounted(() => {
  if (props.pageType !== "add") {
    // 等待各组件先加载完，再赋值：
    nextTick(() => {
      // 外部对接类产品处理没有返回skus数据的情况
      const flag = props.passdata.skuType === productSkuTypeEnum["外部对接类产品"] && !(props.passdata.skus?.length > 0);
      // 编辑和查看回显处理：
      Object.assign(threeForm, {
        skus: flag ? threeForm.skus : props.passdata.skus,
        expensesDesc: props.passdata.expensesDesc,
        skuType: props.passdata.skuType,
        price: props.passdata.price,
        productAttr: props.passdata.productAttr
      });
      initProductData.value = props.passdata.productAttr;
      if (props.passdata.skuType === productSkuTypeEnum["外部对接类产品"]) {
        Object.assign(threeForm, {
          skus: props.passdata.skus?.length > 0 ? props.passdata.skus : threeForm.skus
        });
      }
      // 处理可选计费项
      threeForm.skus.forEach((item: any = {}) => {
        let arr = [];
        if (item.quantityCounting) {
          arr.push("quantityCounting");
          // item.creatJiFeiList.push("quantityCounting");
        }
        if (item.timeCounting) {
          arr.push("timeCounting");
        }
        item.creatJiFeiList = arr;
      });
    });
  }
});

const applicationMsg = ref("");
function saveData(result: any) {
  productStore.setProductDetail(productId, threeForm);
  const apiHandle: any = {
    add: productSave,
    edit: productEdit
  };

  const params: any = productStore.sendProductDetailParam?.[productId];
  if (threeForm.skuType != productSkuTypeEnum["应用类产品"] && threeForm.skuType != productSkuTypeEnum["外部对接类产品"]) {
    delete params.skus;
  } else if (threeForm.skuType != productSkuTypeEnum["计算类产品"]) {
    delete params.productAttr;
  }
  if (props.pageType === "edit") {
    params.applicationMessage = applicationMsg.value;
  }
  apiHandle[props.pageType](params)
    .then((res: any) => {
      ElMessage.success(res.msg);
      closeTabItem();
      router.push("/productManage/list");
    })
    .catch((err: any) => {
      console.log(err);
    });
}
// 关闭tabs导航
function closeTabItem() {
  const tabStore = useTabsStore();
  const keepAliveStore = useKeepAliveStore();
  if (route.meta.isAffix) return;
  tabStore.removeTabs(route.fullPath);
  keepAliveStore.removeKeepAliveName(route.name as string);
}

const onUpdate = () => {
  if (props.pageType === "edit" && props.passdata.publishStatus === 2) {
    applyModalRef.value.handleOpen("update", {});
  } else {
    submitForm(threeFormRef.value);
  }
};
const handleSubmit = (values: any) => {
  const description: string = values.applicationMessage;
  applicationMsg.value = description;
  submitForm(threeFormRef.value);
};
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  if (threeForm.skuType === productSkuTypeEnum["计算类产品"]) {
    const result = productAttrRef.value.submitData;
    threeForm.productAttr = JSON.stringify(result);
  }

  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      // rule表单校验通过后，还要进行产品规格里的内容校验：
      let threeFormCanFlag = true;
      // 编辑加上id
      if (props.pageType === "edit") {
        threeForm.id = productId;
      }
      const params: any = productStore.sendProductDetailParam?.[productId];
      if (threeForm.skuType === productSkuTypeEnum["应用类产品"]) {
        // 处理第三步的threeForm传值
        // let cbId: string | any[] = [];
        if (params?.payType?.includes("1")) {
          let cbId: string[] = reactive([]);
          threeForm.skus.forEach((item: any = {}) => {
            if (cbId.indexOf(item.phoneBillPayCode) != -1) {
              ElMessage.error("同一个CBSS产品ID不能重复使用，请重新填写ID");
              threeFormCanFlag = false;
              return;
            } else {
              cbId.push(item.phoneBillPayCode);
            }
          });
        }
        threeForm.skus.forEach((item: any = {}) => {
          item.quantityCounting = item.creatJiFeiList?.includes("quantityCounting");
          item.timeCounting = item.creatJiFeiList?.includes("timeCounting");
          if (item.probationFlag === "1") {
            if (!item.probationDate) {
              ElMessage.error("请输入试用有效期和使用次数限制");
              threeFormCanFlag = false;
              return;
            }
          }
          // 产品为赠品规格赠品属性传空
          if (params.productType !== MAIN_PRODUCT) {
            item.giftSkuId = [];
            item.skuGiftList = [];
          }
          // 试用次数限制为空时传入null,而不是空字符串
          if (item.probationTime === "" || item.probationTime === null) {
            item.probationTime = null;
          } else {
            item.probationTime = Number(item.probationTime);
          }
        });
        // 处理可选计费项
        threeForm.skus.forEach((item: any = {}) => {
          item.quantityCounting = item.creatJiFeiList?.includes("quantityCounting");
          item.timeCounting = item.creatJiFeiList?.includes("timeCounting");
          if (item.quantityCounting) {
            if (item.minQuantity && item.maxQuantity && parseFloat(item.minQuantity) > parseFloat(item.maxQuantity)) {
              ElMessage.error("购买数量开始数值不能大于结束数值，请重新设置");
              threeFormCanFlag = false;
              return;
            }
            if (!item.minQuantity) {
              ElMessage.error("请输入购买数量开始数值");
              threeFormCanFlag = false;
              return;
            }
            if (!item.maxQuantity) {
              ElMessage.error("请输入购买数量结束数值");
              threeFormCanFlag = false;
              return;
            }
            if (!item.quantityStep) {
              ElMessage.error("请输入数量步长");
              threeFormCanFlag = false;
              return;
            }
            if (!item.skuUnit) {
              ElMessage.error("请选择数量单位");
              threeFormCanFlag = false;
              return;
            }
          }
          if (item.timeCounting) {
            if (item.minTime && item.maxTime && parseFloat(item.minTime) > parseFloat(item.maxTime)) {
              ElMessage.error("购买时长开始数值不能大于结束数值，请重新设置");
              threeFormCanFlag = false;
              return;
            }
            if (!item.minTime) {
              ElMessage.error("请输入购买时长开始数值");
              threeFormCanFlag = false;
              return;
            }
            if (!item.maxTime) {
              ElMessage.error("请输入购买时长结束数值");
              threeFormCanFlag = false;
              return;
            }
            if (!item.timeStep) {
              ElMessage.error("请输入时长步长");
              threeFormCanFlag = false;
              return;
            }
            if (!item.timeUnit) {
              ElMessage.error("请选择时长单位");
              threeFormCanFlag = false;
              return;
            }
          }
          if (item.isSubAccountSharing === true) {
            if (!item.subAccountQuantity) {
              ElMessage.error("请输入子账号数");
              threeFormCanFlag = false;
              return;
            }
          } else {
            item.subAccountQuantity = null;
          }

          if (item.lifeSpan) {
            if (!item.lifeSpanUnit) {
              ElMessage.error("请选择使用有效期单位");
              threeFormCanFlag = false;
              return;
            }
          }
          if (item.lifeSpanUnit) {
            if (!item.lifeSpan) {
              ElMessage.error("请选择使用有效期");
              threeFormCanFlag = false;
              return;
            }
          }

          if (!params?.payType?.includes("1")) {
            Reflect.set(item, "phoneBillPayCode", "");
          }
        });

        if (threeFormCanFlag) {
          let skuPromise = skuRef.value?.ruleValidatePromise();
          Promise.all([...skuPromise])
            .then(result => {
              saveData(result);
            })
            .catch(error => {
              console.log(error);
            });
        }
      } else if (threeForm.skuType === productSkuTypeEnum["计算类产品"]) {
        if (threeFormCanFlag) {
          const promiseItem = productAttrRef.value.checkFormField();
          promiseItem
            .then((res: any) => {
              // // threeForm.skus = [];
              // delete threeForm.skus;
              saveData(res);
            })
            .catch((error: any) => {
              console.log(error);
            });
        }
      } else if (
        threeForm.skuType === productSkuTypeEnum["外部对接类产品"] ||
        threeForm.skuType === productSkuTypeEnum["云主机产品"]
      ) {
        if (threeForm.skuType === productSkuTypeEnum["外部对接类产品"]) {
          // 是否支持子账号
          let flag = false;

          threeForm.skus = threeForm.skus?.map((item: any = {}, index: number) => {
            if (item.isSubAccountSharing === true) {
              if (!item.subAccountQuantity) {
                ElMessage.error("请输入子账号数");
                threeFormCanFlag = false;
                return;
              }
            } else {
              item.subAccountQuantity = null;
            }

            // 产品为赠品规格赠品属性传空
            if (params.productType !== MAIN_PRODUCT) {
              item.giftSkuId = [];
              item.skuGiftList = [];
            }

            // 处理可选计费项
            item.quantityCounting = item.creatJiFeiList?.includes("quantityCounting");
            item.timeCounting = item.creatJiFeiList?.includes("timeCounting");
            if (item.quantityCounting) {
              if (item.minQuantity && item.maxQuantity && parseFloat(item.minQuantity) > parseFloat(item.maxQuantity)) {
                ElMessage.error("购买数量开始数值不能大于结束数值，请重新设置");
                threeFormCanFlag = false;
                return;
              }
              if (!item.minQuantity) {
                ElMessage.error("请输入购买数量开始数值");
                threeFormCanFlag = false;
                return;
              }
              if (!item.maxQuantity) {
                ElMessage.error("请输入购买数量结束数值");
                threeFormCanFlag = false;
                return;
              }
              if (!item.quantityStep) {
                ElMessage.error("请输入数量步长");
                threeFormCanFlag = false;
                return;
              }
              if (!item.skuUnit) {
                ElMessage.error("请选择数量单位");
                threeFormCanFlag = false;
                return;
              }
            }
            if (item.timeCounting) {
              if (item.minTime && item.maxTime && parseFloat(item.minTime) > parseFloat(item.maxTime)) {
                ElMessage.error("购买时长开始数值不能大于结束数值，请重新设置");
                threeFormCanFlag = false;
                return;
              }
              if (!item.minTime) {
                ElMessage.error("请输入购买时长开始数值");
                threeFormCanFlag = false;
                return;
              }
              if (!item.maxTime) {
                ElMessage.error("请输入购买时长结束数值");
                threeFormCanFlag = false;
                return;
              }
              if (!item.timeStep) {
                ElMessage.error("请输入时长步长");
                threeFormCanFlag = false;
                return;
              }
              if (!item.timeUnit) {
                ElMessage.error("请选择时长单位");
                threeFormCanFlag = false;
                return;
              }
            }

            if (item.lifeSpan) {
              if (!item.lifeSpanUnit) {
                ElMessage.error("请选择使用有效期单位");
                threeFormCanFlag = false;
                return;
              }
            }
            if (item.lifeSpanUnit) {
              if (!item.lifeSpan) {
                ElMessage.error("请选择使用有效期");
                threeFormCanFlag = false;
                return;
              }
            }
            const itemExternal = new ExternalDocking(item);

            return {
              skuName: "外部对接类产品",
              skuPrice: 1,
              ...itemExternal
            };
          });

          if (threeFormCanFlag) {
            let skuPromise = externalSkuRef.value?.ruleValidatePromise();
            Promise.all([...skuPromise])
              .then(result => {
                saveData(result);
              })
              .catch(error => {
                console.log(error);
              });
          }
        }
      }
    } else {
      let obj = Object.keys(fields);
      formEl.scrollToField(obj[0]);
      console.log("error submit!", fields);
    }
  });
};

const addSpecification = () => {
  if (threeForm.skus.length >= 10) {
    ElMessage.error("最多添加十个产品规格");
    return;
  }
  threeForm.skus.push({
    skuName: "",
    skuConfig: "",
    skuPrice: "",
    skuDiscountPrice: undefined,
    phoneBillPayCode: "",
    providerSkuId: "",
    creatJiFeiList: [],
    minQuantity: "",
    maxQuantity: "",
    quantityStep: "",
    skuUnit: "",
    minTime: "",
    maxTime: "",
    timeStep: "",
    timeUnit: "",
    probationFlag: "",
    probationDate: "7",
    probationTime: "",
    skuContent: "",
    giftSkuId: []
  });
};
const deleteSpecification = (index: number) => {
  if (threeForm.skus.length < 2) {
    ElMessage.error("最少需添加一个产品规格");
    return;
  }
  threeForm.skus.splice(index, 1);
};

const backto = () => {
  emits("getStepValue", 2);
};
const backtoList = () => {
  router.push("/productManage/list");
};
</script>

<style scoped lang="scss">
.el-form {
  width: 90%;
  .text-center {
    text-align: center;
  }
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
.step-btn {
  margin-top: 30px;
  margin-bottom: 10px;
  text-align: left;
  :deep(.el-button) {
    height: 36px;
    margin-right: 16px;
  }
}
</style>
