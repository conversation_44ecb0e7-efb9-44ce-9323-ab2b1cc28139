<template>
  <BaseDrawer ref="drawerRef" size="75%" @sure-callback="sureCallback">
    <step :step-value="activeStep"></step>
    <div v-show="activeStep === 1">
      <pro-table
        ref="proTable"
        title="产品列表"
        :columns="columns"
        :request-api="getTableList"
        :request-auto="false"
        :tool-button="false"
      ></pro-table>
      <div class="step-btn">
        <el-button type="primary" @click="selectProduct" :disabled="!proTable?.selectedList.length > 0"> 下一步 </el-button>
      </div>
    </div>
    <div v-show="activeStep === 2">
      <div class="sku-box">
        <div class="sku-item title">{{ selectedProDetail?.productName }}</div>
        <div class="sku-item des">
          {{ selectedProDetail?.description }}
        </div>
        <div class="sku-item">
          <span class="info-label">总计</span>
          <span class="info-text price">¥&nbsp;{{ unitPrice * (quantityNum || 1) }}</span>
        </div>
        <div class="sku-item">
          <span class="info-label">产品服务商</span>
          <span class="info-text">{{ selectedProDetail?.provider }}</span>
        </div>
        <div class="sku-item">
          <span class="info-label">交付方式</span>
          <span class="info-text">{{ deliveryName || "-" }}</span>
        </div>
        <div class="sku-item">
          <span class="info-label">产品规格</span>
          <el-radio-group v-model="selectedSize" @change="selectSizeChange">
            <el-radio v-for="(item, index) in sizeList" :key="index + 'size'" :label="item.id" border>
              {{ item.skuName }}
            </el-radio>
          </el-radio-group>
        </div>
        <div class="sku-item" v-if="hasQuantity">
          <span class="info-label">购买数量</span>
          <el-input-number
            v-model="quantityNum"
            :step="quantityStep"
            :min="minQuantity"
            :max="maxQuantity"
            step-strictly
          ></el-input-number>
          <span class="unit">{{ quantityFilter[skuUnit] }}</span>
        </div>
        <div class="sku-item" v-if="hasTime">
          <span class="info-label">购买时长</span>
          <el-input-number v-model="timeNum" :step="timeStep" :min="minTime" :max="maxTime" step-strictly></el-input-number>
          <span class="unit">{{ timeFilter[timeUnit] }}</span>
        </div>
        <div class="sku-item">
          <span class="info-label">支付方式</span>
          <el-radio-group v-model="payType">
            <el-radio v-for="(item, index) in payDicLocal" :key="index + 'payDic'" :label="item.itemKey">
              {{ item.itemValue }}
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="step-btn">
        <el-button plain @click="backOne"> 上一步 </el-button>
        <el-button type="primary" @click="sureSize"> 确认 </el-button>
      </div>
    </div>
  </BaseDrawer>
</template>

<script setup lang="tsx" name="stepOne">
import { ref, nextTick, onMounted, computed } from "vue";
import BaseDrawer from "@/components/BaseDrawer/index.vue";
import { ElMessage } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getProductList, productDetail } from "@/api/modules/product";

import { productStatus } from "@/utils/serviceDict";
import step from "./step.vue";
import { Product } from "@/api/interface/product";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useProductStore } from "@/stores/modules/product";
import { getDictionary } from "@/api/modules/system";

let emits = defineEmits(["selectedProductInfo"]);

const dictionaryStore = useDicStore();
const productStore = useProductStore();
const drawerRef = ref();
const proTable = ref<ProTableInstance>();
const activeStep = ref(1);
const selectedProDetail = ref({});
const deliveryName = ref("");
const sizeList = ref([]);
const selectedSize = ref("");
const selectedSizeName = ref("");
const hasQuantity = ref();
const hasTime = ref();
const quantityNum = ref();
const timeNum = ref();
const quantityStep = ref();
const timeStep = ref();
const timeUnit = ref();
const skuUnit = ref();
const minQuantity = ref();
const maxQuantity = ref();
const minTime = ref();
const maxTime = ref();
const payType = ref("");
const quantityFilter = ref({});
const timeFilter = ref({});
const unitPrice = ref();
const payDicLocal = ref([]);
// const payDicLocal1 = [
//   { itemKey: 1, itemValue: "话费支付" },
//   { itemKey: 2, itemValue: "沃支付" },
//   { itemKey: 3, itemValue: "其他" }
// ];

onMounted(() => {
  dictionaryStore.getCooperateDicList(); // 合作类型
  dictionaryStore.getDeliveryDicList(); // 交付方式
  // 对时长字典进行处理
  getDictionary({ dictCode: "pms_time_uit" }).then((res: any) => {
    res.data.forEach(item => {
      timeFilter.value[item.itemKey] = item.itemValue;
    });
  });
  // 对数量单位字典进行处理
  getDictionary({ dictCode: "sku_quantity_uit" }).then((res: any) => {
    res.data.forEach(item => {
      quantityFilter.value[item.itemKey] = item.itemValue;
    });
  });
  // 产品服务商列表
  productStore.getProviderList();
});
const payDic = computed(() => dictionaryStore.orderPayWayDic);
const cooperateDic = computed(() => dictionaryStore.cooperateDic);
const deliveryDic = computed(() => dictionaryStore.deliveryDic);
// 产品服务商列表
const providerList = computed(() => productStore.providerList);

const showDrawer = () => {
  const params = {
    title: "选择产品",
    sureText: "选择",
    show: true,
    hasSure: false,
    hasCancel: false
  };
  drawerRef.value?.acceptParams(params);
  activeStep.value = 1;
  nextTick(() => {
    proTable.value?.getTableList();
  });
};
const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
  newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
  delete newParams.createTime;
  newParams.publishStatus = 2;
  return getProductList(newParams);
};
// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "productCode", label: "产品编码", width: 110, search: { el: "input" } },
  { prop: "productName", label: "产品名称", search: { el: "input" } },
  { prop: "cooperateType", label: "合作类型", width: 90, enum: cooperateDic, search: { el: "select" } },
  { prop: "providerId", label: "产品服务商", width: 100, enum: providerList, search: { el: "select" } },
  { prop: "categoryName", label: "产品分类", width: 180 },
  {
    prop: "publishStatus",
    label: "上架状态",
    width: 110,
    enum: productStatus, // 产品上架状态字典
    render: scope => {
      return (
        <el-tag type={scope.row.publishStatus === 2 ? "success" : "danger"}>
          {scope.row.publishStatus === 2 ? "上架" : "下架"}
        </el-tag>
      );
    }
  },
  { prop: "creatorName", label: "创建人员", width: 85, search: { el: "input" } },
  {
    prop: "createTime",
    label: "创建时间",
    width: 170,
    sortable: true,
    search: {
      el: "date-picker",
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" }
    }
  },
  {
    prop: "updateTime",
    label: "上架时间",
    width: 170,
    sortable: true
  }
];

const sureCallback = () => {
  drawerRef.value?.acceptParams({ show: false });
};
const selectProduct = () => {
  if (proTable.value?.selectedList.length > 1) {
    ElMessage.error("只能选择1个产品");
  } else {
    if (proTable.value?.selectedList.length === 0) return;
    productDetail({ id: proTable.value?.selectedList[0].id }).then((res: any) => {
      selectedProDetail.value = res.data;
      let ppList = selectedProDetail.value.payType.split(",");
      payDicLocal.value = payDic.value.filter(item => ppList.includes(item.itemKey));
      deliveryDic.value.forEach(item => {
        if (item.itemKey === selectedProDetail.value.deliveryMethod) {
          deliveryName.value = item.itemValue;
        }
      });
      sizeList.value = selectedProDetail.value.skus;
      unitPrice.value = sizeList.value[0].skuPrice; // sku单价
      // 购买数量、时长初始值：
      selectedSize.value = sizeList.value[0].id;
      selectedSizeName.value = sizeList.value[0].skuName;
      hasQuantity.value = sizeList.value[0].quantityCounting;
      if (hasQuantity.value) {
        quantityStep.value = sizeList.value[0].quantityStep;
        skuUnit.value = sizeList.value[0].skuUnit;
        minQuantity.value = sizeList.value[0].minQuantity;
        maxQuantity.value = sizeList.value[0].maxQuantity;
        quantityNum.value = sizeList.value[0].minQuantity; // 初始默认值
      }
      hasTime.value = sizeList.value[0].timeCounting;
      if (hasTime.value) {
        timeStep.value = sizeList.value[0].timeStep;
        timeUnit.value = sizeList.value[0].timeUnit;
        minTime.value = sizeList.value[0].minTime;
        maxTime.value = sizeList.value[0].maxTime;
        timeNum.value = sizeList.value[0].minTime; // 初始默认值
      }
      console.log("selectedProDetail", selectedProDetail.value);
    });
    // emits("selectedListIds", proTable.value?.selectedListIds); // 后端需要的是这个
    activeStep.value = 2;
  }
};
const sureSize = () => {
  if (!payType.value) {
    ElMessage.error("请选择支付方式");
    return;
  }
  if (hasQuantity.value && !quantityNum.value) {
    ElMessage.error("请选择购买数量");
    return;
  }
  if (hasTime.value && !timeNum.value) {
    ElMessage.error("请选择购买时长");
    return;
  }
  let infos = {
    productId: selectedProDetail.value.id,
    productCode: selectedProDetail.value.productCode,
    productName: selectedProDetail.value.productName,
    skuId: selectedSize.value,
    skuName: selectedSizeName.value,
    quantityNum: quantityNum.value,
    timeNum: timeNum.value,
    payType: payType.value,
    price: unitPrice.value, // 单价
    wholeMoney: +unitPrice.value * (quantityNum.value || 1) // 订单总金额
  };
  emits("selectedProductInfo", infos);
  drawerRef.value?.acceptParams({ show: false });
};
const backOne = () => {
  activeStep.value = 1;
};
const selectSizeChange = (value: any) => {
  sizeList.value.forEach(item => {
    if (value === item.id) {
      unitPrice.value = item.skuPrice; // sku单价
      selectedSizeName.value = item.skuName;
      hasQuantity.value = item.quantityCounting;
      hasTime.value = item.timeCounting;
      if (hasQuantity.value) {
        quantityStep.value = item.quantityStep;
        skuUnit.value = item.skuUnit;
        minQuantity.value = item.minQuantity;
        maxQuantity.value = item.maxQuantity;
        quantityNum.value = item.minQuantity; // 初始默认值
      }
      if (hasTime.value) {
        timeStep.value = item.timeStep;
        timeUnit.value = item.timeUnit;
        minTime.value = item.minTime;
        maxTime.value = item.maxTime;
        timeNum.value = item.minTime; // 初始默认值
      }
    }
  });
};
defineExpose({
  showDrawer
});
</script>

<style scoped lang="scss">
:deep(.el-scrollbar) {
  min-height: 120px;
}
.step-btn {
  margin: 20px 0;
  text-align: center;
  :deep(.el-button) {
    width: 140px;
    height: 40px;
    margin-right: 15px;
  }
}
.sku-box {
  padding: 30px 140px;
  .sku-item {
    margin-bottom: 22px;
    font-size: 16px;
    .info-label {
      display: inline-block;
      width: 87px;
      margin-right: 230px;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
    .unit {
      margin-left: 10px;
    }
    .price {
      font-size: 24px;
      color: var(--el-color-primary);
    }
  }
  .title {
    font-size: 20px;
    font-weight: 600;
    color: #3a3a3d;
  }
  .des {
    color: #3a3a3d;
  }
}
</style>
