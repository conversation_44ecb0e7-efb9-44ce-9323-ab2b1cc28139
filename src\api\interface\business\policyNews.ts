import { ReqPageCommon } from "@/api/interface/index";

export namespace PolicyNews {
  export interface ListReqParam extends ReqPageCommon {
    auditStatus?: string; // 审批状态
    maxCreateTime?: string; // 创建时间（结束）
    maxPublishTime?: string; //发布时间（结束）
    minCreateTime?: string; //创建时间（开始）
    minPublishTime?: string; //发布时间（开始）
    newsType?: string; //新闻分类 1-最新动态 2-政策消息,字典[news_type]
    search?: string; // 搜索关键字
    searchCreatorName?: string; //搜索创建人
    status?: string; //状态[news_status]
  }
  export interface ReqParam {
    id?: string;
  }
  export interface PolicyParam {
    coverUrl?: string;
    detail?: string;
    detailUrl?: string;
    id?: string;
    isTop: true;
    newsType?: string;
    publishAgency?: string;
    publishStartTime?: string;
    publishType?: string;
    summary?: string;
    title?: string;
  }
  export interface ChangeReqParam {
    news: PolicyParam;
    publishNow: boolean;
  }
}
