import http from "@/api";

export const getEnterprisePilotReportReviewList = (params: any = {}) => {
  return http.get(`/cnud-operation/admin/enterprisePilot/auditPageList`, {
    desc: "createTime",
    flowStatus: "city_initial_review",
    ...params
  });
};

export const getEnterprisePilotAgreementReviewList = (params?: any) => {
  return http.get(`/cnud-operation/admin/enterprisePilot/auditPageList`, {
    desc: "createTime",
    flowStatus: "city_final_review",
    ...params
  });
};

export const getEnterprisePilotFinanceReviewList = (params?: any) => {
  return http.get(`/cnud-operation/admin/enterprisePilot/auditPageList`, {
    desc: "createTime",
    flowStatus: "city_finance_review",
    ...params
  });
};

export const getEnterprisePilotAgreementIndustryReviewList = (params?: any) => {
  return http.get(`/cnud-operation/admin/enterprisePilot/auditPageList`, {
    desc: "createTime",
    flowStatus: "traction_unit_review",
    ...params
  });
};

export const exportEnterprisePilotList = (params?: any) => {
  return http.get(
    `/cnud-operation/admin/enterprisePilot/export`,
    {
      desc: "createTime",
      ...params
    },
    { responseType: "blob" }
  );
};

export const postEnterprisePilotAudit = (params?: any) => {
  return http.post(`/cnud-operation/admin/enterprisePilot/audit`, params);
};
