<template>
  <div class="analysis-container">
    <div class="header">
      <div class="header-box">
        <el-form :inline="true" ref="orderFormRef" :model="orderForm" :rules="rules">
          <el-form-item label="" prop="btn">
            <div>
              <el-button-group size="large" class="botton-box">
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.day, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.day)"
                >
                  日报表
                </el-button>
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.month, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.month)"
                >
                  月报表
                </el-button>
              </el-button-group>
            </div>
          </el-form-item>
          <el-form-item label="日期" prop="countDate">
            <el-date-picker
              v-if="dateType === DateType.day"
              class="data-picker-style"
              type="daterange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @focus="handleFocus"
              @calendar-change="handleChange"
              :disabled-date="disabledDate"
            />
            <el-date-picker
              v-else
              class="data-picker-style"
              type="monthrange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              :rule="[{}]"
            />
          </el-form-item>
          <el-form-item label="数据维度" prop="dataType">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              v-model="orderForm.dataType"
              placeholder="请选择数据维度"
              clearable
              @change="onChangeDataType"
            >
              <el-option v-for="item in dataDimensionDict" :key="item.itemKey" :label="item.itemValue" :value="item.itemKey" />
            </el-select>
          </el-form-item>
          <el-form-item label="产品名称" prop="productIdList" v-if="orderForm.dataType === '1'">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              :multiple="true"
              v-model="orderForm.dataId"
              placeholder="请选择产品名称"
              clearable
            >
              <el-option v-for="item in productOptions" :key="item.productId" :label="item.productName" :value="item.productId" />
            </el-select>
          </el-form-item>
          <el-form-item label="权益名称" prop="rightsIdList" v-if="orderForm.dataType === '2'">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              :multiple="true"
              v-model="orderForm.dataId"
              placeholder="请选择权益名称"
              clearable
            >
              <el-option v-for="item in rightsIdOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="合作商" prop="providerId">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              v-model="orderForm.providerId"
              placeholder="请选择合作商"
              clearable
            >
              <el-option
                v-for="item in providerOptions"
                :key="item.providerId"
                :label="item.providerName"
                :value="item.providerId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm(orderFormRef)"> 查询 </el-button>
            <el-button @click="resetForm(orderFormRef)"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="analysis-content">
      <div class="content-title">产品使用分析</div>
      <chart-content :params="commonParams" ref="chartContentRef" />
    </div>
    <product-usage-list :params="{ ...commonParams }" ref="analysisListRef"></product-usage-list>
  </div>
</template>

<script setup lang="ts" name="productUsageAnalysis">
import { reactive, ref, onMounted, computed } from "vue";
import dayjs from "dayjs";
import type { FormInstance, FormRules } from "element-plus";
import productUsageList from "./components/productUsageList.vue";
import { startDate, endDate, startMonthDate, endMonthDate } from "../commonAnalysis";
import { getProductList, getProviderList, queryProductRightIdList } from "@/api/modules/statisticAnalysis";
import chartContent from "./components/chartContent.vue";
import { useDict } from "@/hooks/useDict";

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}

interface IProductId {
  productId: string;
  productName: string;
}
interface IProvider {
  providerId: string;
  providerName: string;
}
interface IRightsId {
  id: string;
  name: string;
}
const { pm_data_type } = useDict("pm_data_type");

const dataDimensionDict = computed(() => pm_data_type.value);

const analysisListRef = ref();
const chartContentRef = ref();

const dateType = ref<DateType>(DateType.day);

const commonParams = reactive({
  endTime: endDate,
  startTime: startDate,
  dataType: "0",
  indexUnit: dateType,
  dataId: [],
  providerId: ""
});

const orderFormRef = ref<FormInstance>();
const orderForm = reactive({
  countDate: [startDate, endDate],
  dataType: "0",
  dataId: [],
  providerId: "",
  indexUnit: ""
});

// 月报表入参月份
const handleCountDate = () => {
  if (dateType.value === DateType.month) {
    return {
      endTime: dayjs(commonParams.endTime).format("YYYY-MM-DD"),
      startTime: dayjs(commonParams.startTime).format("YYYY-MM-DD")
    };
  }
  return {};
};

const getAllData = () => {
  chartContentRef.value?.getChat({ ...commonParams });
  analysisListRef.value.getTableList({ ...commonParams, ...handleCountDate() });
};

onMounted(() => {
  getAllData();
  getSearchList();
});

const productOptions = ref<IProductId[]>([]);
const providerOptions = ref<IProvider[]>([]);
const rightsIdOptions = ref<IRightsId[]>([]);
const getSearchList = async () => {
  getProductList().then((res: any) => {
    productOptions.value = res?.data || [];
  });
  getProviderList().then((res: any) => {
    providerOptions.value = res?.data || [];
  });
  queryProductRightIdList().then((res: any) => {
    rightsIdOptions.value = res?.data || [];
  });
};

const changeDate = (type: DateType) => {
  if (type === DateType.month) {
    commonParams.startTime = startMonthDate;
    commonParams.endTime = endMonthDate;
    orderForm.countDate = [startMonthDate, endMonthDate];
  } else {
    commonParams.startTime = startDate;
    commonParams.endTime = endDate;
    orderForm.countDate = [startDate, endDate];
  }
};
const onChangeDateType = (type: DateType) => {
  dateType.value = type;
  commonParams.indexUnit = type;
  changeDate(type);
  getAllData();
};

// 切换数据维度，清除产品名称/权益名称
const onChangeDataType = (value: any) => {
  orderForm.dataId = [];
  orderForm.dataType = value;
  commonParams.dataType = value;
};

const checkDate = (_: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请选择日期"));
  } else {
    // 定义两个日期字符串
    const dateString1 = value[0];
    const dateString2 = value[1];
    // 将日期字符串转换为 dayjs 对象
    const date1 = dayjs(dateString1);
    const date2 = dayjs(dateString2);

    // 获取两个日期之间的月数差
    const monthDiff = Math.abs(date2.diff(date1, "month"));

    if (monthDiff > 11) {
      callback(new Error("最多只能查询12个月的数据"));
    }
  }
  callback();
};

const dateRule: any = computed(() =>
  dateType.value === DateType.day
    ? [{ required: true, message: "请选择日期", trigger: "blur" }]
    : [
        { required: true, message: "请选择日期", trigger: "blur" },
        { trigger: "blur", validator: checkDate }
      ]
);
const rules = reactive<FormRules>({
  countDate: dateRule
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      commonParams.startTime = orderForm.countDate[0];
      commonParams.endTime = orderForm.countDate[1];
      commonParams.dataId = orderForm.dataId || [];
      commonParams.providerId = orderForm.providerId || "";
      getAllData();
    } else {
      console.log("error submit!", fields);
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  // 处理重置日报表与月报表不相同
  changeDate(dateType.value);
  commonParams.dataId = [];
  commonParams.providerId = "";
  getAllData();
};

const chooseDay = ref<any>(null);
const handleChange = (val: Date[]) => {
  const [pointDay] = val;
  chooseDay.value = pointDay;
};
//取值方法
const handleFocus = () => {
  chooseDay.value = null;
};

const disabledDate = (time: number) => {
  if (!chooseDay.value) {
    return false;
  }
  let timeRange = 30;
  const con1 = new Date(chooseDay.value).getTime() - timeRange * 24 * 60 * 60 * 1000;
  const con2 = new Date(chooseDay.value).getTime() + timeRange * 24 * 60 * 60 * 1000;
  return time < con1 || time > con2;
};
</script>

<style lang="scss" scoped>
.cust-border {
  border-bottom: 1px solid #e1e3e6;
}
.analysis-container {
  background-color: #ffffff;
  .header {
    padding: 16px 0 0 24px;
    @extend.cust-border;
    .header-box {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      :deep(.el-form-item--default) {
        margin-bottom: 16px;
      }
      :deep(.el-form--inline .el-form-item) {
        margin-right: 12px;
      }
      :deep(.data-picker-style) {
        max-width: 210px;
      }
      .select-style {
        max-width: 190px;
      }
      .type-box {
        flex: 164px;
      }
    }
    .btn {
      height: 36px;
      &.active {
        color: var(--el-color-primary);
        background-color: var(--el-fill-color-blank);
        border-color: var(--el-color-primary);
      }
    }
  }
  .analysis-content {
    box-sizing: border-box;
    height: 480px;
    padding: 16px 24px 0;
    @extend.cust-border;
    .content-title {
      width: 100%;
      height: 24px;
      margin-bottom: 16px;
      font-family: "PingFangSC, PingFang SC,sans-serif";
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #000000d9;
    }
    .statistical-data {
      display: flex;
      width: 100%;
    }
    .main {
      flex: 1;
      .chart-box {
        width: 100%;
        height: 370px;
      }
    }
    .aside {
      width: 300px;
    }
  }
}
</style>
