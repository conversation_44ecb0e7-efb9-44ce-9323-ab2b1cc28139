<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="title" width="70%" @close="onCancel">
      <el-form name="test" ref="ruleFormRef" :model="userInfoForm" :rules="rules" label-position="left">
        <div class="detail-title">基本信息</div>
        <el-row :gutter="48">
          <el-col :span="12">
            <el-form-item label="所属部门" prop="orgId">
              <span>{{ userInfoForm.orgName }}</span>
              <el-text v-if="isDisable" class="select-new-depart" type="primary" @click="onSelectDepart">选择新部门</el-text>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="userType">
              <el-radio-group v-model="userInfoForm.userType" :disabled="isDisable">
                <el-radio label="1">内部用户</el-radio>
                <el-radio label="2">合作商用户</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="account">
              <el-input v-model="userInfoForm.account" maxlength="20" :disabled="isEdit || isDisable" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户姓名" prop="userName">
              <el-input v-model="userInfoForm.userName" maxlength="20" :disabled="isDisable" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="userInfoForm.phone" maxlength="11" :disabled="isDisable" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户邮箱" prop="email">
              <el-input v-model="userInfoForm.email" maxlength="50" :disabled="isDisable" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号有效期" prop="validType">
              <el-radio-group v-model="userInfoForm.validType" :disabled="isDisable">
                <el-radio :label="1">长期</el-radio>
                <el-radio :label="0">短期</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isShortTerm">
            <el-form-item label="选择有效期" prop="validDate">
              <el-date-picker
                type="date"
                placeholder="选择日期"
                v-model="userInfoForm.validDate"
                :disabled-date="disabledDate"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled="isDisable"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述" prop="description">
              <el-input v-model="userInfoForm.description" type="textarea" maxlength="100" :disabled="isDisable"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="detail-title mt8">业务信息</div>
        <el-row>
          <el-col :span="24">
            <el-form-item label="角色" prop="roleIds">
              <el-select v-model="userInfoForm.roleIds" :multiple="true" placeholder="请选择角色" clearable style="width: 100%">
                <el-option v-for="(item, index) in roleDict" :key="index + 'skuUnit'" :label="item.roleName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否免审" prop="auditType" :disabled="isDisable">
              <el-switch v-model="userInfoForm.auditType" :active-value="1" :inactive-value="0"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <select-depart v-model:visible="isShowSelectDepart" @cancel="onCancelSelect" @confirm="onConfirmSelect"></select-depart>
  </div>
  <el-dialog v-model="createDialogVisible" title="" width="400" center>
    <el-result icon="success" title="创建用户成功" :style="{ paddingTop: 0, paddingBottom: 0 }">
      <template #extra>
        创建【{{ createDialogInfo.userType == "1" ? "内部用户" : "合作商用户" }}】【{{
          createDialogInfo.account
        }}】成功，初始密码是：
        {{ createDialogInfo.initPassWordValue }}
        <el-text v-copy="createDialogInfo.initPassWordValue" :style="{ cursor: 'pointer' }">
          <el-icon><DocumentCopy color="var(--el-color-primary)" /></el-icon>
        </el-text>
      </template>
    </el-result>
  </el-dialog>
</template>

<script setup lang="ts">
import { editType, editTypeChinese } from "@/enums/dialogTypeEnum";
import { computed, ref, reactive, watch, onMounted } from "vue";
import type { FormInstance } from "element-plus";
import { ElMessage, FormRules } from "element-plus";
import { getRoleList, saveUser as addTypeApi, updateList as updateTypeApi, updateUserOrg } from "@/api/modules/system";
import { ResPage } from "@/api/interface/index";
import selectDepart from "./selectDepart.vue";

import { useHandleData } from "@/hooks/useHandleData";
import { checkPhoneNumber } from "@/utils/eleValidate";

const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: editType.add
  },
  params: {
    type: Object,
    default: () => {}
  }
});

const disabledDate = (time: any) => {
  return time.getTime() < Date.now() - 8.64e7;
};

const ruleFormRef = ref();
const isShowSelectDepart = ref(false);

const userInfoForm = ref<any>({
  name: "",
  description: "",
  parentId: "",
  auditType: "0",
  userType: "1",
  validType: 1
});

const createDialogVisible = ref(false);
const createDialogInfo = reactive({
  initPassWordValue: "",
  userType: "",
  account: ""
});

const title = computed(() => (editTypeChinese[props.type] ? editTypeChinese[props.type] + "用户" : "部门转换"));
const isDisable = computed(() => (editTypeChinese[props.type] ? false : true));
const isEdit = computed(() => props.type === editType.edit);
// 短期 才显示 有效期 字段
const isShortTerm = computed(() => userInfoForm.value.validType == 0);

const emits = defineEmits(["update:visible", "cancel", "confirm"]);
watch(
  () => props.params,
  () => {
    const ids = props.params.roles || "";
    if (props.type !== editType.add) {
      userInfoForm.value = {
        orgId: props.params.orgId,
        orgName: props.params.orgName,
        userType: props.params.userType,
        account: props.params.account,
        userName: props.params.userName,
        phone: props.params.phone,
        email: props.params.email,
        validType: props.params.validType,
        validDate: props.params.validDate,
        description: props.params.description,
        roleIds: ids ? ids?.split(",") : [],
        auditType: props.params.auditType ? 1 : 0
      };
    } else {
      userInfoForm.value.orgId = props.params.orgId;
      userInfoForm.value.orgName = props.params.orgName;
    }
  }
);

const roleDict = ref<any>([]);
const getRoleDicts = (params: any = { current: 1, size: 10000 }) => {
  let newParams = JSON.parse(JSON.stringify(params));
  getRoleList(newParams).then(res => {
    if (res.code === 200) {
      roleDict.value = (res.data as ResPage<any>).records;
    }
  });
};
onMounted(() => {
  // 先获取字典
  // dictionaryStore.getUserType(); // 用户类型
  getRoleDicts();
});
// const userTypeDict = computed(() => dictionaryStore.userType);

const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
  isShowSelectDepart.value = false;
};
const userNameRule: any = computed(() =>
  props.type === editType.add
    ? [
        { trigger: "blur", required: true, message: "用户名称不能为空" },
        {
          validator: (_: any, value: any, callback: any) => {
            const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9_\s]+$/;
            if (!reg.test(value)) {
              callback(new Error("不允许输入特殊字符，且限制输入1-20位字符"));
            }
            if (!value) {
              callback(new Error("用户名称不能为空"));
            } else {
              callback();
            }
          }
        }
      ]
    : []
);
const phoneRule: any = computed(() =>
  props.type === editType.add
    ? [
        { trigger: "blur", required: true, message: "手机号码不能为空" },
        { trigger: "blur", validator: checkPhoneNumber }
      ]
    : []
);
const emailRule: any = computed(() =>
  props.type === editType.add
    ? [
        { required: props.type === editType.add, message: "请输入邮箱地址" },
        {
          type: "email",
          message: "请输入正确的邮箱地址"
        }
      ]
    : []
);
const rules = reactive<FormRules>({
  userType: [{ trigger: "blur", required: true, message: "账号类型不能为空" }],
  account: [
    { trigger: "blur", required: true, message: "登录账号不能为空" },
    {
      validator: (_, value, callback) => {
        const reg = /^[A-z0-9]*$/;
        if (!reg.test(value)) {
          callback("限制仅允许输入1-20位英文（区分大小写）及数字0-9的组合");
        } else {
          callback();
        }
      }
    }
  ],
  userName: userNameRule,
  phone: phoneRule,
  email: emailRule,
  validType: [{ trigger: "blur", required: true, message: "账号有效期不能为空" }],
  roleIds: [{ trigger: "blur", required: true, message: "角色不能为空" }],
  auditType: [{ trigger: "blur", required: true, message: "是否免审不能为空" }],
  validDate: [{ trigger: "blur", required: true, message: "选择有效期不能为空" }]
});
const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    userInfoForm.value = {
      name: "",
      description: "",
      parentId: "",
      auditType: "0",
      userType: "1",
      validType: 1
    };
    emits("update:visible", value);
  }
});

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const ids = userInfoForm.value.roleIds;
      if (isDisable.value) {
        const params = {
          orgId: userInfoForm.value.orgId,
          preOrgId: props.params.orgId,
          userId: props.params.id,
          roleIds: ids.join(",") // 部门转换---角色也可修改
        };

        useHandleData(updateUserOrg, { ...params }, `部门转换`).then(() => {
          emits("confirm");
          dialogVisible.value = false;
        });
        return;
      }
      const apiObject = {
        [editType["add"]]: addTypeApi,
        [editType["edit"]]: updateTypeApi
      };
      const formVal = {
        orgName: userInfoForm.value.orgName,
        userType: userInfoForm.value.userType,
        account: userInfoForm.value.account,
        userName: userInfoForm.value.userName,
        phone: userInfoForm.value.phone,
        email: userInfoForm.value.email,
        validType: userInfoForm.value.validType,
        validDate: userInfoForm.value.validDate,
        description: userInfoForm.value.description,
        roleIds: ids.join(","),
        auditType: userInfoForm.value.auditType
      };
      const requestParamsObject = {
        [editType["add"]]: () => {
          return {
            ...formVal,
            orgId: props.params.orgId
          };
        },
        [editType["edit"]]: () => {
          return {
            id: props.params.id,
            ...formVal
          };
        }
      };

      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        emits("confirm");
        if (props.type === 1) {
          createDialogInfo.account = formVal.account;
          createDialogInfo.userType = formVal.userType;
          createDialogInfo.initPassWordValue = res?.data as string;
          createDialogVisible.value = true;

          // ElMessageBox.confirm(`创建【账号类型】【用户账号】成功，初始密码是：${res?.data}`, "创建用户成功", {
          //   type: "success",
          //   center: true,
          //   showCancelButton: false,
          //   showConfirmButton: false
          // }).then(() => {});
        } else {
          ElMessage.success(res.msg);
        }
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};

// 选择部门
const onSelectDepart = () => {
  isShowSelectDepart.value = true;
};

// 取消选择部门
const onCancelSelect = () => {
  isShowSelectDepart.value = false;
};
const onConfirmSelect = (items: { [key: string]: any }) => {
  if (props.params.orgId !== items.orgId) {
    ElMessage.info("请为用户重新选择角色！");
  }
  userInfoForm.value.orgId = items.orgId;
  userInfoForm.value.orgName = items.orgName;
  isShowSelectDepart.value = false;
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
.horizontal.el-form-item {
  display: flex;
  align-items: center;
  :deep(.el-form-item__label) {
    margin-bottom: 0;
  }
}
.detail-title {
  margin-bottom: 18px;
  font-size: 16px;
  font-weight: 500;
  color: #3a3a3d;
  &.mt8 {
    margin-top: 8;
  }
}
.select-new-depart {
  margin-left: 10px;
  cursor: pointer;
}
</style>
