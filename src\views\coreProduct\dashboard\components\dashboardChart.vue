<template>
  <div :id="props.chartId" class="echarts"></div>
</template>

<script setup lang="ts">
import { ECharts, EChartsOption, init } from "echarts";
// import { toThousandFilter } from "@/utils/tools";
import { useEcharts } from "@/hooks/useEcharts";

const props = withDefaults(defineProps<{ chartId?: string }>(), {
  chartId: "pieChartId"
});

interface ChartProp {
  color: string[];
  totalAvailable: number;
  totalRunning: number;
  percentage: number;
}

const initChart = (
  data: ChartProp = {
    color: [],
    totalAvailable: 0,
    totalRunning: 0,
    percentage: 0
  }
) => {
  const charEle = document.getElementById(props.chartId) as HTMLElement;
  const charEch: ECharts = init(charEle);

  const colors = data.color;
  const chartTitle = "";

  const totalAvailable = data.totalAvailable;
  const percentage = data.percentage;

  const option: EChartsOption = {
    color: colors,
    tooltip: {
      trigger: "item"
    },
    title: [
      {
        text: chartTitle,
        textStyle: {
          color: "#3A3A3D",
          fontWeight: 400
        },
        top: 16
      }
    ],

    grid: {
      top: "0%",
      right: "2%",
      bottom: "0",
      left: "0",
      containLabel: true
    },
    series: [
      {
        // name: "渠道",
        type: "pie",
        radius: ["70%", "90%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        selectedMode: false,
        labelLine: {
          show: false
        },
        label: {
          show: true,
          position: "center", // 展示在中间位置
          formatter: () => {
            return `{percentage|${percentage}%}\n\n可用\n\n{totalAvailable|${totalAvailable}}`;
          },
          offset: [0, 6, 0, 0],
          rich: {
            percentage: {
              fontSize: 28,
              fontWeight: 600,
              lineHeight: 40,
              color: "#3A3A3D",
              align: "center",
              padding: [15, 0, 0, 0]
            },
            totalAvailable: {
              fontSize: 20,
              fontWeight: 600,
              color: colors[0],
              align: "center",
              lineHeight: 28
            }
          }
        },
        emphasis: {
          label: {
            show: true
          }
        },
        data: [
          { value: data.totalAvailable, name: "可用" },
          { value: data.totalRunning, name: "运行中" }
        ]
      }
    ]
  };

  useEcharts(charEch, option);
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
</style>
