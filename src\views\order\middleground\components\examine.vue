<template>
  <div class="page-order-detail">
    <div class="order-info">
      <div class="page-title">审核信息</div>
      <div class="form">
        <el-form ref="formRef" :model="examineForm" label-width="83px" class="demo-ruleForm">
          <el-form-item label="处理意见" prop="auditRemark" :rules="[{ required: validRequired, message: '请输入处理意见' }]">
            <el-input
              rows="5"
              v-model="examineForm.auditRemark"
              placeholder="处理时必须填写处理意见，通过默认处理意见为同意"
              type="textarea"
              maxlength="200"
              show-word-limit
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item>
            <!-- <div class="tips">处理时必须填写审核意见，通过默认审核意见为同意</div> -->
          </el-form-item>
          <div class="button">
            <el-button plain @click="handleBack">取消</el-button>
            <el-button type="primary" @click="handleTask('FINISH')">报竣</el-button>
            <el-button type="primary" @click="handleTask('BACKORDER')">退单</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref } from "vue";
import type { Action, FormInstance } from "element-plus";
import { operateZQOrder } from "@/api/modules/order";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";

const router = useRouter();
const formRef = ref<FormInstance>();
const validType = ref<string>("FINISH");

const validRequired = computed(() => {
  return validType.value === "BACKORDER";
});

const examineForm = reactive({
  auditRemark: ""
});

defineEmits(["onChange"]);

const handleTask = (result: string) => {
  validType.value = result;
  nextTick(() => {
    formRef.value?.validate().then(valid => {
      let tips = "";
      if (result === "FINISH") {
        tips = "是否确认报竣";
      } else {
        tips = "是否确认退单";
      }
      ElMessageBox.alert(tips, "操作提示", {
        confirmButtonText: "确定",
        callback: (action: Action) => {
          if (action === "confirm") {
            const auditRemark = examineForm.auditRemark === "" ? "同意" : examineForm.auditRemark;
            operateZQOrder({
              operateRemark: result === "FINISH" ? auditRemark : examineForm.auditRemark,
              returnResult: result,
              parentOrderNo: props.orderId || 0
            }).then(res => {
              // emit("onChange");
              ElMessage({
                message: result === "FINISH" ? "报俊成功" : "退单成功",
                type: "success"
              });
              router.back();
            });
          }
        }
      });
    });
  });
};

const handleBack = () => {
  router.back();
};
const props = defineProps({
  orderId: {
    type: String,
    require: true
  }
});
</script>

<style scoped lang="scss">
.button {
  display: flex;
  justify-content: flex-start;
  margin-top: 30px;
  margin-bottom: 10px;

  // :deep(.el-button) {
  //   height: 36px;
  //   margin-right: 16px;
  // }
}
.order-info {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}
.page-title {
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #3a3a3d;
  border-bottom: 1px solid #e4e7ed;
}
.form {
  margin-top: 20px;
}
.tips {
  color: rgb(156 163 172 / 100%);
}
</style>
