import http from "@/api";
import { themeLDetail } from "@/api/interface/business/theme";

export const getThemeList = async (data: any) => {
  return await http.post("/cnud-operation/admin/themeZone/search", data);
};

export const cancel = async (id: string) => {
  return await http.post("/cnud-operation/admin/themeZone/delete", { id });
};

export const getThemeDetail = async (id: string) => {
  return await http.post<themeLDetail>("/cnud-operation/admin/themeZone/detail", { id });
};

export const add = async (data: themeLDetail) => {
  return await http.post("/cnud-operation/admin/themeZone/submit", data);
};

export const change = async (data: themeLDetail) => {
  return await http.post("/cnud-operation/admin/themeZone/update", data);
};
