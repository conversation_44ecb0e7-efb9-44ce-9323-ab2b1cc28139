<template>
  <el-dialog v-model="dialogVisible" :title="title" width="70%" :before-close="handleClose" :destroy-on-close="true">
    <template v-if="actionType === 'partner'">
      <ProTable
        @current-change="handleCurrentChange"
        :request-auto="false"
        ref="proTable"
        :columns="partnerColumns"
        :tool-button="false"
        :data="tableData"
      >
        <template #pagination>
          <Pagination :pageable="pageable" :handle-size-change="handleSizeChange" :handle-current-change="onCurrentChange" />
        </template>
      </ProTable>
    </template>
    <template v-else>
      <ProTable
        @current-change="handleCurrentChange"
        :request-auto="false"
        ref="proTable"
        :columns="contractColumns"
        :tool-button="false"
        :request-api="getTableList"
      >
      </ProTable>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleOk"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
export type ActionType = "partner" | "contract";
export interface ModalProps {
  handleOpen: (type: ActionType, data: any) => void;
}
interface ModalParams {
  title?: string;
  actionType?: ActionType;
}
import { getProviderAgreementList } from "@/api/modules/partner";
import { pmsProviderList } from "@/api/modules/product";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { nextTick, ref, reactive } from "vue";
import Pagination from "@/components/ProTable/components/Pagination.vue";

const emit = defineEmits(["onSelect"]);

const dialogVisible = ref(false);
const tableData = ref<any[]>([]);
const selectData = ref(null);
const actionType = ref<ActionType>("partner");
const title = ref("");
const rowsData = ref({
  providerId: ""
});
const pageable = reactive({
  current: 1,
  size: 10,
  total: 0
});

const partnerColumns: ColumnProps[] = [
  { type: "radio", width: 50, align: "center" },
  { prop: "name", label: "合作商名称" },
  { prop: "code", label: "社会统一编码" }
];
const contractColumns: ColumnProps[] = [
  { type: "radio", width: 50, align: "center" },
  { prop: "name", label: "合作商名称" },
  { prop: "agreementName", label: "协议名称", search: { el: "input" } }
];

// ProTable 实例
const proTable = ref<ProTableInstance>();

const PROTOCOLTOBEUPLOADED = "4";
const SUCCESSAUDITSTATUS = "8";
// agreement_audit_status 1审核中 2审核不通过 3审核通过
const AUDITSTATUS_SUCCESS = "3";

/* methods */
const handleClose = () => {
  dialogVisible.value = false;
  selectData.value = null;
};

const handleCurrentChange = (val: any) => {
  selectData.value = Object.assign({}, val);
};

/* 合作商列表 */
const partnerFetch = (current = 1, size = 10) => {
  return pmsProviderList({ size, current, auditStatus: `${PROTOCOLTOBEUPLOADED},${SUCCESSAUDITSTATUS}` })
    .then(result => {
      tableData.value = result?.data?.records;
      pageable.current = current;
      pageable.size = size;
      pageable.total = result?.data?.total;
    })
    .catch(err => {});
};
/* 合作商协议列表 */
const getTableList = async (params: any) => {
  const values = { ...params, providerId: rowsData?.value?.providerId, auditStatus: AUDITSTATUS_SUCCESS };
  return await getProviderAgreementList({ ...values });
};

const handleOpen = (type: ActionType, rowData?: any) => {
  dialogVisible.value = true;
  if (type === "partner") {
    actionType.value = "partner";
    title.value = "选择合作商";
    partnerFetch().then(res => {
      proTable.value?.setRadio(rowData?.providerId);
      selectData.value = Object.assign(
        {},
        {
          ...rowData,
          name: rowData.provider,
          id: rowData?.providerId
        }
      );
    });
  } else {
    actionType.value = "contract";
    title.value = "选择合作商协议";
    rowsData.value = rowData;
    nextTick(() => {
      proTable.value?.getTableList();
    });
  }
};

const handleSizeChange = (value: number) => {
  pageable.size = value;
  partnerFetch(pageable.current, value);
};
const onCurrentChange = (value: number) => {
  pageable.current = value;
  partnerFetch(value, pageable.size);
};

const handleOk = () => {
  emit("onSelect", actionType.value, Object.assign({}, selectData.value));
  handleClose();
};

defineExpose({
  handleOpen
});
</script>

<style scoped></style>
