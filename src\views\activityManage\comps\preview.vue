<template>
  <el-dialog
    v-model="dialogVisible"
    title="活动预览"
    width="800"
    :before-close="handleClose"
    draggable
    align-center
    destroy-on-close
  >
    <div class="dialog-body">
      <div class="dialog-body--item">
        <div class="label">活动二维码</div>
        <div class="desc">
          <div class="qr-code">
            <img :src="encodedUrl" id="custom-qrcode" />
            <div class="text">手机扫二维码预览</div>
            <el-button type="primary" @click="download">下载二维码</el-button>
          </div>
        </div>
      </div>
      <div class="dialog-body--item">
        <div class="label">活动链接</div>
        <div class="desc">
          <div class="desc-wrapper">
            <el-input disabled v-model="qrLink"> </el-input>
            <el-button type="primary" v-copy="qrLink">复制</el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import qrcodeParser from "qrcode-parser";
import { generateMarketingPreviewUrl } from "@/api/modules/activityH5";

import { nextTick, ref } from "vue";

const encodedUrl = ref<string>("");

const props = defineProps({
  rowData: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

const download = () => {
  nextTick(() => {
    const image = document.querySelector("#custom-qrcode");
    const link = document.createElement("a");
    link.href = image?.getAttribute("src") as string;
    link.download = "image.png";
    link.click();
    document.body.removeChild(link);
  });
};

const dialogVisible = ref(false);

const qrLink = ref("");

const handleClose = () => {
  dialogVisible.value = false;
};

const handleOpen = () => {
  dialogVisible.value = true;
  nextTick(() => {
    generateMarketingPreviewUrl({ marketingId: props.rowData.marketingId })
      .then(result => {
        encodedUrl.value = result.data;
        qrcodeParser(result.data)
          .then(res => {
            qrLink.value = res;
          })
          .catch(err => {});
      })
      .catch(err => {});
  });
};
defineExpose({
  handleOpen
});
</script>

<style scoped lang="scss">
.dialog-body {
  &--item {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    margin-bottom: 20px;
    .label {
      width: 95px;
    }
    .desc {
      width: 100%;
      .qr-code {
        width: 160px;
        text-align: center;
      }
      .desc-wrapper {
        display: flex;
      }
      .text {
        margin: 10px 0;
      }
    }
  }
}
</style>
