<template>
  <div>
    <div class="infos">
      <el-row>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">审核操作：</span>
            <span class="info-text">{{ useDictLabel(auditOperationDict, auditInfo?.auditOperation) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">提交人员：</span>
            <span class="info-text">{{ auditInfo?.creatorName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">提交时间：</span>
            <span class="info-text">{{ auditInfo?.createTime }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">备注：</span>
            <span class="info-text">{{ auditInfo?.applicationMessage }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">审核状态：</span>
            <span class="info-text">{{ useDictLabel(verifyStatusDict, auditInfo?.auditStatus) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">审核人：</span>
            <span class="info-text">{{ auditInfo?.updaterName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">审核时间：</span>
            <span class="info-text">{{ auditInfo?.updateTime }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from "vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useDictLabel } from "@/hooks/useDict";

const dictionaryStore = useDicStore();

onMounted(() => {
  dictionaryStore.getAuditOperation(); // 审核操作
  dictionaryStore.getProductAuditStatus(); // 审核状态
});
// 审核操作类型
const auditOperationDict: any = computed(() => dictionaryStore.auditOperation);
// 审核状态
const verifyStatusDict: any = computed(() => {
  const status = dictionaryStore.productAuditStatus;
  status?.forEach((item: any = {}) => {
    item.value = String(item.itemKey);
  });
  return status;
});

defineProps({
  auditInfo: {
    type: Object,
    default: () => {}
  }
});
</script>

<style lang="scss" scoped>
.infos {
  margin-bottom: 16px;
  .info-item {
    margin-bottom: 20px;
    margin-left: 16px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
}
</style>
