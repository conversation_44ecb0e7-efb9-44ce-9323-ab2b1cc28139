<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="产品列表"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
    >
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader="scope">
        <el-button plain :icon="Plus" @click="toView({}, 'add')"> 新增 </el-button>
        <el-button plain :icon="Delete" :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          删除
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-tooltip content="查看" placement="top">
          <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, 'details')"></i>
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toView(scope.row, 'edit')"></i>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <i class="iconfont2 opera-icon icon-lajitong" @click="deleteDictionarys(scope.row)"></i>
        </el-tooltip>
      </template>
    </ProTable>
    <OperateDictDialog
      ref="roleRef"
      :dialog-type="dialogInfo.dialogType"
      :dialog-data="dialogInfo.dialogData"
      @cancel="onCancel"
      @confirm="onComfirm"
    ></OperateDictDialog>
  </div>
</template>

<script setup lang="tsx" name="dictManage">
import { ref, reactive, onMounted } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Plus, Delete } from "@element-plus/icons-vue";
import { getDictionaryList, deleteDictionary, getDictionaryInfo } from "@/api/modules/system";
import { Product } from "@/api/interface/product";
import OperateDictDialog from "./components/OperateDictDialog.vue";
import { useHandleData } from "@/hooks/useHandleData";
import CustomTag from "@/components/CustomTag/index.vue";

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

const roleRef = ref();
const defaultDialogInfo = { dialogType: "add", dialogData: {} };
const dialogInfo = reactive({ ...defaultDialogInfo });

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

onMounted(() => {
  // 然后
  proTable.value?.getTableList();
});

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  return getDictionaryList(newParams);
};

// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "dictCode", label: "字典名称", search: { el: "input" } },
  { prop: "itemKey", label: "字典键名", search: { el: "input" } },
  { prop: "itemValue", label: "字典键值", search: { el: "input" } },
  {
    prop: "status",
    label: "状态",
    search: { el: "select" },
    render: scope => {
      return (
        <CustomTag
          type={scope.row.status === 1 ? "success" : "danger"}
          status={String(scope.row.status)}
          label={scope.row.status === 1 ? "正常" : "停用"}
        ></CustomTag>
      );
    },
    enum: [
      {
        label: "正常",
        value: 1
      },
      {
        label: "停用",
        value: 0
      }
    ]
  },
  { prop: "remark", label: "备注", width: 180 },
  {
    prop: "createTime",
    label: "创建时间",
    width: 170,
    sortable: true
    // search: { el: "date-picker" }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 152 }
];

// 批量删除用户信息
const batchDelete = async (ids: string[]) => {
  await useHandleData(deleteDictionary, { ids: ids }, { msg: `删除后不能恢复，是否确认删除所选记录？`, successMsg: "删除" });
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
// 删除
const deleteDictionarys = async (row: any) => {
  if (!row.id) {
    return;
  }
  await useHandleData(deleteDictionary, { ids: [row.id] }, `删除<${row.dictCode}>`);
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const getInfo = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  return getDictionaryInfo(newParams);
};
const toView = async (row: any, type = "add") => {
  dialogInfo.dialogType = type;
  roleRef.value?.openDialog();

  if (row.id) {
    // 查看
    await getInfo({ id: row.id }).then(res => {
      dialogInfo.dialogData = res.data as Object;
    });
  }
};

const onCancel = () => {
  dialogInfo.dialogType = "add";
  dialogInfo.dialogData = {};
};

const onComfirm = () => {
  onCancel();
  proTable.value?.getTableList();
};
</script>
