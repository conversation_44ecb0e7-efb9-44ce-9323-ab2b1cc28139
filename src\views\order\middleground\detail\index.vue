<template>
  <div class="page-order-detail">
    <template v-for="(item, index) in orderArray" :key="item.attrCode + index">
      <div
        class="info-box card"
        v-if="item.attrCode !== 'FEE_PRODUCT_INFO' && item.attrCode != 'PRODUCT_INFO' && item.attrCode !== 'ACCOUNT_INFO'"
      >
        <div class="box-title">
          <div class="title">{{ item.attrName }}</div>
        </div>
        <div class="infos">
          <el-row>
            <template v-for="(item2, index2) in item.children" :key="index2">
              <el-col :span="8" :push="1" v-if="!item.isVirtual">
                <div class="info-item">
                  <span class="info-label">{{ item2.attrName }}：</span>
                  <span class="info-text">{{ item2.attrValueName }}</span>
                </div>
              </el-col>
            </template>
          </el-row>
        </div>
      </div>

      <div class="info-box card" v-if="item.attrCode === 'FEE_PRODUCT_INFO' || item.attrCode === 'PRODUCT_INFO'">
        <div class="box-title">
          <div class="title">{{ item.attrName }}</div>
        </div>
        <div class="infos">
          <template v-for="(item2, index2) in item.children" :key="index2">
            <el-collapse>
              <el-collapse-item name="1">
                <template #title>
                  <el-row style="width: 100%">
                    <el-col :span="8" :push="1">
                      <div class="info-item">
                        <span class="info-label">{{ item2.children[0].attrName }}：</span>
                        <span class="info-text">{{ item2.children[0].attrValueName }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8" :push="1">
                      <div class="info-item">
                        <span class="info-label">{{ item2.children[1].attrName }}：</span>
                        <span class="info-text">{{ item2.children[1].attrValueName }}</span>
                      </div>
                    </el-col>
                  </el-row>
                </template>

                <template #default>
                  <el-row>
                    <template v-for="(item3, index3) in item2.children" :key="index3">
                      <el-col :span="8" :push="1" v-if="index3 > 1 && !item3.isVirtual">
                        <div class="info-item" :class="index3 % 3 === 1 ? 'pr80' : ''">
                          <span class="info-label">{{ item3.attrName }}：</span>
                          <span class="info-text">{{ item3.attrValueName }}</span>
                        </div>
                      </el-col>
                    </template>
                  </el-row>
                </template>
              </el-collapse-item>
            </el-collapse>
          </template>
        </div>
      </div>
    </template>
    <div class="info-box card">
      <div class="box-title">
        <div class="title">账号信息</div>
        <div class="sub-tips" v-show="tableData?.length > 1 && auditTagType === 1"></div>
      </div>

      <accountTable :data="tableData" :is-edit="isEdit" :order-info="orderInfo" :table-data-params="tableDataParams">
      </accountTable>
    </div>
    <div class="info-box card">
      <div class="box-title">
        <div class="title">处理信息</div>
        <!-- <div class="sub-tips" v-show="tableData?.length > 1 && auditTagType === 1"></div> -->
      </div>

      <handleTable :data="dealWithData"> </handleTable>
    </div>
    <!--  && orderInfo.auditStatus === '2' -->
    <template v-if="isEdit">
      <examine :order-id="orderInfo.parentOrderNo" @on-change="handleChange"></examine>
    </template>
    <div class="order-info" v-if="!isEdit">
      <el-button plain @click="handleBack"> 返回订单列表 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="viewPortalOrder">
import accountTable from "../components/accountTable.vue";
import handleTable from "../components/handleTable.vue";
import { computed, onMounted, ref } from "vue";
import Examine from "../components/examine.vue";
import { parentOrderDetail, subOrderDetail } from "@/api/modules/order";
import { useRoute, useRouter } from "vue-router";
/* 获取字典 */
const isEdit = computed(() => {
  // && orderInfo.value?.auditStatus == "2"
  return auditTagType.value === 1 && orderInfo.value?.status == "9";
});
// const { ord_pay_type, ord_order_type, ord_audit_status } = useDict("ord_pay_type", "ord_order_type", "ord_audit_status");
// 账号列表请求参数
const tableDataParams = ref({
  platform: "",
  mainPhone: "",
  subPhones: ""
  // userAccount: ""
});
const route = useRoute();
const router = useRouter();
const orderInfo = ref<Record<string, any>>({});
const auditTagType = ref<number>(0);
const orderArray = ref([] as any[]);
const tableData = ref([] as any[]);
const handleChange = () => {
  getDetailList({
    parentOrderNo: orderInfo.value.parentOrderNo,
    auditTag: 1
  });
  // parentOrderDetail().then(res => {
  //   res.data.subOrderDetailList.forEach((item: any) => {
  //     orderArray.value = [...orderArray.value, ...item.attributes];
  //   });
  // });
};
const dealWithData = computed(() => {
  return [
    {
      orderType: orderInfo.value.orderType,
      status: orderInfo.value.status,
      operateRemark: orderInfo.value.operateRemark,
      operatorName: orderInfo.value.operatorName,
      operatorTime: orderInfo.value.operatorTime
    }
  ];
});

const handleBack = () => {
  router.back();
};

onMounted(() => {
  initData();
});

const initData = () => {
  const { orderId, auditTag, subOrderId } = route.query;

  auditTagType.value = Number(auditTag);
  if (Reflect.has(route.query, "orderId") && Reflect.has(route.query, "auditTag")) {
    getDetailList({
      parentOrderNo: orderId,
      auditTag: 1
    });
  }
  if (Reflect.has(route.query, "subOrderId")) {
    subOrderDetail({
      subOrderId: subOrderId
    }).then(res => {
      orderInfo.value = res.data;
    });
  }
};

function getDetailList(params: any) {
  tableDataParams.value = {
    platform: "",
    mainPhone: "",
    subPhones: ""
    // userAccount: ""
  };
  parentOrderDetail(params).then(res => {
    orderInfo.value = res.data;
    // tableDataParams.value.userAccount = orderInfo.value.customerAccount;
    res.data.subOrderDetailList.forEach((item: any) => {
      item.attributes.forEach((item2: any) => {
        if (item2?.attrCode === "ACCOUNT_INFO") {
          item2.children.forEach((item3: any) => {
            if (!item3) return;
            const paramsObject: any = {
              PLATFORM: () => {
                //平台
                item3.attrValue ? (tableDataParams.value.platform = item3.attrValue) : "";
              },
              MAIN: () => {
                //主账号
                tableDataParams.value.mainPhone = item3.attrValue || "";
              },
              SUB: () => {
                if (item3.attrValue) {
                  //子账号
                  tableDataParams.value.subPhones = item3.attrValue || "";
                }
              }
            };
            paramsObject[item3.attrCode]();
          });
        }
      });
      orderArray.value = [...orderArray.value, ...item.attributes];
    });
  });
}
</script>

<style scoped lang="scss">
.order-info {
  margin-top: 30px;
  margin-bottom: 10px;
  text-align: left;
  :deep(.el-button) {
    height: 36px;
    margin-right: 16px;
  }
}
.page-title {
  padding: 26px 23px;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}
.table {
  padding: 25px;
}
.info-box {
  margin-bottom: 15px;
  .box-title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-weight: 600;
    color: #3a3a3d;
    border-bottom: 1px solid #e4e7ed;
    .title {
      font-size: 18px;
      .card-btn {
        float: right;
        margin-top: -5px;
        margin-right: 10px;
      }
    }
    .sub-tips {
      color: red;
    }
  }
  .info-item {
    margin-bottom: 20px;
    margin-left: 16px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
}
.total-price {
  margin-top: 36px;
  font-size: 24px;
  font-weight: 600;
  color: #0052d9;
  text-align: right;
  span {
    font-size: 14px;
    font-weight: 400;
    color: #3a3a3d;
  }
}
</style>
