<template>
  <el-dialog
    v-model="modalVisible"
    width="1000"
    :title="isEdit ? '编辑权益' : '新增权益'"
    :destroy-on-close="true"
    :before-close="handleClose"
    append-to-body
  >
    <el-form ref="oneFormRef" :model="oneForm" label-position="left" :rules="rules" label-width="200px">
      <el-form-item label="权益名称" prop="name">
        <el-input :max="20" v-model="oneForm.name" placeholder="输入内容" />
      </el-form-item>

      <el-form-item label="权益图片" prop="pic">
        <UploadImg :file-size="2" v-model:image-url="oneForm.pic" width="135px" height="135px">
          <template #tip>建议上传图片尺寸为50px×50px,图片大小不超过2M，支持图片格式jpg/jpeg/png</template>
        </UploadImg>
      </el-form-item>

      <el-form-item label="合作商名称" prop="providerId">
        <el-select v-model="oneForm.providerId" placeholder="请选择" @change="handleChange">
          <el-option v-for="item in providerList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="开通接口" prop="apiSwitch">
        <el-radio-group v-model="oneForm.apiSwitch">
          <el-radio v-for="item in right_api_switch" :key="item.itemKey" :label="item.itemKey">{{ item.itemValue }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="门户权益使用类型" prop="useType">
        <el-radio-group v-model="oneForm.useType" @change="handleUseType">
          <el-radio v-for="item in right_useType" :key="item.itemKey" :label="item.itemKey">{{ item.itemValue }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <formTypeItem
        :source="'portal'"
        :type-val="oneForm.useType"
        v-model:descVal="oneForm.portalUseDescription"
        v-model:qrCodeUrl="oneForm.portalQRCodeUrl"
        v-model:qrCodeDesc="oneForm.portalQRCodeDescription"
        v-model:linkUrl="oneForm.url"
      ></formTypeItem>

      <el-form-item label="小程序、H5权益使用类型" prop="miniAppsUseType">
        <el-radio-group v-model="oneForm.miniAppsUseType">
          <el-radio v-for="item in right_useType" :key="item.itemKey" :label="item.itemKey">{{ item.itemValue }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <formTypeItem
        :source="'miniApps'"
        :type-val="oneForm.miniAppsUseType"
        v-model:descVal="oneForm.miniAppsUseDescription"
        v-model:qrCodeUrl="oneForm.miniAppsQRCodeUrl"
        v-model:qrCodeDesc="oneForm.miniAppsQRCodeDescription"
        v-model:linkUrl="oneForm.miniAppsUrl"
      ></formTypeItem>

      <el-form-item label="启用状态" prop="status">
        <el-radio-group v-model="oneForm.status">
          <el-radio v-for="item in right_status" :key="item.itemKey" :label="item.itemKey">{{ item.itemValue }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="goBack()"> 取消 </el-button>
      <el-button type="primary" @click="submitForm(oneFormRef)"> 确定 </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
interface IProvider {
  id: string;
  name: string;
}
export type IAction = "Add" | "Edit";

import { onMounted, reactive, ref, toRaw, nextTick, toRefs } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import UploadImg from "@/components/Upload/Img.vue";
import formTypeItem from "../components/formTypeItem.vue";
import { useDict } from "@/hooks/useDict";
import { pmsProviderNoPageList, queryViewDetail, queryCreatePmsRights, queryUpdatePmsRights } from "@/api/modules/benefitManage";
import { PmsRights } from "@/api/interface/benefitManage";

const { right_api_switch, right_status, right_useType } = useDict("right_api_switch", "right_status", "right_useType");
const emit = defineEmits(["refresh"]);

const providerList = ref<IProvider[]>([]);

const modalVisible = ref(false);

const fetch = () => {
  pmsProviderNoPageList().then((res: any) => {
    providerList.value = res.data;
  });
};

onMounted(() => {
  fetch();
});

const isEdit = ref<boolean>(false);

const oneFormRef = ref<FormInstance>();

let oneForm = reactive<PmsRights.CreateParams>({
  name: "",
  pic: "",
  providerId: "",
  providerName: "",
  apiSwitch: "0",
  useType: "1",
  url: "",
  miniAppsUseType: "1",
  miniAppsUrl: "",
  status: "1",
  portalQRCodeDescription: "",
  portalUseDescription: "",
  portalQRCodeUrl: "",
  miniAppsUseDescription: "",
  miniAppsQRCodeUrl: "",
  miniAppsQRCodeDescription: ""
});

const rowData = ref<PmsRights.EditParams>({ ...oneForm, id: "" });

const rules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入权益名称", trigger: "blur" },
    { max: 20, message: "最多输入20个字符", trigger: "blur" }
  ],
  pic: [{ required: true, message: "请上传图片", trigger: "change" }],
  providerId: [{ required: true, message: "请选择合作商", trigger: "change" }],
  apiSwitch: [{ required: true, message: "请选择开通接口", trigger: "blur" }],
  useType: [{ required: true, message: "请选择门户权益使用类型", trigger: "blur" }],
  url: [
    { max: 150, message: "最多输入150个字符", trigger: "blur" },
    {
      validator: (rule: any, value: any, callback: any) => {
        const portalQRCodeUrl = oneForm.portalQRCodeUrl === "" || oneForm.portalQRCodeUrl === null;

        if (oneForm.useType === "3") {
          if (portalQRCodeUrl && value === "" && value === null) {
            callback(new Error("二维码和跳转链接必须选择1种方式配置"));
          } else if (!portalQRCodeUrl && value !== "" && value !== null) {
            callback(new Error("二维码和跳转链接只可选择1种方式输入"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  portalUseDescription: [
    { required: true, message: "请输入使用指引说明", trigger: "blur" },
    { max: 500, message: "最多输入150个字符", trigger: "blur" }
  ],

  portalQRCodeUrl: [
    // { required: true, message: "请上传二维码" },
    {
      validator: (rule: any, value: any, callback: any) => {
        const url = oneForm.url === "" || oneForm.url === null;
        if (oneForm.useType === "3") {
          if (url && (value === "" || value === null)) {
            callback(new Error("二维码和跳转链接必须选择1种方式配置"));
          } else if (value !== "" && value !== null && !url) {
            callback(new Error("二维码和跳转链接只可选择1种方式输入"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    }
  ],
  portalQRCodeDescription: [{ required: true, max: 500, message: "最多输入150个字符", trigger: "blur" }],
  miniAppsUseType: [{ required: true, message: "请选择小程序、H5权益使用类型", trigger: "blur" }],
  miniAppsUrl: [
    { max: 150, message: "最多输入150个字符", trigger: "blur" },
    {
      validator: (rule: any, value: any, callback: any) => {
        const miniAppsQRCodeUrl = oneForm.miniAppsQRCodeUrl === "" || oneForm.miniAppsQRCodeUrl === null;

        if (oneForm.miniAppsUseType === "3") {
          if (miniAppsQRCodeUrl && value === "" && value === null) {
            callback(new Error("二维码和跳转链接必须选择1种方式配置"));
          } else if (!miniAppsQRCodeUrl && value !== "" && value !== null) {
            callback(new Error("二维码和跳转链接只可选择1种方式输入"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    }
  ],
  miniAppsQRCodeUrl: [
    {
      validator: (rule: any, value: any, callback: any) => {
        const url = oneForm.miniAppsUrl === "" || oneForm.miniAppsUrl === null;
        if (oneForm.miniAppsUseType === "3") {
          if (url && (value === "" || value === null)) {
            callback(new Error("二维码和跳转链接必须选择1种方式配置"));
          } else if (value !== "" && value !== null && !url) {
            callback(new Error("二维码和跳转链接只可选择1种方式输入"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    }
  ],
  miniAppsQRCodeDescription: [{ required: true, max: 500, message: "最多输入150个字符", trigger: "blur" }],
  miniAppsUseDescription: [
    { required: true, message: "请输入使用指引说明", trigger: "blur" },
    { max: 500, message: "最多输入150个字符", trigger: "blur" }
  ],
  status: [{ required: true, message: "请选择启动状态", trigger: "blur" }]
});

const handleUseType = (value: string) => {
  nextTick(() => {
    if (value === "3") {
      oneFormRef.value?.clearValidate("url");
    }
  });
};

const handleChange = (value: string) => {
  const provider = providerList.value.find((item: IProvider) => item.id === value);
  oneForm.providerId = provider.id;
  oneForm.providerName = provider.name;
};

const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;

  formEl.validate().then(valid => {
    if (valid) {
      const data = {
        ...oneForm
      };
      if (isEdit.value) {
        queryUpdatePmsRights({ ...data, id: rowData.value.id }).then(res => {
          if (res.code === 200) {
            ElMessage.success("编辑成功");
            modalVisible.value = false;
            emit("refresh");
          }
        });
      } else {
        queryCreatePmsRights(data).then(res => {
          if (res.code === 200) {
            ElMessage.success("新增成功");
            modalVisible.value = false;
            emit("refresh");
          }
        });
      }
    }
  });
};

const goBack = () => {
  modalVisible.value = false;
};

const handleOpen = (active: IAction, rowId: string) => {
  modalVisible.value = true;
  if (active === "Add") {
    isEdit.value = false;

    oneForm.name = "";
    oneForm.pic = "";
    oneForm.providerId = "";
    oneForm.providerName = "";
    oneForm.apiSwitch = "0";
    oneForm.useType = "1";
    oneForm.url = "";
    oneForm.miniAppsUseType = "1";
    oneForm.miniAppsUrl = "";
    oneForm.status = "1";
    oneForm.portalQRCodeDescription = "";
    oneForm.portalUseDescription = "";
    oneForm.portalQRCodeUrl = "";
    oneForm.miniAppsUseDescription = "";
    oneForm.miniAppsQRCodeUrl = "";
    oneForm.miniAppsQRCodeDescription = "";
  } else {
    isEdit.value = true;
    if (rowId) {
      console.log(3);
      queryViewDetail(rowId)
        .then((res: any) => {
          rowData.value = res.data;
          Object.assign(oneForm, res.data);
          oneForm.status = String(oneForm.status);
        })
        .catch(err => {});
    }
  }
};

const handleClose = () => {
  modalVisible.value = false;
};

defineExpose({ handleOpen });
</script>

<style scoped lang="scss"></style>
