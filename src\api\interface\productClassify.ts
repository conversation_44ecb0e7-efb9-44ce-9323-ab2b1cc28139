/*
 * @Author: <EMAIL>
 * @Date: 2023-09-15 10:26:13
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-09-20 20:18:30
 * @Description: file content
 */
//标签模块

export interface deleteParams {
  ids: string;
}
//分页
export interface pageParams {
  ascs?: string;
  current?: number;
  descs?: string;
  name?: string;
  size?: number;
  parentId?: string;
}
//分类
export interface typeParams {
  description: string;
  icon: string;
  name: string;
  parentId: string;
  sort: number;
  id?: string;
  parentName?: string;
}
