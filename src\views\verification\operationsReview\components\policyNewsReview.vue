<template>
  <div class="card">
    <div class="header-title-common title">审核内容</div>
    <policy-news-content :is-edit="false" :id="id" />

    <examine
      :verification-id="'id'"
      :audit-msg="reviewForm.auditMsg"
      :is-disabled="viewStatus"
      :is-show-operate-btn="!viewStatus"
      :is-show-backed-btn="false"
      @on-confirm="onConfirm"
    >
    </examine>

    <div class="other-dynamic-box">
      <p
        class="other-dynamic"
        @click="refreshContent(prePolicyNews.bizId, prePolicyNews.id)"
        v-if="prePolicyNews && prePolicyNews.title"
      >
        上一篇：{{ prePolicyNews.title }}
      </p>
      <p
        class="other-dynamic"
        @click="refreshContent(nextPolicyNews.bizId, nextPolicyNews.id)"
        v-if="nextPolicyNews && nextPolicyNews.title"
      >
        下一篇：{{ nextPolicyNews.title }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts" name="policyNewsReview">
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { closeTabItem } from "@/utils/closeTab";
import { backtoOperationList, OperateType } from "../type";
import policyNewsContent from "./policyNewsContent.vue";
import Examine from "@/views/verification/examine.vue";
import { policyNewsStorage } from "@/utils/storage/edit";
import { bannerDoAudit, policyAuditDetail } from "@/api/modules/business/banner";

const route = useRoute();
const router = useRouter();
const id: string = (route.query.id as string) || "";
const type = Number(route.query.type as string) || OperateType.detail;
const viewStatus = type === OperateType.detail;
const auditRemark = (route.query.auditMsg as string) || "";
const PENDING_AUDIT = 1; // 待审核

const prePolicyNews = ref();
const nextPolicyNews = ref();

const reviewForm = ref({
  auditMsg: auditRemark
});

onMounted(() => {
  const policy = policyNewsStorage.get(id);
  prePolicyNews.value = policy?.preAuditVO || {};
  nextPolicyNews.value = policy?.nextAuditVO || {};
});
const onConfirm = (values: any) => {
  const policy = policyNewsStorage.get(id);
  bannerDoAudit({
    id: policy?.listId,
    auditMsg: values.auditRemark,
    status: values.type == "pass" ? 2 : 3,
    bizType: "policy"
  }).then(res => {
    if (res.code === 200) {
      ElMessage({
        message: values.type == "pass" ? "审核通过成功!" : "审核不通过完成!",
        type: "success"
      });
      closeTabItem(route);
      backtoOperationList();
    } else {
      ElMessage({
        type: "error",
        message: res.msg
      });
    }
  });
};

const refreshContent = (bizId: string, id: string) => {
  if (id) {
    policyAuditDetail({ bizId, id })
      .then(resp => {
        const respData: any = resp.data || {};
        policyNewsStorage.set(id, { ...respData, listId: id });
        router.push({
          path: "/verification/operationsReview/policyNewsReview",
          query: {
            id,
            type: respData?.status === PENDING_AUDIT ? OperateType.review : OperateType.detail,
            auditMsg: auditRemark
          }
        });
      })
      .catch(err => {
        console.log(err);
      });
  }
};
</script>

<style lang="scss" scoped>
.title {
  margin-bottom: 16px;
}
.other-dynamic-box {
  margin-top: 40px;
  .other-dynamic {
    margin-top: 16px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #3a3a3d;
    cursor: pointer;
    &:first-child {
      margin-top: 0;
    }
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
