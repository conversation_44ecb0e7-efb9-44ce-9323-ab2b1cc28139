<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="editTypeChinese[props.type] + '部门'" width="30%">
      <el-form ref="ruleFormRef" :model="labelForm" :rules="rules" label-width="120px" label-position="top">
        <el-form-item label="上级部门" class="horizontal" prop="orgId" v-if="props.type == 1">
          <el-text>{{ labelForm.parentOrgName }}</el-text>
          <!-- <el-input class="label-name" v-model="labelForm.orgId" readonly></el-input> -->
        </el-form-item>
        <el-form-item label="部门名称" prop="orgName">
          <el-input class="label-name" v-model="labelForm.orgName" placeholder="请输入部门名称" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="部门编码" prop="orgCode">
          <el-input class="label-name" v-model="labelForm.orgCode" placeholder="请输入部门编码" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            class="label-name"
            v-model="labelForm.description"
            type="textarea"
            placeholder="请输入描述"
            maxlength="255"
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import { computed, ref, reactive, watch } from "vue";
import type { FormInstance } from "element-plus";
import { saveOrg as addTypeApi, updateOrg as updateTypeApi } from "@/api/modules/system";
import { ElMessage } from "element-plus";
// import { Label } from "@/api/interface/label";
const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: 1
  },
  params: {
    type: Object,
    default: () => {}
  }
});
const ruleFormRef = ref();
const labelForm = ref<any>({
  name: "",
  description: "",
  orgId: "",
  orgName: ""
});
const emits = defineEmits(["update:visible", "cancel", "confirm"]);
watch(
  () => props.params,
  () => {
    labelForm.value.orgName = props.params.orgName || "";
    labelForm.value.orgCode = props.params.orgCode || "";
    labelForm.value.description = props.params.description || "";
    labelForm.value.orgId = props.params.orgId;
    labelForm.value.parentOrgName = props.params.parentOrgName || "";
  }
);
const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
};

const rules = reactive({
  orgName: [
    { trigger: "blur", required: true, message: "部门名称不能为空" },
    { max: 50, message: "部门名称长度1~50位" }
  ],
  orgCode: [
    { trigger: "blur", required: true, message: "部门编码不能为空" },
    { max: 50, message: "部门编码长度1~50位" }
  ],
  description: [{ max: 100, message: "部门编码最大长度100位" }]
});
const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    labelForm.value = {
      name: "",
      list: []
    };
    emits("update:visible", value);
  }
});

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;

  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: addTypeApi,
        [editType["edit"]]: updateTypeApi
      };
      const requestParamsObject = {
        [editType["add"]]: () => {
          return {
            orgName: labelForm.value.orgName,
            orgCode: labelForm.value.orgCode,
            description: labelForm.value.description,
            parentId: props.params.id // 新增传上级组织DI
          };
        },
        [editType["edit"]]: () => {
          return {
            orgName: labelForm.value.orgName,
            orgCode: labelForm.value.orgCode,
            description: labelForm.value.description,
            id: props.params.id // 编辑传组织id
          };
        }
      };
      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        emits("confirm");
        ElMessage.success(res.msg);
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
.horizontal.el-form-item {
  display: flex;
  align-items: center;
  :deep(.el-form-item__label) {
    margin-bottom: 0;
  }
}
</style>
