import http from "@/api";
import { System } from "@/api/interface/system";

// 图片上传
export const getDictionary = (params: any) => {
  return http.get(`/system/common/dict/dictList`, params);
};

// 角色列表
export const getRoleList = (params: System.RoleListParams) => {
  return http.get(`/system/role/list`, params);
};

// 查看角色信息
export const getRoleInfo = (params: { id: string }) => {
  return http.get(`/system/role/info`, params);
};

// 保存角色
export const saveRole = (params: System.SaveRoleParams) => {
  return http.post(`/system/role/save`, params);
};

// 修改角色
export const updateRole = (params: System.SaveRoleParams) => {
  return http.post(`/system/role/update`, params);
};
// 删除角色
export const deleteRole = (params: System.DeleteRoleParams) => {
  return http.post(`/system/role/delete`, params);
};

// 树结构列表
export const getTreeList = (params: {}) => {
  return http.get(`/system/menu/treeList`, params);
};

// 组织树结构列表
export const getOrgTreeList = (params: System.OrgTreeListParams) => {
  return http.get(`/system/org/treeList`, params);
};

// 组织保存
export const saveOrg = (params: System.OrgOperateParams) => {
  return http.post(`/system/org/save`, params);
};

// 组织修改
export const updateOrg = (params: System.OrgOperateParams) => {
  return http.post(`/system/org/update`, params);
};

// 组织删除
export const deleteOrg = (params: { ids: string[] }) => {
  return http.post(`/system/org/delete`, params);
};

// 组织排序
export const sortOrg = (params: System.OrgOperateParams) => {
  return http.post(`/system/org/sort`, params);
};

// 用户个人信息列表
export const getOperateUserList = (params: System.OrgDeleteParams) => {
  return http.get(`/system/operateUser/list`, params);
};

// 用户个人信息列表
export const getAllUserList = (params: any) => {
  return http.get(`/system/portalUser/list`, params);
};

// 修改个人信息列表
export const updateList = (params: any) => {
  return http.post(`/system/operateUser/update`, params);
};

// 重置密码-返回密码信息
export const resetPassWord = (params: { id: string }) => {
  return http.post(`/system/operateUser/resetPassWord`, params);
};

// 变更组织
export const updateUserOrg = (params: System.UpdateUserOrg) => {
  return http.post(`/system/operateUser/userOrg`, params);
};

// 新增用户-返回密码信息
export const saveUser = (params: any) => {
  return http.post(`/system/operateUser/save`, params);
};

// 导出列表
export const exportOperateUser = (params: System.ExportUserParams) => {
  return http.get(`/system/operateUser/export`, params, { responseType: "blob" });
};

// 删除用户
export const deleteUser = (params: any) => {
  return http.post(`/system/operateUser/delete`, params);
};

// 启用或停用账户
export const statusUser = (params: System.StatusUserParams) => {
  return http.post(`/system/operateUser/status`, params);
};

// 获取验证码-指定渠道与修改类型
export const getVerifyCode = (params: System.UserVerifyCode) => {
  return http.post(`/system/operateUser/verifyCode`, params);
};

// 校验验证码-指定渠道与修改类型
export const verifyChecked = (params: System.UserVerifyCode) => {
  return http.post(`/system/operateUser/verifyCheck`, params);
};

// 修改手机号码、邮箱、密码
export const getNewVerifyCode = (params: System.UserVerifyCode) => {
  return http.post(`/system/operateUser/newVerifyCode`, params);
};

// 修改手机号码、邮箱、密码
export const modifyVerifyInfo = (params: System.UserVerifyCode) => {
  return http.post(`/system/operateUser/verifyInfo`, params);
};

/** 参数配置 -- start */
// 列表
export const getParamConfig = (params: System.ParamConfigParams) => {
  return http.get(`/system/paramConfig/list`, params);
};

// 保存
export const saveParamConfig = (params: System.ParamConfigParams) => {
  return http.post(`/system/paramConfig/save`, params);
};

// 修改
export const updateParamConfig = (params: System.ParamConfigParams) => {
  return http.post(`/system/paramConfig/update`, params);
};

// 删除
export const deleteParamConfig = (params: { ids: string[] }) => {
  return http.post(`/system/paramConfig/delete`, params);
};

// 信息
export const getParamConfigInfo = (params: { id: string }) => {
  return http.post(`/system/paramConfig/info`, params);
};

// 获取单个配置信息
export const searchConfigInfo = (params: { paramKey: string }) => {
  return http.post(`/system/paramConfig/search`, params);
};

/** 参数配置 -- end */

/** 菜单管理 -- start */
// 菜单列表
export const getMenuList = (params: { paramKey: System.MenuListParams }) => {
  return http.get(`/system/menu/list`, params);
};

// 菜单保存
export const saveMenu = (params: { paramKey: System.MenuListParams }) => {
  return http.post(`/system/menu/save`, params);
};

// 菜单修改
export const updateMenu = (params: { paramKey: System.MenuListParams }) => {
  return http.post(`/system/menu/update`, params);
};

// 菜单删除
export const deleteMenu = (params: { ids: string[] }) => {
  return http.post(`/system/menu/delete`, params);
};

// 菜单信息
export const getMenuInfo = (params: { id: string }) => {
  return http.post(`/system/menu/info`, params);
};

/** 菜单管理 -- end */

/** 字典管理 -- start */
// 系统字典列表
export const getDictionaryList = (params: System.DictionaryList) => {
  return http.get(`/system/dict/list`, params);
};

// 系统字典保存
export const saveDictionary = (params: System.DictParams) => {
  return http.post(`/system/dict/save`, params);
};

// 系统字典修改
export const updateDictionary = (params: System.DictParams) => {
  return http.post(`/system/dict/update`, params);
};

// 系统字典删除
export const deleteDictionary = (params: { ids: string[] }) => {
  return http.post(`/system/dict/delete`, params);
};

// 系统字典信息
export const getDictionaryInfo = (params: { id: string }) => {
  return http.get(`/system/dict/info/`, params);
};

/** 字典管理 -- end */
// 客户管理（门户用户）列表
export const getPortalUserList = (params: System.PortalUserParams) => {
  return http.get(`/system/portalUser/list`, params);
};

/** 系统日志 -- start */
// 审计日志表-列表查询(分页)
export const getLogAudit = (params: System.LogParams) => {
  return http.get(`/cnud-log/logAudit/pageList`, params);
};
/** 系统日志 -- end */

/* 根据用户id查询认证信息查询 */

export const userCertificateInfo = (params: { id: string }) => {
  return http.get(`/system/certificate/userCertificateInfo`, params);
};

/* 企业认证信息表-新增企业认证与用户开通门户账号 */
export const saveUserEnterprise = (data: { enterpriseCertificate: any; portalUser: any }) => {
  return http.post(`/system/enterpriseCertificate/enterpriseCertificate-saveUserEnterprise`, data);
};

export const paramConfigSearch = (paramKey: string): Promise<any> => {
  return http.post<any>("/system/common/paramConfig/search", { paramKey });
};
