import dayjs from "dayjs";

// 默认显示近31天的日期，t-31到t-1;
export const startDate = dayjs().subtract(31, "days").format("YYYY-MM-DD");
export const endDate = dayjs().subtract(1, "day").format("YYYY-MM-DD");

export const startMonthDate = dayjs().subtract(11, "month").format("YYYY-MM");
export const endMonthDate = dayjs().startOf("month").format("YYYY-MM");

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}
// 月报表入参月份
export const handleCountDate = (start: any, end: any, dateTypeValue = DateType.day) => {
  if (dateTypeValue === DateType.month) {
    return {
      countEndDate: dayjs(start).format("YYYY-MM"),
      countStartDate: dayjs(end).format("YYYY-MM")
    };
  }
  return {};
};
