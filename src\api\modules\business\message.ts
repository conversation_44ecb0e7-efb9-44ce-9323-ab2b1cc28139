import http from "@/api";
import { array } from "@amcharts/amcharts4/core";
import { number } from "echarts";
export const getPageList = (data: any, auditList?: number) => {
  return http.get("/cnud-transmit/admin/notice/queryNoticeList", { ...data, auditList });
}; //消息列表（分页）

// message审核详情接口
export const messageAuditDetail = (noticeId: number, auditList?: number) => {
  return http.post("/cnud-transmit/admin/notice/queryNoticeDetail", { noticeId: noticeId, auditList: auditList });
};

// 审核接口
export const messageDoAudit = (auditRemark: string, noticeId: number, result: string) => {
  return http.post("/cnud-transmit/admin/notice/auditMessage", { auditRemark: auditRemark, noticeId: noticeId, result: result });
};

export const cancel = (id: number) => {
  return http.post("/cnud-transmit/admin/notice/cancelNotice", { noticeIdList: [id] });
}; //取消发布

export const add = (data: any) => {
  return http.post("/cnud-transmit/admin/notice/submitNotice", data);
}; //新增发布

export const save = (data: any) => {
  return http.post("/cnud-transmit/admin/notice/saveNotice", data);
}; //保存发布

export const messageRevoke = (id: number) => {
  return http.post("/cnud-transmit/admin/notice/withdrawNotice", { noticeIdList: [id] });
}; //撤回发布

export const del = (id: number) => {
  return http.post("/cnud-transmit/admin/notice/deleteNotice", { noticeIdList: [id] });
}; //删除发布
