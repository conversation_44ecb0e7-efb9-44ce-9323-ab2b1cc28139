<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-22 09:56:46
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-28 09:07:37
 * @Description: file content
-->
<template>
  <el-table
    :data="tableData"
    class="w100"
    border
    :header-cell-style="{ fontWeight: 'bold', color: '#303133', background: '#f5f7fa' }"
  >
    <el-table-column align="left" prop="subOrderNo" label="账号类型">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" :content="accountTypeEnum[row.isMainAccount]" placement="top-start">
            {{ accountTypeEnum[row?.isMainAccount] }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="providerSkuId" label="账号">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="row?.phone" placement="top-start">
            {{ row?.phone }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="productName" label="客户账号">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="row?.userAccount" placement="top-start">
            {{ row?.userAccount }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="skuName" label="合作平台">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="cooperatePlatformDicObject[row?.platform]" placement="top-start">
            {{ cooperatePlatformDicObject[row?.platform] }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="orderType" label="合作平台账号">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="row?.platformAccount" placement="top-start">
            {{ row?.platformAccount }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>

    <el-table-column align="left" label="操作" width="130" v-if="isEdit">
      <template #default="{ row }">
        <span v-if="row.platformAccount" v-auth:partnerAccountManage="'unbind'">
          <el-tooltip content="解绑" placement="top">
            <i class="iconfont2 opera-icon icon-chexiao" @click="showBindDialog(row, operateTypeConfig.UNBIND)"></i>
          </el-tooltip>
        </span>
        <span v-else v-auth:partnerAccountManage="'bind'">
          <el-tooltip content="绑定" placement="top">
            <i class="iconfont2 opera-icon icon-zhuanhuan" @click="showBindTableDialog(row)"></i>
          </el-tooltip>
        </span>
      </template>
    </el-table-column>
  </el-table>
  <BindDialog ref="bindDialogRef" @update="getData"></BindDialog>
  <account-dialog-table v-model:visible="showAccountDialog" :search-params="searchParams"></account-dialog-table>
</template>

<script setup lang="ts">
interface TablePropType {
  data?: any[];
}

import { operateTypeConfig } from "@/views/partner/partnerAccountManage/config";
import { accountTypeEnum } from "@/enums/orderEnum";
import { getPlatformAccount } from "@/api/modules/order";
import accountDialogTable from "./accountDialogTable.vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import BindDialog from "@/views/partner/partnerAccountManage/components/bindDialog.vue";
import { storeToRefs } from "pinia";
import { ref, watch } from "vue";
const dictionaryStore = useDicStore();

const showAccountDialog = ref(false);
const searchParams = ref({});
const bindDialogRef = ref<InstanceType<BindDialog>>();
const tableData = ref([]);
/* 获取字典 */
const { cooperatePlatformDic } = storeToRefs(dictionaryStore);
const cooperatePlatformDicObject: any = {};
cooperatePlatformDic.value?.forEach((item: any) => {
  cooperatePlatformDicObject[item.value] = item.label;
});
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  tableDataParams: {
    type: Object,
    default: () => {}
  },
  orderInfo: {
    type: Object,
    default: () => {}
  }
});

watch(
  [() => props.tableDataParams, showAccountDialog],
  () => {
    getData();
  },
  {
    deep: true
  }
);
const showBindDialog = (row: any, type: string) => {
  bindDialogRef.value!.show({
    type,
    id: row.id,
    userAccount: row.userAccount,
    subOrderNo: props.orderInfo.parentOrderNo,
    disabled: true
  });
};
const showBindTableDialog = (row: any) => {
  searchParams.value = {
    isBind: 0,
    platform: props.tableDataParams.platform,
    userAccount: row.userAccount,
    subOrderNo: props.orderInfo.parentOrderNo
  };
  showAccountDialog.value = true;
};
const getData = () => {
  if (props.tableDataParams?.platform) {
    getPlatformAccount(props.tableDataParams).then(res => {
      tableData.value = res.data as any;
    });
  }
};
</script>

<style scoped lang="scss">
.text {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.time-text {
  span {
    display: block;
  }
}
.w100 {
  width: 100%;
}
</style>
