/*
 * @Author: <EMAIL>
 * @Date: 2023-10-27 15:53:06
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-11-23 21:00:45
 * @Description: file content
 */
import http from "@/api";

/**
 * @name 订单管理模块
 */

// 订单管理列表 multiPoint：1-商城订单 2-生态触点订单 3-政企中台订单
export const getOrderList = (params: Object) => {
  return http.get(`/cnud-order/order-manage/support-platform/page`, params);
};

/**
 * 父子订单详情页面
 * @param params
 * @returns
 */
export const parentOrderDetail = (params: Object) => {
  return http.get<Record<string, any>>(`/cnud-order/order-manage/common/parent-order-detail`, params);
};

/**
 * 子订单详情页面
 * @param params
 * @returns
 */
export const subOrderDetail = (params: Object) => {
  return http.get<Record<string, any>>("/cnud-order/order-manage/support-platform/detail", params);
};

/**
 * 订单审核
 * @param params
 * @returns
 */
export const orderAuditOrder = (params: { auditRemark: string; operate: string; parentOrderNo: string | number }) => {
  return http.post("/cnud-order/order-manage/support-platform/auditOrder", params);
};

/**
 * 新增订单（代客下单）
 * @param params
 * @returns
 */
export const addOrder = (params: Object) => {
  return http.post("/cnud-order/order-manage/support-platform/addOrder", params);
};
/**
 * 订单导出
 * @param params
 * @returns
 */
export const queryOrderListExcel = (params: any) => {
  return http.download(`/cnud-order/order-manage/support-platform/export`, params);
};
// 合作账号列表查询
export const getPlatformAccount = (params: any) => {
  return http.get<Array<Order.orderAccountListItem>>("/system/sysPlatformAccount/order-account-list", params);
};
// 政企中台报竣和退单
export const operateZQOrder = (params: any) => {
  return http.post("/cnud-order/out-service/zq-operate-order", params);
};

/**
 * 第三方订单查询
 * @param params
 * @returns
 */
export const getOtherOrderList = (params: Object) => {
  return http.get<Record<string, any>>("/cnud-order/admin/computing-portal/order/queryOrderInfo", params);
};
// 绑定订单合同
export const otherOrderUpload = (params: Object) => {
  return http.post<Record<string, any>>("/cnud-order/admin/computing-portal/order/uploadContract", params);
};

/**
 * 合同备案 列表
 * @param params
 * @returns
 */
export const getOrderContractList = (params: Object) => {
  return http.get<Record<string, any>>("/cnud-product/admin/computing-portal/contract/queryContractInfo", params);
};

/**
 * 合同备案 新增
 * @param params
 * @returns
 */
export const addContract = (params: Object) => {
  return http.post<Record<string, any>>("/cnud-product/admin/computing-portal/contract/addContract", params);
};

/**
 * 合同备案 修改
 * @param params
 * @returns
 */
export const modContract = (params: Object) => {
  return http.post<Record<string, any>>("/cnud-product/admin/computing-portal/contract/modContract", params);
};

/* 手动开通详情 */
export const getManualOrderDetail = (params: { auditTag: string; parentOrderNo: string }) => {
  return http.get<Record<string, any>>("/cnud-order/admin/order-manage/support-platform/manual-order-detail", params);
};

/* 手动开通详情报竣 */
export const manualOrderHandle = (data: {
  effectTime: string;
  effectType: number;
  operateRemark: string;
  parentOrderNo: string;
}) => {
  return http.post<Record<string, any>>("/cnud-order/admin/order-manage/support-platform/manual-order-handle", data);
};

/* 订单获取报错详细信息 */
export const getFailMessage = (data: { subOrderId: string }) => {
  return http.get<string | any>("/cnud-order/admin/order-manage/support-platform/failMessage", data);
};
