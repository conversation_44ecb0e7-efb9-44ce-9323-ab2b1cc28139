import http from "@/api";
import { Verification } from "@/api/interface/verification";
import { downloadMange } from "../interface/upload";

// 个人认证信息表-列表查询
export const getPersonalCertificate = (params: any) => {
  return http.get(`/system/personalCertificate/personalCertificate-pageList`, params);
};

// 个人认证信息表-详情
export const getPersonalCertDetail = (params: { id: string }) => {
  return http.get(`/system/personalCertificate/personalCertificate-detail`, params);
};
// 个人认证信息表-审核详情
export const queryPersonalCertDetail = (params: { id: string }) => {
  return http.get(`/system/admin/personalCertificate/personalCertificate-auditDetail`, params);
};

// 个人认证信息表-审核
export const auditPersonalCert = (params: Verification.AuditParams) => {
  return http.post(`/system/personalCertificate/personalCertificate-audit`, params);
};

// 企业认证信息表-列表查询
export const getEnterpriseCertificate = (params: Verification.QueryListParams) => {
  return http.get(`/system/enterpriseCertificate/enterpriseCertificate-pageList`, params);
};

// 企业认证信息表-详情
export const getEnterpriseCertDetail = (params: { id: string }) => {
  return http.get(`/system/enterpriseCertificate/enterpriseCertificate-detail`, params);
};

/**
 * 企业认证信息表-审核详情
 * @param params { id: string }
 * @returns
 */
export const queryEnterpriseCertDetail = (params: { id: string }) => {
  return http.get(`/system/admin/enterpriseCertificate/enterpriseCertificate-auditDetail`, params);
};

// 企业认证信息表-审核
export const auditEnterpriseCert = (params: Verification.AuditParams) => {
  return http.post(`/system/enterpriseCertificate/enterpriseCertificate-audit`, params);
};

/* 下载审核列表 */
export const queryDownloadAudit = (params: any) => {
  return http.post<Array<downloadMange.IDownLoadItem>>(`/cnud-file/admin/export-audit/download_audit`, params);
};

/* 审核 */
export const exportAuditHandle = (data: downloadMange.DownloadReviewType) => {
  return http.post(`/cnud-file/admin/export-audit/handle`, data);
};
