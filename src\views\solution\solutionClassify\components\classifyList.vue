<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="列表"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      @sort-change="sortChange"
      :request-auto="false"
      :tool-button="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button
          v-auth="'delete'"
          :icon="Delete"
          plain
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          删除
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="openDrawer(2, scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="deleteLabel(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
    <add-edit-type-dialog
      v-model:visible="labelDialogVisible"
      :type="labelDialogParams.type"
      :params="labelDialogParams.params"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-type-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { Delete } from "@element-plus/icons-vue";
import { type Sort } from "element-plus";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import { solutionCategory, solutionDeleteType } from "@/api/modules/solution";
import { Soluiton } from "@/api/interface/solution";
import addEditTypeDialog from "./addOrEditTypeDialog.vue";
import * as ProductType from "@/api/interface/productClassify";
import { typeStatus } from "@/utils/serviceDict";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

interface typeDialog {
  type: number;
  params: Object;
}
const emits = defineEmits(["modify"]);

const proTable = ref<ProTableInstance>();
const props = defineProps({
  parentItem: {
    type: Object,
    default: () => {}
  }
});
watch(
  () => props.parentItem,
  () => {
    proTable.value?.getTableList();
  }
);
onMounted(() => {
  proTable.value?.getTableList();
});
const onCancel = () => {};
const onConfirm = () => {
  proTable.value?.getTableList();
  emits("modify");
};
const dataCallback = (data: any) => {
  return data;
  // return data.record;
};
//排序
const sortChange = (data: Sort) => {
  const sortObject = {
    ascending: () => {
      initParam.ascs = data.prop;
      initParam.descs = "";
    },
    descending: () => {
      initParam.ascs = "";
      initParam.descs = data.prop;
    }
  };
  sortObject[data.order]();
};
const labelDialogVisible = ref(false);
const labelDialogParams = ref<typeDialog>({
  params: {},
  type: editType["add"]
});
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  props.parentItem.id ? (newParams.parentId = props.parentItem.id) : delete newParams.parentId;
  newParams.descs = "createTime";
  return solutionCategory(newParams);
};
const initParam = reactive({
  parentId: props.parentItem.id,
  ascs: "",
  descs: ""
});

// 表格配置项
const columns: ColumnProps<Soluiton.SolutionCategoryItem>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "name", label: "分类名称", search: { el: "input", props: { maxlength: "10" } } },
  { prop: "level", label: "分类类型", sortable: true, width: 110, enum: typeStatus, search: { el: "select" } },
  { prop: "cnt", label: "方案数量", sortable: true, width: 130 },
  { prop: "creatorName", label: "创建人员", width: 130 },
  { prop: "createTime", label: "创建时间", sortable: true },
  { prop: "operation", label: "操作", fixed: "right", width: 170 }
];

// 批量删除
const batchDelete = async (id: string[]) => {
  await useHandleData(solutionDeleteType, { ids: id.join(",") }, "删除所选分类");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
const deleteLabel = async (data: any) => {
  await useHandleData(solutionDeleteType, { ids: data.id as string }, `删除${data.name}分类`);
  proTable.value?.getTableList();
  emits("modify");
};

const openDrawer = (operateType: number, row: ProductType.typeParams) => {
  const dialogParams = {
    type: operateType,
    params: {
      name: row.name,
      description: row.description,
      id: row.id,
      level: row.level,
      icon: row.icon,
      pic: row.pic,
      parentId: props.parentItem.id,
      parentList: row.level === 2 ? ["全部分类", row.parentName] : ["全部分类"]
    }
  };
  props.parentItem.id ? dialogParams.params.parentList.push(props.parentItem.name) : void 0;
  labelDialogVisible.value = true;
  labelDialogParams.value = dialogParams;
};
</script>
