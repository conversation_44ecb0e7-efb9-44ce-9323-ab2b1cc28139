<template>
  <div class="gift-table">
    <!-- <ProTable ref="proTable" :data="tableList" :pageable="pageable" :tool-button="false" :columns="columns" :has-padding="false"> -->
    <ProTable ref="proTable" :data="tableList" :pagination="false" :tool-button="false" :columns="columns" :has-padding="false">
      <template #empty>
        <!-- <img src="@/assets/images/notData.png" alt="notData" /> -->
        <p>暂无数据</p>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span>
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="handelDelete(scope.row)"></i>
          </el-tooltip>
          <el-button class="icon" link v-if="scope.$index !== tableList.length - 1" @click="xiayiItem(scope.$index, tableList)">
            <i class="iconfont2 icon-xiayi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link v-if="scope.$index !== 0" @click="shangyiItem(scope.$index, tableList)">
            <i class="iconfont2 icon-shangyi" style="font-size: 15px"></i>
          </el-button>
        </span>
      </template>
      <!-- <template #pagination>
        <Pagination :pageable="pageable" :handle-size-change="() => {}" :handle-current-change="() => {}" />
      </template> -->
    </ProTable>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import { type ColumnProps } from "@/components/ProTable/interface/index";
import ProTable from "@/components/ProTable/index.vue";
import Pagination from "@/components/ProTable/components/Pagination.vue";
import { getCategory } from "@/api/modules/business/productArea";
import { productItem } from "@/api/interface/business/theme";
const props = defineProps({
  maxItem: {
    type: Number,
    default: () => 0
  },
  modelIndex: {
    type: Number,
    default: () => 0
  },
  skuIds: {
    type: Array<productItem>,
    default: () => []
  }
});

const emit = defineEmits(["updateSkuIds"]);
const tableList = ref(props.skuIds || []);
const pageable = reactive({
  current: 1,
  size: 10,
  total: 0
});

const handelDelete = (row: any) => {
  const newList = tableList.value.filter((item: any) => item.id !== row.id);
  emit("updateSkuIds", newList, props.modelIndex);
};

const shangyiItem = (index: number, list: any[]) => {
  list[index] = list.splice(index - 1, 1, list[index])[0];
  emit("updateSkuIds", list, props.modelIndex);
};
const xiayiItem = (index: number, list: any[]) => {
  list[index] = list.splice(index + 1, 1, list[index])[0];
  emit("updateSkuIds", list, props.modelIndex);
};

watch(
  () => props.skuIds,
  () => {
    tableList.value = props.skuIds;
    pageable.total = props.skuIds?.length ?? 0;
  }
);

const columns = ref<ColumnProps[]>([
  {
    prop: "productName",
    label: "产品名称"
  },
  {
    prop: "categoryName",
    label: "产品分类",
    width: 120,
    enum: getCategory,
    fieldNames: {
      value: "id",
      label: "name"
    },
    isFilterEnum: false
  },
  {
    prop: "skuName",
    label: "产品规格"
  },
  {
    prop: "skuPrice",
    label: "规格单价"
  },
  {
    prop: "skuDiscountPrice",
    label: "折扣价"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
]);
</script>
<style lang="scss" scoped>
.gift-table {
  :deep(.card) {
    border: none !important;
    box-shadow: none !important;
  }
}
</style>
