import { Banner } from "@/api/interface/business/banner";
import http from "@/api";

export const getBannerPageList = (params: any) => {
  return http.get(`/cnud-operation/admin/banner-pageList`, params); // 正常 post json 请求  ==>  application/json
};

export const deleteBanner = (id: string) => {
  return http.post("/cnud-operation/admin/banner-delete", { ids: [id] });
};

export const changeBanner = (data: any) => {
  return http.post("/cnud-operation/admin/banner-update", data);
};

export const addBanner = (data: any) => {
  return http.post("/cnud-operation/admin/banner-save", data);
};

export const bannerAuditList = (data: Banner.AuditListParams) => {
  return http.get("/cnud-operation/admin/audit/pageList", data);
};

// 运营商管理审核接口
export const bannerDoAudit = (data: Banner.DoAuditParams) => {
  return http.post("/cnud-operation/admin/audit/doAudit", data);
};

// banner审核详情接口
export const bannerAuditDetail = (data: { bizId: string }) => {
  return http.post("/cnud-operation/admin/audit/bannerAuditDetail", data);
};

// news审核详情接口
export const newsAuditDetail = (data: { bizId: string }) => {
  return http.post("/cnud-operation/admin/audit/newsAuditDetail", data);
};

// 政策消息审核详情接口
export const policyAuditDetail = (data: { bizId: string; id: string }) => {
  return http.post("/cnud-operation/admin/audit/policyAuditDetail", data);
};
