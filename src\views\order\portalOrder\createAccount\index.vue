<template>
  <el-dialog
    v-model="dialogVisible"
    title="客户开户"
    width="80%"
    @close="beforeClose"
    :before-close="handleClose"
    :destroy-on-close="true"
    label-position="left"
  >
    <el-form ref="proForm" :model="formData">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="企业证件类型" prop="certificateType" style="display: none">
            <el-input v-model="formData.certificateType" placeholder="请输入企业名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="企业名称"
            prop="companyName"
            :rules="[
              { required: true, message: '请填写企业名称', trigger: 'blur' },
              { max: 100, message: '最多输入50个字符', trigger: 'blur' }
            ]"
          >
            <el-input v-model="formData.companyName" placeholder="请输入企业名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :rules="[
              { required: true, message: '请填写社会信用统一代码', trigger: 'blur' },
              { max: 18, message: '最多输入18个字符', trigger: 'blur' }
            ]"
            label="社会信用统一代码"
            prop="usciCode"
          >
            <el-input v-model="formData.usciCode" placeholder="请输入社会信用统一代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :rules="[{ required: true, message: '请选择所属行业', trigger: 'change' }]"
            label="所属行业"
            prop="industryType"
          >
            <el-select v-model="formData.industryType" placeholder="请选择所属行业" style="width: 100%">
              <el-option v-for="item in dict_industry_type" :key="item.itemKey" :label="item.itemValue" :value="item.itemKey" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-row :gutter="20">
            <el-col :span="14">
              <el-form-item
                :rules="[{ required: true, message: '请选择公司注册地', trigger: 'change' }]"
                label="公司注册地"
                prop="provinceCode"
              >
                <el-select
                  @change="handleChange"
                  v-model="formData.provinceCode"
                  placeholder="请输入公司注册地"
                  style="width: 100%; margin-left: 42px"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.regionCode"
                    :label="item.regionName"
                    :value="item.regionCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="-" prop="cityCode">
                <el-select
                  @change="handleCity"
                  :rules="[{ required: true, message: '请选择公司注册地', trigger: 'change' }]"
                  v-model="formData.cityCode"
                  style="width: 100%"
                >
                  <el-option v-for="item in cityList" :key="item.regionCode" :label="item.regionName" :value="item.regionCode" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-form-item
            v-bind="$attrs"
            prop="registeredTime"
            :label="'注册时间'"
            :rules="[{ required: true, message: '请输入注册时间', trigger: 'change' }]"
          >
            <el-date-picker
              placeholder="请输入注册时间"
              :value-format="'YYYY-MM-DD'"
              v-model="formData.registeredTime"
              type="date"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :rules="[{ required: true, message: '请输入注册资金（万元）', trigger: 'blur' }]"
            v-bind="$attrs"
            prop="registeredCapital"
            label="注册资金（万元）"
          >
            <el-input-number v-model="formData.registeredCapital" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="通讯地址"
            prop="companyAddress"
            :rules="[
              { required: true, message: '请输入通讯地址', trigger: 'blur' },
              { max: 50, message: '最多输入50个字符', trigger: 'blur' }
            ]"
          >
            <el-input v-model="formData.companyAddress" placeholder="请输入通讯地址" />
          </el-form-item>
        </el-col>
        <el-col :span="24" style="margin-top: 10px">
          <el-form-item
            label="营业执照"
            prop="companyCertificateUrl"
            :rules="[{ required: true, message: '请上传营业执照', trigger: 'change' }]"
          >
            <UploadImg
              :is-private="true"
              :file-size="10"
              v-model:image-url="formData.companyCertificateUrl"
              width="135px"
              height="135px"
            >
              <template #tip>
                上传的图片格式要求jpg、jpeg、bmp、png，不超过10M。
                可以添加“华为云备案、云服务申请、云服务认证、云服务备案”等水印；但不能遮挡关键信息，例如企业名称、公司证件号。
              </template>
            </UploadImg>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="info-box">
            <div class="box-title">
              <div class="title">客户联系人信息</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="姓名"
            prop="portalUser.userName"
            :rules="[
              { required: true, message: '请输入姓名', trigger: 'blur' },
              { max: 10, message: '最多输入10个字符', trigger: 'blur' }
            ]"
          >
            <el-input v-model="formData.portalUser.userName" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="电话"
            prop="portalUser.phone"
            :rules="[
              { required: true, message: '请输入电话', trigger: 'blur' },
              { validator: checkPhone, trigger: 'blur' },
              { max: 11, message: '最多输入11个字符', trigger: 'blur' }
            ]"
          >
            <el-input maxlength="11" v-model="formData.portalUser.phone" placeholder="请输入电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="邮箱"
            prop="portalUser.email"
            :rules="[
              { required: true, message: '请填写邮箱', trigger: 'blur' },
              { type: 'email', message: '请输入正确的邮箱地址' },
              { max: 50, message: '最多输入50个字符', trigger: 'blur' }
            ]"
          >
            <el-input v-model="formData.portalUser.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="info-box">
            <el-alert
              title=" 注意事项: 创建成功后，系统将为客户联系人手机号码自动创建昊算账号"
              type="warning"
              :closable="false"
            />
          </div>
        </el-col>
        <el-col :span="24">
          <div class="info-box">
            <div class="box-title">
              <div class="title">客户经理信息</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名" prop="portalUser.managerName">
            <el-input disabled v-model="formData.portalUser.managerName" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="电话" prop="portalUser.managerPhone">
            <el-input disabled v-model="formData.portalUser.managerPhone" placeholder="请输入电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="邮箱" prop="portalUser.managerEmail" :rules="[{ type: 'email', message: '请输入正确的邮箱地址' }]">
            <el-input disabled v-model="formData.portalUser.managerEmail" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handelSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";
import UploadImg from "@/components/Upload/Img.vue";
import useDialog from "./index";
import { ElMessage } from "element-plus";
import { reactive, onMounted } from "vue";
import { useDict } from "@/hooks/useDict";
import { queryRegionList } from "@/api/modules/partner";
import { useUserStore } from "@/stores/modules/user";
import type { FormInstance } from "element-plus";
import { storeToRefs } from "pinia";
import { saveUserEnterprise } from "@/api/modules/system";
const { dict_industry_type } = useDict("dict_industry_type");

const useStore = useUserStore();
const { userInfo } = storeToRefs(useStore);
const dialogVisible = ref(false);
const formData = reactive({
  certificateType: "1",
  companyName: "",
  provinceCode: "",
  cityCode: "",
  registeredTime: "",
  usciCode: "",
  industryType: "",
  registeredCapital: 0,
  companyAddress: "",
  companyCertificateUrl: "",
  province: "",
  city: "",
  portalUser: {
    managerEmail: userInfo.value.email,
    managerName: userInfo.value.userName,
    managerPhone: userInfo.value.phone,
    phone: "",
    userName: "",
    email: ""
  }
});
const provinceList = ref<{ regionName: string; regionCode: string }[]>([]);
const cityList = ref<{ regionName: string; regionCode: string }[]>([]);
const handleChange = (value: string) => {
  const provinceName = provinceList.value.find((item: any) => item.regionCode === value);
  formData.province = provinceName.regionName;
  formData.cityCode = "";
  getCityList(value);
};
const handleCity = (value: string) => {
  const cityName = cityList.value.find((item: any) => item.regionCode === value);
  formData.city = cityName.regionName;
};

const getCityList = (regionCode: string) => {
  queryRegionList({ parentRegionCode: regionCode })
    .then((result: any) => {
      const cities = result.data.map((item: { regionCode: string; regionName: string }) => {
        return { ...item, label: item.regionName, value: item.regionCode };
      });
      cityList.value = cities;
    })
    .catch(err => {
      console.log(err);
    });
};
const proForm = ref<FormInstance>();

const handleClose = () => {
  proForm.value?.resetFields();
  dialogVisible.value = false;
};
const handleOpen = () => {
  dialogVisible.value = true;
};
const checkPhone = (rule: any, value: any, callback: any) => {
  const regexp = /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[3-8]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[5-7]{1}))+\d{8})$/;
  if (value === "") callback();
  if (!regexp.test(value)) {
    callback(new Error("请输入正确的手机号码"));
  } else {
    return callback();
  }
};
const handelSubmit = () => {
  proForm.value?.validate().then(res => {
    saveUserEnterprise({
      enterpriseCertificate: {
        certificateType: formData.certificateType,
        companyName: formData.companyName,
        provinceCode: formData.provinceCode,
        cityCode: formData.cityCode,
        registeredTime: formData.registeredTime,
        usciCode: formData.usciCode,
        industryType: formData.industryType,
        registeredCapital: formData.registeredCapital,
        companyAddress: formData.companyAddress,
        companyCertificateUrl: formData.companyCertificateUrl,
        province: formData.province,
        city: formData.city
      },
      portalUser: formData.portalUser
    })
      .then(result => {
        if (result.code === 200) {
          ElMessage.success("开户成功");
          dialogVisible.value = false;
        }
      })
      .catch(err => {});
  });
};

const beforeClose = () => {};

onMounted(() => {
  nextTick(() => {
    queryRegionList({})
      .then((result: any) => {
        const province = result.data.map((item: { regionCode: string; regionName: string }) => {
          return { ...item, label: item.regionName, value: item.regionCode };
        });
        provinceList.value = province;
      })
      .catch(err => {
        console.log(err);
      });
  });
});

defineExpose({
  handleOpen
});
</script>

<style scoped lang="scss">
.info-box {
  margin-bottom: 15px;
  .box-title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-weight: 600;
    color: #3a3a3d;
    border-bottom: 1px solid #e4e7ed;
    .title {
      font-size: 18px;
      .card-btn {
        float: right;
        margin-top: -5px;
        margin-right: 10px;
      }
    }
  }
}
</style>
