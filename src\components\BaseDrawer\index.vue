<template>
  <el-drawer class="base-drawer" :destroy-on-close="true" :size="size" v-model="drawerVisible" :title="drawerProps.title">
    <div class="base-drawer-container">
      <!-- default slot -->
      <slot />
    </div>
    <template #footer>
      <el-button v-show="drawerProps.hasCancel" @click="drawerVisible = false"> 取消 </el-button>
      <el-button v-show="drawerProps.hasSure" type="primary" @click="handleSubmit">
        {{ drawerProps.sureText || "确定" }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="baseDrawer">
import { ref } from "vue";
// import { ElMessageBox } from "element-plus";

defineProps({
  size: {
    type: String,
    default: "820px"
  }
});
let emits = defineEmits(["sureCallback"]);

interface DrawerProps {
  show: boolean;
  title: string;
  hasSure: boolean;
  hasCancel: boolean;
  sureText: string;
  row: Object;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  hasSure: true,
  hasCancel: true,
  title: "",
  show: true,
  sureText: "",
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = drawerProps.value.show;
};
const handleSubmit = () => {
  emits("sureCallback");
  // drawerVisible.value = false;
};

defineExpose({
  acceptParams
});
</script>

<style scoped lang="scss">
.base-drawer {
  display: flex;
  flex-direction: column;
  &:deep(.el-drawer__body) {
    display: flex;
    flex: 1;
    flex-direction: column;
    font-size: 14px;
  }
}
.base-drawer-container {
  flex: 1;
  min-height: 400px;
  padding: 4px;
  overflow: auto;
}
</style>
