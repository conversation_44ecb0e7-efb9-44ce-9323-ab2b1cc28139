<template>
  <el-dialog
    :append-to-body="true"
    :destroy-on-close="true"
    class="custom-modal"
    v-model="dialogVisible"
    :width="1000"
    :before-close="handleClose"
    title="选择合作协议"
  >
    <div class="table-box">
      <pro-table
        ref="proTable"
        title="合作协议列表"
        :columns="columns"
        :request-api="getTableList"
        :request-auto="false"
        :tool-button="false"
      >
        <template #auditStatus="{ row }">
          <CustomTag
            :type="'order'"
            :status="row.auditStatus"
            :label="useDictLabel(auditStatusDict, row.auditStatus) || '--'"
          ></CustomTag>
        </template>
      </pro-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose"> 取消 </el-button>
        <el-button @click="sureSize" type="primary"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="agreementModal">
import { nextTick, onMounted, ref, computed } from "vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useDictLabel } from "@/hooks/useDict";
import { Product } from "@/api/interface/product";
import { getProviderAgreementList } from "@/api/modules/partner";
import CustomTag from "@/components/CustomTag/index.vue";

const dialogVisible = ref(false);
const proTable = ref<ProTableInstance>();

const props = defineProps({
  providerId: {
    type: String,
    default: ""
  }
});
const dictionaryStore = useDicStore();
// agreement_audit_status 1审核中 2审核不通过 3审核通过
const AUDITSTATUS_SUCCESS = "3";
onMounted(() => {
  dictionaryStore.getProviderAgreementStatus();
});
const auditStatusDict: any = computed(() => dictionaryStore.providerAgreementStatus);

// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  { prop: "index", type: "selection", fixed: "left" },
  { prop: "agreementName", label: "合作协议名称", search: { el: "input" } },

  {
    prop: "validTime",
    label: "有效期",
    render: ({ row: { validStartTime, validEndTime } }: any) => {
      return validStartTime ? validStartTime + " ~ " + validEndTime : "--";
    }
  },
  {
    prop: "auditStatus",
    label: "状态",
    enum: auditStatusDict
  }
];

const emits = defineEmits(["selectedAgreement"]);
/* 关闭 */
const handleClose = () => {
  dialogVisible.value = false;
};

/* 打开 */
const handleOpen = () => {
  dialogVisible.value = true;
  nextTick(() => {
    proTable.value?.getTableList();
  });
};

const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.validTime && (newParams.validStartTime = newParams.validTime[0]);
  newParams.validTime && (newParams.validEndTime = newParams.validTime[1]);
  delete newParams.validTime;
  newParams.providerId = props.providerId;
  newParams.auditStatus = AUDITSTATUS_SUCCESS;
  return getProviderAgreementList(newParams);
};

const sureSize = () => {
  let infos = proTable.value?.selectedList;
  console.log("infos", infos);
  emits("selectedAgreement", infos);
  handleClose();
};

defineExpose({
  handleOpen
});
</script>

<style scoped lang="scss">
:global(.custom-modal .el-dialog__header) {
  padding: 22px 24px;
  border-bottom: 1px solid #e1e3e6;
}
:deep(.prod-sku) {
  .el-radio__input {
    display: none;
  }
}
:global(.prod-sku .el-radio) {
  margin-right: 16px;
  &.is-bordered {
    border: 1px solid #73787f;
  }
}
.sku-box {
  display: flex;
  justify-content: center;
  .sku-item {
    display: flex;
    align-items: center;
    margin-bottom: 22px;
    font-size: 16px;
    .info-label {
      min-width: 104px;
      color: #73787f;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
    .unit {
      margin-left: 10px;
    }
    .price {
      font-size: 24px;
      color: var(--el-color-primary);
    }
  }
  .title {
    margin-bottom: 12px;
    font-size: 20px;
    font-weight: 600;
    color: #3a3a3d;
  }
  .des {
    color: #3a3a3d;
  }
}
</style>
