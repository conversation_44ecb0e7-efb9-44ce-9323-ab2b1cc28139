<template>
  <div class="table-box custom-list">
    <pro-table
      ref="proTable"
      title="门户访问量按日期统计分析总列表"
      :request-auto="false"
      :columns="columns"
      :init-param="initParam"
      :tool-button="false"
      :pagination="false"
      :data="tableData"
    >
      <template #tableHeader>
        <el-button plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <template #pvCumMom="{ row }">
        {{ row.pvCumMom }}
        <HBArrow v-if="row.pvCumMom !== '-'" :value="checkNumberRate(row.pvCumMom)" />
      </template>
      <template #uvCumMom="{ row }">
        {{ row.uvCumMom }}
        <HBArrow v-if="row.uvCumMom !== '-'" :value="checkNumberRate(row.uvCumMom)" />
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span>
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="orderAnalysisList">
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDownload } from "@/hooks/useDownload";
import { Statistic } from "@/api/interface/statisticAnalysis";
import { getAccessTableData, exportAccessTable } from "@/api/modules/statisticAnalysis";
import HBArrow from "@/components/HBArrow/index.vue";

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}
const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
});

const router = useRouter();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive(props.params);
const tableData = ref([]);
const tableParams = ref(props.params);

onMounted(() => {
  getTableList();
});

const getTableList = (params: any = {}) => {
  tableParams.value = { ...initParam, ...params };
  getAccessTableData({ ...initParam, ...params }).then((res: any) => {
    const list = res.data;
    tableData.value = list;
    nextTick(() => {
      proTable.value?.element?.sort("indexDate", "descending");
    });
  });
};

const dateTitle = computed(() => (props.params.indexUnit === DateType.day ? "日" : "月"));

// 表格配置项
const columns: ColumnProps<Statistic.OrderDateItem>[] = [
  { prop: "indexDate", label: "统计时间", minWidth: 100 },
  { prop: "pvCum", label: "累计访问量（PV）", minWidth: 130 },
  { prop: "pvNa", label: "新增访问量（PV）", minWidth: 130 },
  { prop: "uvCum", label: "累计访问人数（UV）", minWidth: 140 },
  { prop: "uvNa", label: "新增访问人数（UV）", minWidth: 140 },
  {
    prop: "pvCumMom",
    label: "累计访问量（PV）日环比",
    headerRender: () => {
      return <>{`累计访问量（PV）${dateTitle.value}环比`}</>;
    },
    minWidth: 180
  },
  {
    prop: "uvCumMom",
    label: "累计访问人数（UV）日环比",
    headerRender: () => {
      return <>{`累计访问人数（UV）${dateTitle.value}环比`}</>;
    },
    minWidth: 190
  }
];

const checkNumberRate = (value: string) => {
  const re = /^[0 -9]+\.?\d*$/;
  if (!re.test(value)) {
    return "";
  } else {
    //转换为数字格式
    const rat = value.replace(/%/g, "");
    const val = parseFloat(rat);
    if (val > 0) {
      return "1";
    } else if (val < 0) {
      return "2";
    } else {
      return "";
    }
  }
};

const toView = async (row: any) => {
  router.push({
    path: "/statisticAnalysis/productAnalysis/productDimension",
    query: {
      dateType: props.params.dateType,
      countDate: [row.countDate, row.countDate]
    }
  });
};

const downloadFile = () => {
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() => {
    const queryData = {
      ...tableParams.value
    };
    useDownload(exportAccessTable, queryData);
  });
};
defineExpose({
  getTableList
});
</script>
<style lang="scss" scoped>
.custom-list {
  :deep(.card.table-main) {
    border: none !important;
    box-shadow: none !important;
  }
}
:deep(.el-table .el-table__header .el-table__cell > .cell, .el-table .el-table__header .el-table__cell > .cell) {
  white-space: wrap;
}
:deep(.el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell) {
  color: var(--el-color-primary);
  background-color: #ffffff;
}
</style>
