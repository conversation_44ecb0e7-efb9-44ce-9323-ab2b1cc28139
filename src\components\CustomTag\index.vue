<template>
  <div class="tag" :class="`${propsType} ${propsType}_${propsStatus}`">{{ label ? label : "" }}</div>
</template>

<script setup lang="tsx">
import { computed } from "vue";
/**
 * status 状态
 * type:类型
 * label：翻译好的文字
 */
const props = defineProps<{ label: String; status?: string; type?: string }>();
const propsType = computed(() => {
  return props?.type ? props?.type : "default";
});
const propsStatus = computed(() => {
  return props?.status ? props?.status : "0";
});
</script>

<style scoped lang="scss">
.blue {
  color: #0052d9;
  background: rgb(0 82 217 / 10%);
  border: 1px solid #75a1e9;
  border-radius: 4px;
}
.green {
  color: #19be6b;
  background: rgb(25 190 107 / 10%);
  border: 1px solid #6de4a8;
  border-radius: 4px;
}
.yellow {
  color: #ffaa00;
  background: rgb(255 170 0 / 10%);
  border: 1px solid #eac376;
  border-radius: 4px;
}
.red {
  color: #e5302f;
  background: rgb(214 0 15 / 10%);
  border: 1px solid #ed7b83;
  border-radius: 4px;
}
.grey {
  color: #9ca3ac;
  background: rgb(115 120 127 / 10%);
  border: 1px solid #bbc2cb;
  border-radius: 4px;
}
.tag {
  display: inline-block;
  padding: 0 9px;
  font-size: 14px;
  &.noBgColor {
    background: none;
  }
  &.default {
    @extend.blue;
  }

  /* ------------------------base------------------------------- */
  &.success {
    @extend.green;
  }
  &.info {
    @extend.grey;
  }
  &.warning {
    @extend.yellow;
  }
  &.danger {
    @extend.red;
  }

  /* -------------------订单状态--------------------------------- */

  /* 待审核 */
  &.order_1 {
    @extend.yellow;
  }

  /* 待支付 */
  &.order_2 {
    @extend.red;
  }

  /* 订购中 */
  &.order_3 {
    @extend.blue;
  }

  /* 订购失败 */
  &.order_4 {
    @extend.grey;
  }

  /* 订单退订中 */
  &.order_6 {
    @extend.blue;
  }

  /* 退订完成 */
  &.order_7 {
    @extend.green;
  }

  /* 订购成功 */
  &.order_5 {
    @extend.green;
  }

  /* 已付款 */
  &.order_8 {
    @extend.green;
  }

  /* 待开通 */
  &.order_9 {
    @extend.blue;
  }

  /* 正在开通 */
  &.order_10 {
    @extend.blue;
  }

  /* 退订失败 */
  &.order_11 {
    @extend.grey;
  }

  /* -------------------状态--------------------------------- */

  /* 待审核 */
  &.partner_1 {
    @extend.yellow;
  }

  /* 审核不通过 */
  &.partner_2 {
    @extend.grey;
  }

  /* 有效 */
  &.partner_3 {
    @extend.green;
  }

  /* 已到期 */
  &.partner_4 {
    @extend.blue;
  }

  /* 已失效 */
  &.partner_5 {
    @extend.red;
  }

  /* 待审核 */
  &.bill_1 {
    @extend.yellow;
  }

  /* 审核不通过 */
  &.bill_2 {
    @extend.green;
  }

  /* 通过 */
  &.bill_3 {
    @extend.red;
  }

  /* -------------------审核状态--------------------------------- */

  /* 审核中 */
  &.audit_1 {
    @extend.yellow;
  }

  /* 审核不通过 */
  &.audit_2 {
    @extend.red;
  }

  /* 通过 */
  &.audit_3 {
    @extend.green;
  }

  /* -------------------权益状态--------------------------------- */

  /* 停用 */
  &.benefit_0 {
    @extend.red;
  }

  /* 启用 */
  &.benefit_1 {
    @extend.green;
  }

  /* -------------------文件下载--------------------------------- */
  &.audit_download_2 {
    @extend.green;
  }
  &.audit_download_1 {
    @extend.yellow;
  }
  &.audit_download_3 {
    @extend.red;
  }
}
</style>
