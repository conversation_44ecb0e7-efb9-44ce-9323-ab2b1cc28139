<template>
  <div class="dialog-box">
    <el-dialog
      :append-to-body="true"
      v-model="dialogVisible"
      title="已有子商户号上传"
      width="500px"
      :before-close="handleClose"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="rules">
        <el-form-item prop="merchantNumber" label="子商户号：" style="margin: 0 auto">
          <el-input v-model="formData.merchantNumber" placeholder="输入子商户号" />
        </el-form-item>
      </el-form>
      <div class="tips">注意：子商户号将作为后续产品分成金额入账的依据，请确认填写正确。</div>
      <template #footer>
        <div class="button-group">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit(formRef)"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { setMerchantNumber } from "@/api/modules/partner";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { nextTick, reactive, ref } from "vue";

/* data */

const dialogVisible = ref(false);

const formData = reactive({ merchantNumber: "", id: "" });

const formRef = ref<FormInstance>();

const rules = reactive<FormRules>({
  merchantNumber: [{ required: true, message: "请输入子商户号", trigger: "blur" }]
});

const emit = defineEmits(["reload"]);

/* methods */
const handleClose = () => {
  formRef.value?.resetFields();
  dialogVisible.value = false;
};

const handleModalOpen = (row: { id: string }) => {
  formData.id = row.id;
  nextTick(() => {
    dialogVisible.value = true;
  });
};

const handleSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    const valid = await formEl.validate();
    if (valid) {
      const { code, msg } = await setMerchantNumber(formData);
      if (code === 200) {
        ElMessage({
          message: msg,
          type: "success"
        });
        emit("reload");
        handleClose();
      }
    }
  } catch (e) {
    console.log(e);
  }
};

defineExpose({
  handleOpen: handleModalOpen
});
</script>

<style scoped lang="scss">
.button-group {
  width: 100%;
  text-align: right;
}
.tips {
  margin-top: 24px;
  font-family: PingFangSC, "PingFang SC";
  font-size: 14px;
  font-weight: 400;
  color: #e5302f;
}
</style>
