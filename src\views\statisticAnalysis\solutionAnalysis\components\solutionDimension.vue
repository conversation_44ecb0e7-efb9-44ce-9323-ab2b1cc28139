<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="产品维度"
      :columns="columns"
      :request-auto="false"
      :tool-button="false"
      :pagination="false"
      :data="tableData"
      :init-param="initParam"
      :request-api="getTableList"
      :summary-method="getSummaries"
      show-summary
      :collapsed="false"
    >
      <template #itemCategory="{ row }">
        <div v-html="row.itemCategory"></div>
      </template>
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button plain @click="downloadFile"> <i class="iconfont2 iconfont icon-a-lineicon_share"></i> 导出 </el-button>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="productDimension">
import { ref, onMounted, reactive } from "vue";
import { useRoute } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { useDownload } from "@/hooks/useDownload";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getBusinessDetail, exportBusinessDetail } from "@/api/modules/statisticAnalysis";
import { getBusinessNameList, getBusinessProviderList } from "@/api/modules/statisticAnalysis";
import { solutionCategoryTree } from "@/api/modules/solution";

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}
interface DataType {
  list?: any;
  total?: any;
}

const route = useRoute();
const { countDate, dateType, itemId = "", providerId = "" } = route.query;

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({
  dateType,
  adviceType: "2"
});

const proTable = ref<ProTableInstance>();
const tableData = ref([]);
const totalData = ref([]);
const myCascader = ref();
const categoryDict = ref([]);

onMounted(() => {
  proTable.value?.getTableList();
  solutionCategoryTree({}).then((res: any) => {
    categoryDict.value = res.data || [];
  });
});

// 表格配置项
const columns: ColumnProps<any>[] = [
  { prop: "index", label: "序号", type: "index", width: 80 },
  {
    prop: "countDate",
    label: "日期",
    isShow: false,
    search: {
      el: "date-picker",
      props: {
        type: dateType === DateType.day ? "daterange" : "monthrange",
        valueFormat: dateType === DateType.day ? "YYYY-MM-DD" : "YYYY-MM",
        format: dateType === DateType.day ? "YYYY-MM-DD" : "YYYY-MM"
      },
      defaultValue: countDate || ["", ""]
    }
  },
  {
    prop: "itemName",
    label: "解决方案名称",
    isFilterEnum: false,
    enum: async () => {
      return await getBusinessNameList({ adviceType: "2" });
    },
    fieldNames: { label: "itemName", value: "itemId" },
    search: {
      el: "select",
      key: "itemId",
      props: { multiple: true, clearable: true, "collapse-tags": true, "collapse-tags-tooltip": true, filterable: true },
      defaultValue: itemId || ""
    }
  },
  {
    prop: "itemCategory",
    label: "解决方案分类",
    enum: solutionCategoryTree,
    isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格
    search: {
      el: "cascader",
      render: (scope: any) => {
        return (
          <el-cascader
            ref={myCascader}
            model-value={scope.modelValue}
            props={{ label: "name", value: "id" }}
            placeholder="请选择产品分类"
            options={scope.options}
            clearable
            emitPath
            onChange={(value: any) => {
              scope.searchParam.categoryIdList = myCascader.value?.getCheckedNodes()[0]?.pathLabels || [];
              return value;
            }}
          ></el-cascader>
        );
      }
    }
  },
  {
    prop: "providerName",
    label: "合作商",
    enum: getBusinessProviderList,
    isFilterEnum: false,
    fieldNames: { label: "providerName", value: "providerId" },
    search: {
      el: "select",
      key: "providerId",
      props: { "collapse-tags": true, "collapse-tags-tooltip": true, filterable: true, clearable: true },
      defaultValue: providerId || ""
    }
  },
  { prop: "netGrowthCount", label: "新增商机数", minWidth: 100 }
];

const getTableList = async (search: any = {}) => {
  const { countDate, itemId = "", providerId = "", categoryIdList = [] } = search;

  let itemCategory = "";
  if (categoryIdList?.length > 0) {
    itemCategory = categoryIdList.join(" > ");
  }
  const responeData: any = await getBusinessDetail({
    dateType,
    adviceType: initParam.adviceType,
    countStartDate: (countDate && countDate[0]) || "",
    countEndDate: (countDate && countDate[1]) || "",
    itemId,
    providerId,
    itemCategory
  });
  if (responeData.code === 200) {
    const data: DataType = responeData.data || {};
    tableData.value = data.list || [];
    totalData.value = data.total || [];
    return new Promise(resolve => {
      resolve({
        data: {
          records: data.list
        }
      });
    });
  } else {
    return new Promise(resolve => {
      resolve({
        data: {
          records: []
        }
      });
    });
  }
};
const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = "总计";
      return;
    }
    sums[index] = totalData.value[column.property];
  });

  return sums;
};
const downloadFile = () => {
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() => {
    const { countDate, itemId = "", providerId = "", categoryIdList = [] } = proTable.value?.searchParam || {};
    let itemCategory = "";
    if (categoryIdList?.length > 0) {
      itemCategory = categoryIdList.join(" > ");
    }
    const params = {
      dateType,
      adviceType: initParam.adviceType,
      countStartDate: (countDate && countDate[0]) || "",
      countEndDate: (countDate && countDate[1]) || "",
      itemId,
      providerId,
      itemCategory
    };
    useDownload(exportBusinessDetail, params);
  });
};
</script>

<style lang="scss" scoped>
:deep(.el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell) {
  color: var(--el-color-primary);
  background-color: #ffffff;
}
</style>
