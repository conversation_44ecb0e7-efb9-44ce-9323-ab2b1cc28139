<template>
  <!-- 服务器负载趋势 -->
  <div id="ServerLoadTrend" class="echarts"></div>
</template>

<script setup lang="ts">
import { ECharts, EChartsOption, init } from "echarts";
interface ChartProp {
  label: string;
  value: string[];
}
const initChart = (data: any = {}): ECharts => {
  const charEle = document.getElementById("ServerLoadTrend") as HTMLElement;
  const gradientColors1 = ["rgba(59, 146, 255, 0.4)", "rgba(40, 212, 214, 0.4)", "rgba(255, 170, 56, 0.4)"];
  const gradientColors2 = ["rgba(59, 146, 255, 0)", "rgba(40, 212, 214, 0)", "rgba(255, 170, 56, 0)"];
  const charEch: ECharts = init(charEle);
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        z: 1,
        type: "line",
        lineStyle: {
          type: "solid",
          color: "#5092FF"
        }
      },
      borderWidth: 0, //边框线宽
      padding: 0,
      backgroundColor: "transparent",
      formatter: (p: any) => {
        let str = "";
        p.forEach((val: any) => {
          str += `
          <div class="year-item">
            <span class="year-dot" style="background-color: ${val.color};"></span>
            <span class="year-name">${val.seriesName}</span>
            <span class="year-value">${val.data}%</span>
          </div>
          `;
        });
        let dom = `
                    <div class="annual-tooTip">
                      <span class="annual-month">${p[0].name}时</span>
                      <div class="annual-list">
                        ${str}
                      </div>
                    </div>
                  `;
        return dom;
      }
    },
    legend: {
      right: "2%",
      top: "0%",
      itemWidth: 7,
      itemHeight: 7,
      align: "auto",
      icon: "rect",
      itemGap: 15,
      textStyle: {
        color: "#ebebf0"
      }
    },
    grid: {
      top: "17%",
      left: "3",
      right: "26",
      bottom: "5",
      containLabel: true
    },
    xAxis: [
      {
        // name: "时",
        // nameLocation: "end",
        // nameTextStyle: {
        //   color: "#C6DAF0",
        //   fontSize: 16,
        //   fontFamily: "PingFangSC-Regular,PingFang SC"
        // },
        type: "category",
        boundaryGap: false,
        axisLine: {
          show: true,
          lineStyle: {
            color: "#2C4BB8"
          }
        },
        axisLabel: {
          // 坐标轴刻度标签的相关设置
          color: "#C6DAF0",
          padding: 0,
          fontSize: 16,
          fontFamily: "PingFangSC-Regular,PingFang SC",
          formatter: function (value, index) {
            if (index === data.columns.length - 1) {
              return value + "时";
            } else {
              return value;
            }
          }
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        data: data.columns
      }
    ],
    yAxis: {
      // name: "%",
      nameTextStyle: {
        color: "#C6DAF0",
        fontSize: 16,
        fontFamily: "PingFangSC-Regular,PingFang SC",
        padding: [0, 30, 0, 0]
      },
      // nameGap:18,
      minInterval: 1,
      // min: 4,
      splitNumber: 5,
      splitLine: {
        show: true,
        lineStyle: {
          color: "#2541A1",
          type: "dashed"
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#2C4BB8"
        }
      },
      axisLabel: {
        show: true,
        color: "#C6DAF0",
        fontSize: 16,
        fontFamily: "PingFangSC-Regular,PingFang SC",
        padding: 0,
        formatter: "{value}%"
      },
      axisTick: {
        show: false
      }
    },
    series: data.data.map((val: ChartProp, index: number) => {
      return {
        name: val.label,
        type: "line",
        showSymbol: false,
        smooth: true,
        lineStyle: {
          width: 1,
          color: data.colors[index] // 线条颜色
          // borderColor: data.colors[index]
        },
        itemStyle: {
          color: data.colors[index]
        },
        tooltip: {
          show: true
        },
        areaStyle: {
          // 区域填充样式
          // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: gradientColors1[index] // 0% 处的颜色
              },
              {
                offset: 1,
                color: gradientColors2[index] // 100% 处的颜色
              }
            ],
            global: false // 缺省为 false
          }
        },
        data: val.value
      };
    })
  };
  charEch.setOption(option);
  return charEch;
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
:deep(.annual-tooTip) {
  box-sizing: border-box;
  height: 124px;
  padding: 5px 16px;
  background: rgb(3 11 27 / 80%);
  border: 1px solid #5a7dc0;
  .annual-month {
    display: inline-block;
    margin-bottom: 6px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
  }
  .annual-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    .year-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 22px;
      margin-bottom: 5px;
      font-family: PingFangSC-Regular, "PingFang SC";
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      .year-dot {
        width: 8px;
        height: 8px;
        margin-right: 5px;
      }
      .year-name {
        width: 150px;
        margin: 0 2px;
      }
      .year-value {
        display: inline-block;

        // margin-left: 30px;
      }
    }
  }
}
</style>
