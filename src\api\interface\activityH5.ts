export namespace activityH5 {
  export interface activityParams {
    endTime: string;
    joinUserType: string | null;
    marketingProductList: Array<marketingProductListItem>;
    name: string;
    rules: string;
    shareContent: string;
    shareIcon: string;
    shareTitle: string;
    startTime: string;
    status?: number;
    backgroundImage: string;
    headImage: string;
  }

  export interface activityParamsItem extends activityParams {
    marketingId: string;
  }

  export interface activitySearchParams {
    name?: string;
    status?: string;
    startTime?: string;
    endTime?: string;
  }

  export interface marketingProductListItem {
    description: string;
    marketingId: string;
    marketingProductRelId: string;
    productId: string;
    skuId: string;
    totalLimit: number;
    userLimit: number;
  }
  export interface getAnalysisItem {
    getCount: number;
    getRate: string;
    productName: string;
    productSkuName: string;
    surplusCount: number;
    total: number;
  }

  export interface getAnalysisData {
    productAnalysis: Array<getAnalysisItem>;
    joinNum: number;
    loginNum: number;
    pv: number;
    uv: number;
  }

  export interface JoinDetailPageItem {
    createTime: string;
    phone: string;
    productName: string;
    productSkuName: number;
  }
}
