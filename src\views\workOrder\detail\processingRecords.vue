<template>
  <div>
    <div class="header-title-common">{{ title }}</div>
    <div class="record-detail">
      <recordsList :data-source="listData" :title-key-name="'solutionName'"></recordsList>
    </div>
  </div>
</template>

<script setup lang="ts">
import recordsList from "./recordsList.vue";

defineProps({
  title: {
    type: String,
    required: true
  },
  listData: {
    type: Array,
    default: () => []
  }
});
</script>

<style lang="scss" scoped>
.record-detail {
  margin-top: 24px;
  background: #ffffff;
  border: 1px solid #e1e3e6;
  border-radius: 4px;
}
</style>
