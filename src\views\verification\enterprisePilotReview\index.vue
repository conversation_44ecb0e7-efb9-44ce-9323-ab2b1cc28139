<template>
  <div class="table-box">
    <el-tabs v-model="activeName" class="demo-tabs tab-box" @tab-click="handleTabClick">
      <el-tab-pane label="诊断报告" :name="TabType.REQUIRE"></el-tab-pane>
      <el-tab-pane label="合同签订" :name="TabType.SCHEME"> </el-tab-pane>
      <el-tab-pane label="资金预发" :name="TabType.FINANCE"> </el-tab-pane>
    </el-tabs>
    <div v-show="activeName === TabType.REQUIRE" class="list-box">
      <pro-table
        ref="requireTableRef"
        title="诊断报告审核列表"
        :request-auto="false"
        :tool-button="false"
        :columns="columns"
        :request-api="getEnterprisePilotReportReviewList"
      >
        <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
        <template #tableHeader>
          <el-button v-auth="'export'" plain @click="exportFile('city_initial_review', '诊断报告')">
            <i class="iconfont2 icon-a-lineicon_share"></i>
            导出
          </el-button>
        </template>
        <template #auditStatus="{ row }">
          <custom-tag
            :type="statusTypeMap[row.auditStatus]"
            :status="row.auditStatus"
            :label="getAuditStatusLabel(row, 'initial')"
          ></custom-tag>
        </template>
        <template #diagnosticFileUrl="{ row }">
          <el-button type="primary" size="small" @click="handleDownload(row.diagnosticFileUrl)">下载</el-button>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span>
            <el-tooltip
              v-if="scope.row.stepType === 'city_initial_review' && scope.row.auditStatus === 'wait'"
              content="审核"
              placement="top"
            >
              <i class="iconfont2 opera-icon icon-shenhe" @click="openReviewDialog(scope.row)"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>
    <div v-show="activeName === TabType.SCHEME" class="list-box">
      <pro-table
        ref="schemeTableRef"
        title="合同签订审核"
        :request-auto="false"
        :tool-button="false"
        :columns="columns1"
        :request-api="getEnterprisePilotAgreementReviewList"
      >
        <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
        <template #tableHeader>
          <el-button v-auth="'export'" plain @click="exportFile('city_final_review', '合同签订')">
            <i class="iconfont2 icon-a-lineicon_share"></i>
            导出
          </el-button>
        </template>
        <template #auditStatus="{ row }">
          <custom-tag
            :type="statusTypeMap[row.auditStatus]"
            :status="row.auditStatus"
            :label="getAuditStatusLabel(row, 'final')"
          ></custom-tag>
        </template>
        <template #renovationAgreementUrl="{ row }">
          <el-button type="primary" size="small" @click="handleDownload(row.renovationAgreementUrl)">下载</el-button>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span>
            <el-tooltip
              v-if="scope.row.stepType === 'city_final_review' && scope.row.auditStatus === 'wait'"
              content="审核"
              placement="top"
            >
              <i class="iconfont2 opera-icon icon-shenhe" @click="openReviewDialog(scope.row)"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>
    <div v-show="activeName === TabType.FINANCE" class="list-box">
      <pro-table
        ref="financeTableRef"
        title="资金预发审核"
        :request-auto="false"
        :tool-button="false"
        :columns="columns2"
        :request-api="getEnterprisePilotFinanceReviewList"
      >
        <template #tableHeader>
          <el-button v-auth="'export'" plain @click="exportFile('city_finance_review', '资金预发')">
            <i class="iconfont2 icon-a-lineicon_share"></i>
            导出
          </el-button>
        </template>
        <template #auditStatus="{ row }">
          <custom-tag
            :type="statusTypeMap[row.auditStatus]"
            :status="row.auditStatus"
            :label="getAuditStatusLabel(row, 'finance')"
          ></custom-tag>
        </template>
        <template #bankTermsUrls="{ row }">
          <el-button type="primary" size="small" @click="handleDownloadZip(row)" :loading="row.downloading">
            {{ row.downloading ? "打包中..." : "下载ZIP" }}
          </el-button>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span>
            <el-tooltip
              v-if="scope.row.stepType === 'city_finance_review' && scope.row.auditStatus === 'wait'"
              content="审核"
              placement="top"
            >
              <i class="iconfont2 opera-icon icon-shenhe" @click="openReviewDialog(scope.row)"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>
    <el-dialog v-model="reviewDialogVisible" title="审核" width="500px">
      <el-form>
        <el-form-item label="企业名称">
          <span>{{ auditForm.companyName }}</span>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input
            v-model="auditForm.auditMsg"
            type="textarea"
            :rows="4"
            placeholder="驳回时必须填写审核意见，通过默认审核意见为同意"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="submitReview('refuse')">审核不通过</el-button>
          <el-button type="primary" @click="submitReview('pass')">审核通过</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="tsx" name="enterprisePilotReview">
import { ref, onBeforeMount, onActivated, reactive } from "vue";
import type { TabsPaneContext } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import CustomTag from "@/components/CustomTag/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDicStore } from "@/stores/modules/dictionaty";
import {
  getEnterprisePilotReportReviewList,
  getEnterprisePilotAgreementReviewList,
  getEnterprisePilotFinanceReviewList,
  postEnterprisePilotAudit,
  exportEnterprisePilotList
} from "@/api/modules/enterprisePilot";
import { TabType } from "./type";
import { useUserStore } from "@/stores/modules/user";
import axios from "axios";
import { ElMessage } from "element-plus";
import JSZip from "jszip";

const statusTypeMap: { [key: string]: string } = {
  wait: "yellow",
  pass: "blue",
  refuse: "red",
  close: "grey"
};
const dictionaryStore = useDicStore();

const reviewDialogVisible = ref(false);
const auditForm = reactive({
  id: "",
  companyName: "",
  auditMsg: ""
});

const openReviewDialog = (row: any) => {
  auditForm.id = row.id;
  auditForm.companyName = row.companyName;
  auditForm.auditMsg = "";
  reviewDialogVisible.value = true;
};

const submitReview = async (status: "pass" | "refuse") => {
  try {
    await postEnterprisePilotAudit({
      id: auditForm.id,
      auditStatus: status,
      auditMsg: auditForm.auditMsg
    });
    ElMessage.success("审核操作成功");
    reviewDialogVisible.value = false;
    if (activeName.value === TabType.REQUIRE) {
      requireTableRef.value?.getTableList();
    } else if (activeName.value === TabType.SCHEME) {
      schemeTableRef.value?.getTableList();
    } else if (activeName.value === TabType.FINANCE) {
      financeTableRef.value?.getTableList();
    }
  } catch (error) {
    console.error("审核失败", error);
  }
};

const getAuditStatusLabel = (row: any, context: "initial" | "final" | "finance") => {
  if (row.auditStatus === "pass") return "通过";
  if (row.auditStatus === "refuse") return "不通过";
  if (row.auditStatus === "wait") {
    const expectedStep =
      context === "initial" ? "city_initial_review" : context === "final" ? "city_final_review" : "city_finance_review";
    return row.stepType === expectedStep ? "待审核" : "非审核状态";
  }
  return "非审核状态";
};

const handleDownload = async (url: string) => {
  if (!url) return;
  const userStore = useUserStore();
  try {
    const res = await axios.get(url, {
      responseType: "blob",
      headers: {
        "cnud-auth": `bearer ${userStore.token}`,
        "cnud-tenant": "haosuan" // 临时租户字段
      }
    });

    const blob = new Blob([res.data]);
    let fileName = "";
    const contentDisposition = res.headers["content-disposition"];
    if (contentDisposition) {
      const match = contentDisposition.match(/filename="?([^"]+)"?/);
      if (match && match[1]) {
        fileName = decodeURIComponent(match[1]);
      }
    }
    if (!fileName) {
      fileName = url.substring(url.lastIndexOf("/") + 1);
    }

    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = downloadUrl;
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error("下载文件失败", error);
    ElMessage.error("下载文件失败");
  }
};

const exportFile = async (flowStatus: string, fileName: string) => {
  try {
    const res = await exportEnterprisePilotList({ flowStatus });
    const blob = new Blob([res.data as BlobPart]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    //中小企试点-诊断报告审核列表yyyyMMddHHmmSS.xlsx
    const d = new Date();
    const pad = (n: number) => n.toString().padStart(2, "0");
    const fileName1 = `中小企试点-${fileName}审核列表${[
      d.getFullYear(),
      pad(d.getMonth() + 1),
      pad(d.getDate()),
      pad(d.getHours()),
      pad(d.getMinutes()),
      pad(d.getSeconds())
    ].join("")}.xlsx`;
    // const fileName = `中小企试点-诊断报告审核列表${new Date().toLocaleString().replace(/\//g, "-")}.xlsx`;
    link.setAttribute("download", fileName1);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("导出失败", error);
    ElMessage.error("导出失败");
  }
};

// 下载ZIP文件
const handleDownloadZip = async (row: any) => {
  if (!row.bankTermsUrls) {
    ElMessage.warning("没有可下载的文件");
    return;
  }

  // 设置下载状态
  row.downloading = true;

  const userStore = useUserStore();
  const urls = row.bankTermsUrls.split(",").filter((url: string) => url.trim());

  if (urls.length === 0) {
    ElMessage.warning("没有可下载的文件");
    row.downloading = false;
    return;
  }

  try {
    const zip = new JSZip();
    const fileNames = ["项目合同", "监管协议", "营业执照", "收款账户确认函"];

    // 下载单个文件的函数
    const downloadFile = async (url: string, index: number) => {
      if (!url.trim()) return null;

      try {
        const res = await axios.get(url.trim(), {
          responseType: "blob",
          headers: {
            "cnud-auth": `bearer ${userStore.token}`,
            "cnud-tenant": "haosuan"
          }
        });

        // 获取文件扩展名
        let extension = ".pdf"; // 默认扩展名
        const contentDisposition = res.headers["content-disposition"];
        if (contentDisposition) {
          const match = contentDisposition.match(/filename="?([^"]+)"?/);
          if (match && match[1]) {
            const fileName = decodeURIComponent(match[1]);
            const dotIndex = fileName.lastIndexOf(".");
            if (dotIndex !== -1) {
              extension = fileName.substring(dotIndex);
            }
          }
        } else {
          // 从URL获取扩展名
          const urlFileName = url.substring(url.lastIndexOf("/") + 1);
          const dotIndex = urlFileName.lastIndexOf(".");
          if (dotIndex !== -1) {
            extension = urlFileName.substring(dotIndex);
          }
        }

        const finalFileName = `${fileNames[index]}${extension}`;

        return {
          fileName: finalFileName,
          data: res.data
        };
      } catch (error) {
        console.error(`下载文件失败: ${url}`, error);
        return null;
      }
    };

    // 并行下载所有文件
    const downloadPromises = urls.map(downloadFile);
    const results = await Promise.all(downloadPromises);
    const validFiles = results.filter(result => result !== null);

    if (validFiles.length === 0) {
      ElMessage.error("所有文件下载失败");
      return;
    }

    // 将文件添加到ZIP
    validFiles.forEach(file => {
      if (file) {
        zip.file(file.fileName, file.data);
      }
    });

    // 生成并下载ZIP文件
    const zipBlob = await zip.generateAsync({ type: "blob" });
    const downloadUrl = window.URL.createObjectURL(zipBlob);
    const link = document.createElement("a");
    link.href = downloadUrl;
    link.setAttribute("download", `备案资料-${row.companyName || "企业"}.zip`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    ElMessage.success("成功下载备案资料");
  } catch (error) {
    console.error("备案资料下载失败", error);
    ElMessage.error("备案资料下载失败");
  } finally {
    row.downloading = false;
  }
};

// 表格配置项
const requireTableRef = ref<ProTableInstance>();
const columns: ColumnProps<any>[] = [
  {
    prop: "companyName",
    minWidth: 110,
    label: "企业名称",
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "diagnosticFileUrl",
    minWidth: 100,
    label: "诊断报告"
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120
  },
  {
    prop: "createTime",
    minWidth: 110,
    label: "提交时间",
    sortable: true
  },
  {
    prop: "auditorName",
    minWidth: 90,
    label: "审核人"
  },
  {
    prop: "auditTime",
    minWidth: 110,
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const columns1: ColumnProps<any>[] = [
  {
    prop: "companyName",
    minWidth: 110,
    label: "企业名称",
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "renovationAgreementUrl",
    minWidth: 100,
    label: "合同协议"
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120
  },
  {
    prop: "createTime",
    minWidth: 110,
    label: "提交时间",
    sortable: true
  },
  {
    prop: "auditorName",
    minWidth: 90,
    label: "审核人"
  },
  {
    prop: "auditTime",
    minWidth: 110,
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const columns2: ColumnProps<any>[] = [
  {
    prop: "companyName",
    minWidth: 110,
    label: "企业名称",
    search: { el: "input", props: { maxlength: "20" } }
  },
  {
    prop: "bankTermsUrls",
    minWidth: 120,
    label: "备案资料"
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    width: 120
  },
  {
    prop: "createTime",
    minWidth: 110,
    label: "提交时间",
    sortable: true
  },
  {
    prop: "auditorName",
    minWidth: 90,
    label: "审核人"
  },
  {
    prop: "auditTime",
    minWidth: 110,
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const financeTableRef = ref<ProTableInstance>();
const schemeTableRef = ref<ProTableInstance>();

const activeName = ref(TabType.REQUIRE);
const handleTabClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name as TabType;
  if (tab.props.name === TabType.REQUIRE) {
    requireTableRef.value?.getTableList();
  } else if (tab.props.name === TabType.SCHEME) {
    schemeTableRef.value?.getTableList();
  } else if (tab.props.name === TabType.FINANCE) {
    financeTableRef.value?.getTableList();
  }
};

onBeforeMount(() => {
  dictionaryStore.requireReleaseStatus.length === 0 && dictionaryStore.getRequireReleaseStatus();
  dictionaryStore.requireType.length === 0 && dictionaryStore.getRequireType();
  dictionaryStore.demandConstructionAddress.length === 0 && dictionaryStore.getDemandConstructionAddress();
});
onActivated(() => {
  if (activeName.value === TabType.REQUIRE) {
    requireTableRef.value?.getTableList();
  } else if (activeName.value === TabType.SCHEME) {
    schemeTableRef.value?.getTableList();
  } else if (activeName.value === TabType.FINANCE) {
    financeTableRef.value?.getTableList();
  }
});
</script>
<style scoped lang="scss">
.tab-box {
  background-color: #ffffff;
}
:deep(.el-tabs__item) {
  width: 100px !important;
}
.list-box {
  display: flex;
  flex-direction: column;
  height: calc(100% - 54px);
}

// 下载按钮样式优化
:deep(.el-button--primary.is-loading) {
  pointer-events: none;
}
</style>
