import { ReqPageCommon } from "@/api/interface/index";

export namespace Bill {
  export interface ExportBalanceParams {
    account?: string;
    phone?: string;
  }
  export type BillListParams = ReqPageCommon & ExportBalanceParams;

  export interface BalanceRecordParams extends ReqPageCommon {
    balanceChangeType?: string; // 类型(1-订购 2-使用 3-过期)
    happenTimeEnd?: string; // 发生结束时间
    happenTimeStart?: string; // 发生开始时间
    typeList?: string; // 类型列表（前端不用传）
    userId: string; // 用户Id
  }
  export interface DskJobBillParams extends ReqPageCommon {
    account?: string; // 客户账号
    changeType?: string; // 类型
    clusterId?: string; // 集群id
    happenTimeEnd?: string; // 发生结束时间
    happenTimeStart?: string; // 发生开始时间
    jobType?: string; // 作业类型
    payStatus?: string; // 	支付状态
    phone?: string; // 	手机号码
  }

  export interface RelativParams {
    jobBillId?: string; // 明细账单Id
    subOrderNo?: string; // 订单编码
  }
  export interface DskBillParams extends ReqPageCommon {
    billStartDate?: string; // 账期开始时间
    billEndDate?: string; // 账期结束时间
    productName?: string; // 产品名称
  }

  export interface IClusterPlatform {
    clusterId: string;
    clusterName: string;
    platformCode: string;
    displayName?: string;
  }

  export interface IClusterItem extends IClusterPlatform {
    cpu: number;
    createTime: string;
    creatorId: string;
    creatorName: string;
    gpu: number;
    graphics: number;
    hardwareCode: string;
    instanceName: string;
    memory: number;
    price: number;
    status: string;
    storage: string;
    updateTime: string;
    updaterId: string;
    updaterName: string;
  }
  export interface ICluster {
    clusterCode: string | null;
    clusterId: string | null;
    clusterName: string | null;

    aiClusterId: string | null;
    aiClusterName: string | null;
    hpcClusterId: string | null;
    hpcClusterName: string | null;
    platformCode: string | null;
    resourceId: string | null;
  }
}
