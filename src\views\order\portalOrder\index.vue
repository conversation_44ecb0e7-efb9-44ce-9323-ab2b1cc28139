<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="商城订单列表"
      :columns="columns"
      :tool-button="false"
      :request-api="getTableList"
      :request-auto="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="createOrder"> 新增 </el-button>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>
      <template #status="{ row }">
        <CustomTag
          :type="'order'"
          :status="row.status"
          :label="useDictLabel(dictionaryStore?.orderStatusDic, row.status)"
        ></CustomTag>
      </template>
      <template #skuName="{ row }">
        <div v-html="row.skuName"></div>
      </template>

      <!-- 表格操作 -->
      <template #operation="{ row }">
        <span v-auth="'review'">
          <el-tooltip v-if="row?.auditStatus === '2'" content="审核" placement="top">
            <i class="iconfont2 opera-icon icon-shenhe" @click="verify(row)"></i>
          </el-tooltip>
        </span>
        <!-- 
          1、若下单触点为政企中台，订单状态为【待开通】时显示此按钮，跳转【政企中台订单详情】；
          2、若下单触点为政企中台以外的触点，订单状态为【待开通】时显示此按钮，跳转到【订单开通页面】；
         -->
        <template v-if="row.channelCode === 'ZQ' && row.status === '9'">
          <span v-auth="'deal_button'">
            <el-tooltip content="处理" placement="top">
              <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toVerify(row)"></i>
            </el-tooltip>
          </span>
        </template>
        <template v-if="row.channelCode !== 'ZQ' && row.status === '9'">
          <span v-auth="'handleOpen'">
            <el-tooltip content="开通" placement="top">
              <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="handleOrderOpen(row)"></i>
            </el-tooltip>
          </span>
        </template>
      </template>
    </pro-table>
    <FailMessageModal
      :visible="failSubOrder.showFailMessage"
      :sub-order-id="failSubOrder.subOrderId"
      @update:visible="onUpdatedVisible"
    >
    </FailMessageModal>
  </div>
</template>

<script setup lang="tsx" name="portalOrder">
import { ref, onMounted, onActivated, computed, reactive } from "vue";

import { Plus } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
// import { useDownload } from "@/hooks/useDownload";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getOrderList, queryOrderListExcel } from "@/api/modules/order";
import { useDicStore } from "@/stores/modules/dictionaty";
import { getChannelList } from "@/api/modules/product";
import { useDownload } from "@/hooks/useDownload";
import { useDictLabel } from "@/hooks/useDict";
import CustomTag from "@/components/CustomTag/index.vue";
import FailMessageModal from "./components/failMessageModal.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useHandleRoute } from "../useOrder";
const { BUTTONS } = useAuthButtons();

const proTable = ref<ProTableInstance>();
const dictionaryStore = useDicStore();
const contactorList = ref([]); // 下单触点

// const { yes_no_type } = useDict("yes_no_type");
const failSubOrder = reactive({
  showFailMessage: false,
  subOrderId: ""
});

const { verify, toVerify, handleToDetail, createOrder, handleOrderOpen } = useHandleRoute();
onMounted(() => {
  // 先获取字典
  dictionaryStore.getQuantityDicList(); // 数量单位
  dictionaryStore.getTimeDicList(); // 时长单位
  dictionaryStore.getOrderType(); // 订单类型
  dictionaryStore.getOrderStatus(); // 订单状态
  dictionaryStore.getAuditStatus(); // 审核状态
  dictionaryStore.getOrderPayDicList(); // 订单-支付方式
  dictionaryStore.getprobationFlagDic(); // 订单-是否支持试用
  // 获取渠道列表-渲染下单触点的下拉框
  getChannelList({}).then((res: any) => {
    res.data.forEach((item: any = {}) => {
      // 订单查询的下单触点（渠道列表）和产品模块的下单触点处理逻辑不一样，这边用的code，产品用的id
      contactorList.value.push({ value: item.code, label: item.name });
    });
  });
  // proTable.value?.getTableList();
});
// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  proTable.value?.getTableList();
});
const orderTypeDic: any = computed(() => dictionaryStore.orderTypeDic);
const orderStatusDic: any = computed(() => dictionaryStore.orderStatusDic);
const auditStatusDic: any = computed(() => dictionaryStore.auditStatusDic);
const orderPayWayDic: any = computed(() => dictionaryStore.orderPayWayDic);
const probationFlagDic: any = computed(() => dictionaryStore.probationFlagDic);

// 表格配置项
const columns: ColumnProps[] = [
  // { type: "selection", fixed: "left", width: 50 },
  { prop: "customerAccount", label: "客户账号", width: 110, search: { el: "input" } },
  { prop: "purchasePhone", label: "手机号码", width: 120, search: { el: "input" } },
  {
    prop: "channelName",
    label: "下单触点",
    width: 125,
    isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格,
    enum: contactorList,
    search: { el: "select", key: "channelCode" }
  },
  {
    prop: "parentOrderNo",
    label: "订单编号",
    width: 180,
    search: { el: "input" },
    render: ({ row }) => {
      return (
        <el-button type="text" onClick={() => handleToDetail("parentOrderNo", row)}>
          {row.parentOrderNo}
        </el-button>
      );
    }
  },
  {
    prop: "subOrderNo",
    label: "子订单编号",
    width: 180,
    search: { el: "input" },
    render: ({ row }) => {
      return (
        <el-button type="text" onClick={() => handleToDetail("subOrderNo", row)}>
          {row.subOrderNo}
        </el-button>
      );
    }
  },
  { prop: "sourceOrderNo", label: "外部订单编号", width: 110, search: { el: "input" }, isShow: false },
  { prop: "productName", label: "产品名称", width: 170, search: { el: "input" } },
  { prop: "skuName", label: "规格名称", width: 240, search: { el: "input" } },
  { prop: "cbProductId", label: "话费订购支付编码", width: 145, search: { el: "input" }, isShow: false },
  { prop: "orderType", label: "订单类型", width: 110, enum: orderTypeDic, sortable: true, search: { el: "select" } },
  {
    prop: "status",
    label: "订单状态",
    width: 105,
    enum: orderStatusDic,
    search: { el: "select", key: "statusList", props: { multiple: true, placeholder: "请选择（可多选）" } }
  },
  {
    prop: "adminStatusRemark",
    label: "状态码描述",
    width: 200,
    render: ({ row }) => {
      if (row.adminStatusRemark === "其他异常") {
        return (
          <el-button type="text" onClick={() => handleFailMessage(row.subOrderNo)}>
            {row.adminStatusRemark}
          </el-button>
        );
      } else {
        return <>{row.adminStatusRemark || "--"}</>;
      }
    }
  },
  { prop: "payType", label: "支付方式", width: 90, enum: orderPayWayDic, search: { el: "select" } },
  {
    prop: "probationFlag",
    label: "试用产品",
    width: 90,
    enum: probationFlagDic,
    search: { el: "select" }
  },
  { prop: "prePayFee", label: "订单总金额（元）", width: 145 },
  { prop: "payFee", label: "应付金额（元）", width: 145 },
  { prop: "productProviderName", label: "产品服务商", width: 100 },
  {
    prop: "subscribeTime",
    label: "订购时间",
    sortable: true,
    width: 170,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "unsubscribeTime",
    label: "退订时间",
    isShow: false,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    // prop: "lifeEffectTimeBegin",
    prop: "prodEffectTime",
    label: "产品使用生效时间",
    sortable: true,
    width: 170,
    search: {
      el: "date-picker",
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" },
      transform: (value: any) => {
        console.log(value);
        return {
          lifeEffectTimeBegin: value[0],
          lifeEffectTimeEnd: value[1]
        };
      }
    },
    render: ({ row }) => {
      return row.lifeEffectTimeBegin;
    }
  },
  {
    prop: "lifeEffectTimeEnd",
    label: "产品使用到期时间",
    sortable: true,
    width: 170,
    render: ({ row }) => {
      return row.lifeEffectTimeEnd;
    }
  },
  {
    prop: "finishTime",
    label: "订购完成时间",
    isShow: false,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "closeTime",
    label: "订购失败时间",
    isShow: false,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  { prop: "auditStatus", label: "审核状态", enum: auditStatusDic, width: 100, search: { el: "select" }, isShow: false },
  { prop: "auditor", label: "审核人", isShow: false, search: { el: "input" } },
  {
    prop: "auditTime",
    label: "审核时间",
    isShow: false,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  // {
  //   prop: "failFlag",
  //   label: "异常单",
  //   isShow: false,
  //   search: {
  //     render: (scope: any) => {
  //       return (
  //         <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
  //           {yes_no_type.value?.map((item: { itemKey: string; itemValue: string }) => (
  //             <el-option label={item.itemValue} value={item.itemKey} />
  //           ))}
  //         </el-select>
  //       );
  //     }
  //   }
  // },

  // { prop: "multiPoint", label: "订单分类", width: 90, enum: orderTypeList, search: { el: "select" } },

  // {
  //   prop: "creatorName",
  //   label: "后台创建人",
  //   width: 100,
  //   // tag: true,
  //   render: scope => {
  //     return scope.row.channelName === "一体化支撑平台" ? scope.row.creatorName : "-";
  //   }
  // },

  { prop: "operation", label: "操作", fixed: "right", width: 100 }
];

const getTableList = async (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  console.log(params);
  let newParams = JSON.parse(JSON.stringify(params));

  if (params.statusList && Array.isArray(params.statusList)) {
    newParams.statusList = params.statusList.join(",");
  }
  // 订购时间
  newParams.subscribeTime && (newParams.subscribeTimeOfStart = newParams.subscribeTime[0]);
  newParams.subscribeTime && (newParams.subscribeTimeOfEnd = newParams.subscribeTime[1]);
  delete newParams.subscribeTime;
  // 退订时间
  newParams.unsubscribeTime && (newParams.unsubscribeTimeOfStart = newParams.unsubscribeTime[0]);
  newParams.unsubscribeTime && (newParams.unsubscribeTimeOfEnd = newParams.unsubscribeTime[1]);
  delete newParams.unsubscribeTime;
  // 生效时间
  newParams.effectTime && (newParams.effectTimeOfStart = newParams.effectTime[0]);
  newParams.effectTime && (newParams.effectTimeOfEnd = newParams.effectTime[1]);
  delete newParams.effectTime;
  // 到期时间
  newParams.endTime && (newParams.endTimeOfStart = newParams.endTime[0]);
  newParams.endTime && (newParams.endTimeOfEnd = newParams.endTime[1]);
  delete newParams.endTime;
  // 订购完成时间
  newParams.finishTime && (newParams.finishTimeOfStart = newParams.finishTime[0]);
  newParams.finishTime && (newParams.finishTimeOfEnd = newParams.finishTime[1]);
  delete newParams.finishTime;
  // 订购失败时间
  newParams.closeTime && (newParams.closeTimeOfStart = newParams.closeTime[0]);
  newParams.closeTime && (newParams.closeTimeOfEnd = newParams.closeTime[1]);
  delete newParams.closeTime;
  // 审核时间
  newParams.auditTime && (newParams.auditTimeOfStart = newParams.auditTime[0]);
  newParams.auditTime && (newParams.auditTimeOfEnd = newParams.auditTime[1]);
  delete newParams.auditTime;

  // /* 产品使用生效时间 */
  // if (Reflect.has(params, "prodEffectTime")) {
  //   Reflect.set(newParams, "lifeEffectTimeBegin", Reflect.get(params, "prodEffectTime")[0]);
  //   Reflect.set(newParams, "lifeEffectTimeEnd", Reflect.get(params, "prodEffectTime")[1]);
  //   Reflect.deleteProperty(newParams, "prodEffectTime");
  // }
  // 订单类型（1-商城订单 2-生态触点订单 3-政企中台订单）
  // newParams.multiPoint = multiPoint;
  return await getOrderList(newParams);
};

const handleFailMessage = (subOrderId: string) => {
  failSubOrder.subOrderId = subOrderId;
  failSubOrder.showFailMessage = true;
};

const onUpdatedVisible = (value: boolean) => {
  failSubOrder.showFailMessage = value;
};

// 导出解决列表
const downloadFile = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));

  // 订购时间
  newParams.subscribeTime && (newParams.subscribeTimeOfStart = newParams.subscribeTime[0]);
  newParams.subscribeTime && (newParams.subscribeTimeOfEnd = newParams.subscribeTime[1]);
  delete newParams.subscribeTime;
  // 退订时间
  newParams.unsubscribeTime && (newParams.unsubscribeTimeOfStart = newParams.unsubscribeTime[0]);
  newParams.unsubscribeTime && (newParams.unsubscribeTimeOfEnd = newParams.unsubscribeTime[1]);
  delete newParams.unsubscribeTime;
  // 生效时间
  newParams.effectTime && (newParams.effectTimeOfStart = newParams.effectTime[0]);
  newParams.effectTime && (newParams.effectTimeOfEnd = newParams.effectTime[1]);
  delete newParams.effectTime;
  // 到期时间
  newParams.endTime && (newParams.endTimeOfStart = newParams.endTime[0]);
  newParams.endTime && (newParams.endTimeOfEnd = newParams.endTime[1]);
  delete newParams.endTime;
  // 订购完成时间
  newParams.finishTime && (newParams.finishTimeOfStart = newParams.finishTime[0]);
  newParams.finishTime && (newParams.finishTimeOfEnd = newParams.finishTime[1]);
  delete newParams.finishTime;
  // 订购失败时间
  newParams.closeTime && (newParams.closeTimeOfStart = newParams.closeTime[0]);
  newParams.closeTime && (newParams.closeTimeOfEnd = newParams.closeTime[1]);
  delete newParams.closeTime;
  // 审核时间
  newParams.auditTime && (newParams.auditTimeOfStart = newParams.closeTime[0]);
  newParams.auditTime && (newParams.auditTimeOfEnd = newParams.closeTime[1]);
  delete newParams.auditTime;
  // 订单状态
  newParams.statusList && (newParams.statusList = newParams.statusList.join(","));
  // 订单类型（1-商城订单 2-生态触点订单 3-政企中台订单）
  // /* 产品使用生效时间 */
  // if (Reflect.has(newParams, "prodEffectTime")) {
  //   Reflect.set(newParams, "lifeEffectTimeBegin", Reflect.get(newParams, "prodEffectTime")[0]);
  //   Reflect.set(newParams, "lifeEffectTimeEnd", Reflect.get(newParams, "prodEffectTime")[1]);
  //   Reflect.deleteProperty(newParams, "prodEffectTime");
  // }

  for (const key in newParams) {
    if (Object.prototype.hasOwnProperty.call(newParams, key)) {
      const element = newParams[key];
      if (element === "" || element === undefined || element === null) {
        Reflect.deleteProperty(newParams, key);
      }
    }
  }

  newParams.size = 10000;
  // ElMessageBox.confirm("确认导出订单列表?", "提示", { type: "warning" }).then(() => useDownload(queryOrderListExcel, newParams));

  queryOrderListExcel(newParams)
    .then(res => {
      ElMessage.success("请前往下载管理中下载文件");
    })
    .catch(() => {
      ElMessage.error("下载有误，请重新下载");
    });
};
</script>
