export const downloadUtil = {
  type: {
    excel: "application/vnd.ms-excel;charset=utf-8",
    zip: "application/zip"
  },
  // 创建一个a标签，并做下载点击事件
  downloadFile: function (blob, fileName) {
    const link = document.createElement("a");
    link.href = window.URL.createObjectURL(new Blob([blob]));
    link.download = fileName;
    // 此写法兼容可火狐浏览器
    document.body.appendChild(link);
    const evt = document.createEvent("MouseEvents");
    evt.initEvent("click", false, false);
    link.dispatchEvent(evt);
    document.body.removeChild(link);
  },
  // 将Base64文件转为 Blob
  buildBlobByByte: function (data) {
    const raw = window.atob(data);
    const rawLength = raw.length;
    const uInt8Array = new Uint8Array(rawLength);
    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array]);
  },
  // 二进制数组 生成文件
  downloadFileByByte: function (data, fileName) {
    const blob = this.buildBlobByByte(data);
    this.downloadFile(blob, fileName);
  },
  downloadFileByArrayBuffer: function (data, fileName, type = downloadUtil.type.excel) {
    console.log("data======>", data);
    const blob = new Blob([data], {
      type
    });
    this.downloadFile(blob, fileName);
  },
  downloadNewFile: function (url: string, name: string) {
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    link.setAttribute("download", name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },
  downloadBlobFile: function (
    buffer: ArrayBuffer,
    name: string,
    blobPropertyBag: BlobPropertyBag = { type: "application/octet-stream" }
  ) {
    const url = window.URL.createObjectURL(new Blob([buffer], blobPropertyBag));
    this.downloadNewFile(url, name);
    window.URL.revokeObjectURL(url);
  }
};
