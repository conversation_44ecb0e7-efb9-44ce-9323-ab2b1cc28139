<template>
  <div class="card" style="padding-left: 32px">
    <div class="header-title">企业信息</div>
    <el-form ref="detailsFormRef" :model="reviewForm" :rules="rules" label-width="140px" label-position="left">
      <el-form-item label="企业名称" prop="companyName">
        <el-input
          type="textarea"
          :disabled="viewStatus"
          v-model="reviewForm.companyName"
          placeholder="请输入企业名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="企业法人" prop="legalPerson">
        <el-input :disabled="viewStatus" v-model="reviewForm.legalPerson" placeholder="请输入企业法人" clearable />
      </el-form-item>
      <el-form-item label="社会信用统一代码" prop="usciCode">
        <el-input :disabled="viewStatus" v-model="reviewForm.usciCode" placeholder="请输入真实姓名" clearable />
      </el-form-item>
      <!-- <el-form-item :disabled="viewStatus" label="所属行业" prop="industryType">
        <el-select
          :disabled="viewStatus"
          v-model="reviewForm.industryType"
          placeholder="请选择所属行业"
          clearable
          style="width: 400px"
        >
          <el-option
            v-for="(item, index) in industryList"
            :key="index + 'industryList'"
            :label="item.itemValue"
            :value="item.itemKey"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="公司注册地" prop="province">
        <el-select
          :disabled="viewStatus"
          v-model="reviewForm.province"
          placeholder="请选择公司注册地"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="(item, index) in provinceList"
            :key="index + 'provinceList'"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <span style="margin: 0 10px"> - </span>
        <el-select :disabled="viewStatus" v-model="reviewForm.city" placeholder="请选择公司注册地" clearable style="width: 200px">
          <el-option v-for="(item, index) in cityList" :key="index + 'cityList'" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="注册时间" prop="registeredTime">
        <el-date-picker
          :disabled="viewStatus"
          v-model="reviewForm.registeredTime"
          type="datetime"
          placeholder="请选择注册时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="注册资金（万元）" prop="registeredCapital">
        <el-input
          :disabled="viewStatus"
          type="text"
          v-model="reviewForm.registeredCapital"
          placeholder="请输入注册资金"
          clearable
        />
      </el-form-item>
      <el-form-item label="通讯地址" prop="companyAddress">
        <el-input
          type="textarea"
          :disabled="viewStatus"
          v-model="reviewForm.companyAddress"
          placeholder="请输入通讯地址"
          clearable
        />
      </el-form-item> -->

      <div class="header-title">认证授权代表</div>
      <el-form-item label="真实姓名" prop="contactName">
        <el-input :disabled="viewStatus" v-model="reviewForm.contactName" placeholder="请输入真实姓名" clearable />
      </el-form-item>
      <el-form-item label="身份证号码" prop="idcardNumber">
        <el-input :disabled="viewStatus" v-model="reviewForm.idcardNumber" placeholder="请输入身份证号码" clearable />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input :disabled="viewStatus" type="text" v-model="reviewForm.phone" placeholder="请输入手机号码" clearable />
      </el-form-item>

      <div class="header-title">实名认证材料</div>
      <el-form-item label="营业执照" prop="companyCertificateUrl">
        <UploadImg
          v-model:image-url="reviewForm.companyCertificateUrl"
          :limit="5"
          :file-size="1"
          :disabled="viewStatus"
          height="140px"
          width="140px"
        >
        </UploadImg>
      </el-form-item>
      <!-- <el-row justify="start">
        <el-form-item label="授权代表证件" prop="certificateFrontUrl">
          <UploadImg
            v-model:image-url="reviewForm.certificateFrontUrl"
            :limit="1"
            :file-size="1"
            :disabled="viewStatus"
            height="140px"
            width="140px"
          >
            <template #tip>身份证人像面</template>
          </UploadImg>
        </el-form-item>
        <el-form-item label-width="20" prop="certificateBackUrl">
          <UploadImg
            v-model:image-url="reviewForm.certificateBackUrl"
            :limit="1"
            :file-size="1"
            :disabled="viewStatus"
            height="140px"
            width="140px"
          >
            <template #tip>身份证国徽面</template>
          </UploadImg>
        </el-form-item>
      </el-row> -->
      <!-- <el-form-item label="上传授权书" prop="authorizeFileUrl">
        <UploadImg
          v-model:image-url="reviewForm.authorizeFileUrl"
          :limit="5"
          :file-size="1"
          :disabled="viewStatus"
          height="140px"
          width="140px"
        >
        </UploadImg>
      </el-form-item> -->

      <Examine
        :verification-id="id"
        :audit-msg="reviewForm.auditMsg"
        :is-disabled="isView"
        :is-show-operate-btn="!isView"
        @on-confirm="onConfirm"
        @on-back="backtoList"
      />
    </el-form>
  </div>
</template>

<script setup lang="ts" name="busiReview">
import { reactive, ref, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { getEnterpriseCertDetail, auditEnterpriseCert, queryEnterpriseCertDetail } from "@/api/modules/verification";
import Examine from "@/views/verification/examine.vue";
import UploadImg from "@/components/Upload/Img.vue";
// import { getDictionary } from "@/api/modules/system";
import { TabPaneName } from "element-plus";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { editType } from "@/enums/pageTypeEnum";
import sm2DoDecrypt from "@/utils/sm2DoDecrypt";

const route = useRoute();
const router = useRouter();
const detailsFormRef = ref<FormInstance>();
const { auditTag } = route.query;
const isView = auditTag == "1"; // 是否是详情
const viewStatus = auditTag == "1" || auditTag == "2";
const validType = ref<string>("pass");
const id = route.query.id as string;

const tabStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();

// Remove Tab
const tabRemove = (fullPath: TabPaneName) => {
  const name = tabStore.tabsMenuList.filter(item => item.path == fullPath)[0].name || "";
  keepAliveStore.removeKeepAliveName(name);
  tabStore.removeTabs(fullPath as string, fullPath == route.fullPath);
};

const defFormValue = {
  idType: "0",
  companyName: "",
  legalPerson: "",
  usciCode: "",
  // industryType: "",
  // province: "",
  // city: "",
  // registeredTime: "",
  // registeredCapital: "",
  companyAddress: "",
  contactName: "",
  idcardNumber: "",
  phone: "",
  companyCertificateUrl: "",
  authorizeFileUrl: "",
  auditMsg: "",
  certificateBackUrl: "",
  certificateFrontUrl: ""
};
const reviewForm = ref(defFormValue);
// const industryList = ref<any>([]);
// const provinceList = ref<any>([]);
// const cityList = ref<any>([]);
/* 请求 */
onMounted(() => {
  // 行业类型字典
  // getDictionary({ dictCode: "dict_industry_type" }).then((res: any) => {
  //   const data = res?.data;
  //   data.forEach((item: any = {}) => {
  //     item.value = item.itemKey;
  //     item.label = item.itemValue;
  //   });
  //   industryList.value = JSON.parse(JSON.stringify(data));
  // });
  if (auditTag === String(editType.audit)) {
    queryEnterpriseCertDetail({ id }).then((res: any) => {
      reviewForm.value = {
        ...defFormValue,
        idType: res.data.idType,
        companyName: res.data.companyName,
        legalPerson: res.data?.legalPerson,
        usciCode: res.data.usciCode,
        contactName: res.data.contactName,
        // idcardNumber: res.data.idcardNumber,
        // phone: res.data.phone,
        companyCertificateUrl: res.data.companyCertificateUrl,
        authorizeFileUrl: res.data.authorizeFileUrl,
        auditMsg: res.data.auditMsg,
        certificateFrontUrl: res.data.certificateFrontUrl,
        certificateBackUrl: res.data.certificateBackUrl
      };
      sm2DoDecrypt({
        idcardNumber: res.data.idcardNumber,
        phone: res.data.phone
      })
        .then(data => {
          reviewForm.value.idcardNumber = data?.idcardNumber;
          reviewForm.value.phone = data?.phone;
        })
        .catch(err => {});
    });
  } else {
    getEnterpriseCertDetail({ id }).then((res: any) => {
      // provinceList.value = res.data.province
      //   ? [
      //       {
      //         label: res.data.province,
      //         value: res.data.province
      //       }
      //     ]
      //   : [];
      // cityList.value = res.data.city
      //   ? [
      //       {
      //         label: res.data.city,
      //         value: res.data.city
      //       }
      //     ]
      //   : [];
      reviewForm.value = {
        ...defFormValue,
        idType: res.data.idType,
        companyName: res.data.companyName,
        legalPerson: res.data?.legalPerson,
        usciCode: res.data.usciCode,
        // industryType: res.data.industryType,
        // province: res.data.province,
        // city: res.data.city,
        // registeredTime: res.data.registeredTime,
        // registeredCapital: res.data.registeredCapital,
        // companyAddress: res.data.companyAddress,
        contactName: res.data.contactName,
        idcardNumber: res.data.idcardNumber,
        phone: res.data.phone,
        companyCertificateUrl: res.data.companyCertificateUrl,
        authorizeFileUrl: res.data.authorizeFileUrl,
        auditMsg: res.data.auditMsg,
        // certificateFrontUrl: "http://minio-perf.*************.nip.io/cnud/upload/20231024/510cd692a22937f273312e9a8a3b84c8.jpg",
        // certificateBackUrl: "http://cnud-dev.*************.nip.io/" + res.data.certificateBackUrl
        certificateFrontUrl: res.data.certificateFrontUrl,
        certificateBackUrl: res.data.certificateBackUrl
      };
    });
  }
});

const rules = reactive<FormRules>({
  companyName: [{ required: true, message: "请输入企业名称", trigger: "blur" }],
  legalPerson: [{ required: true, message: "请输入企业法人", trigger: "blur" }],
  usciCode: [{ required: true, message: "请输入社会信用统一代码:", trigger: "blur" }],
  // industryType: [{ required: true, message: "请选择所属行业", trigger: "blur" }],
  // province: [{ required: true, message: "请选择公司注册地", trigger: "blur" }],
  // city: [{ required: true, message: "请选择公司注册地", trigger: "blur" }],
  // registeredTime: [{ required: true, message: "请选择注册时间", trigger: "blur" }],
  // registeredCapital: [{ required: true, message: "请输入注册资金", trigger: "blur" }],
  contactName: [{ required: true, message: "请输入真实姓名", trigger: "blur" }],
  idcardNumber: [{ required: true, message: "请输入身份证号码", trigger: "blur" }],
  phone: [{ required: true, message: "请输入手机号码", trigger: "change" }],
  companyCertificateUrl: [{ required: true, message: "请上传营业执照", trigger: "change" }],
  certificateFrontUrl: [{ required: true, message: "请上传授权代表证件", trigger: "change" }],
  certificateBackUrl: [{ required: true, message: "请上传授权代表证件", trigger: "change" }],
  authorizeFileUrl: [{ required: true, message: "请上传授权书", trigger: "change" }],
  auditMsg: [{ required: validType.value === "noPass", message: "请输入审核意见", trigger: "change" }]
});

const backtoList = () => {
  router.push("/verification/verificationReview");
};

const onConfirm = (values: any) => {
  auditEnterpriseCert({
    id: id,
    msg: values.auditRemark,
    pass: values.type == "pass" ? "1" : "0"
  }).then(res => {
    if (res.code === 200) {
      ElMessage({
        message: values.type == "pass" ? "审核通过成功!" : "审核不通过完成!",
        type: "success"
      });
      tabRemove(route.fullPath);
      backtoList();
    } else {
      ElMessage({
        type: "error",
        message: res.msg
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.el-form {
  width: 60%;
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
</style>
