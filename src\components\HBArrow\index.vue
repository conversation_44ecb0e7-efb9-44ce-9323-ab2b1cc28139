<template>
  <span>
    <i v-if="value === ArrowType.up" class="iconfont2 icon-lineicon_caret_down xiala up" :style="iconStyle"></i>
    <i v-else-if="value === ArrowType.down" class="iconfont2 icon-lineicon_caret_down xiala down" :style="iconStyle"></i>
    <template v-else></template>
  </span>
</template>

<script setup lang="ts" name="HBArrow">
enum ArrowType {
  up = "1",
  down = "2"
}
defineProps({
  value: {
    type: String,
    default: ""
  },
  iconStyle: {
    type: Object,
    default: () => {}
  }
});
</script>

<style lang="scss" scoped>
.up {
  display: inline-block;
  color: #f5222d;
  transform: rotate(180deg);
}
.down {
  color: #52c41a;
}
</style>
