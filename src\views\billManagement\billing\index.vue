<!--账单管理（月）-->
<template>
  <div class="table-box">
    <ProTable ref="proTable" title="账单列表" :columns="columns" :tool-button="false" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="{ row }">
        <div class="operation-btn">
          <auth-button :row-data="row" :auth-list="authList"></auth-button>
        </div>
      </template>
      <!-- <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看明细" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
      </template> -->
    </ProTable>
  </div>
</template>

<script setup lang="ts" name="billing">
import { ref } from "vue";
import { cloneDeep } from "lodash";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import type { IAuthButtonItem } from "@/components/AuthButton/typings";
import { getDskBill, exportDskBill } from "@/api/modules/billManagement";
import { checkMax12Months } from "@/utils/eleValidate";

const { BUTTONS } = useAuthButtons();
const proTable = ref<ProTableInstance>();

const router = useRouter();

const getTableList = (params: any) => {
  if (!BUTTONS.value?.query) {
    return Promise.resolve();
  }
  const newParams = cloneDeep(params);
  delete newParams.billDateString;
  return getDskBill(newParams);
};

const downloadFile = async () => {
  const newParams = cloneDeep(proTable.value?.searchParam);
  delete newParams.billDateString;
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() => useDownload(exportDskBill, newParams));
};

const authList: IAuthButtonItem[] = [
  {
    contentName: "详情",
    iconClass: "View",
    itemClick: scope => toView(scope),
    authName: "view"
  }
];

const columns: ColumnProps<any>[] = [
  { prop: "id", label: "账单ID" },
  {
    prop: "billDateString",
    label: "账期",
    search: {
      el: "date-picker",
      props: {
        type: "monthrange",
        valueFormat: "YYYY-MM",
        rules: [{ trigger: "blur", validator: checkMax12Months }]
      },
      transform: (value: any) => {
        return {
          billStartDate: value[0],
          billEndDate: value[1]
        };
      }
    }
  },
  { prop: "productName", label: "产品名称", search: { el: "input", props: { maxLength: 20 } } },
  { prop: "billAmount", label: "本期账单金额（元）" },
  { prop: "generateTime", label: "账单生成时间", align: "left" },
  { prop: "operation", label: "操作", fixed: "right", width: 120 }
];

const toView = async (row: any) => {
  router.push({ path: `/billManagement/billingDetail` });
};
</script>
