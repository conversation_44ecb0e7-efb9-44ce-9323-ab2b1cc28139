<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      @selection-change="selectChange"
      title="权益管理列表"
      :columns="columns"
      :request-api="getTableList"
      :tool-button="false"
    >
      <template #status="{ row }">
        <CustomTag :type="'benefit'" :status="row.status" :label="row.status === 1 ? '启用' : '停用'"></CustomTag>
      </template>
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader="scope">
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => handelAdd()"> 新建权益 </el-button>
        <el-button
          :disabled="!scope.isSelected"
          v-auth="'delete'"
          plain
          :icon="Delete"
          @click="handleDeleteMore(scope.selectedListIds)"
        >
          批量删除
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click.stop="handelEdit(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="handelDelete(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'">
          <el-tooltip :content="scope.row.status === 1 ? '停用' : '启用'" placement="top">
            <i v-if="scope.row.status === 1" class="iconfont2 opera-icon icon-tingyong" @click="handleStatus(scope.row)"></i>
            <i v-else class="iconfont2 opera-icon icon-shenhe" @click="handleStatus(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <Edit ref="editRef" @refresh="refresh"></Edit>
  </div>
</template>

<script setup lang="tsx" name="partnerManagement">
import { ref } from "vue";
import { Plus, Delete } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { benefitDelete, getPageList, queryUpdatePmsRights, queryViewDetail } from "@/api/modules/benefitManage";
import Edit, { IAction } from "./edit/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { PmsRights } from "@/api/interface/benefitManage";
import CustomTag from "@/components/CustomTag/index.vue";

const { BUTTONS } = useAuthButtons();

const proTable = ref<ProTableInstance>();

const editRef = ref<{ handleOpen: (active: IAction, rowData?: PmsRights.EditParams) => void } | null>(null);

const isMoreSelect = ref(true);

const getTableList = async (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }

  let newParams = JSON.parse(JSON.stringify(search));
  return getPageList(newParams);
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "name", label: "权益名称", search: { el: "input", props: { maxLength: 20 } } },
  { prop: "providerName", label: "合作商名称" },
  {
    prop: "useType",
    label: "门户权益使用类型",
    enum: [
      { label: "跳转外部链接", value: "1" },
      { label: "跳转内部链接", value: "2" },
      { label: "使用指引说明", value: "4" },
      { label: "二维码", value: "3" }
    ]
  },
  {
    prop: "miniAppsUseType",
    label: "小程序、H5使用类型",
    enum: [
      { label: "跳转外部链接", value: "1" },
      { label: "跳转内部链接", value: "2" },
      { label: "使用指引说明", value: "4" },
      { label: "二维码", value: "3" }
    ]
  },
  {
    prop: "apiSwitch",
    label: "开通接口",
    enum: [
      { label: "不支持", value: "0" },
      { label: "打开", value: "1" },
      { label: "关闭", value: "2" }
    ]
    // enum: industryType
  },
  {
    prop: "status",
    label: "状态",
    width: 180,
    // enum: auditStatusDict
    enum: [
      { label: "停用", value: 0 },
      { label: "启用", value: 1 }
    ]
  },
  {
    prop: "creatorName",
    label: "创建人员"
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    sortable: true,
    width: 170
  },
  { prop: "operation", label: "操作", fixed: "right", width: 130 }
];

const handelAdd = () => {
  editRef.value?.handleOpen("Add");
};

const handelEdit = (data: PmsRights.EditParams) => {
  editRef.value?.handleOpen("Edit", data.id);
};

const handelDelete = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此权益？", "确认操作").then(async () => {
    await benefitDelete([data.id]);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};
const handleStatus = (data: any) => {
  queryUpdatePmsRights({ ...data, status: data.status === 1 ? "0" : "1" }).then(res => {
    if (res.code === 200) {
      ElMessage.success("修改成功");
      proTable.value?.getTableList();
    }
  });
};

const handleDeleteMore = (ids: Array<string>) => {
  if (ids.length > 0) {
    isMoreSelect.value = false;
    ElMessageBox.confirm("请确认是否删除权益？", "确认操作").then(async () => {
      await benefitDelete([...ids]);
      ElMessage.success("操作成功！");
      proTable.value?.clearSelection();
      proTable.value?.getTableList();
    });
  } else {
    isMoreSelect.value = true;
  }
};

const selectChange = (values: any) => {
  if (values.length > 0) {
    isMoreSelect.value = false;
  } else {
    isMoreSelect.value = true;
  }
};

const refresh = () => {
  proTable.value?.getTableList();
};
</script>

<style scoped></style>
