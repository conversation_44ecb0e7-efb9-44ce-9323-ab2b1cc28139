<template>
  <div class="card content-box">
    <div class="edit-title">{{ route.meta.title }}</div>
    <el-form ref="ruleFormRef" :model="ruleForm" label-position="left" :rules="rules" label-width="110px">
      <el-form-item label="消息标题" prop="title" required>
        <el-input :disabled="disabled" v-model="ruleForm.title" :max="50" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="消息类型" prop="bizTypeId" required>
        <el-radio-group v-model="ruleForm.bizTypeId" :disabled="disabled">
          <el-radio label="1">系统通知</el-radio>
          <el-radio label="2">活动通知</el-radio>
          <el-radio label="3">产品通知</el-radio>
          <el-radio label="4">其他</el-radio>
          <!-- <el-radio v-for="(item, index) in noticeTypeDic" :key="index + 'noticeType'" :label="item.itemKey">
            {{ item.itemValue }}
          </el-radio> -->
        </el-radio-group>
      </el-form-item>
      <el-form-item label="发布时间" prop="publishType" required>
        <el-radio-group v-model="ruleForm.publishType" :disabled="disabled">
          <el-radio :label="0">立即发布</el-radio>
          <el-radio :label="1">定时发布</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="定时发布" prop="publishTime" v-if="ruleForm.publishType == 1">
        <el-date-picker
          :disabled="disabled"
          v-model="ruleForm.publishTime"
          type="datetime"
          placeholder="请选择发布时间"
          :disabled-date="disabledDate"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="消息内容" prop="noticeContent" placeholder="请输入内容" required>
        <WangEditor :disabled="disabled" :max="5000" v-model:value="ruleForm.noticeContent" height="200px" />
      </el-form-item>
      <div v-if="!disabled">
        <el-button type="primary" @click="submitForm(ruleFormRef, false)"> 保存 </el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef, true)"> 提交发布</el-button>
        <el-button @click="goBack()"> 取消 </el-button>
      </div>
      <div v-else>
        <el-button @click="goBack()"> 返回 </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="messageEdit">
import { reactive, ref, computed } from "vue";
import { FormInstance, FormRules, messageConfig } from "element-plus";
import WangEditor from "@/components/WangEditor/index.vue";
import { useRoute, useRouter } from "vue-router";
import { messageStorage } from "@/utils/storage/edit";
import { add, save } from "@/api/modules/business/message";
import { closeTabItem } from "@/utils/closeTab";
import { useDicStore } from "@/stores/modules/dictionaty";
const route = useRoute();
const id: string = (route.query.id as string) || "";
const type: string = (route.query.type as string) || "";
let disabled = false;

const getInitForm = () => {
  const item = messageStorage.get(id);
  if (item) {
    if (type !== "edit") {
      // isEdit = true;
      disabled = true;
      // delete item.isCheck;
    }
    return {
      ...item
    };
  } else {
    return {
      title: "",
      bizTypeId: "",
      publishType: 0,
      publishTime: "",
      noticeContent: ""
    };
  }
};
const dictionaryStore = useDicStore();
const noticeTypeDic = computed(() => dictionaryStore.noticeTypeDic);
const router = useRouter();

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive(getInitForm());

const disabledDate = (time: Date) => time.getTime() < Date.now() - 60 * 60 * 24 * 1000;

const validatePublishTime = (rule: any, value: any, callback: any) => {
  if (ruleForm.publishType == 1 && value === "") {
    callback(new Error("请输入定时发布时间"));
  }
  callback();
};

const rules = reactive<FormRules>({
  title: [
    { required: true, message: "请输入消息标题", trigger: "blur" },
    { max: 50, message: "最多输入50个字符", trigger: "blur" }
  ],
  bizTypeId: [{ required: true, message: "请选择消息类型", trigger: "blur" }],
  publishType: [{ required: true, message: "请选择发布形式", trigger: "blur" }],
  publishTime: [{ validator: validatePublishTime, trigger: "blur" }],
  noticeContent: [
    { required: true, message: "请输入消息内容", trigger: "blur" },
    { max: 5000, message: "最多输入5000个字符", trigger: "blur" }
  ]
});

const submitForm = async (formEl: FormInstance | undefined, isPublish: boolean) => {
  if (!formEl) return;
  if (isPublish) {
    await formEl.validate(async (valid, fields) => {
      // if (ruleForm.noticeContent?.includes("<a href")) {
      //   ruleForm.noticeContent = ruleForm.noticeContent.replace(
      //     /<a\s+([^>]*)(?! target=['"]_blank['"])[^>]*>/gi,
      //     function (match, p1) {
      //       return "<a " + 'target="_blank" ' + (p1 ? p1.trim() + " " : "") + ">";
      //     }
      //   );
      // }
      if (valid) {
        await add(ruleForm);
        goBack();
      }
    });
  } else {
    await formEl.validate(async valid => {
      if (valid) {
        // if (ruleForm.noticeContent?.includes("<a href")) {
        //   ruleForm.noticeContent = ruleForm.noticeContent.replace(
        //     /<a\s+([^>]*)(?! target=['"]_blank['"])[^>]*>/gi,
        //     function (match, p1) {
        //       return "<a " + (p1 ? p1.trim() + " " : "") + 'target="_blank">';
        //     }
        //   );
        // }
        await save(ruleForm);
        goBack();
      }
    });
  }
};
const goBack = () => {
  messageStorage.clear();
  closeTabItem(route);
  router.push("/operation/messageManage");
};
</script>

<style scoped lang="scss">
.content-box {
  align-items: start;
}
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
</style>
