<template>
  <div class="card content-box">
    <div class="edit-title">{{ title }}</div>
    <policy-news-content :is-edit="isEdit" ref="policyNewsContentRef" />
    <div v-if="isEdit">
      <el-button type="primary" @click="submitForm(false)"> 保存 </el-button>
      <el-button type="primary" @click="submitForm(true)"> 提交 </el-button>
      <el-button @click="goBack()"> 取消 </el-button>
    </div>
    <div v-else>
      <el-button @click="goBack()"> 返回 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="policyNewsEdit">
import { computed, onMounted, ref } from "vue";
import type { FormInstance } from "element-plus";
import { updatePolicy, addPolicy } from "@/api/modules/business/policyNews";
import { useRoute, useRouter } from "vue-router";
import policyNewsContent from "@/views/verification/operationsReview/components/policyNewsContent.vue";
import { policyNewsStorage } from "@/utils/storage/edit";

const route = useRoute();
const router = useRouter();

const policyNewsContentRef = ref();
const id: string = (route.query.id as string) || "";
const pageType: string = (route.query.type as string) || "";

const title = computed(() => (pageType === "edit" ? "政策消息编辑" : pageType === "add" ? "政策消息新增" : "政策消息查看"));
let isEdit = computed(() => {
  const policy = policyNewsStorage.get(id);

  if (policy) {
    return policy?.isCheck;
  } else {
    return true;
  }
});

const submitForm = async (isSave = false) => {
  const formEl: FormInstance | undefined = policyNewsContentRef.value.policyFormRef;

  if (!formEl) return;
  await formEl.validate(async valid => {
    if (valid) {
      const data = {
        ...policyNewsContentRef.value.policyForm,
        newsType: "2", // 新闻分类 1-最新动态 2-政策消息,字典[news_type]
        publishType: "1"
      };
      if (isEdit.value && id) {
        await updatePolicy(data, isSave);
      } else {
        await addPolicy(data, isSave);
      }
      goBack();
    }
  });
};

const goBack = () => {
  router.push("/operation/policyNews");
};
</script>

<style scoped lang="scss">
.content-box {
  align-items: start;
}
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
</style>
