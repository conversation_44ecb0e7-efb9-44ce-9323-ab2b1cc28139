.dataScreen-container {
  width: 100%;
  height: 100%;
  background-color: #232943;
  .dataScreen {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 999;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: url("@/assets/images/screen/bg.png");
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-position: center;
    background-size: 100% 100%;
    background-size: cover;

    // transition: transform 0.3s;
    transform-origin: left top;
    .dataScreen-header {
      width: 100%;
      font-family: PingFangSC-Regular, "PingFang SC";
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      .header-ct {
        width: 508px;
        height: 86px;
        margin-top: 17px;
        background: url("@/assets/images/screen/title.png") no-repeat center;
        background-size: 100% 100%;
      }
      .header-lf {
        position: absolute;
        top: 19px;
        left: 32px;
        width: 105px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
        background: url("@/assets/images/screen/left-btn.png") no-repeat center;
        background-size: 100% 100%;
        img {
          width: 19px;
          height: 19px;
          margin-bottom: 3px;
          vertical-align: middle;
        }
        span {
          margin-left: 7px;
        }
      }
      .header-rt {
        position: absolute;
        top: 19px;
        right: 32px;
        width: 105px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
        background: url("@/assets/images/screen/right-btn.png") no-repeat center;
        background-size: 100% 100%;
        img {
          width: 17px;
          height: 17px;
          margin-bottom: 3px;
          vertical-align: middle;
        }
        span {
          margin-left: 9px;
        }
      }
    }
    .dataScreen-main {
      box-sizing: border-box;
      display: flex;
      flex: 1;
      width: 100%;
      padding: 0 32px 32px;
      .dataScreen-lf,
      .dataScreen-rt {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 412px;
        height: 100%;
        .chart-box {
          width: 100%;
          height: 243px;
        }
        .big-box {
          height: 289px;
        }
        .reduce-box {
          height: 220px;
        }
      }
      .dataScreen-md {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;

        // justify-content: space-between;
        height: 100%;
        .dataScreen-map {
          position: relative;
          flex: 1;
          width: 100%;
          margin-top: 40px;
          text-align: center;
          .map-box {
            width: 980px;
            height: 680px;
            margin: 0 auto;
          }
          .map-di {
            width: 920px;
          }
          .map-dao {
            position: absolute;
            right: 100px;
            bottom: 150px;
            width: 82px;
          }
        }
      }
    }
  }
}
