<template>
  <div class="table-box">
    <div class="platform-search-box">
      <label class="search-label">合作平台 :</label>
      <el-select
        v-model="platform"
        :disabled="platformDisabled"
        placeholder="请选择合作平台"
        clearable
        @change="onPlatformChange"
      >
        <el-option v-for="item in platformOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <ProTable
      ref="proTable"
      title="合作平台账号管理列表"
      request-auto
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
      v-if="platform"
    >
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="toView(null, operateTypeConfig.ADD)"> 新增 </el-button>
        <el-button v-auth="'import'" plain @click="uploadFile">
          <i class="iconfont2 icon-daoru"></i>
          导入
        </el-button>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <template #platform="{ row }">
        <p class="margin-0">{{ getPlatformName(row.platform) }}</p>
      </template>

      <template #subOrderNo="{ row }">
        <p class="order-no margin-0" @click="toOrderDetail(row)">{{ row.subOrderNo }}</p>
      </template>

      <template #accountStatus="{ row }">
        <p class="margin-0">{{ row.accountStatus === 1 ? "有效" : "无效" }}</p>
      </template>

      <template #isMainAccount="{ row }">
        <p class="margin-0">{{ row.isMainAccount === 1 ? "是" : "否" }}</p>
      </template>

      <template #isBind="{ row }">
        <p class="margin-0">{{ row.userAccount ? "是" : "否" }}</p>
      </template>

      <template #bindTime="{ row }">
        <p class="margin-0">{{ row.userAccount ? row.bindTime : row.unbindTime }}</p>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <template v-if="scope.row.accountStatus !== 1">
          <span v-auth="'view'">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, operateTypeConfig.VIEW)"></i>
            </el-tooltip>
          </span>
        </template>
        <template v-else>
          <span v-auth="'view'">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, operateTypeConfig.VIEW)"></i>
            </el-tooltip>
          </span>
          <template v-if="scope.row.userAccount">
            <span v-auth="'unbind'">
              <el-tooltip content="解绑" placement="top">
                <i class="iconfont2 opera-icon icon-chexiao" @click="showBindDialog(scope.row, operateTypeConfig.UNBIND)"></i>
              </el-tooltip>
            </span>
          </template>
          <template v-else>
            <span v-auth:partnerAccountManage="'bind'">
              <el-tooltip content="绑定" placement="top">
                <i class="iconfont2 opera-icon icon-zhuanhuan" @click="showBindDialog(scope.row, operateTypeConfig.BIND)"></i>
              </el-tooltip>
            </span>
          </template>
          <span v-auth="'edit'">
            <el-tooltip content="编辑" placement="top">
              <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toView(scope.row, operateTypeConfig.EDIT)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'delete'">
            <el-tooltip content="删除" placement="top">
              <i class="iconfont2 opera-icon icon-lajitong" @click="deleteClick(scope.row)"></i>
            </el-tooltip>
          </span>
        </template>
      </template>
    </ProTable>
    <el-empty description="请选择合作平台" v-else class="text" />
    <BindDialog ref="bindDialogRef" @update="onBindUpdate"></BindDialog>
    <ImportExcel ref="importExcelRef"></ImportExcel>
  </div>
</template>

<script setup lang="ts" name="partnerAccountManage">
import ProTable from "@/components/ProTable/index.vue";
import { computed, onActivated, onMounted, reactive, ref } from "vue";
import { ProTableInstance } from "@/components/ProTable/interface";
import BindDialog from "./components/bindDialog.vue";
import ImportExcel from "@/components/ImportExcel/index.vue";
import { operateTypeConfig } from "./config";
import { useRouter } from "vue-router";
import { Plus } from "@element-plus/icons-vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import {
  getPlatformAccountList,
  platformAccountDelete,
  platformAccountExport,
  platformAccountImport,
  platformAccountImportTemplate
} from "@/api/modules/platform";
import { useHandleData } from "@/hooks/useHandleData";
import { ElMessageBox } from "element-plus";
import { useDownload } from "@/hooks/useDownload";
import { orderChannelConfig } from "@/config/order";

const props = defineProps({
  isPage: {
    type: Boolean,
    default: true
  },
  platformDisabled: {
    type: Boolean,
    default: false
  },
  searchParams: {
    type: Object,
    default: () => {
      return {
        platform: null,
        isBind: null,
        userAccount: null,
        subOrderNo: null
      };
    }
  }
});

const router = useRouter();

const dictionaryStore = useDicStore();

const bindDialogRef = ref<InstanceType<BindDialog>>();

const importExcelRef = ref<InstanceType<ImportExcel>>();

const proTable = ref<ProTableInstance>();

const initParam = reactive<any>({});

const platform = ref<string>(props.searchParams?.platform ?? "");

const platformDisabled = ref<boolean>(props.platformDisabled ?? false);

const platformOptions: any = computed(() => {
  return dictionaryStore.cooperatePlatformDic;
});

const columns = [
  {
    prop: "platform",
    label: "合作平台",
    width: 180
  },
  {
    prop: "userAccount",
    label: "关联客户账号",
    search: { el: "input", props: { maxLength: 50 } },
    width: 180
  },
  {
    prop: "platformAccount",
    label: "合作平台账号",
    search: { el: "input", props: { maxLength: 20 } },
    width: 180
  },
  {
    prop: "isMainAccount",
    label: "是否为主账号",
    enum: [
      {
        value: 1,
        label: "是"
      },
      {
        value: 0,
        label: "否"
      }
    ],
    search: { el: "select", key: "isMainAccount" },
    width: 120
  },
  {
    prop: "mainAccount",
    label: "主账号",
    width: 180
  },
  {
    prop: "accountStatus",
    label: "平台账号状态",
    width: 120
  },
  {
    prop: "isBind",
    label: "是否已绑定",
    enum: [
      {
        value: 1,
        label: "是"
      },
      {
        value: 0,
        label: "否"
      }
    ],
    search: { el: "select", key: "isBind", defaultValue: props.searchParams?.isBind ?? null },
    width: 110
  },
  {
    prop: "subOrderNo",
    label: "关联订单号",
    search: { el: "input", props: { maxLength: 20 } },
    width: 180
  },
  {
    prop: "bindTime",
    label: "绑定/解绑时间",
    width: 180
  },
  { prop: "operation", label: "操作", fixed: "right", width: 180 }
];

const uploadFile = () => {
  const params = {
    title: "合作平台账号",
    showDataCover: false,
    autoUpload: false,
    tempApi: platformAccountImportTemplate,
    tempApiParams: {
      paramKey: "platform_import_template"
    },
    tempType: "paramConfig",
    templateName: "合作平台账号导入模板",
    importApi: platformAccountImport,
    getTableList: proTable.value?.getTableList
  };
  importExcelRef.value!.acceptParams(params);
};

const downloadFile = async () => {
  ElMessageBox.confirm("确认导出合作平台账号数据?", "温馨提示", { type: "warning" })
    .then(res => {
      const params = proTable.value?.searchParam || {};
      const queryData = {
        current: 1,
        size: 10000,
        platform: platform.value,
        ...params
      };
      useDownload(platformAccountExport, queryData);
    })
    .catch(e => {});
};

const getTableList = (params: any = {}) => {
  return getPlatformAccountList({
    ...params,
    platform: platform.value
  });
};

const toView = (row: any, type: string) => {
  const params: { type: string; item?: string; platform: string } = {
    type,
    platform: platform.value as string
  };

  if (row) {
    params.item = window.btoa(encodeURIComponent(JSON.stringify(row)));
  }

  router.push({
    path: "/partner/partnerAccountManage/partnerAccountEdit",
    query: params
  });
};

const deleteClick = async (row: any) => {
  try {
    await useHandleData(platformAccountDelete, { ids: [row.id] }, `删除该合作平台账号`, "warning", { showRequestMsg: true });
    proTable.value?.getTableList();
  } catch (e) {}
};

const showBindDialog = (row: any, type: string) => {
  bindDialogRef.value!.show({
    type,
    id: row.id,
    userAccount: props.isPage ? row.userAccount : props.searchParams?.userAccount,
    subOrderNo: props.isPage ? row.subOrderNo : props.searchParams?.subOrderNo,
    disabled: !props.isPage
  });
};

const toOrderDetail = (row: any) => {
  const { orderChannel } = row;
  if (orderChannel === orderChannelConfig.PORTAL || orderChannel === orderChannelConfig.MANAGE) {
    router.push({
      path: "/order/portalOrder/detail",
      query: {
        orderId: row.subOrderNo,
        auditTag: 0
      }
    });
  } else if (
    orderChannel === orderChannelConfig.WECHAT ||
    orderChannel === orderChannelConfig.H5 ||
    orderChannel === orderChannelConfig.CB
  ) {
    router.push({
      path: "/order/ecologyOrder/detail",
      query: {
        orderId: row.subOrderNo,
        auditTag: 0
      }
    });
  } else if (orderChannel === orderChannelConfig.ZQ) {
    router.push({
      path: "/order/middleground/detail",
      query: {
        orderId: row.subOrderNo,
        auditTag: 0
      }
    });
  }
};

const onBindUpdate = () => {
  proTable.value?.getTableList();
};

const getPlatformName = val => {
  if (platformOptions.value?.length) {
    const platformItem = platformOptions.value.find(item => item.value === val);
    if (platformItem) {
      return platformItem.label;
    }
  }
  return null;
};

const onPlatformChange = e => {
  if (e) {
    proTable.value?.reset();
  }
};

onMounted(() => {
  dictionaryStore.getCooperatePlatformList();
});

onActivated(() => {
  proTable.value?.getTableList();
});
</script>

<style lang="scss" scoped>
.text {
  height: 100%;
}
.table-box {
  .platform-search-box {
    display: flex;
    align-items: center;
    padding: 18px;
    margin-bottom: 10px;
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    .search-label {
      margin-right: 12px;
      font-size: 14px;
      color: #3a3a3d;
    }
  }
  .order-no {
    color: var(--el-color-primary);
    cursor: pointer;
  }
  .margin-0 {
    margin: 0;
  }
}
</style>
