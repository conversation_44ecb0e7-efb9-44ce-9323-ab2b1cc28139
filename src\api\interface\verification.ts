export namespace Verification {
  export interface QueryListParams {
    applyType?: number; // 申请类型1首次认证2认证表更[dict_certificate_apply_type]
    ascs?: string; // 根据该字段排正序
    auditMsg?: string; // 审核信息
    auditStatus?: string; // 审核状态[dict_audit_status 1审核中 2审核不通过 3审核通过]
    auditTime?: string; // 审核时间
    auditUserId?: string; // 审核人id
    auditUserName?: string; // 审核人名称
    careerType?: number; // 职业类型[dict_career_id]
    certificateBackUrl?: string; // 身份证反面照
    certificateFrontUrl?: string; // 身份证正面照
    city?: string; // 市
    cityCode?: string; // 市编码
    country?: string; // 国家
    countryCode?: string; // 国家
    current?: number; // 当前页
    descs?: string; // 根据该字段排倒序
    id?: string; // id
    idcardNumber?: string; // 身份证号码
    idcardType?: string; // 证件类型[]
    industryType?: number; // 所属行业类型[dict_industry_id]
    phone?: string; // 手机号码
    province?: string; // 省
    provinceCode?: string; // 省编码
    realName?: string; // 真实姓名
    size?: number; // 每页的数量
    smsCode?: string; // 短信验证码-门户提交校验
    userId?: string; // 用户id
  }
  export interface AuditParams {
    id: string;
    msg: string;
    pass: string;
  }
}
