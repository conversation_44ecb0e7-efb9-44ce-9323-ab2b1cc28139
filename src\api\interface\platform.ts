export namespace Platform {
  export interface PlatformListParams {
    current?: number; // 当前页
    size?: number; // 每页的数量
    platform: string;
    userAccount?: string;
    platformAccount?: string;
    isMainAccount?: number;
    isBind?: number;
    subOrderNo?: string;
  }

  export interface PlatformBindParams {
    id: string;
    isBind: number;
    userAccount: string;
    subOrderNo?: string;
  }

  export interface PlatformAccountAddOrEditParams {
    id?: string;
    platform: string;
    platformAccount: string;
    platformPwd?: string;
    isMainAccount: number;
    mainAccount?: string;
    userAccount?: string;
    subOrderNo?: string;
  }

  export interface PlatformAccountDeleteParams {
    ids: string[];
  }

  export interface PlatformAccountExportParams {
    current: number; // 当前页
    size: number; // 每页的数量
    platform: string;
    userAccount?: string;
    platformAccount?: string;
    isMainAccount?: number;
    isBind?: number;
    subOrderNo?: string;
  }

  export interface PlatformAccountImportTemplateParams {
    paramKey: string;
  }
}
