import { computed, defineComponent, nextTick, onMounted, PropType, ref, watch } from "vue";
import type { IAuthButtonItem } from "./typings";
import { useAuthButtons } from "@/hooks/useAuthButtons";

enum ButtonType {
  /* 编辑 */
  Edit = "icon-a-lineicon_edit",
  /* 查看 */
  View = "icon-chakan",
  /* 删除 */
  Delete = "icon-lajitong",
  /* 停用 */
  Stop = "icon-tingyong",
  /* 启用 */
  Start = "icon-shenhe",
  /* 关联订单 */
  Related = "icon-guanliandingdan",
  /* 预览 */
  Preview = "icon-lineicon_eye_open",
  /* 撤回 */
  Revoke = "icon-chexiao"
}

export default defineComponent({
  name: "AuthButton",
  props: {
    authList: {
      type: Array as PropType<Array<IAuthButtonItem>>,
      default: () => {
        return [];
      }
    },
    rowData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },

  setup(props, { emit }) {
    const { BUTTONS } = useAuthButtons();

    /* 业务 */
    const isRender = (item: IAuthButtonItem) => {
      if (Reflect.has(item, "show")) {
        if (item.show?.(props.rowData)) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    };

    /* 权限 */
    const authRender = (item: IAuthButtonItem) => {
      if (!item.authName) return true;
      if (item?.authName && Reflect.has(BUTTONS.value, item.authName)) {
        return true;
      } else {
        return false;
      }
    };

    /* 优化后的 render */
    const memoList = computed(() => {
      return props.authList.filter((el: IAuthButtonItem) => {
        if (isRender(el) && authRender(el)) {
          return { ...el };
        }
      });
    });

    return {
      memoList
    };
  },
  render() {
    return (
      <>
        {this.memoList.map(item => {
          return (
            <span>
              <el-tooltip
                content={item.contentName ? item.contentName : item.toolTipProps?.content}
                {...item?.toolTipProps}
                placement={item?.toolTipProps?.placement ?? "top"}
              >
                <i
                  class={`iconfont2 opera-icon  ${ButtonType[item?.iconClass as keyof typeof ButtonType]}`}
                  onClick={() => item?.itemClick(this.rowData)}
                ></i>
              </el-tooltip>
            </span>
          );
        })}
      </>
    );
  }
});
