<template>
  <div class="activity-page">
    <div class="mb20 header-title-common">基础信息</div>
    <el-form ref="formRef" :model="formData" label-width="auto" :disabled="pageType === 'view'">
      <el-form-item
        label="活动名称"
        style="max-width: 600px; margin-bottom: 32px"
        size="large"
        prop="name"
        :rules="[
          {
            required: true,
            message: '请输入活动名称',
            trigger: 'blur'
          },
          {
            max: 20,
            message: '最大输入20个字符',
            trigger: 'blur'
          }
        ]"
      >
        <el-input :disabled="isEdit" v-model="formData.name" placeholder="活动名称" type="text" autocomplete="off" />
      </el-form-item>
      <el-form-item
        label="活动时间"
        style="max-width: 800px; margin-bottom: 32px"
        size="large"
        prop="startTime"
        :rules="[
          {
            required: true,
            message: '请选择活动时间',
            trigger: 'blur'
          }
        ]"
      >
        <el-date-picker
          :disabled="isEdit"
          :disabled-date="disabledDate"
          :disabled-hours="disabledHours"
          :disabled-minutes="disabledMinutes"
          :disabled-seconds="disabledSeconds"
          v-model="formData.startTime"
          format="YYYY-MM-DD HH:mm:ss"
          date-format="YYYY/MM/DD ddd"
          time-format="A hh:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetime"
          placeholder="开始时间"
          :default-value="defaultTime"
        />
        <span style="margin: 0 10px; color: #dcdfe6">—</span>
        <el-date-picker
          :disabled="isEdit"
          :disabled-date="disabledDate"
          v-model="formData.endTime"
          format="YYYY-MM-DD HH:mm:ss"
          date-format="YYYY/MM/DD ddd"
          time-format="A hh:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetime"
          placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item
        style="max-width: 600px; margin-bottom: 32px"
        label="参与对象"
        size="large"
        prop="joinUserType"
        :rules="[
          {
            required: true,
            message: '请选择参与对象',
            trigger: 'change'
          }
        ]"
      >
        <el-radio-group :disabled="isEdit" v-model="formData.joinUserType">
          <el-radio v-for="item in user_business_classify" :key="item.itemKey" :label="item.itemKey">{{
            item.itemValue
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item style="max-width: 1000px; margin-bottom: 32px" label="活动规则说明" size="large" prop="rules">
        <!-- <el-input
          v-model="formData.rules"
          type="textarea"
          :disabled="isEdit"
          show-word-limit
          maxlength="1000"
          :autosize="{ minRows: 6, maxRows: 6 }"
        /> -->
        <wang-editor :disabled="isEdit" v-model:value="formData.rules" height="200px" />
      </el-form-item>
      <el-form-item
        style="max-width: 600px; margin-bottom: 32px"
        label="活动头图"
        size="large"
        prop="headImage"
        :rules="[
          {
            required: true,
            message: '请上传活动头图',
            trigger: 'blur'
          }
        ]"
      >
        <div class="icon-wrapper">
          <div class="upload-pic">
            <UploadImg
              :disabled="isEdit"
              v-model:image-url="formData.headImage"
              width="145px"
              height="145px"
              :file-type="fileType"
              :file-size="2"
            >
            </UploadImg>
            <span class="common-icon" v-show="!isEdit && pageType !== 'view'" @click="handleDefault('headImage')">
              使用默认活动头图
            </span>
          </div>
          <div class="upload-tips">建议图片尺寸750*750px，图片大小不超过2M，支持图片格式jpg/jpeg/png</div>
        </div>
      </el-form-item>
      <el-form-item
        style="max-width: 600px; margin-bottom: 32px"
        label="活动背景图"
        size="large"
        prop="backgroundImage"
        :rules="[
          {
            required: true,
            message: '请上传活动背景图',
            trigger: 'blur'
          }
        ]"
      >
        <div class="icon-wrapper">
          <div class="upload-pic">
            <UploadImg
              :disabled="isEdit"
              v-model:image-url="formData.backgroundImage"
              width="145px"
              height="145px"
              :file-type="fileType"
              :file-size="2"
            >
            </UploadImg>
            <span class="common-icon" v-show="!isEdit && pageType !== 'view'" @click="handleDefault('backgroundImage')">
              使用默认活动背景图
            </span>
          </div>
          <div class="upload-tips">建议图片尺寸750*1000px，图片大小不超过2M，支持图片格式jpg/jpeg/png</div>
        </div>
      </el-form-item>
      <div class="mb20 header-title-common">奖品设置</div>
      <div class="flex-set mt20">
        <div class="page-title mr30"><span>*</span> 选择奖品</div>
        <el-button type="primary" @click="handleChoose" v-show="!isEdit">选择</el-button>
      </div>
      <div class="mt20 ml95">
        <ProdTable
          :is-edit="isEdit"
          :c-list="formData.marketingProductList"
          :page-type="pageType"
          @on-delete="handleDelete"
        ></ProdTable>
      </div>
      <div class="mb20 mt40 header-title-common">分享信息</div>
      <el-form-item
        style="max-width: 600px; margin-bottom: 32px"
        label="分享标题"
        prop="shareTitle"
        :rules="[
          {
            required: true,
            message: '请输入分享信息',
            trigger: 'blur'
          },
          {
            max: 20,
            message: '最大输入20个字符',
            trigger: 'blur'
          }
        ]"
        size="large"
      >
        <el-input :disabled="isEdit" v-model="formData.shareTitle" placeholder="分享标题" type="text" autocomplete="off" />
      </el-form-item>
      <el-form-item
        prop="shareContent"
        :rules="[
          {
            required: true,
            message: '请输入分享内容',
            trigger: 'blur'
          },
          {
            max: 50,
            message: '最大输入50个字符',
            trigger: 'blur'
          }
        ]"
        label="分享内容"
        style="max-width: 600px; margin-bottom: 32px"
        size="large"
      >
        <el-input :disabled="isEdit" v-model="formData.shareContent" placeholder="分享内容" type="text" autocomplete="off" />
      </el-form-item>
      <el-form-item
        style="max-width: 600px; margin-bottom: 32px"
        label="分享图标"
        size="large"
        prop="shareIcon"
        :rules="[
          {
            required: true,
            message: '请输入分享图标',
            trigger: 'blur'
          }
        ]"
      >
        <div class="icon-wrapper">
          <div class="upload-pic">
            <UploadImg
              :disabled="isEdit"
              v-model:image-url="formData.shareIcon"
              width="145px"
              height="145px"
              :file-type="fileType"
              :file-size="1"
            >
            </UploadImg>
            <span class="common-icon" v-show="!isEdit && pageType !== 'view'" @click="handleDefault('shareIcon')">
              使用默认图标
            </span>
          </div>
          <div class="upload-tips">建议尺寸：200*200像素，1：1比例，支持jpg/jpeg/png格式，1M以内</div>
        </div>
      </el-form-item>
      <el-form-item v-show="pageType !== 'view'">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="onSubmit">提交</el-button>
      </el-form-item>
    </el-form>

    <DialogTable
      :page-type="pageType"
      :status="formData.status"
      @on-select="handleSelect"
      v-model="dialogVisible"
      :c-list="formData.marketingProductList"
    ></DialogTable>
  </div>
</template>

<script setup lang="ts">
import { computed, isRef, reactive, readonly, ref, unref, watch } from "vue";
import ProdTable from "./comps/selectTable.vue";
import DialogTable from "./comps/dialogTable.vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { paramConfigSearch } from "@/api/modules/system";
import { useDict } from "@/hooks/useDict";
import { queryAddMarketing, queryMarketingUpdate } from "@/api/modules/activityH5";
import {
  compareTime,
  disabledHours,
  disabledMinutes,
  disabledSeconds,
  replaceProducts,
  validateTimestamps
} from "./disabledTime";
import useActive from "./useActive";
import UploadImg from "@/components/Upload/Img.vue";
import { onBeforeRouteLeave, useRoute, useRouter } from "vue-router";
import { useTabsStore } from "@/stores/modules/tabs";
import dayjs from "dayjs";
import WangEditor from "@/components/WangEditor/index.vue";

import { useKeepAliveStore } from "@/stores/modules/keepAlive";

const defaultTime = dayjs(new Date()).toDate();

const router = useRouter();

const route = useRoute();

const fileType = ["image/jpeg", "image/png", "image/jpg"];

const { user_business_classify } = useDict("user_business_classify");

const dialogVisible = ref(false);

const { pageType, formData, activeTime } = useActive();

/**
 * 2 进行中
 * 3 未开始
 */
const isEdit = computed(() => {
  const isStatus = Reflect.has(formData.value, "status");

  const status = isStatus ? Reflect.get(formData.value, "status") : null;

  if (pageType.value === "edit" && status === 3) {
    return false;
  }
  if (pageType.value === "edit" && status === 2) {
    return true;
  }

  return false;
});

const disabledDate = data => {
  // return targetTime.isBefore(currentTime);

  return data.getTime() < Date.now() - 8.64e7;
};

const handleDelete = (index: number) => {
  formData.value.marketingProductList.splice(index, 1);
  // formData.value.marketingProductList = cList.value;
};

watch(activeTime, nVal => {
  if (Array.isArray(nVal)) {
    formData.value.startTime = nVal[0];
    formData.value.endTime = nVal[1];
  } else {
    formData.value.startTime = "";
    formData.value.endTime = "";
  }
});

const formRef = ref<FormInstance>();

const handleSelect = value => {
  const prodList = formData.value.marketingProductList.map((el: any) => {
    return { ...el };
  });
  const filterList = replaceProducts(value, prodList);
  const selectList = filterList.map((item: any) => {
    return {
      productName: item.productName,
      productId: item.productId,
      skuType: item.skuType,
      description: item.description ? item.description : "",

      totalLimit: item.totalLimit ? item.totalLimit : 1,
      userLimit: item.userLimit ? item.userLimit : 1,

      skuId: item.skuId,
      skuPrice: item.skuPrice,
      skuName: item.skuName,
      marketingId: item.marketingId ? item.marketingId : null,
      marketingProductRelId: item.marketingProductRelId ? item.marketingProductRelId : null
    };
  });
  formData.value.marketingProductList = selectList;
};

const handleChoose = () => {
  dialogVisible.value = true;
};

const onSubmit = () => {
  if (!validateTimestamps(formData.value.startTime, formData.value.endTime)) {
    ElMessage.error("时间格式有误，结束时间不能早于开始时间");
    return;
  }
  if (formData.value.marketingProductList.length === 0) {
    ElMessage.error("选择奖品不能为空");
    return;
  }
  console.log(!compareTime(new Date(), new Date(formData.value.startTime)));

  if ((formData.value?.status && formData.value.status !== 2) || pageType.value === "create") {
    if (!compareTime(new Date(), new Date(formData.value.startTime))) {
      ElMessage.error("开始时间必须大于当前时间");
      return;
    }
  }

  formRef.value?.validate().then(res => {
    if (pageType.value === "edit") {
      pageType.value = "view";
      queryMarketingUpdate({ ...formData.value })
        .then(result => {
          ElMessage.success("更新成功");
          closeTabItem();
          router.push("/operation/activityManage");
        })
        .catch(err => {})
        .finally(() => {
          pageType.value = "edit";
        });
    } else {
      pageType.value = "view";
      queryAddMarketing({ ...formData.value })
        .then(result => {
          ElMessage.success("新增成功");
          closeTabItem();
          router.push("/operation/activityManage");
        })
        .catch(err => {})
        .finally(() => {
          pageType.value = "create";
        });
    }
  });
};

const handleCancel = () => {
  ElMessageBox.confirm("退出当前页面内容不保存，确认退出？", "操作提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      router.back();
    })
    .catch(() => {});
};

const handleDefault = (imageName: string) => {
  if (imageName === "shareIcon") {
    paramConfigSearch("MARKETING_SHARE_ICON").then(res => {
      formData.value.shareIcon = res.data.paramValue;
    });
  } else if (imageName === "headImage") {
    paramConfigSearch("MARKETING_HEAD_IMAGE").then(res => {
      formData.value.headImage = res.data.paramValue;
    });
  } else if (imageName === "backgroundImage") {
    paramConfigSearch("MARKETING_BACKGROUND_IMAGE").then(res => {
      formData.value.backgroundImage = res.data.paramValue;
    });
  }
};

function closeTabItem() {
  const tabStore = useTabsStore();
  const keepAliveStore = useKeepAliveStore();
  if (route.meta.isAffix) return;
  tabStore.removeTabs(route.fullPath);
  keepAliveStore.removeKeepAliveName(route.name as string);
}
</script>

<style scoped lang="scss">
.activity-page {
  padding: 24px;
  background-color: #ffffff;
}
.flex-set {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.page-title {
  span {
    color: red;
  }

  font-size: 14px;
  color: #3a3a3d;
}
.upload-icon {
  display: table-cell;
  width: 140px;
  height: 140px;
  text-align: center;
  vertical-align: middle;

  // border: 1px dashed #c4c6cc;
  i {
    display: block;
    margin-right: 0;
    font-size: 46px;
    color: #9ca3ac;
  }
  span {
    width: 100%;
    font-size: 14px;
    color: #0052d9;
  }
}
:deep(.el-upload) {
  display: inline-flex;
}
.common-icon {
  display: inline-block;
  margin-left: 18px;
  font-size: 14px;
  color: #0052d9;
  cursor: pointer;
  transform: translate(0, -24px);
}
.icon-wrapper {
  .upload-pic {
    display: flex;
    align-items: flex-end;
  }
}
</style>
