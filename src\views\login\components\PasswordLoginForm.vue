<template>
  <div class="form-box">
    <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
      <el-form-item prop="account">
        <el-input v-model="loginForm.account" placeholder="请输入账号/手机号/邮箱">
          <template #prefix>
            <el-icon class="el-input__icon">
              <svg-icon name="lineicon_user"></svg-icon>
            </el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" show-password autocomplete="new-password">
          <template #prefix>
            <el-icon class="el-input__icon">
              <svg-icon name="lineicon_lock"></svg-icon>
            </el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="sms">
        <phoneSms
          v-model:value="loginForm.sms"
          @sms-click="smsClick"
          :phone-number="loginForm.account"
          :is-send-message="isSendMessage"
        ></phoneSms>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from "vue";
import { useRouter } from "vue-router";
// import { HOME_URL } from "@/config";
import { getTimeState } from "@/utils";
import { Login } from "@/api/interface";
import { ElMessage, ElNotification } from "element-plus";
import { loginApi } from "@/api/modules/login";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import phoneSms from "@/components/PhoneSMS/index.vue";
// import phoneSms from "@/components/PhoneSMS/index.vue";
import type { ElForm } from "element-plus";
import SvgIcon from "@/components/SvgIcon/index.vue";
import md5 from "md5";
import { sm2 } from "sm-crypto";

const router = useRouter();
const userStore = useUserStore();
const tabsStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();
const props = defineProps({ isSendMessage: { type: Boolean, default: false } });
type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive({
  account: [{ required: true, message: "请输入账号/手机号/邮箱", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }]
});
const emits = defineEmits(["update:canLogin", "showVerify"]);
const loading = ref(false);
const loginForm = reactive<Login.ReqLoginForm>({
  account: "",
  grantType: "password",
  password: "",
  refreshToken: "",
  userType: "Operate",
  sms: ""
});

const smsClick = () => {
  if (!loginForm.password) {
    ElMessage.warning("请输入登录密码");
    return;
  }
  emits("showVerify");
};
watch([loginForm, () => props.isSendMessage], () => {
  checkLoginStatus();
});

const checkLoginStatus = () => {
  emits("update:canLogin", !!(loginForm.account && loginForm.password && loginForm.sms && props.isSendMessage));
};
const publicKey: any = computed(() => userStore.publicKey);
const sysInfo: any = computed(() => userStore.sysInfo);

// login
const login = (formEl?: FormInstance | undefined) => {
  console.log("passwordLogin");
  if (!formEl) formEl = loginFormRef.value as FormInstance;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      // 1.执行登录接口
      let pwd: any = loginForm.password;
      const { data } = await loginApi({
        account: loginForm.account,
        grantType: loginForm.grantType,
        refreshToken: loginForm.refreshToken,
        userType: loginForm.userType,
        code: loginForm.sms,
        password: sm2.doEncrypt(md5(pwd), publicKey.value, 1)
      });
      // const { data } = await loginApi({ ...loginForm, password: sm2.doEncrypt(md5(pwd), publicKey.value, 1) });
      userStore.setToken(data.accessToken);
      userStore.setUserInfo(data.operateUser);

      // 2.添加动态路由
      await initDynamicRouter();

      // 3.清空 tabs、keepAlive 数据
      tabsStore.closeMultipleTab();
      keepAliveStore.setKeepAliveName();

      // 4.跳转到首页
      router.push("./");
      ElNotification({
        title: getTimeState(),
        message: `欢迎登录${sysInfo.value.siteTitle}`,
        type: "success",
        duration: 3000
      });
    } finally {
      loading.value = false;
    }
  });
};
onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value) return;
      login(loginFormRef.value);
    }
  };
});
defineExpose({ login, checkLoginStatus, loginForm });
</script>

<style scoped lang="scss">
@import "../index.scss";
</style>
