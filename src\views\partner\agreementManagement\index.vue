<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="合作商协议管理列表"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
    >
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="toView({}, editType.add)"> 新增 </el-button>
      </template>

      <template #auditStatus="{ row }">
        <CustomTag
          :type="'partner'"
          :status="row.auditStatus"
          :label="useDictLabel(auditStatusDict, row.auditStatus) || '--'"
        ></CustomTag>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row, editType.details)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top" v-if="scope.row.auditStatus == status.noPass">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toView(scope.row, editType.edit)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'revoke'">
          <el-tooltip content="撤回" placement="top" v-if="scope.row.auditStatus == status.reviewing">
            <i class="iconfont2 opera-icon icon-chexiao" @click="toView(scope.row, editType.revoke)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="agreementManagement">
import { ref, reactive, onMounted, computed, onActivated } from "vue";
import { useRouter } from "vue-router";
import { Plus } from "@element-plus/icons-vue";
import { useDictLabel } from "@/hooks/useDict";
import { useHandleData } from "@/hooks/useHandleData";
import { useDicStore } from "@/stores/modules/dictionaty";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getProviderAgreementList, revokeProviderAgreement } from "@/api/modules/partner";
import CustomTag from "@/components/CustomTag/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();
enum editType {
  "add" = 1,
  "edit" = 2,
  "revoke" = 3,
  "details" = 4
}

enum status { // 1审核中 2审核不通过 3审核通过
  "reviewing" = 1,
  "noPass" = 2,
  "pass" = 3
}

const router = useRouter();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

const dictionaryStore = useDicStore();

onMounted(() => {
  dictionaryStore.getProviderAgreementStatus();
});

onActivated(() => {
  proTable.value?.getTableList();
});
const auditStatusDict: any = computed(() => dictionaryStore.providerAgreementStatus);

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.validTime && (newParams.validStartTime = newParams.validTime[0]);
  newParams.validTime && (newParams.validEndTime = newParams.validTime[1]);
  delete newParams.validTime;
  return getProviderAgreementList(newParams);
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  { prop: "name", label: "合作商名称", search: { el: "input" } },
  {
    prop: "agreementName",
    label: "合作协议名称",
    search: { el: "input" }
  },
  {
    prop: "validTime",
    label: "有效期",
    align: "left",
    // search: {
    //   el: "date-picker",
    //   props: {
    //     type: "datetimerange",
    //     "range-separator": "-",
    //     format: "YYYY/MM/DD",
    //     valueFormat: "YYYY/MM/DD"
    //   }
    // },
    render: ({ row: { validStartTime, validEndTime } }: any) => {
      return validStartTime ? validStartTime + " ~ " + validEndTime : "--";
    }
  },
  {
    prop: "auditStatus",
    label: "状态",
    enum: auditStatusDict,
    search: { el: "select" }
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

const toView = async (row: any, type = editType.add) => {
  if (type !== editType.revoke) {
    router.push({
      path: "/partner/agreementManagement/agreementEdit",
      query: {
        type,
        id: row.id
      }
    });
  } else {
    await useHandleData(revokeProviderAgreement, { id: row.id }, { msg: `是否撤回 ${row.name} ？`, successMsg: "撤回" });
    proTable.value?.getTableList();
  }
};
</script>

<style lang="scss" scoped>
:global(.overflow-tooltip-style) {
  max-width: calc(100% - 24px);
  white-space: pre-wrap;
}
</style>
