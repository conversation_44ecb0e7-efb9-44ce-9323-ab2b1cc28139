<template>
  <div class="statistical-data">
    <div class="order-main">
      <indicator-button
        :indicator-title="'订单指标'"
        :list="orderIndex"
        :active="activeBtn"
        @item-click="onChangeOrder"
      ></indicator-button>
      <indicator-button
        :indicator-title="'渠道指标'"
        :list="customerSource"
        :active="indicators"
        @item-click="onChangeIndicators"
      ></indicator-button>
      <div class="chart-box">
        <line-chart ref="orderLineChartRef" :chart-id="channelLineId" />
      </div>
    </div>
    <div class="order-aside">
      <ranking-list :ranking-title="'渠道占比'" :more="false" :ranking-list="[]">
        <div class="chart-box">
          <pie-chart ref="channelProportionRef" />
        </div>
      </ranking-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue";
import { ECharts } from "echarts";
import { useDict } from "@/hooks/useDict";
import { getChannelChartData, getChannelPieData } from "@/api/modules/statisticAnalysis";
import { toThousandFilter } from "@/utils/tools";
import { startDate, endDate, handleCountDate } from "../../commonAnalysis";
import IndicatorButton from "@/components/IndicatorButton/index.vue";
import rankingList from "./rankingList.vue";
import lineChart from "@/views/statisticAnalysis/orderAnalysis/components/lineChart.vue";
import pieChart from "./pieChart.vue";

// 获取子组件的ref
interface ChartExpose {
  initChart: (params: any, name?: string) => ECharts;
}
enum IndicatorsType {
  all = ""
}

interface IndexType {
  label: string;
  value: string;
}
enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}

const dateType = ref<DateType>(DateType.day);
const activeBtn = ref("1");
const indicators = ref(IndicatorsType.all);
const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  },
  channelLineId: {
    type: String,
    default: () => "channelLineId"
  },
  channelPieId: {
    type: String,
    default: () => "channelPieId"
  }
});

let requestParams = reactive({
  countEndDate: endDate,
  countStartDate: startDate,
  dateType: dateType,
  orderAnalysisTypeKey: activeBtn,
  providerId: "",
  productIds: "",
  channelType: indicators,
  productIdList: [],
  productName: "",
  ...props.params
});

// 订单分析指标类型枚举：1-订购订单数、2-退订订单数、3-净增订单数、4-累计生效订单数、5-交易金额GMV、6-累计交易金额GMV
const orderIndex = [
  {
    label: "订购订单数",
    value: "1"
  },
  {
    label: "退订订单数",
    value: "2"
  },
  {
    label: "净增订单数",
    value: "3"
  },
  {
    label: "累计生效订单数",
    value: "4"
  }
];

const { channel_code } = useDict("channel_code");

const userSorceDict = computed(() => {
  const sourceList = channel_code.value;
  if (sourceList && sourceList.length > 0) {
    const newList = sourceList.map((item: any) => {
      return {
        ...item,
        label: item.itemValue,
        value: item.itemKey
      };
    });
    newList.unshift({
      label: "全部",
      value: ""
    });
    return newList;
  } else {
    return [];
  }
});
const customerSource = userSorceDict;
const nameList: IndexType[] = [...orderIndex];

const orderLineChartRef = ref<ChartExpose>();
const channelProportionRef = ref<ChartExpose>();
const pieChartData = ref<any>([]);

// 图表
const getChat = (reqParams?: any) => {
  indicators.value = reqParams.channelType;
  getChannelChartData({
    ...reqParams,
    ...handleCountDate(reqParams.countStartDate, reqParams.countEndDate, dateType.value)
  }).then((res: any) => {
    if (res.code === 200) {
      const { dateList = [], countList = [] } = res.data;

      const currentItem = nameList.filter(item => item.value === activeBtn.value);
      orderLineChartRef.value?.initChart(
        {
          data: countList,
          nameList: dateList
        },
        currentItem.length ? currentItem[0].label : ""
      );
    }
  });
};

// 渠道占比
const getPieData = (reqParams?: any) => {
  getChannelPieData({
    ...reqParams,
    ...handleCountDate(reqParams.countStartDate, reqParams.countEndDate)
  }).then((res: any) => {
    if (res.code === 200) {
      const { channelList = [], totalNumber = 0 } = res.data;
      pieChartData.value = channelList || [];
      const currentItem = orderIndex.find(item => item.value === activeBtn.value);
      channelProportionRef.value?.initChart({
        chartData: channelList || [],
        total: toThousandFilter(totalNumber),
        title: currentItem?.label
      });
    }
  });
};

const getChannelCharts = (reqParams?: any) => {
  const req = {
    ...requestParams,
    ...reqParams
  };
  requestParams = {
    ...req
  };
  getChat(req);
  getPieData(req);
};
const emit = defineEmits(["onChangeIndex", "onChangeIndicator"]);
const onChangeOrder = (values: any) => {
  activeBtn.value = values.value;
  requestParams.orderAnalysisTypeKey = values.value;
  emit("onChangeIndex", values.value);
  getChannelCharts();
};
const onChangeIndicators = (values: any) => {
  indicators.value = values.value;
  requestParams.channelType = values.value;
  emit("onChangeIndicator", values.value);
  getChat({ ...requestParams });
};

defineExpose({
  getChannelCharts
});
</script>

<style lang="scss" scoped>
.statistical-data {
  display: flex;
  width: 100%;
}
.order-main {
  flex: 1;
  .chart-box {
    width: 100%;
    height: 336px;
  }
}
.order-aside {
  width: 300px;
  .chart-box {
    height: 390px;
  }
}
</style>
