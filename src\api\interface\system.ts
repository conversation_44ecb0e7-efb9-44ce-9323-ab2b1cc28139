export namespace System {
  export interface PostCommonParams {
    ascs?: string; // 根据该字段排正序
    descs?: string; // 根据该字段排倒序
    id?: string;
    current?: number; // 当前页
    size?: number; // 每页的数量
    status?: number;
  }
  export interface RoleListParams extends PostCommonParams {
    cooperateType?: string; // 产品合作类型
    createTimeStart?: string;
    createTimeEnd?: string;
    createTime?: string;
    updateTime?: string;
    perms?: string; // 权限
    roleCode?: string; // 产品编码
    roleName?: string; // 产品名
    sort?: number;
  }
  export interface DeleteRoleParams {
    roleId: string[];
    sure?: string;
  }

  export interface SysRolePerms {
    menuId?: string; // 菜单id
    permissionCode?: string; // 权限编码 all 全部，org 本部门与子部门，personal 个人
    permissionType?: string; // 权限类型[permission_type] 字典 1菜单 2数据权限字典
    roleId?: string; // 角色ID
    [property: string]: any;
  }
  export interface SaveRoleParams {
    description?: string; // 描述
    roleCode?: string; // 角色编码
    roleId?: string; // 角色ID
    roleName?: string; // 角色名称
    rolePerms?: SysRolePerms[]; // 角色权限
    sort?: number; // 显示顺序
  }

  export interface OrgTreeListParams {
    description?: string; // 描述
    id?: string; // id
    orgCode?: string; // 组织编码
    orgName?: string; // 组织名称
    orgPath?: string; // 组织层级全id路径  如['001','002','003']
    orgType?: string; // 组织类型 0内部 1外部
    parentId?: string; // 父组合ID
  }

  export interface OrgOperateParams {
    description?: string; // 描述
    id?: string; // id
    orgCode?: string; // 组织编码
    orgName?: string; // 组织名称
    orgType?: string; // 组织类型 0内部 1外部
    parentId?: string; // 父组合ID
    sort?: number; // 显示顺序
  }
  export interface ExportUserParams {
    account?: string; // 用户账号
    ascs?: string; // 根据该字段排正序
    containSubOrg?: string; // 是否包含子部门,示例值(0 否 1 是)
    current?: string; // 当前页
    descs?: string; // 根据该字段排倒序
    email?: string; // 用户邮箱
    nickName?: string; // 用户昵称
    orgId?: string; // 组织id
    phone?: string; // 手机号码
    size?: string; // 每页的数量
    status?: string; // 业务状态（1正常 0停用）
    userName?: string; // 用户名称
    userSource?: string; // 用户来源 关联字典 [opt_user_source]
    userType?: string; // 用户类型 [opt_user](1内部用户 2合作商用户)
    auditType?: string;
    silentState?: string;
    validDate?: string;
    validStatus?: string;
    validType?: string;
  }

  export interface OrgDeleteParams {
    ids?: string[];
    account?: string;
  }
  export interface UpdateUserOrg {
    orgId?: string;
    preOrgId?: string;
    userId?: string;
  }
  export interface StatusUserParams {
    id?: string;
    status?: number;
  }
  export interface UserVerifyCode {
    email?: string; // 修改后邮箱
    password?: string; // 修改后密码
    phone?: string; // 修改后手机号码
    verifyCode?: string; // 验证码
    verifyContent?: number; // 修改类型 1 手机号码 ,2 邮件,3 password
    verifyType?: number; // 接收验证码渠道 1手机号码 ,2 邮件
    verifyToken: string;
  }

  export interface ParamConfigParams extends PostCommonParams {
    paramKey?: string; //	参数键	query	false
    paramName?: string; //	名	query	false
    paramValue?: string; //	参数值	query	false
    remark?: string; //	参数备注	query	false
  }

  export interface MenuListParams extends PostCommonParams {
    createTime?: string; // 创建时间
    creatorId?: number;
    description?: string; // 描述
    icon?: string; // 菜单图标
    isDel?: string;
    menuCode?: string; // 菜单编码
    menuName?: string; // 菜单名称
    menuType?: number; // 菜单类型（1目录 2菜单 3页面元素）
    menuUrl?: string; // 路由地址
    parentId?: string; // 父菜单ID
    permissionCode?: string; // 权限标识
    sort?: number; // 排序
    status?: number; // 业务状态（1正常 0停用）
    updaterId?: number;
    updateTime?: string; // 更新时间
    visible?: string; // 菜单状态（0显示 1隐藏）
  }

  export interface DictionaryList extends PostCommonParams {
    dictCode?: string; // 字典码
    itemKey?: string; // 字典键
    itemValue?: string; // 字典值
    parentId?: string; // 父主键
    remark?: string; // 字典备注
    sort?: number; // 排序
  }

  export interface DictParams {
    createTime?: Date; // 创建时间
    creatorId?: string; // 创建者id
    dictCode?: string; // 字典码
    id?: string;
    isDel?: boolean;
    itemKey?: string;
    itemValue?: string; // 字典值
    parentId?: string; // 父主键
    remark?: string; // 字典备注
    sort?: number; // 排序
    status?: number; // 业务状态（1正常 0停用）
    updaterId?: string; // 更新者id
    updateTime?: Date; // 更新时间
  }
  export interface PortalUserParams {
    account?: string;
    id?: string;
    province?: string;
    city?: string;
    userName?: string;
    email?: string;
    phone?: number;
    status?: number; // 用户状态（1正常 0停用）
    verifyLevel?: number; // 认证级别(1-未实名,2-已实名)
    userSource?: number; // 用户来源 （1-门户 2-小程序 3-政企OA 4 其他）
    company?: string;
    current?: number; // 当前页
    size?: number; // 每页的数量
    [key: string]: any;
  }

  export interface LogParams extends PostCommonParams {
    bizName?: string; // 	业务名
    timeEnd?: string; // 结束时间
    timeStart?: string; // 	起始时间
    userName?: string;
  }
}
