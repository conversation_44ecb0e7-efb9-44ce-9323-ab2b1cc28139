<template>
  <div class="dialog-box">
    <el-dialog
      :model-value="true"
      width="470"
      @close="closeDialog"
      title="修改排序"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-form ref="sortFormRef" :model="sortForm" :rules="rules" label-width="55px">
        <el-form-item label="排序" prop="editSort">
          <el-input-number :controls="false" v-model="sortForm.editSort" />
        </el-form-item>
        <p class="tips">数字越大，排序越靠前。数字重复，则最新上架的靠前。</p>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="confirm(sortFormRef)"> 确定 </el-button>
          <el-button @click="closeDialog">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
interface Props {
  sort: number;
  showDialog: boolean;
}
const props = defineProps<Props>();
const emit = defineEmits(["update:showDialog", "save"]);
// const editSort = ref<number>(props.sort);
const sortFormRef = ref<FormInstance>();
const sortForm = reactive({
  editSort: props.sort
});
const rules = reactive<FormRules>({
  editSort: [{ required: true, message: "请输入排序", trigger: "blur" }]
});

const closeDialog = () => {
  emit("update:showDialog", false);
};

const confirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      if (sortForm.editSort !== props.sort) {
        emit("save", sortForm.editSort);
      }
      closeDialog();
    }
  });
};
</script>
<style scoped lang="scss">
.tips {
  margin-left: 53px;
  font-size: 14px;
  line-height: 16px;
  color: #9ca3ac;
}
</style>
