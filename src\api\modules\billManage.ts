import http from "@/api";

/**
 * 运营人员
 * @param params
 */
export const getBillPageList = (params: any) => {
  return http.get("/cnud-order/admin/order-account/account/operate-page", params);
}; //运营人员账单列表查询(分页)

export const exportBillPageList = (params: Object) => {
  return http.get("/cnud-order/admin/order-account/account/operate-export", params, { responseType: "blob" });
}; //运营人员账单导出

export const getBillDetailPageList = (params: any) => {
  return http.get(`/cnud-order/admin/order-account/account/account-detail`, params);
}; //账单明细列表（分页）(运营人员、合作方共用)

export const exportBillDetailPageList = (params: Object) => {
  return http.get(`/cnud-order/admin/order-account/account/operate-account-export`, params, { responseType: "blob" });
}; //账单明细列表导出

export const rejectBill = (id: string) => {
  return http.post(`/cnud-order/admin/order-account/account/reject-revoke`, { id });
}; //账单撤回

export const getPaymentDetailPageList = (params: any) => {
  return http.get(`/cnud-order/admin/order-account/account/appropriate-detail`, params);
}; //拨付明细列表（分页）
export const exportPaymentDetailPageList = (params: Object) => {
  return http.get(`/cnud-order/admin/order-account/account/appropriate-export`, params, { responseType: "blob" });
}; //拨付明细列表导出

export const appropriateResend = (id: string) => {
  return http.post(`/cnud-order/admin/order-account/account/appropriate-resend`, { id });
}; //拨付重送

export const exportProduct = (params: Object) => {
  return http.get(`/cnud-order/admin/order-account/account/operate-export`, params, { responseType: "blob" });
}; //导出账单查询结果

/**
 * 合作商账单表
 * @param params
 */
export const getPartnerBillPageList = (params: any) => {
  return http.get("/cnud-order/admin/order-account/account/provider-page", params);
}; //合作商账单表-列表查询(分页)

export const billConfirm = (id: string) => {
  return http.post(`/cnud-order/admin/order-account/account/confirm-account`, { id });
}; //合作商人员账单确认

export const billReject = (id: string, rejectionReason: string) => {
  return http.post(`/cnud-order/admin/order-account/account/reject-account`, { id, rejectionReason });
}; //合作商人员账单驳回

export const exportPartnerBillList = (params: Object) => {
  return http.get(`/cnud-order/admin/order-account/account/provider-export`, params, { responseType: "blob" });
}; //合作商人员账单导出

export const exportPartnerBillDetailList = (params: Object) => {
  return http.get(`/cnud-order/admin/order-account/account/provider-account-export`, params, { responseType: "blob" });
}; //合作商人员账单明细导出
