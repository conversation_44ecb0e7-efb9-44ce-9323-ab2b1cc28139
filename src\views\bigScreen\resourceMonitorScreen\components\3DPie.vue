<template>
  <div :id="chartId" class="amchart" ref="amchartRef"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from "vue";
import * as am4core from "@amcharts/amcharts4/core";
import * as am4charts from "@amcharts/amcharts4/charts";
import am4themes_animated from "@amcharts/amcharts4/themes/animated";
const props = defineProps({
  dataSet: {
    required: true,
    type: Array,
    default: () => {
      return [
        { type: "其他", total: 22 },
        { type: "北京生物", total: 44 },
        { type: "北京科兴中维", total: 34 }
      ];
    }
  },
  num: {
    type: String,
    default: "3"
  }
});
const amchartRef = ref();
const chartId = ref("chart");
let mychart = ref("");

// watch(
//   () => props.dataSet,
//   (newV, oldV) => {
//     if (newV.length > 1) {
//       if (mychart.value) {
//         mychart.value.data = props.dataSet;
//         mychart.value.invalidateData();
//       } else {
//         setChart(props.dataSet);
//       }
//     } else {
//       if (mychart.value) {
//         mychart.value.data = props.dataSet;
//         mychart.value.invalidateData();
//       }
//     }
//   },
//   { deep: true, immediate: true }
// );
const setChart = (data: any) => {
  let rgm = new am4core.RadialGradientModifier();
  let colorList = ["#F2F4FF", "#ffd238", "#0188FE", "#ff5858", "#00A6A8"];
  rgm.brightnesses.push(-0.51, 1);
  // rgm.opacities.push(1,0);
  let option = {
    hiddenState: {
      properties: {
        depth: 0
        // opacity:0,
        // radius :am4core.percent(0)
      },
      transitionDuration: 5000,
      transitionEase: am4core.ease.circleOut
    },
    data: data,
    depth: 15,
    angle: 30,
    radius: "70%",
    label: {
      show: false,
      position: "center"
    },
    labelLine: {
      show: false
      // length: 10,
    },
    innerRadius: "50%",
    legend: {
      position: "top",
      contentAlign: "left",
      useDefaultMarker: true,
      fontSize: 14,
      labels: {
        fill: "#90AFD0"
      },
      valueLabels: {
        // visible: false,
        align: "left",
        fill: "#90AFD0",
        fontSize: 14
      },
      markers: {
        width: 8,
        height: 8
      }
    },
    series: [
      {
        type: "PieSeries3D",
        // hiddenState: {
        //   properties: {
        //     radius :am4core.percent(0)
        //   },
        //   transitionDuration: 5000,
        //   transitionEase: am4core.ease.circleOut
        // },
        colors: {
          list: colorList
        },
        // columns: {
        //   tooltipText: "{name}: {valueY.value}"
        // },
        dataFields: {
          value: "total",
          category: "type"
        },
        alignLabels: true,
        labels: {
          // draggable:true,
          maxWidth: 100,
          wrap: true,
          // html: '<p class="title">{category}</p><p class="num" style="color: #F2F4FF;font-family: DS-Digital;text-shadow: 0px 0px 24px rgba(242, 244, 255, 0.8); font-size: 0.24rem;"><span>{value.value}</span><span style="display:inline-block;padding-left: 0.15rem">{value.percent.formatNumber(\'#.0\')}%</span></p>',
          html: '<p class="title">{category}</p><p class="num" style="color: #F2F4FF;font-family: DS-Digital;text-shadow: 0px 0px 24px rgba(242, 244, 255, 0.8); font-size: 0.24rem;"><span>{value.value}</span></p>',
          // text:"{category}\n[font-family: DS-Digital font-size: 24px;]{value.value}[/][font-family: DS-Digital font-size: 24px; dx:30px]{value.percent.formatNumber('#.0')}%[/]",
          fill: "#fff",
          fontSize: 14,
          // paddingTop: 0,
          // paddingBottom: 0,
          radius: "25%" //控制label距离中心点远近
        },
        states: {
          hover: {
            properties: {
              scale: 1,
              shiftRadius: 0.2
            }
          },
          active: {
            properties: {
              scale: 1,
              shiftRadius: 0.2
            }
          }
        },
        ticks: {
          stroke: "#ffffff",
          strokeOpacity: 1,
          strokeWidth: 1,
          length: 10,
          strokeLinejoin: "round",
          adapter: {
            stroke: (color, target) => {
              return colorList[target.dataItem.index];
            }
          }
        },
        slices: {
          stroke: "#4a2abb",
          strokeWidth: 2,
          strokeOpacity: 1,
          template: {
            fillModifier: rgm,
            fillOpacity: 0.9,
            strokeModifier: rgm,
            strokeOpacity: 0.8,
            strokeWidth: 2,
            cornerRadius: 25
          }
        }
      }
    ]
  };
  mychart.value = am4core.createFromConfig(option, amchartRef.value, am4charts.PieChart3D);
  mychart.value.logo.scale = 0;
  // 设置actvie状态
  // let pieSeries = mychart.value.series.getIndex(0);
  // function restoreSelected() {
  //   pieSeries.slices.each(slice => {
  //     slice.setState('active');
  //   });
  // }
  // timeInstance = setInterval(()=>{
  // 	mychart.value.reinit()//重新加载
  // },12000)
};
onMounted(() => {
  let num = Math.round(Math.random() * 1000000);
  chartId.value += num;
  am4core.useTheme(am4themes_animated);
  setChart(props.dataSet);
});
onBeforeUnmount(() => {
  mychart.value.dispose();
});
</script>

<style scoped lang="scss">
.amchart {
  width: 100%;
  height: 100%;
  background-position: 50% 39%;
}
</style>
