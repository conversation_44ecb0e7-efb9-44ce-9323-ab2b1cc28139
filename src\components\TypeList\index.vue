<!--
 * @Author: <EMAIL>
 * @Date: 2023-09-06 16:23:09
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-09-18 17:07:18
 * @Description: file content
-->
<template>
  <div class="type-menu">
    <div class="type-title">{{ title }}</div>
    <div class="item-list">
      <slot name="operateBtn"></slot>
      <slot name="listContent">
        <div
          class="type-item"
          v-for="(item, index) in typeList"
          :key="item[configType.key]"
          :class="activeItem === index ? 'active' : ''"
          @click="activeItem = index"
        >
          <span>{{ item[configType.name] }}</span>
          <more-operate
            v-if="itemOperate.length"
            :operate-type="itemOperate"
            @on-delete="onDelete(item)"
            @on-edit="onEdit(item)"
            @on-add="onAdd(item)"
          >
          </more-operate>
        </div>
      </slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import moreOperate from "./moreOperate.vue";
interface IRowsItem {
  [propName: string]: any;
}
const props = defineProps({
  title: {
    type: String,
    default: "标题"
  },
  typeList: {
    type: Array<IRowsItem>,
    default: () => []
  },
  activeIndex: {
    type: Number,
    default: 0
  },
  configType: {
    type: Object,
    default: () => {
      return {
        name: "name",
        key: "id"
      };
    }
  },
  itemOperate: {
    type: Array<String>,
    default: () => []
  }
});
const emits = defineEmits(["update:activeIndex", "deleteItem", "editItem", "addItem"]);
const activeItem = computed({
  get() {
    return props.activeIndex;
  },
  set(value) {
    emits("update:activeIndex", value);
  }
});
const onDelete = (item: IRowsItem) => {
  emits("deleteItem", item);
};
const onEdit = (item: IRowsItem) => {
  emits("editItem", item);
};
const onAdd = (item: IRowsItem) => {
  emits("addItem", item);
};
</script>
<style lang="scss" scoped>
.type-menu {
  display: flex;
  flex-direction: column;
  width: 236px;
  margin-right: 16px;
  background: var(--el-color-white);
  .type-item {
    display: flex;
    justify-content: space-between;
    height: 20px;
    padding: 4px;
    margin-top: 18px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 14px;
    font-weight: 400;
    color: #3a3a3d;
    cursor: pointer;
    &.active {
      color: var(--el-color-primary);

      // :deep(.el-button) {
      //   color: var(--el-color-primary);
      // }
    }
  }
}
.type-title {
  height: 22px;
  padding: 16px;
  font-family: PingFangSC-Medium, "PingFang SC";
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  color: #3a3a3d;
  border-bottom: 1px solid #e1e3e6;
}
.item-list {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 16px;
  overflow-y: auto;
}
</style>
