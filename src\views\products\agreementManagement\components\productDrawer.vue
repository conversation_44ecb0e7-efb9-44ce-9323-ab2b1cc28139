<template>
  <el-drawer v-model="visible" :destroy-on-close="true" size="900px" title="选择产品">
    <div class="table-box">
      <ProTable ref="proTable" :request-api="getTableList" :tool-button="false" :columns="columns">
        <!-- 表格 header 按钮 -->
        <template #tableHeader="scope">
          <el-button type="primary" @click="() => confirm(scope.selectedList)">确认</el-button>
          <el-button @click="cancel">取消</el-button>
        </template>
      </ProTable>
    </div>
  </el-drawer>
</template>
<script setup lang="ts" name="UserDrawer">
import { ref, reactive, defineProps, defineExpose } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { type ProTableInstance, type ColumnProps, type HeaderRenderScope } from "@/components/ProTable/interface/index";
import ProTable from "@/components/ProTable/index.vue";
import { getProductPageList, getCategory } from "@/api/modules/business/productArea";
import { useProductAgreementStore } from "@/stores/modules/productAgreement";

const productStore = useProductAgreementStore();

const visible = ref(false);

// const props = defineProps({});

const getTableList = (search: any) => {
  const { productName, current, size, categoryName = [] } = search;
  return getProductPageList({
    productName,
    categoryId: categoryName.length ? categoryName[categoryName.length - 1] : undefined,
    current,
    size
  });
};

defineExpose({ visible });

const columns = ref<ColumnProps[]>([
  {
    type: "selection",
    fixed: "left",
    width: 80,
    selectable: row => {
      return !productStore.productDetailList.filter((item: any) => item.id === row.id).length;
    }
  },
  {
    prop: "productName",
    label: "产品名称",
    search: { el: "input", props: { maxLength: 10 } }
  },
  {
    prop: "categoryName",
    label: "产品分类",
    width: 120,
    // enum: getCategory,
    fieldNames: {
      value: "id",
      label: "name"
    },
    search: { el: "cascader" }
  },
  {
    prop: "provider",
    label: "产品服务商"
  }
]);

const cancel = () => {
  visible.value = false;
};

const confirm = (selectedList: any) => {
  const num = 1 - productStore.productDetailList.length;
  if (selectedList.length > num) {
    ElMessage({
      message: `选择的产品不能超过1个！`,
      type: "error"
    });
    return;
  }
  productStore.productDetailList = [...productStore.productDetailList, ...selectedList] as any;
  visible.value = false;
};
</script>
