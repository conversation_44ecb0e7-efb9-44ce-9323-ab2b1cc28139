<template>
  <div class="table-box custom-list">
    <pro-table
      ref="proTable"
      title="订单按日期统计分析总列表"
      :request-auto="false"
      :columns="columns"
      :init-param="initParam"
      :tool-button="false"
      :pagination="false"
      :data="tableData"
      :summary-method="getSummaries"
      show-summary
      :default-sort="{ prop: 'dateWithYear', order: 'descending' }"
    >
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <template #accumulateHB="{ row }">
        {{ row.accumulateHB }} <HBArrow v-if="row.accumulateHBArrow" :value="row.accumulateHBArrow" />
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span>
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="orderAnalysisList">
import { ref, reactive, computed, watch, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDownload } from "@/hooks/useDownload";
import { Statistic } from "@/api/interface/statisticAnalysis";
import { exportBusinessDateList } from "@/api/modules/statisticAnalysis";
import HBArrow from "@/components/HBArrow/index.vue";

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}
const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  },
  tableData: {
    type: Array,
    default: () => []
  },
  totalData: {
    type: Object,
    default: () => {}
  }
});

const router = useRouter();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive(props.params);

const tableData = ref(props.tableData);
const totalData = ref(props.totalData);
const tableParams = ref(props.params);

watch(
  () => props.params,
  () => {
    tableParams.value = { ...tableParams.value, ...props.params };
  },
  { immediate: true }
);

watch(
  () => props.tableData,
  () => {
    tableData.value = props.tableData;
  },
  { immediate: true }
);
watch(
  () => props.totalData,
  async () => {
    totalData.value = props.totalData;
    nextTick(() => {
      proTable.value?.element?.sort("dateWithYear", "descending");
    });
  },
  { immediate: true }
);

const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = "总计";
      return;
    }
    sums[index] = totalData.value[column.property];
  });

  return sums;
};
const dateTitle = computed(() => (props.params.dateType === DateType.day ? "日" : "月"));

// 表格配置项
const columns: ColumnProps<Statistic.OrderDateItem>[] = [
  { prop: "dateWithYear", label: "统计时间", minWidth: 100 },
  { prop: "accumulateCount", label: "累计产品商机数" },
  { prop: "netGrowthCount", label: "新增产品商机数" },
  {
    prop: "accumulateHB",
    label: "累计产品商机数日环比",
    headerRender: () => {
      return <>{`累计产品商机数${dateTitle.value}环比`}</>;
    }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 80 }
];

const toView = async (row: any) => {
  router.push({
    path: "/statisticAnalysis/productAnalysis/productDimension",
    query: {
      dateType: props.params.dateType,
      countDate: [row.dateWithYear, row.dateWithYear],
      itemId: tableParams.value.itemId,
      providerId: tableParams.value.providerId
    }
  });
};

const downloadFile = () => {
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() => {
    const queryData = {
      ...tableParams.value
    };
    useDownload(exportBusinessDateList, queryData);
  });
};
</script>
<style lang="scss" scoped>
.custom-list {
  :deep(.card.table-main) {
    border: none !important;
    box-shadow: none !important;
  }
}
:deep(.el-table .el-table__header .el-table__cell > .cell, .el-table .el-table__header .el-table__cell > .cell) {
  white-space: wrap;
}
:deep(.el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell) {
  color: var(--el-color-primary);
  background-color: #ffffff;
}
</style>
