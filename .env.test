###
 # @Author: <EMAIL>
 # @Date: 2023-09-08 09:49:57
 # @LastEditors: linzewei4270
 # @LastEditTime: 2023-11-21 10:33:46
 # @Description: file content
### 
# 测试环境
VITE_USER_NODE_ENV = test

# 公共基础路径
VITE_PUBLIC_PATH = ./

# 是否启用 gzip 或 brotli 压缩打包，如果需要多个压缩规则，可以使用 “,” 分隔
# Optional: gzip | brotli | none
VITE_BUILD_COMPRESS = gzip

# 打包压缩后是否删除源文件
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# 打包时是否删除 console
VITE_DROP_CONSOLE = true

# 是否开启 VitePWA
VITE_PWA = false

# 测试环境
VITE_API_URL = /api

# 开发环境跨域代理，支持配置多个
VITE_PROXY = [["/api","http://cnud-test.*************.nip.io"],["/minio","http://cnud-test.*************.nip.io"]]
