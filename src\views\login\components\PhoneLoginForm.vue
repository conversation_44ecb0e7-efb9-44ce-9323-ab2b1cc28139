<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
    <el-form-item prop="phoneNumber">
      <el-input v-model="loginForm.phoneNumber" placeholder="请输入手机号">
        <template #prepend>
          <span style="color: #333333">+86</span>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="sms">
      <phoneSms
        v-model:value="loginForm.sms"
        @sms-click="smsClick"
        :phone-number="loginForm.phoneNumber"
        :is-send-message="isSendMessage"
      ></phoneSms>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { HOME_URL } from "@/config";
import { getTimeState } from "@/utils";
import { Login } from "@/api/interface";
import { ElNotification } from "element-plus";
import { loginApi } from "@/api/modules/login";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import phoneSms from "@/components/PhoneSMS/index.vue";

import type { ElForm } from "element-plus";
import md5 from "md5";
const emits = defineEmits(["update:canLogin", "showVerify"]);
const props = defineProps({ isSendMessage: { type: Boolean, default: false } });
const router = useRouter();
const userStore = useUserStore();
const tabsStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();
const phonePre = ref("+86");
const phoneOptions = [
  {
    value: "+86",
    label: "+86"
  }
  // {
  //   value: "01",
  //   label: "+01"
  // }
];
const loginForm = reactive({
  phoneNumber: "",
  sms: ""
});
watch([loginForm, () => props.isSendMessage], () => {
  checkLoginStatus();
});

const checkLoginStatus = () => {
  emits("update:canLogin", !!(loginForm.phoneNumber && loginForm.sms && props.isSendMessage));
};
type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive({
  phoneNumber: [{ required: true, message: "请输入手机号", trigger: "blur" }],
  sms: [{ required: true, message: "请输入验证码", trigger: "blur" }]
});

const loading = ref(false);
// login
const login = (formEl?: FormInstance | undefined) => {
  console.log("smsLogin");
  if (!formEl) formEl = loginFormRef.value as FormInstance;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      // 1.执行登录接口
      const { data } = await loginApi({
        userType: "operate",
        phone: loginForm.phoneNumber,
        code: loginForm.sms,
        grantType: "phone",
        autoCreate: 1
      });

      userStore.setToken(data.accessToken);
      userStore.setUserInfo(data.operateUser);
      // 2.添加动态路由
      await initDynamicRouter();

      // 3.清空 tabs、keepAlive 数据
      tabsStore.closeMultipleTab();
      keepAliveStore.setKeepAliveName();

      // 4.跳转到首页
      router.push("./");
      ElNotification({
        title: getTimeState(),
        message: "欢迎登录一体化支撑平台",
        type: "success",
        duration: 3000
      });
    } finally {
      loading.value = false;
    }
  });
};
const smsClick = () => {
  emits("showVerify");
};
onMounted(() => {
  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value) return;
      login(loginFormRef.value);
    }
  };
});
defineExpose({ login, loginForm, checkLoginStatus });
</script>

<style scoped lang="scss">
@import "../index.scss";
.el-select {
  width: 80px;
  background: #f3f4f9;
  :deep(.el-input__suffix) {
    display: none;
  }
  :deep(.el-input__wrapper) {
    padding-left: 0;
  }
  :deep(.el-input__inner) {
    height: 34px;
    text-align: center;
    border-right: 1px solid #e1e3e6;
  }
}
:deep(.el-input-group__prepend) {
  padding-right: 0;
  background-color: #f3f4f9;
  box-shadow: none !important;
}
</style>
