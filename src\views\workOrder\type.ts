// 1-待处理，2-处理中，3-已处理，4-已关闭
export enum OrderStatus {
  PendingProcessing = "1",
  Processing = "2",
  Success = "3",
  Close = "4"
}

export const operationBtn = {
  [OrderStatus.PendingProcessing]: ["check", "cancel"],
  [OrderStatus.Processing]: ["check"],
  [OrderStatus.Success]: ["check"],
  [OrderStatus.Close]: ["check"]
};

export const orderStatus = {
  [OrderStatus.PendingProcessing]: ["yellow", "待处理"],
  [OrderStatus.Processing]: ["blue", "处理中"],
  [OrderStatus.Success]: ["green", "已处理"],
  [OrderStatus.Close]: ["grey", "已关闭"]
};

export interface OrderDetail {
  attachmentIds: string;
  code: string;
  createTime: string;
  creatorId: string;
  creatorName: string;
  description: string;
  durationDesc: string;
  handleAccount: string;
  handleBy: string;
  handleName: string;
  handleParty: string;
  handleTime: string;
  id: string;
  orderStatus: string;
  orderType: string;
  phone: string;
  updateTime: string;
  updaterId: string;
  updaterName: string;
  urgency: string;
}
