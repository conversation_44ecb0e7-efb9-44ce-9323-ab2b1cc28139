<template>
  <!-- 算力接入总数 -->
  <div class="echarts-box">
    <div id="ResourceModelChart" class="echarts"></div>
    <div class="page-dot">
      <span
        :class="{ active: index === currentEchartsPage }"
        v-for="(item, index) in followEchartsPages"
        :key="index"
        @mouseover="chooseEchartsPage(index)"
      ></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ECharts, EChartsOption, init } from "echarts";
import * as echarts from "echarts";

interface ChartProp {
  name: string;
  label: string;
  value: string[];
  maxValue?: string;
}

const allData = ref<any>([]);
const currentEchartsPage = ref<any>(0); // 当前显示页数
const echartsPage = ref<any>(8); // 八条数据一页
const followEchartsPages = ref<any>([]); // 有多少页

const chooseEchartsPage = (index: number) => {
  currentEchartsPage.value = index;
  const data = allData.value[index];
  drewCharts(data);
};

const initChart = (datas: any) => {
  const chartsData = datas.data;
  // 分割数组的方法
  function arrayChunk(array: string[], size: number) {
    let data = [];
    for (let i = 0; i < array.length; i += size) {
      data.push(array.slice(i, i + size));
    }
    return data;
  }

  allData.value = arrayChunk(chartsData, echartsPage.value);
  // 获取完数据后，默认显示第一页
  currentEchartsPage.value = 0;
  for (let j = 0; j < allData.value.length; j++) {
    followEchartsPages.value.push(j + 1);
  }

  const data = allData.value[currentEchartsPage.value];
  drewCharts(data);
};

const drewCharts = (data: any): ECharts => {
  const charEle = document.getElementById("ResourceModelChart") as HTMLElement;
  const charEch: ECharts = init(charEle);
  const option: EChartsOption = {
    grid: {
      top: "4%",
      left: "4%",
      right: "2%",
      bottom: "10%",
      containLabel: true
    },
    xAxis: {
      type: "value",
      axisLine: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      triggerEvent: false
    },
    yAxis: [
      {
        show: true,
        data: data.map((val: ChartProp) => val.name),
        inverse: true,
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: "#fff"
        },
        triggerEvent: false
      },
      {
        show: true,
        inverse: true,
        data,
        axisLabel: {
          fontSize: 14,
          color: "#fff",
          formatter: (value: number) => {
            return value >= 10000 ? (value / 10000).toFixed(2) + "w" : value + "";
          }
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        triggerEvent: false
      }
    ],
    series: [
      {
        name: "条",
        type: "bar",
        yAxisIndex: 0,
        data,
        barWidth: 10,
        itemStyle: {
          borderRadius: 30,
          color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            {
              offset: 0,
              color: "rgba(80,146,255,1)"
            },
            {
              offset: 1,
              color: "rgba(80,146,255,0)"
            }
          ])
        },
        label: {
          show: false
        }
      },
      {
        name: "框",
        type: "bar",
        yAxisIndex: 1,
        data: data.map((val: ChartProp) => {
          if (!val.maxValue) return 10000;
          return val.maxValue;
        }),
        barWidth: 10,
        itemStyle: {
          color: "none",
          borderWidth: 1,
          borderColor: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            {
              offset: 0,
              color: "rgba(160,196,225,1)"
            },
            {
              offset: 1,
              color: "rgba(61,126,235,1)"
            }
          ]),
          borderRadius: 15
        },
        silent: true
      }
    ]
  };
  charEch.setOption(option);
  return charEch;
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts-box {
  position: relative;
  width: 100%;
  height: 100%;
}
.echarts {
  width: 100%;
  height: 100%;
}
.page-dot {
  position: absolute;
  bottom: 2%;
  left: 50%;
  width: 100%;
  text-align: center;
  transform: translate(-50%, 0);
  span {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin: 0 4px;
    cursor: pointer;
    background: #5092ff;
    border-radius: 100%;
    opacity: 0.2;
    &.active {
      opacity: 1;
    }
  }
}
</style>
