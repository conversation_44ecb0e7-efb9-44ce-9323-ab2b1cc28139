<template>
  <div class="table-box">
    <ProTable :request-auto="false" ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="handleCreate"> 新增 </el-button>
      </template>
      <template #operation="{ row }">
        <!-- <div class="btn" @click="handleOpen(row, 'view')">查看</div>
          <div class="btn" @click="handleOpen(row, 'edit')">编辑</div>
          <div class="btn" @click="handleDelete(row)">删除</div> -->
        <auth-button :row-data="row" :auth-list="authList"></auth-button>
      </template>
    </ProTable>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600"
      :before-close="handleClose"
      destroy-on-close
      append-to-body
    >
      <el-form ref="FormRef" :model="form" label-width="auto" :rules="rules">
        <el-form-item label="集群名称:" prop="platformCode">
          <el-select
            style="width: 100%"
            v-model="form.platformCode"
            :disabled="operateType === 'view' || operateType === 'edit'"
            placeholder="请选择集群"
            @change="handleSelectCluster"
          >
            <el-option v-for="item in clusterList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="硬件资源:" prop="hardwareCode">
          <el-select
            style="width: 100%"
            v-model="form.hardwareCode"
            :disabled="operateType === 'view' || operateType === 'edit' || form.platformCode.length === 0"
            placeholder="请选择硬件资源"
          >
            <el-option v-for="item in hardwareList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="CPU单位权重:" prop="cpuWeight">
          <el-input
            v-model="form.cpuWeight"
            v-d-input-point2
            v-d-input-max="100000"
            @change="checkRang"
            :disabled="operateType === 'view'"
          />
        </el-form-item>
        <el-form-item label="CPU单价（核时/小时）:" prop="cpuPrice">
          <el-input
            v-model="form.cpuPrice"
            :disabled="operateType === 'view'"
            v-d-input-point2
            v-d-input-max="100000"
            @change="checkRang"
          />
        </el-form-item>
        <el-form-item label="GPU单位权重:" prop="gpuWeight">
          <el-input
            v-model="form.gpuWeight"
            :disabled="operateType === 'view'"
            v-d-input-point2
            v-d-input-max="100000"
            @change="checkRang"
          />
        </el-form-item>
        <el-form-item label="GPU单价（核时/小时）:" prop="gpuPrice">
          <el-input
            v-model="form.gpuPrice"
            :disabled="operateType === 'view'"
            v-d-input-point2
            v-d-input-max="100000"
            @change="checkRang"
          />
        </el-form-item>
        <el-form-item label="内存单位权重:" prop="memoryWeight">
          <el-input
            v-model="form.memoryWeight"
            :disabled="operateType === 'view'"
            v-d-input-point2
            v-d-input-max="100000"
            @change="checkRang"
          />
        </el-form-item>
        <el-form-item label="内存单价（核时/小时）:" prop="memoryPrice">
          <el-input
            v-model="form.memoryPrice"
            :disabled="operateType === 'view'"
            v-d-input-point2
            v-d-input-max="100000"
            @change="checkRang"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button v-if="operateType === 'view'" @click="resetForm(FormRef)">返回</el-button>
        <el-button v-if="operateType !== 'view'" type="primary" @click="submitForm(FormRef)"> 确认 </el-button>
        <el-button v-if="operateType !== 'view'" @click="resetForm(FormRef)">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { Plus } from "@element-plus/icons-vue";
import type { CoreSystem } from "@/api/interface/coreSystem";
import { Bill } from "@/api/interface/billManagement";
import {
  queryHardwareWeightConfigList,
  addHardwareWeightConfig,
  deleteHardwareWeightConfig,
  updateHardwareWeightConfig,
  // getClusterList,
  getHardwareCodeList
} from "@/api/modules/coreSystem/hardwareWeightConfig";
import { queryGetCluster } from "@/api/modules/coreSystem/unitPriceConfig";
// import { getCluster } from "@/api/modules/billManagement";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance } from "@/components/ProTable/interface";
import { onMounted, reactive, ref } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import type { IAuthButtonItem } from "@/components/AuthButton/typings";

interface HardwareListItem {
  value: string;
  label: string;
}

const operateType = ref("add"); //操作类型 add:新增，edit:编辑，view:查看
const dialogTitle = ref("");
// const id = ref("");
const FormRef = ref<FormInstance>();

// const getPlatformCode = ref(""); //平台code
const clusterList = ref<HardwareListItem[]>([]); // 集群
const hardwareList = ref<HardwareListItem[]>([]); // 硬件资源
const proTable = ref<ProTableInstance>();

const dialogVisible = ref(false);

const findCluster = (rowData: CoreSystem.hardwareWeightItem) => {
  const itemData = clusterList.value.find(el => el.value === rowData.platformCode);

  if (itemData) {
    return itemData.label;
  }

  return rowData.platformCode;
};

const columns = [
  {
    prop: "platformCode",
    label: "集群名称",
    // isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格,
    // enum: clusterList,
    // search: { el: "select", key: "platformCode" }
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable placeholder="请选择集群名称">
            {clusterList.value.map(item => (
              <el-option key={item.value} label={item.label} value={item.value} />
            ))}
          </el-select>
        );
      }
    },
    render: ({ row }) => <>{findCluster(row)}</>
  },
  {
    prop: "hardwareCode",
    label: "硬件资源",
    search: {
      render: (scope: any) => {
        return <SelectDict model-value={scope.modelValue} dictKey="hardware_code" label="硬件资源"></SelectDict>;
      }
    }
  },
  { prop: "cpuWeight", label: "CPU单位权重" },
  {
    prop: "gpuWeight",
    label: "GPU单位权重"
  },
  {
    prop: "memoryWeight",
    label: "内存单位权重"
  },
  {
    prop: "updateTime",
    label: "更新时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];
const form = reactive<CoreSystem.hardwareWeightItem>({
  id: "",
  platformCode: "",
  hardwareCode: "",
  cpuWeight: null,
  cpuPrice: null,
  gpuWeight: null,
  gpuPrice: null,
  memoryWeight: null,
  memoryPrice: null
});

const authList: IAuthButtonItem<CoreSystem.hardwareWeightItem>[] = [
  {
    contentName: "查看",
    iconClass: "View",
    itemClick: scope => handleOpen(scope, "view"),
    authName: "hardwareWeightConfigView"
  },
  {
    contentName: "编辑",
    iconClass: "Edit",
    itemClick: scope => handleOpen(scope, "edit"),
    authName: "hardwareWeightConfigEdit"
  },
  {
    contentName: "删除",
    iconClass: "Delete",
    itemClick: scope => handleDelete(scope),
    authName: "hardwareWeightConfigDelete"
  }
];

const validateGreaterThanZero = (rule: any, value: number, callback: any) => {
  if (value <= 0) {
    callback(new Error("请输入大于0的值"));
  } else {
    callback();
  }
};

const rules = reactive({
  platformCode: [{ required: true, message: "请输入cpu单位权重", trigger: "blur" }],
  hardwareCode: [{ required: true, message: "请输入cpu单位权重", trigger: "blur" }],
  cpuWeight: [
    { required: true, message: "请输入cpu单位权重", trigger: "blur" },
    { validator: validateGreaterThanZero, trigger: "blur" }
  ],
  gpuWeight: [
    { required: true, message: "请输入gpu单位权重", trigger: "blur" },
    { validator: validateGreaterThanZero, trigger: "blur" }
  ],
  memoryWeight: [
    { required: true, message: "请输入内存单位权重", trigger: "blur" },
    { validator: validateGreaterThanZero, trigger: "blur" }
  ],
  cpuPrice: [
    { required: true, message: "请输入cpu单价", trigger: "blur" },
    { validator: validateGreaterThanZero, trigger: "blur" }
  ],
  gpuPrice: [
    { required: true, message: "请输入gpu单价", trigger: "blur" },
    { validator: validateGreaterThanZero, trigger: "blur" }
  ],
  memoryPrice: [
    { required: true, message: "请输入内存单价", trigger: "blur" },
    { validator: validateGreaterThanZero, trigger: "blur" }
  ]
});

const getTableList = async (params?: CoreSystem.hardwareWeightSearch) => {
  return await queryHardwareWeightConfigList({ ...params });
};

const handleClose = () => {
  dialogVisible.value = false;
};

const submitForm = (formEl: FormInstance | undefined) => {
  formEl?.validate().then(() => {
    if (operateType.value === "add") {
      addHardwareWeightConfig({ ...form }).then(res => {
        if (res.code === 200) {
          handleClose();
          ElMessage({
            message: "创建成功！",
            type: "success"
          });
          proTable.value?.getTableList();
        } else {
          ElMessage({
            showClose: true,
            message: "创建失败",
            type: "error"
          });
        }
      });
    } else if (operateType.value === "edit") {
      updateHardwareWeightConfig({ ...form }).then(res => {
        if (res.code === 200) {
          handleClose();
          ElMessage({
            message: "修改成功！",
            type: "success"
          });
          proTable.value?.getTableList();
        } else {
          ElMessage({
            showClose: true,
            message: "修改失败",
            type: "error"
          });
        }
      });
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  dialogVisible.value = false;
};

const handleSelectCluster = (id: string) => {
  hardwareList.value = [];
  getHardwareCodeList(id).then((res: any) => {
    res.data.forEach((item: string) => {
      hardwareList.value.push({ value: item, label: item });
    });
  });
};

const handleCreate = () => {
  operateType.value = "add";
  dialogTitle.value = "新增资源权重";
  form.platformCode = "";
  form.hardwareCode = "";
  form.cpuWeight = null;
  form.cpuPrice = null;
  form.gpuWeight = null;
  form.gpuPrice = null;
  form.memoryWeight = null;
  form.memoryPrice = null;
  dialogVisible.value = true;
};

const handleOpen = (row: CoreSystem.hardwareWeightItem, operate: string) => {
  operateType.value = operate;
  if (operate === "edit") {
    dialogTitle.value = "编辑资源权重";
  } else if (operate === "view") {
    dialogTitle.value = "查看资源权重";
  }
  form.id = row.id;
  form.platformCode = row.platformCode;
  form.hardwareCode = row.hardwareCode;
  form.cpuWeight = row.cpuWeight;
  form.cpuPrice = row.cpuPrice;
  form.gpuWeight = row.gpuWeight;
  form.gpuPrice = row.gpuPrice;
  form.memoryWeight = row.memoryWeight;
  form.memoryPrice = row.memoryPrice;
  dialogVisible.value = true;
};
const handleDelete = (row: any) => {
  deleteHardwareWeightConfig(row.id).then(res => {
    if (res.code === 200) {
      handleClose();
      ElMessage({
        message: "删除成功！",
        type: "success"
      });
      proTable.value?.getTableList();
    } else {
      ElMessage({
        showClose: true,
        message: "删除失败",
        type: "error"
      });
    }
  });
};
const checkRang = () => {
  if (form.cpuWeight) {
    form.cpuWeight = Math.min(Math.max(form.cpuWeight, 0), 100000);
  }
  if (form.gpuWeight) {
    form.gpuWeight = Math.min(Math.max(form.gpuWeight, 0), 100000);
  }
  if (form.memoryWeight) {
    form.memoryWeight = Math.min(Math.max(form.memoryWeight, 0), 100000);
  }
  if (form.cpuPrice) {
    form.cpuPrice = Math.min(Math.max(form.cpuPrice, 0), 100000);
  }
  if (form.gpuPrice) {
    form.gpuPrice = Math.min(Math.max(form.gpuPrice, 0), 100000);
  }
  if (form.memoryPrice) {
    form.memoryPrice = Math.min(Math.max(form.memoryPrice, 0), 100000);
  }
};

onMounted(() => {
  proTable.value?.getTableList();
  queryGetCluster()
    .then(result => {
      // const { aiClusterId, displayName, platformCode } = result.data[0];
      // getPlatformCode.value = platformCode;
      // form.platformCode = result.data[0].platformCode;
      clusterList.value = result.data.map(item => ({ value: item.platformCode, label: item.displayName }));
    })
    .catch(err => {});
});
</script>

<style scoped></style>
