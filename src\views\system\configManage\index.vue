<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="产品列表"
      :request-auto="false"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :tool-button="false"
    >
      <template #tableHeader="scope">
        <el-button plain :icon="Plus" @click="toView({}, editType.add)"> 新增 </el-button>
        <el-button plain :icon="Delete" :disabled="!scope.isSelected" @click="batchDelete(scope.selectedListIds)">
          删除
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-tooltip content="编辑" placement="top">
          <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toView(scope.row, editType.edit)"></i>
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <i class="iconfont2 opera-icon icon-lajitong" @click="deleteConfig(scope.row)"></i>
        </el-tooltip>
      </template>
    </ProTable>

    <operate-config-dialog
      :visible="dialogInfo.dialogVisible"
      :type="dialogInfo.dialogType"
      :params="dialogData"
      @cancel="onCancel"
      @confirm="onComfirm"
    ></operate-config-dialog>
  </div>
</template>

<script setup lang="tsx" name="configManage">
import { ref, reactive, onMounted } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Plus, Delete } from "@element-plus/icons-vue";
import { getParamConfig, deleteParamConfig, searchConfigInfo } from "@/api/modules/system";
import { Product } from "@/api/interface/product";
import operateConfigDialog from "./components/operateConfigDialog.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { editType } from "@/enums/dialogTypeEnum";

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

const defaultDialogInfo = { dialogVisible: false, dialogType: editType.add };
const dialogInfo = reactive({ ...defaultDialogInfo });
const dialogData = ref({});
// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

onMounted(() => {
  // 然后
  proTable.value?.getTableList();
});

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  return getParamConfig(newParams);
};

// 页面按钮权限（按钮权限既可以使用 hooks，也可以直接使用 v-auth 指令，指令适合直接绑定在按钮上，hooks 适合根据按钮权限显示不同的内容）
// const { BUTTONS } = useAuthButtons();

// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: "参数主键" },
  { prop: "paramName", label: "参数名称", search: { el: "input" } },
  { prop: "paramKey", label: "参数键名", search: { el: "input" } },
  {
    prop: "paramValue",
    label: "参数键值",
    search: { el: "input" },
    showOverflowTooltip: {
      popperClass: "overflow-tooltip-style"
    }
  },
  // { prop: "paramValue", label: "系统内置", search: { el: "input" } },
  // { prop: "status", label: "状态" },
  {
    prop: "createTime",
    label: "创建时间",
    width: 170
  },
  { prop: "remark", label: "备注", width: 180 },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

// 批量删除用户信息
const batchDelete = async (ids: string[]) => {
  await useHandleData(deleteParamConfig, { ids: ids }, { msg: "删除后不能恢复，是否确认删除所选记录？", successMsg: "删除" });
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
// 删除
const deleteConfig = async (row: any) => {
  if (!row.id) {
    return;
  }
  await useHandleData(deleteParamConfig, { ids: [row.id] }, `删除<${row.paramName}>`);
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};

const getInfo = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  return searchConfigInfo(newParams);
};
const toView = async (row: any, type = editType.add) => {
  dialogInfo.dialogVisible = true;
  dialogInfo.dialogType = type;

  if (row.id) {
    // 获取单个配置信息
    await getInfo({ paramKey: row.paramKey }).then(res => {
      dialogData.value = res.data as Object;
    });
  }
};

const onCancel = () => {
  dialogInfo.dialogType = editType.add;
  dialogData.value = {};
  dialogInfo.dialogVisible = false;
};

const onComfirm = () => {
  onCancel();
  proTable.value?.getTableList();
};
</script>
<style lang="scss" scoped>
:global(.overflow-tooltip-style) {
  max-width: calc(100% - 24px);
  white-space: pre-wrap;
}
</style>
