<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-28 09:16:50
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-28 16:55:28
 * @Description: file content
-->
<template>
  <el-dialog v-model="dialogVisible" title="新增" width="30%" :before-close="handleClose">
    <el-form ref="formRef" :model="formData" label-width="140px" :rules="rules" label-position="left">
      <el-form-item label="云平台：" prop="cloudProvider">
        <el-select v-model="formData.cloudProvider" placeholder="请选择" style="width: 100%">
          <el-option
            :label="item.itemValue"
            :value="item.itemValue"
            v-for="(item, index) in cloudPlatformList"
            :key="item.itemKey + index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资源池名称：" prop="resourcePoolNumber">
        <el-select v-model="formData.resourcePoolNumber" placeholder="请选择" style="width: 100%">
          <el-option
            :label="item.resourcePoolName"
            :value="item.resourcePoolNumber"
            v-for="(item, index) in showResourcePoolList"
            :key="index"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="云账号名称：" prop="name">
        <el-input v-model="formData.name" type="text" placeholder="请输入云账号名称" maxlength="40" />
      </el-form-item>
      <el-form-item label="Access Key：" prop="accessKey">
        <el-input v-model="formData.accessKey" type="text" placeholder="请输入Access Key" maxlength="40" />
      </el-form-item>
      <el-form-item label="Access Secret：" prop="secretKey">
        <el-input v-model="formData.secretKey" type="password" placeholder="请输入Access Secret" maxlength="40" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel" size="large">取消</el-button>
        <el-button type="primary" @click="saveData(formRef)" size="large" v-if="isCheck">确定</el-button>
        <el-button type="primary" @click="checkAKSK()" size="large" v-else>验证</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue";
import { ElMessage, type FormInstance } from "element-plus";
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import { useDicStore } from "@/stores/modules/dictionaty";
import { sm2 } from "sm-crypto";
import { useUserStore } from "@/stores/modules/user";
import {
  addCloudAccount,
  editCloudAccount,
  queryResourcePoolByCloudProvider,
  executeCloudAccountVerify
} from "@/api/modules/resource";
import md5 from "md5";

const userStore = useUserStore();
const publicKey: any = computed(() => userStore.publicKey);
const formRef = ref<FormInstance>();
//云平台字典
const isCheck = ref(false);
const dictionaryStore = useDicStore();
const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  type: {
    type: Number,
    default: editType["add"]
  },
  listItemInfo: {
    type: Object,
    default: () => {}
  }
});
const showResourcePoolList: any = computed(() => {
  if (props.listItemInfo?.resourcePoolName) {
    return [
      ...resourcePoolList.value,
      {
        resourcePoolName: props.listItemInfo.resourcePoolName,
        resourcePoolNumber: props.listItemInfo.resourcePoolNumber
      }
    ];
  } else {
    return [...resourcePoolList.value];
  }
});
const resourcePoolList: any = ref([]);
const emits = defineEmits(["update:visible", "confirm"]);
const dialogVisible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    if (!value) {
      closeDialog();
    } else {
      emits("update:visible", value);
    }
  }
});
const formData = ref({
  resourcePoolNumber: "",
  name: "",
  cloudProvider: "",
  accessKey: "",
  secretKey: ""
});

watch(
  () => formData.value.cloudProvider,
  value => {
    getResourcePoolByCloudProvider();
  }
);
watch(
  [
    () => formData.value.accessKey,
    () => formData.value.cloudProvider,
    () => formData.value.resourcePoolNumber,
    () => formData.value.secretKey
  ],
  value => {
    isCheck.value = false;
  }
);
watch(
  () => props.listItemInfo,
  async value => {
    if (props.type === editType["edit"]) {
      formData.value.cloudProvider = value.cloudProvider;
      formData.value.resourcePoolNumber = value.resourcePoolNumber;
      formData.value.name = value.name;
      formData.value.accessKey = value.accessKey;
      formData.value.secretKey = value.secretKey;
    } else {
      formData.value = {
        resourcePoolNumber: "",
        name: "",
        cloudProvider: "",
        accessKey: "",
        secretKey: ""
      };
      formRef?.value?.resetFields();
    }
  },
  {
    deep: true
  }
);

const rules = reactive({
  resourcePoolNumber: [
    {
      required: true,
      message: "请选择资源池",
      trigger: "change"
    }
  ],
  cloudProvider: [
    {
      required: true,
      message: "请选择云平台",
      trigger: "change"
    }
  ],
  name: [
    {
      required: true,
      message: "请输入云账号名称",
      trigger: "blur"
    },
    { max: 40, message: "最长40个字符", trigger: "blur" }
  ],
  accessKey: [
    {
      required: true,
      message: "请输入Access Key",
      trigger: "blur"
    }
  ],
  secretKey: [
    {
      required: true,
      message: "请输入Access Secret",
      trigger: "blur"
    }
  ]
});
//云平台字典
const cloudPlatformList: any = computed(() => dictionaryStore.cloudPlatformList);
// 提交数据
const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: addCloudAccount,
        [editType["edit"]]: editCloudAccount
      };
      const praamsObject = {
        [editType["add"]]: () => {
          return {
            accessKey: formData.value.accessKey,
            cloudProvider: formData.value.cloudProvider,
            name: formData.value.name,
            resourcePoolName: showResourcePoolList.value.find(
              (item: any) => item.resourcePoolNumber === formData.value.resourcePoolNumber
            )?.resourcePoolName,
            resourcePoolNumber: formData.value.resourcePoolNumber,
            secretKey: sm2.doEncrypt(formData.value.secretKey, publicKey.value, 1),
            status: 1
          };
        },
        [editType["edit"]]: () => {
          return {
            accessKey: formData.value.accessKey,
            cloudProvider: formData.value.cloudProvider,
            name: formData.value.name,
            resourcePoolName: showResourcePoolList.value.find(
              (item: any) => item.resourcePoolNumber === formData.value.resourcePoolNumber
            )?.resourcePoolName,
            resourcePoolNumber: formData.value.resourcePoolNumber,
            secretKey: sm2.doEncrypt(formData.value.secretKey, publicKey.value, 1),
            status: props.listItemInfo.status,
            uuid: props.listItemInfo.uuid
          };
        }
      };

      apiObject[props.type as 1 | 2](praamsObject[props.type as 1 | 2]()).then((res: any) => {
        if (res.code === 200) {
          ElMessage.success("操作成功");
          emits("confirm");
          dialogVisible.value = false;
        }
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
const onCancel = () => {
  dialogVisible.value = false;
};

const getAccountList = () => {
  // 先获取字典
  dictionaryStore.getCloudPlatformList(); // 状态
};

const getResourcePoolByCloudProvider = () => {
  if (formData.value.cloudProvider) {
    queryResourcePoolByCloudProvider({
      cloudProvider: formData.value.cloudProvider
    }).then(res => {
      resourcePoolList.value = [...(res.data as Array<any>)];
    });
  } else {
    resourcePoolList.value = [];
  }
};
const checkAKSK = () => {
  const params = {
    accessKey: formData.value.accessKey,
    cloudProvider: formData.value.cloudProvider,
    resourcePoolName: showResourcePoolList.value.find(
      (item: any) => item.resourcePoolNumber === formData.value.resourcePoolNumber
    )?.resourcePoolName,
    resourcePoolNumber: formData.value.resourcePoolNumber,
    secretKey: sm2.doEncrypt(formData.value.secretKey, publicKey.value, 1)
  };
  executeCloudAccountVerify(params).then(res => {
    ElMessage.success("验证通过");
    isCheck.value = true;
  });
};
const handleClose = () => {
  dialogVisible.value = false;
};
const closeDialog = () => {
  isCheck.value = false;
  formRef?.value?.clearValidate();

  emits("update:visible", false);
};
getAccountList();
onMounted(() => {});
</script>
<style scoped lang="scss"></style>
