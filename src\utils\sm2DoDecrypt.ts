import { paramConfigSearch } from "@/api/modules/system";
import { sm2 } from "sm-crypto";

export default async <T extends object>(smKeyCode: T): Promise<T> => {
  const doDecryptKey = {} as T;

  const response = await paramConfigSearch("smKey").catch(err => {
    console.log(err);
  });

  Object.keys(smKeyCode).forEach(key => {
    if (Reflect.get(smKeyCode, key)) {
      const paramsKey = sm2.doDecrypt(Reflect.get(smKeyCode, key) as string, response.data.paramValue);
      Reflect.set(doDecryptKey, key, paramsKey);
    }
  });

  return Promise.resolve(doDecryptKey);
};
