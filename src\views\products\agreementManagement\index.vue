<template>
  <div class="table-box">
    <ProTable ref="proTable" title="产品服务协议列表" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => createAgreement()"> 新增 </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toEdit(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="handelDelete(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <!-- <Edit ref="editRef" @refresh="refresh"></Edit> -->
    <!-- <productDrawer ref="drawerRef" @refresh="refresh" /> -->
  </div>
</template>

<script setup lang="tsx" name="productAgreementManagement">
import { ref, reactive } from "vue";
import { Plus, Delete } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, FormRules } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
// import Edit, { IAction } from "./edit/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { productAgreementList, productAgreementdelete, productAgreementQuery } from "@/api/modules/product";
// import productDrawer from "./components/productDrawer.vue";
import { useRouter } from "vue-router";
import { productAgreementStorage } from "@/utils/storage/edit";
const { BUTTONS } = useAuthButtons();
const router = useRouter();

const proTable = ref<ProTableInstance>();

const getTableList = async (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }

  let newParams = JSON.parse(JSON.stringify(search));
  return productAgreementList(newParams);
};

// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "protocolTitle", label: "产品服务协议名称", search: { el: "input", props: { maxLength: 30 } } },
  { prop: "productName", label: "产品名称", search: { el: "input", props: { maxLength: 20 } } },
  {
    prop: "creatorName",
    label: "创建人员",
    width: 180
    // enum: auditStatusDict
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    sortable: true,
    width: 170
  },
  { prop: "operation", label: "操作", fixed: "right", width: 130 }
];

const toEdit = async (row: any) => {
  const data: any = (await productAgreementQuery(row.id)).data;
  console.log(data.protocolTitle);

  router.push({
    path: `/productManage/agreementManagement/edit`,
    query: {
      protocolTitle: data.protocolTitle,
      productIds: data.productIds,
      content: data.content,
      id: row.id,
      type: "edit"
    }
    // query: { data: data, type: "edit" }
  });
};

const createAgreement = () => {
  // productAgreementStorage.clearExpired();
  router.push({ path: `/productManage/agreementManagement/edit`, query: { type: "add" } });
};

const handelDelete = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此协议？", "确认操作").then(async () => {
    await productAgreementdelete(data.protocolRelId);
    ElMessage.success("删除成功！");
    proTable.value?.getTableList();
  });
};
</script>

<style scoped></style>
