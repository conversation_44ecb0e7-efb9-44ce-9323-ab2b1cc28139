<!--
 * @Author: <EMAIL>
 * @Date: 2023-09-13 15:15:13
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-09-15 18:33:26
 * @Description: file content
-->
<template>
  <div class="type-item-btns">
    <el-popover
      placement="right"
      title=""
      trigger="hover"
      :teleported="false"
      :popper-options="{ modifiers: [{ enabled: true }], strategy: 'fixed', placement: 'auto' }"
    >
      <div class="more-btn">
        <slot name="default">
          <el-button
            class="more-edit"
            @click="onEdit"
            v-if="operateType.indexOf('edit') != -1"
            text
            :icon="EditPen"
            plain
            size="large"
          >
            编辑
          </el-button>
          <el-button
            class="more-delete"
            @click="onDelete"
            v-if="operateType.indexOf('delete') != -1"
            text
            :icon="Delete"
            plain
            size="large"
          >
            删除
          </el-button>
          <el-button
            class="more-add"
            @click="onAdd"
            v-if="operateType.indexOf('add') != -1"
            text
            :icon="CirclePlus"
            plain
            size="large"
          >
            新增
          </el-button>
        </slot>
      </div>

      <template #reference>
        <div class="more-icon">
          <div>
            <slot name="tipsIcon">
              <el-icon>
                <i class="iconfont2 icon-gengduo"></i>
              </el-icon>
            </slot>
          </div>
        </div>
      </template>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { More, Delete, EditPen, CirclePlus } from "@element-plus/icons-vue";

// import { ref, computed } from "vue";
defineProps({
  operateType: { type: Array<String>, default: () => [] }
});
// const enterIcon = ref(false);
// const enterOperate = ref(false);
// const isShow = computed(() => {
//   return enterIcon.value || enterOperate.value;
// });
// const leaveIcon = () => {
//   setTimeout(() => {
//     enterIcon.value = false;
//   }, 500);
// };
const emits = defineEmits(["onDelete", "onEdit", "onAdd"]);
const onDelete = () => {
  emits("onDelete");
};
const onEdit = () => {
  emits("onEdit");
};
const onAdd = () => {
  emits("onAdd");
};
</script>
<style lang="scss" scoped>
.more-icon {
  position: relative;
  z-index: 9;
  .more-btn {
    position: absolute;
    top: 10px;
    width: 138px;
    padding: 4px 0;
    color: var(--el-color-primary);
    background: var(--el-color-white);
    box-shadow: 0 2px 6px 0 rgb(58 58 61 / 12%);
    :deep(.el-button) {
      display: flex;
      justify-content: flex-start;
      width: 100%;
      color: var(--el-color-primary);
      background: var(--el-color-white);
    }
  }
}
.more-btn {
  :deep(.el-button + .el-button) {
    margin-left: 0 !important;
  }
}
.type-item-btns {
  :deep(.el-popover) {
    padding: 4px 0;
  }
  :deep(.el-button) {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    &:hover {
      color: var(--el-button-hover-text-color);
      background: #f3f7ff;
    }
  }
}
</style>
