<template>
  <el-dialog
    v-model="dialogVisible"
    :title="modalTitle"
    width="830"
    label-position="left"
    :before-close="handleClose"
    :destroy-on-close="true"
  >
    <div class="wrapper">
      <el-row>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">是否自营： </span>
            <span class="info-text">{{ data?.cooperateType == "Z" ? "是" : "否" ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">合作商： </span>
            <span class="info-text">{{ data?.providerName ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">主商户号： </span>
            <span class="info-text">{{ data?.unicomMerchantNumber ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">子商户号： </span>
            <span class="info-text">{{ data?.merchantNumber ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">总金额(元)：</span>
            <span class="info-text">{{ data?.totalFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">
              <el-popover :width="232" trigger="hover">
                <div>
                  账单总金额：根据清算下发对账文件的账单实付款累计计算<br />
                  合作商总金额：系统根据提交时按照分成比例计算的金额<br />
                  平台总金额：订单实付金额-合作商分成金额<br />
                  账单是否对平：账单总金额=合作商总金额+平台总金额+手续费
                </div>
                <template #reference>
                  <el-icon class="icon" size="14px"><Warning /></el-icon>
                </template>
              </el-popover>
              账单是否对平：
            </span>
            <span class="info-text">{{ data?.billFlag == "1" ? "是" : "否" ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">平台总金额(元)：</span>
            <span class="info-text">{{ data?.unicomTotalFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">合作商总金额(元)：</span>
            <span class="info-text">{{ data?.providerTotalFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="info-item">
            <span class="info-label">手续费（元）：</span>
            <span class="info-text">{{ data?.commissionTotalFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">
              <el-popover :width="232" trigger="hover">
                <div>
                  平账订单: 原本为长款、短款、错账，被处理的订单<br />
                  短款：系统存在订单，支付中心不存在支付记录<br />
                  长款：系统不存在订单，支付中心存在支付记录<br />
                  错账：系统和支付中心存在记录，金额不一致
                </div>
                <template #reference>
                  <el-icon class="icon" size="14px" style="line-height: 20px"><Warning /></el-icon>
                </template>
              </el-popover>
              一致订单（笔）：
            </span>
            <span class="info-text">{{ data?.rightNumber ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">一致订单总金额（元）：</span>
            <span class="info-text">{{ data?.rightFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">平账订单（笔）：</span>
            <span class="info-text">{{ data?.fitNumber ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">平账订单总金额（元）：</span>
            <span class="info-text">{{ data?.fitFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">短款订单（笔）：</span>
            <span class="info-text">{{ data?.shortNumber ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">短款订单金额（元）：</span>
            <span class="info-text">{{ data?.shortFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">长款订单（笔）：</span>
            <span class="info-text">{{ data?.longNumber ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">长款订单金额（元）：</span>
            <span class="info-text">{{ data?.longFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">错账订单（笔）：</span>
            <span class="info-text">{{ data?.wrongNumber ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">错账订单金额（元）：</span>
            <span class="info-text">{{ data?.wrongFee ?? "-" }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <el-form class="cust-form-style" ref="applyRef" :rules="rules" :model="applyForm" label-position="left">
            <el-form-item label="账单日期：" prop="billDate">
              <el-date-picker
                type="month"
                v-model="applyForm.billDate"
                placeholder="请选择账单日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 90%"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <span class="info-label">是否需要拨付审核：</span>
            <span class="info-text">{{ data?.cooperateType == "Z" ? "否" : "是" ?? "-" }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button :disabled="!isSucceeded || data?.billFlag !== '1'" type="primary" @click="handleSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { generateBill, generateBillAll, confirmBill } from "@/api/modules/reconciliationManage";

type MODALACTION = "all" | "create";

const modalTitle = "生成账单";

const dialogVisible = ref(false);
const modalAction = ref("all");
const applyRef = ref<FormInstance>();
const data = ref();
const isSucceeded = ref(false);

const applyForm = ref<any>({
  billDate: ""
});

const emit = defineEmits(["onSubmitBill"]);
const disabledDate = (time: Date) => time.getTime() >= Date.now() - 60 * 60 * 24 * 1000;

const handleOpen = (action: MODALACTION, reqParams?: any) => {
  modalAction.value = action;
  dialogVisible.value = true;
  isSucceeded.value = false;
  if (action == "all") {
    getAllDetails(reqParams);
  } else {
    getDetails(reqParams);
  }
};

const getAllDetails = (params: string[]) => {
  generateBillAll(params).then(resp => {
    if (resp.code == 200) {
      data.value = resp.data;
      isSucceeded.value = true;
    }
  });
};

const getDetails = (ids: string[]) => {
  generateBill({
    idList: ids
  }).then(resp => {
    if (resp.code == 200) {
      data.value = resp.data;
      isSucceeded.value = true;
    }
  });
};

const handleClose = () => {
  applyRef.value?.resetFields();
  dialogVisible.value = false;
};

const handleSubmit = () => {
  if (data.value?.billFlag !== "1") {
    ElMessage({
      message: "账单没有对平！",
      type: "info"
    });
    return;
  }
  applyRef.value
    ?.validate()
    .then(() => {
      confirmBill({
        ...data.value,
        ...applyForm.value
      }).then(resp => {
        if (resp.code == 200) {
          ElMessage({
            message: "生成账单成功！",
            type: "success"
          });
          handleClose();
          emit("onSubmitBill");
        } else {
          ElMessage({
            message: "生成账单失败！",
            type: "error"
          });
        }
      });
    })
    .catch((err: string) => {
      console.log(err);
    });
};

const rules = reactive<FormRules>({
  billDate: [{ required: true, message: "请选择账单日期", trigger: "blur" }]
});

defineExpose({
  handleOpen,
  handleClose
});
</script>

<style scoped lang="scss">
.wrapper {
  .info-item {
    margin-bottom: 16px;
    .info-label {
      font-size: 14px;
      font-weight: 400;
      color: #73787f;
    }
    .info-text {
      font-size: 14px;
      font-weight: 400;
      color: #3a3a3d;
    }
  }
}
:deep(.cust-form-style.el-form--label-left .el-form-item__label) {
  color: #73787f;
}
</style>
