import http from "@/api";

// 查询所属领域
export const queryOclDemandCategory = () => {
  return http.post(`/cnud-operation/admin/oclDemandCategory/list/withChildren`);
};
// 需求发布审核-需求列表
export const getRequireReviewList = (params?: any) => {
  return http.get(`/cnud-operation/admin/oclDemand/auditPageList`, params);
};
// 需求发布审核-需求详情
export const getRequireDetail = (params?: any) => {
  return http.get(`/cnud-operation/admin/oclDemand/detail`, params);
};
// 需求发布审核-审核
export const reviewRequire = (params?: any) => {
  return http.post(`/cnud-operation/admin/oclDemand/audit`, params);
};
// 需求发布审核-方案列表
export const getSchemeReviewList = (params?: any) => {
  return http.get(`/cnud-operation/admin/oclDemandSolution/auditPageList`, params);
};
// 需求发布审核-方案详情
export const getSchemeDetail = (params?: any) => {
  return http.get(`/cnud-operation/admin/oclDemandSolution/detail`, params);
};
// 需求发布审核-方案审核
export const reviewScheme = (params?: any) => {
  return http.post(`/cnud-operation/admin/oclDemandSolution/audit`, params);
};
// 需求发布审核-获取上一条和下一条
export const getBeforeAndAfter = (params?: any) => {
  return http.get(`/cnud-operation/admin/oclDemand/beforeAndAfter`, params);
};

// 需求商机管理-需求列表
export const getBusinessRequireList = (params?: any) => {
  return http.get(`/cnud-operation/admin/oclDemand/businessPageList`, params);
};
// 需求商机管理-方案列表
export const getBusinessSchemeList = (params?: any) => {
  return http.get(`/cnud-operation/admin/oclDemandSolution/businessPageList`, params);
};
// 需求商机管理-需求关闭
export const closeDemand = (params?: any) => {
  return http.post(`/cnud-operation/admin/oclDemand/close`, params);
};
