<template>
  <div class="page-container">
    <div class="header-title">{{ title }}</div>
    <el-form
      ref="ruleFormRef"
      class="info-form"
      :model="ruleForm"
      :rules="rules"
      label-width="160px"
      label-position="left"
      status-icon
      :disabled="currType === operateTypeConfig.VIEW"
    >
      <el-form-item label="合作平台" prop="platform">
        <el-select v-model="ruleForm.platform" placeholder="请选择合作平台" disabled>
          <el-option v-for="item in platformOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="合作平台账号" prop="platformAccount">
        <el-input
          v-model="ruleForm.platformAccount"
          placeholder="请填写合作平台账号"
          minlength="1"
          maxlength="50"
          clearable
          :disabled="currType === operateTypeConfig.EDIT"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="合作平台密码" prop="platformPwd">
        <el-input
          v-model="ruleForm.platformPwd"
          placeholder="请填写合作平台密码"
          minlength="1"
          maxlength="50"
          clearable
          :disabled="currType === operateTypeConfig.EDIT"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="是否为主账号" prop="isMainAccount">
        <el-select v-model="ruleForm.isMainAccount" placeholder="请选择是否为主账号">
          <el-option v-for="item in isMainOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="主账号" prop="mainAccount">
        <el-input v-model="ruleForm.mainAccount" placeholder="请填写合作平台主账号" minlength="1" maxlength="50" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="关联客户账号" prop="userAccount">
        <el-input v-model="ruleForm.userAccount" placeholder="请填写关联客户账号" minlength="1" maxlength="50" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="关联订单" prop="subOrderNo">
        <el-input v-model="ruleForm.subOrderNo" placeholder="请填写订单号" minlength="1" maxlength="50" clearable> </el-input>
      </el-form-item>
    </el-form>
    <div class="btn-box">
      <el-button @click="returnBack">取消</el-button>
      <el-button type="primary" :loading="confirmLoading" @click="confirmClick">提交</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup name="partnerAccountEdit">
import { computed, onMounted, reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { operateTypeConfig } from "./config";
import { useDicStore } from "@/stores/modules/dictionaty";
import { platformAccountAdd, platformAccountEdit } from "@/api/modules/platform";
import { Platform } from "@/api/interface/platform";
import { ElMessage } from "element-plus";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { sm2 } from "sm-crypto";
import { useUserStore } from "@/stores/modules/user";

const userStore = useUserStore();
const publicKey: any = computed(() => userStore.publicKey);
const route = useRoute();
const router = useRouter();

const dictionaryStore = useDicStore();

const ruleFormRef = ref();

const ruleForm = reactive({
  platform: "",
  platformAccount: "",
  platformPwd: "",
  isMainAccount: null,
  mainAccount: "",
  userAccount: "",
  subOrderNo: ""
});

const rules = reactive({
  platform: [
    {
      required: true,
      message: "请选择合作平台",
      trigger: "change"
    }
  ],
  platformAccount: [
    {
      required: true,
      message: "请填写合作平台账号",
      trigger: "change"
    }
  ],
  platformPwd: [],
  isMainAccount: [
    {
      required: true,
      message: "请选择是否为主账号",
      trigger: "change"
    }
  ],
  mainAccount: [],
  userAccount: [],
  subOrderNo: []
});

const platformOptions: any = computed(() => {
  return dictionaryStore.cooperatePlatformDic;
});

const isMainOptions = ref<any[]>([
  {
    label: "是",
    value: 1
  },
  {
    label: "否",
    value: 0
  }
]);

let currId;

const currType = ref<string>("");

const confirmLoading = ref<boolean>(false);

// 关闭tabs导航
function closeTabItem() {
  const tabStore = useTabsStore();
  const keepAliveStore = useKeepAliveStore();
  if (route.meta.isAffix) return;
  tabStore.removeTabs(route.fullPath);
  keepAliveStore.removeKeepAliveName(route.name as string);
}

const returnBack = () => {
  closeTabItem();
  router.back();
};

const confirmAdd = () => {
  confirmLoading.value = true;
  ruleForm.platformPwd = sm2.doEncrypt(ruleForm.platformPwd, publicKey.value, 1);
  platformAccountAdd(ruleForm as Platform.PlatformAccountAddOrEditParams)
    .then(res => {
      confirmLoading.value = false;
      ElMessage({
        message: "新增成功",
        type: "success"
      });
      returnBack();
    })
    .catch(() => {
      ruleForm.platformPwd = "";
      confirmLoading.value = false;
    });
};

const confirmEdit = () => {
  confirmLoading.value = true;
  const queryData = {
    id: currId,
    ...ruleForm
  };
  platformAccountEdit(queryData as Platform.PlatformAccountAddOrEditParams)
    .then(res => {
      confirmLoading.value = false;
      ElMessage({
        message: "编辑成功",
        type: "success"
      });
      returnBack();
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const confirmClick = () => {
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      if (currType.value === operateTypeConfig.ADD) {
        confirmAdd();
      } else if (currType.value === operateTypeConfig.EDIT) {
        confirmEdit();
      } else {
        returnBack();
      }
    }
  });
};

const title = computed(() => {
  let titleStr = "";
  switch (currType.value) {
    case operateTypeConfig.ADD:
      titleStr = "新增账号";
      break;
    case operateTypeConfig.VIEW:
      titleStr = "查看账号";
      break;
    case operateTypeConfig.EDIT:
      titleStr = "编辑账号";
      break;
    default:
  }
  return titleStr;
});

const initInfo = () => {
  const { type, item, platform } = route.query;
  currType.value = type as string;
  if (platform) {
    ruleForm.platform = platform as string;
  }
  if (item) {
    const info = JSON.parse(decodeURIComponent(window.atob(item as string)));
    const { id, platformAccount, platformPwd, isMainAccount, mainAccount, userAccount, subOrderNo } = info;
    currId = id;
    ruleForm.platformAccount = platformAccount;
    ruleForm.platformPwd = platformPwd;
    ruleForm.isMainAccount = isMainAccount;
    ruleForm.mainAccount = mainAccount;
    ruleForm.userAccount = userAccount;
    ruleForm.subOrderNo = subOrderNo;
  }
};

onMounted(() => {
  initInfo();
  dictionaryStore.getCooperatePlatformList();
});
</script>

<style lang="scss" scoped>
.page-container {
  box-sizing: border-box;
  min-height: 100%;
  padding: 18px;
  overflow-y: scroll;
  background-color: #ffffff;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  .header-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    font-size: 16px;
    font-weight: 400;
    &::before {
      display: block;
      width: 3px;
      height: 14px;
      margin-right: 8px;
      content: "";
      background-color: var(--el-color-primary);
      border-radius: 1px;
    }
  }
  .info-form {
    display: flex;
    flex-wrap: wrap;
    .el-form-item {
      box-sizing: border-box;
      width: 50%;
      &:nth-child(odd) {
        padding-right: 24px;
      }
      &:nth-child(even) {
        padding-left: 24px;
      }
      .el-select {
        width: 100%;
      }
      :deep(.el-input) {
        width: 100% !important;
      }
    }
  }
  .btn-box {
    display: flex;
    justify-content: left;
    margin-top: 24px;
  }
}
</style>
