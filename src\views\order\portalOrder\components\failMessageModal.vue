<template>
  <el-dialog :model-value="visible" width="800" title="其他异常" destroy-on-close :close-on-click-modal="false" @close="goBack">
    <div>{{ messageContent }}</div>
    <template #footer>
      <el-button @click="goBack()"> 取消 </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="failMessageModal">
import { ref, watch } from "vue";

import { getFailMessage } from "@/api/modules/order";

const emit = defineEmits(["update:visible"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: () => false
  },
  subOrderId: {
    type: String,
    required: true
  }
});

const messageContent = ref("");

const getDetail = (value: string) => {
  getFailMessage({ subOrderId: value }).then(res => {
    console.log("🚀 ~ getFailMessage ~ res:", res);
    if (res.code === 200) {
      messageContent.value = res.data || "";
    } else {
      messageContent.value = "";
    }
  });
};
watch(
  () => props.visible,
  () => {
    if (props.visible) {
      getDetail(props.subOrderId);
    }
  },
  {
    immediate: true
  }
);

const goBack = () => {
  emit("update:visible", false);
};
</script>

<style scoped lang="scss"></style>
