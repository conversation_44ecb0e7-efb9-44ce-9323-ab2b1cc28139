<template>
  <div class="statistical-data">
    <div class="main">
      <indicator-button
        :indicator-title="'数据指标'"
        :list="dataIndex"
        :active="activeBtn"
        @item-click="onChangeOrder"
      ></indicator-button>
      <div class="chart-box">
        <line-chart ref="orderLineChartRef" :chart-id="productUsageLineId" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="cha">
import { ref } from "vue";
import dayjs from "dayjs";
import { ECharts } from "echarts";
import { queryProductUseChart } from "@/api/modules/statisticAnalysis";
import IndicatorButton from "@/components/IndicatorButton/index.vue";
import lineChart from "@/views/statisticAnalysis/orderAnalysis/components/lineChart.vue";

// 获取子组件的ref
interface ChartExpose {
  initChart: (params: any, name?: string) => ECharts;
}
enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}

// 月报表入参月份
const handleCountDate = (start: any, end: any, dateTypeValue = DateType.day) => {
  if (dateTypeValue === DateType.month) {
    return {
      startTime: dayjs(start).format("YYYY-MM-DD"),
      endTime: dayjs(end).format("YYYY-MM-DD")
    };
  }
  return {};
};

const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  },
  productUsageLineId: {
    type: String,
    default: () => "productUsageLineId"
  }
});

const dataIndex = [
  {
    label: "新增使用用户数",
    value: "1"
  },
  {
    label: "累计使用用户数",
    value: "2"
  },
  {
    label: "新增使用次数",
    value: "3"
  },
  {
    label: "累计使用次数",
    value: "4"
  }
];

const orderLineChartRef = ref<ChartExpose>();
const activeBtn = ref<any>("1");
const chartData = ref({
  userNa: [],
  userCum: [],
  useNumNa: [],
  useNumCum: []
});

const drewChart = (
  params = {
    userNa: [],
    userCum: [],
    useNumNa: [],
    useNumCum: []
  },
  type: string
) => {
  const { userNa = [], userCum = [], useNumNa = [], useNumCum = [] } = params;
  let currentChart = userNa;
  switch (type) {
    case "1":
      currentChart = userNa;
      break;
    case "2":
      currentChart = userCum;
      break;
    case "3":
      currentChart = useNumNa;
      break;
    case "4":
      currentChart = useNumCum;
      break;
    default:
      break;
  }
  const nameList = currentChart?.map((item: any) => item.indexLabel);
  const currentItem = dataIndex.filter(item => item.value === type);
  orderLineChartRef.value?.initChart(
    { nameList, data: currentChart?.map((item: any) => item.indexValue) },
    currentItem.length ? currentItem[0].label : ""
  );
};

// 图表
const getChat = (reqParams?: any) => {
  const req = {
    ...props.params,
    ...reqParams
  };
  queryProductUseChart({
    ...req,
    ...handleCountDate(req.startTime, req.endTime, req.indexUnit)
  }).then((res: any) => {
    if (res.code === 200) {
      // userNa-新增使用用户数 userCum-累计使用用户数 useNumNa-新增使用次数 useNumCum-累计使用次数
      const { userNa = [], userCum = [], useNumNa = [], useNumCum = [] } = res.data || {};
      chartData.value = {
        ...chartData.value,
        userNa,
        userCum,
        useNumNa,
        useNumCum
      };
      drewChart(
        {
          ...chartData.value,
          userNa,
          userCum,
          useNumNa,
          useNumCum
        },
        activeBtn.value
      );
    }
  });
};

const onChangeOrder = (values: any) => {
  activeBtn.value = values.value;
  drewChart(chartData.value, values.value);
};

defineExpose({
  getChat
});
</script>

<style lang="scss" scoped>
.statistical-data {
  display: flex;
  width: 100%;
}
.main {
  flex: 1;
  .chart-box {
    width: 100%;
    height: 370px;
  }
}
</style>
