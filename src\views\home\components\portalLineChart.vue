<template>
  <div :id="props.chartId" class="echarts"></div>
</template>

<script setup lang="ts">
import { ECharts, EChartsOption, init } from "echarts";
import { useEcharts } from "@/hooks/useEcharts";

interface ChartProp {
  data: any;
  nameList: string[];
}

const props = withDefaults(defineProps<{ chartId?: string }>(), {
  chartId: "portalLineChart"
});

const initChart = (
  data: ChartProp = {
    data: [],
    nameList: []
  }
) => {
  const charEle = document.getElementById(props.chartId) as HTMLElement;

  const charEch: ECharts = init(charEle);
  const nameList = data.nameList || [];
  const dataList = data.data || [];
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      formatter: "{a} <br/>{b} : {c} " //提示框提示的信息，{a}series内的名字，{b}为块状的名字，{c}为数值
    },
    grid: {
      top: "1%",
      left: "0",
      right: "0",
      bottom: "1%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category", //数据类型为不连续数据
        boundaryGap: false, //坐标轴两边是否留白
        axisLine: {
          show: false,
          lineStyle: {
            color: "#233e64" //x轴颜色
          }
        },
        axisLabel: {
          show: false
        },
        axisTick: { show: false }, //刻度点数轴
        data: nameList
      }
    ],
    yAxis: [
      {
        type: "value", //y轴数据类型为连续的数据
        splitLine: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        axisTick: { show: false }
      }
    ],
    series: [
      {
        name: "累计访问量",
        type: "line",
        smooth: true,
        symbolSize: 0, //数据点的大小，[0,0]//b表示宽度和高度
        lineStyle: {
          color: "#0052D9" // 线条颜色
        },
        areaStyle: {
          color: "#ccddf7"
        },
        data: dataList
      }
    ]
  };
  useEcharts(charEch, option);
};
defineExpose({
  initChart
});
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
:deep(.annual-tooTip) {
  box-sizing: border-box;
  height: 124px;
  padding: 5px 16px;
  background: rgb(3 11 27 / 80%);
  border: 1px solid #5a7dc0;
  .annual-month {
    display: inline-block;
    margin-bottom: 6px;
    font-family: PingFangSC-Regular, "PingFang SC";
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
  }
  .annual-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    .year-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 22px;
      margin-bottom: 5px;
      font-family: PingFangSC-Regular, "PingFang SC";
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      .year-dot {
        width: 8px;
        height: 8px;
        margin-right: 5px;
      }
      .year-name {
        width: 150px;
        margin: 0 2px;
      }
      .year-value {
        display: inline-block;
      }
    }
  }
}
</style>
