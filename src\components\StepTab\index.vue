<template>
  <el-row class="step-box">
    <div
      v-for="(item, index) in list"
      :key="item.title"
      class="step-item"
      :class="{ 'step-item-active': stepValue >= index + 1 }"
    >
      <div class="step-item-content" @click="itemClick(index + 1)">
        <div class="icon-box">
          <i class="iconfont" :class="item.icon" />
        </div>
        <p class="step-title">{{ item.title }}</p>
      </div>
    </div>
  </el-row>
</template>

<script lang="ts" setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  },
  stepValue: {
    type: Number,
    default: 1
  }
});

const emits = defineEmits(["itemClick"]);

const itemClick = (index: number) => {
  emits("itemClick", index);
};
</script>

<style lang="scss" scoped>
.step-box {
  display: flex;
  justify-content: center;
  margin: 0 auto 25px;
  .step-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 254px;
    &:first-child {
      margin-left: 0;
      &::before {
        display: none;
      }
    }
    &::before {
      position: absolute;
      top: 23px;
      right: 68px;
      width: 230px;
      height: 2px;
      content: " ";
      background-color: #e1e3e6;
    }
    .step-item-content {
      .icon-box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        color: #bbc0c6;
        background: #f4f5f7;
        border-radius: 24px;
        .iconfont {
          font-size: 26px;
        }
      }
      .step-title {
        margin-top: 5px;
        font-family: PingFangSC-Regular, "PingFang SC";
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: #9ca3ac;
      }
    }
    &-active {
      &::before {
        background-color: var(--el-color-primary);
      }
      .step-item-content {
        .icon-box {
          color: #ffffff;
          background: var(--el-color-primary);
        }
        .step-title {
          color: var(--el-color-primary);
        }
      }
    }
  }
}
</style>
