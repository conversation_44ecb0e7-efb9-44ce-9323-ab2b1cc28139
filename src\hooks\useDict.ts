type DictItemType = {
  createTime: string;
  creatorId: string;
  creatorName: string;
  dictCode: string;
  id: string;
  isDel: boolean;
  itemKey: string;
  itemValue: string;
  parentId: string;
  remark: string;
  sort: number;
  status: number;
  updateTime: string;
  updaterId: string;
  updaterName: string;
};

import { getDictionary } from "@/api/modules/system";
import useDictStore from "@/stores/modules/dict";
import { ref, toRefs } from "vue";

/**
 * 获取字典数据并翻译
 */
export function useDict(...args: string[]) {
  const res = ref<any>(useDictStore().dictsRes);
  return (() => {
    args.forEach(async (dictType, index) => {
      res.value[dictType] = [];
      if (!useDictStore().dictsMap.has(dictType)) {
        useDictStore().isDict(dictType, res.value[dictType]);
        const dicts = useDictStore().getDict(dictType);
        if (dicts) {
          res.value[dictType] = dicts;
        } else {
          const { data } = await getDictionary({ dictCode: dictType });
          res.value[dictType] = data;
          useDictStore().setDict(dictType, res.value[dictType]);
          useDictStore().isDict(dictType, res.value[dictType]);
        }
      } else {
        res.value[dictType] = useDictStore().dictsMap.get(dictType);
      }
    });
    return toRefs(res.value);
  })();
}

export function useDictLabel(dictData: DictItemType | any, value: any, emptyValue = "") {
  if (value === undefined) {
    return emptyValue;
  }
  if (value === null) {
    return emptyValue;
  }
  let actions = [];
  Object.keys(dictData).some(key => {
    if (dictData[key].itemKey == "" + value) {
      actions.push(dictData[key].itemValue);
      return true;
    }
  });
  if (actions.length === 0) {
    actions.push(value);
  }
  return actions.join("");
}
