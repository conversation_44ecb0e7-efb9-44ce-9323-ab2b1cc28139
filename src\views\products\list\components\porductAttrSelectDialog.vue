<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-09 17:43:59
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-28 16:55:03
 * @Description: file content
-->
<template>
  <div class="dialog">
    <div class="content">
      <div class="left">
        <div class="title">可选属性</div>
        <div class="all-data">
          <div class="data-search">
            <el-input v-model="searchData" class="w-50 m-2" placeholder="请输入关键字" :suffix-icon="Search" />
          </div>

          <el-table :data="tableData" ref="multipleTableRef" style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column v-for="item in propList" :prop="item.prop" :label="item.label" :key="item.prop" />
          </el-table>
        </div>
      </div>
      <div class="right">
        <div class="title">已选属性</div>
        <div class="all-data">
          <el-table :data="nowSelectData" style="width: 100%">
            <el-table-column v-for="(item, index) in propList" :prop="item.prop" :label="item.label" :key="index" />
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button
                  type="primary"
                  text
                  :icon="Delete"
                  style="padding: 0"
                  @click="deleteItem(scope.$index, scope.row)"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="btn-group">
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from "vue";
import { Search, Delete } from "@element-plus/icons-vue";
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  selectData: {
    type: Array,
    default: () => []
  },
  propList: {
    type: Array as any,
    default: () => []
  },
  dialogVisible: {
    type: Boolean,
    default: false
  }
});
const propsSelectData = ref([]);
const tableData = computed(() => {
  return props.data.filter((item: any) => {
    return item.attrCode.indexOf(searchData.value) != -1 || !searchData.value;
  });
});
const multipleTableRef = ref(null);
//关闭弹窗
const close = () => {
  emits("update:dialogVisible", false);
};
//确定选择
const confirm = () => {
  emits(
    "update:select-data",
    // nowSelectData.value.map(item => {
    //   const selectDataItem = props.selectData?.find(item2 => item2.id === item.id);
    //   if (selectDataItem) {
    //     return selectDataItem;
    //   } else {
    //     return item;
    //   }
    // })
    nowSelectData.value
  );
  emits("changeSelectData", nowSelectData.value);

  close();
};
//删除某一项
const deleteItem = (index: number, row: any) => {
  (multipleTableRef.value as any).toggleRowSelection(row);
  nowSelectData.value.splice(index, 1);
};

const emits = defineEmits(["update:select-data", "update:dialogVisible", "changeSelectData"]);
const searchData = ref("");
const nowSelectData = ref([]);
const checkTableData = () => {
  if (multipleTableRef.value) {
    tableData.value.forEach((item: any) => {
      if (propsSelectData.value.find((item2: any) => item2.id === item.id)) {
        (multipleTableRef.value as any).toggleRowSelection(item, true);
      }
    });
  }
};
onMounted(() => {
  setTimeout(() => {
    checkTableData();
  });
});
watch(
  [() => props.selectData],
  value => {
    propsSelectData.value =
      (props.selectData?.filter((item: any) => item.attrCode != "BUY_DURATION" && item.attrCode != "BUY_COUNT") as never[]) || [];
    if (multipleTableRef.value) {
      checkTableData();
    }
  },
  {
    immediate: true
  }
);

const handleSelectionChange = (data: Array<any>) => {
  (nowSelectData.value as any[]) = data;
};
</script>
<style lang="scss" scoped>
$dialog-border-color: #cccccc;
.content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  .left {
    width: 48%;
    border: 1px solid $dialog-border-color;
  }
  .right {
    width: 48%;
    border: 1px solid $dialog-border-color;
  }
  .title {
    padding: 8px 12px;
    border-bottom: 1px solid $dialog-border-color;
  }
  .data-search {
    padding: 8px 12px;
  }
}
.btn-group {
  display: flex;
  justify-content: center;
}
</style>
