<template>
  <div class="indicator-container">
    <el-radio-group v-model="activeBtn">
      <span class="title">{{ props.indicatorTitle }}：</span>
      <el-button
        type="default"
        plain
        size="small"
        :class="{ 'indicator-active': item.value == activeBtn }"
        v-for="(item, index) in props.list"
        :key="item.value + index"
        :label="item.value"
        @click="onChange(item)"
      >
        {{ item.label }}
      </el-button>
    </el-radio-group>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref, watch } from "vue";

interface ListType {
  label: string;
  value: string;
}

const props = defineProps({
  indicatorTitle: {
    type: String,
    default: () => ""
  },
  list: {
    type: Array as PropType<ListType[]>,
    default: () => []
  },
  active: {
    type: String,
    default: ""
  }
});

watch(
  () => props.active,
  () => {
    activeBtn.value = props.active;
  }
);

const activeBtn = ref(props.active);

const emits = defineEmits(["itemClick"]);

const onChange = (info: ListType) => {
  activeBtn.value = info.value;
  emits("itemClick", info);
};
</script>

<style lang="scss" scoped>
.indicator-container {
  .title {
    height: 20px;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 20px;
    color: #73787f;
  }
}
:deep(.el-button.is-plain) {
  margin-bottom: 12px;
}
:deep(.el-button.is-plain.indicator-active) {
  color: var(--el-color-primary);
  background-color: var(--el-fill-color-blank);
  border-color: var(--el-color-primary);
}
</style>
