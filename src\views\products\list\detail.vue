<template>
  <div class="card table-box" style="padding-left: 32px">
    <div class="header-title">
      {{ pageType === "add" ? "新增产品" : pageType === "edit" ? "产品编辑" : "查看详情" }}
    </div>
    <step :step-value="activeStep" :view-status="viewStatus" @get-step-value="handleShowStep"></step>
    <!-- 1.基本信息 -->
    <div v-show="activeStep === 1">
      <step-one
        @get-step-value="handleShowStep"
        :passdata="productDetailObj"
        :page-type="pageType"
        :view-status="viewStatus"
      ></step-one>
    </div>
    <div v-show="activeStep === 2">
      <step-two
        @get-step-value="handleShowStep"
        :passdata="productDetailObj"
        :page-type="pageType"
        :view-status="viewStatus"
      ></step-two>
    </div>
    <div v-show="activeStep === 3">
      <step-three
        @get-step-value="handleShowStep"
        :passdata="productDetailObj"
        :page-type="pageType"
        :view-status="viewStatus"
      ></step-three>
    </div>
  </div>
</template>

<script setup lang="ts" name="productManageListDetail">
import { computed, ref } from "vue";
import { useRoute } from "vue-router";
import step from "./components/step.vue";
import stepOne from "./components/stepOne.vue";
import stepTwo from "./components/stepTwo.vue";
import stepThree from "./components/stepThree.vue";
import { useProductStore } from "@/stores/modules/product";

const route = useRoute();
const productStore = useProductStore();
const activeStep = ref(1); // 进来默认在第一步
const { pageType } = route.query;
const viewStatus = pageType === "view"; // 是否是查看详情，用来控制输入框的disabled
console.log("route.params", route.params);
console.log("route.query", route.query);

const productId = route.params.id as string;
const productDetailObj = computed(() => productStore.productDetailObj?.[productId]); // 详情obj传给子组件，避免三个子组件拿三次

const handleShowStep = (value: number) => {
  activeStep.value = value;
};
</script>

<style lang="scss" scoped>
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
</style>
