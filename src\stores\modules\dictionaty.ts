import { defineStore } from "pinia";
import piniaPersistConfig from "@/config/piniaPersist";
import { getDictionary } from "@/api/modules/system";
import { DictionaryState } from "@/stores/interface";

export const useDicStore = defineStore({
  id: "cnud-dictionary",
  state: (): DictionaryState => ({
    cooperateDic: [], // 合作类型字典
    deliveryDic: [], // 交付方式字典
    timeDic: [], // 时长字典
    quantityDic: [], // 数量单位字典
    bannerActionType: [], // banner的交互形式
    newsStatus: [], // 最新动态状态
    orderTypeDic: [], // 订单类型字典
    orderStatusDic: [], // 订单状态字典
    auditStatusDic: [], // 审核状态字典
    orderPayWayDic: [], // 订单-支付方式字典
    jumpTypeDic: [], // 产品-跳转类型字典
    productOpenMethodDic: [], //产品-开通方式类型字典
    skuTypeDic: [], //产品-规格类型字典
    cooperatePlatformDic: [], // 合作平台
    resourcePoolStatus: [], //资源平台状态
    cloudPlatformList: [], //云平台账号
    productCustomerType: [], // 产品销售对象
    auditStatusEnterprise: [], // 合作方管理-审核状态字典
    industryTypeEnterprise: [], // 合作方管理-行业类型字典
    certificateApplyType: [], // 审核管理--申请类型
    applyAuditStatus: [], // 审核管理--资质管理状态
    introduceAuditStatus: [], // 审核管理--引入管理状态
    agreementAuditStatus: [], // 审核管理--合作协议状态
    providerAgreementStatus: [], // 合作协议管理--状态
    dataPermission: [], // 数据权限字典
    auditOperation: [], // 审核操作
    productAuditStatus: [], // 审核状态
    bannerAuditAction: [], // 审核操作 AUDIT_ACTION
    auditApplicantOperation: [], // 产品属性变更类型
    noticeStatus: [], //消息状态
    noticeAuditStatus: [], //消息审核状态
    orderRegion: [],
    orderScore: [], // 订单评价分数
    noticeTypeDic: [], //消息类型
    orderProdType: [], // 产品类型
    orderState: [], // 订单状态
    probationFlagDic: [], // 是否支持试用字典
    operateStatus: [], //政企中台订单处理状态
    requireReleaseStatus: [], // 需求供需-需求方案状态
    requireReleaseBusinessStatus: [], // 需求供需-需求方案状态(商机管理)
    requireType: [], // 需求类型
    jbgsRequireType: [], // 需求类型
    demandConstructionAddress: [], // 实施地址
    demandAcceptStatus: [] // 需求供需-方案采纳状态
  }),
  actions: {
    /* 产品销售对象 */
    async queryProdCustomerType() {
      let { data }: any = await getDictionary({ dictCode: "pms_product_customer_type" });
      console.log(data);

      this.productCustomerType = data;
    },
    // 合作类型
    async getCooperateDicList() {
      let { data }: any = await getDictionary({ dictCode: "pms_cooperate_type" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.cooperateDic = data;
    },
    // 交付方式
    async getDeliveryDicList() {
      const { data }: any = await getDictionary({ dictCode: "pms_delivery_method" });
      this.deliveryDic = data;
    },
    // 时长单位
    async getTimeDicList() {
      const { data }: any = await getDictionary({ dictCode: "pms_time_uit" });
      this.timeDic = data;
    },
    // 数量单位
    async getQuantityDicList() {
      const { data }: any = await getDictionary({ dictCode: "sku_quantity_uit" });
      this.quantityDic = data;
    },
    // banner的交互形式
    async getBannerActionType() {
      if (this.bannerActionType.length) {
        return this.bannerActionType;
      }
      const { data }: any = await getDictionary({ dictCode: "banner_action" });
      this.bannerActionType = data;
      return data;
    },
    // 最新动态状态
    async getNewsStatus() {
      if (this.newsStatus.length) {
        return this.newsStatus;
      }
      const { data }: any = await getDictionary({ dictCode: "news_status" });
      this.newsStatus = data;
      return data;
    },
    // 订单类型
    async getOrderType() {
      let { data }: any = await getDictionary({ dictCode: "ord_order_type" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.orderTypeDic = data;
    },
    // 订单状态
    async getOrderStatus() {
      let { data }: any = await getDictionary({ dictCode: "ord_order_status" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.orderStatusDic = data;
    },
    // 审核状态
    async getAuditStatus() {
      let { data }: any = await getDictionary({ dictCode: "ord_audit_status" });
      data.forEach((item: any) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.auditStatusDic = data;
    },
    // 订单-支付方式
    async getOrderPayDicList() {
      let { data }: any = await getDictionary({ dictCode: "ord_pay_type" });
      data.forEach((item: any) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.orderPayWayDic = data;
    },
    // 产品-跳转类型
    async getJumpTypeDicList() {
      let { data }: any = await getDictionary({ dictCode: "product_jump_type" });
      this.jumpTypeDic = data;
    },
    // 产品-产品开通接口字典
    async getProductOpenMethodDicList() {
      let { data }: any = await getDictionary({ dictCode: "product_open_method" });
      this.productOpenMethodDic = data;
    },
    // 产品-产品规格类型字典
    async getskuTypeDicList() {
      let { data }: any = await getDictionary({ dictCode: "sku_type" });
      this.skuTypeDic = data;
    },
    // 资源池管理-状态
    async getResourceOrderStatus() {
      let { data }: any = await getDictionary({ dictCode: "resource_pool_status" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.resourcePoolStatus = data;
    },
    // 获取云平台账号
    async getCloudPlatformList() {
      let { data }: any = await getDictionary({ dictCode: "cloudProvider" });
      this.cloudPlatformList = data;
    },
    async getCooperatePlatformList() {
      try {
        const { data }: any = await getDictionary({ dictCode: "sys_platform_account" });
        this.cooperatePlatformDic = data.map(item => {
          const { itemKey, itemValue } = item;
          return {
            label: itemValue,
            value: itemKey
          };
        });
      } catch (e) {}
    },
    //  合作方管理-审核状态字典
    async getAuditStatusEnterprise() {
      let { data }: any = await getDictionary({ dictCode: "dict_provider_status" }); // pms_provider_agreement_audit_status
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.auditStatusEnterprise = data;
    },
    //  合作方管理-行业类型字典
    async getIndustryTypeEnterprise() {
      let { data }: any = await getDictionary({ dictCode: "dict_industry_type" }); // pms_provider_agreement_audit_status
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.industryTypeEnterprise = data;
    },
    // 审核管理-申请类型
    async getCertificateApplyType() {
      let { data }: any = await getDictionary({ dictCode: "dict_certificate_apply_type" });
      data.forEach((item: any = {}) => {
        item.value = Number(item.itemKey);
        item.label = item.itemValue;
      });
      this.certificateApplyType = data;
    },
    // 审核管理-资质管理状态
    async getApplyAuditStatus() {
      let { data }: any = await getDictionary({ dictCode: "dict_provider_apply_audit_status" });
      data.forEach((item: any = {}) => {
        item.value = Number(item.itemKey);
        item.label = item.itemValue;
      });
      this.applyAuditStatus = data;
    },
    // 审核管理-引入管理状态
    async getIntroduceAuditStatus() {
      let { data }: any = await getDictionary({ dictCode: "dict_provider_introduce_audit_status" });
      data.forEach((item: any = {}) => {
        item.value = Number(item.itemKey);
        item.label = item.itemValue;
      });
      this.introduceAuditStatus = data;
    },
    // 审核管理-合作协议管理状态
    async getAgreementAuditStatus() {
      let { data }: any = await getDictionary({ dictCode: "dict_provider_agreement_audit_status" });
      data.forEach((item: any = {}) => {
        item.value = Number(item.itemKey);
        item.label = item.itemValue;
      });
      this.agreementAuditStatus = data;
    },
    // 合作协议管理-状态
    async getProviderAgreementStatus() {
      let { data }: any = await getDictionary({ dictCode: "dict_provider_agreement_status" });
      data.forEach((item: any = {}) => {
        item.value = Number(item.itemKey);
        item.label = item.itemValue;
      });
      this.providerAgreementStatus = data;
    },
    // 合作协议管理-状态
    async getDataPermission() {
      let { data }: any = await getDictionary({ dictCode: "dit_data_permission" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.dataPermission = data;
    },
    // 产品审核操作
    async getAuditOperation() {
      let { data }: any = await getDictionary({ dictCode: "audit_operation" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.auditOperation = data;
    },
    // 产品审核状态
    async getProductAuditStatus() {
      let { data }: any = await getDictionary({ dictCode: "audit_status" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.productAuditStatus = data;
    },
    // banner审核-审核操作
    async getBannerAuditAction() {
      let { data }: any = await getDictionary({ dictCode: "AUDIT_ACTION" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.bannerAuditAction = data;
    },
    //消息状态
    async getNoticeStatus() {
      let { data }: any = await getDictionary({ dictCode: "notice_status" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.noticeStatus = data;
    },
    //消息审核状态
    async getNoticeAuditStatus() {
      let { data }: any = await getDictionary({ dictCode: "notice_audit_status" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.noticeAuditStatus = data;
    },
    //消息类型
    async getNoticeTypeDicList() {
      let { data }: any = await getDictionary({ dictCode: "notice_biz_type" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.noticeTypeDic = data;
    },
    // 产品属性变更类型
    async getAuditApplicantOperation() {
      let { data }: any = await getDictionary({ dictCode: "audit_applicant_operation" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.auditApplicantOperation = data;
    },
    // 订单区域
    async getOrderRegion() {
      let { data }: any = await getDictionary({ dictCode: "ORDER_REGION" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.orderRegion = data;
    },
    async getprobationFlagDic() {
      let { data }: any = await getDictionary({ dictCode: "yes_no_type" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.probationFlagDic = data;
    },
    // 订单评价
    async getOrderState() {
      let { data }: any = await getDictionary({ dictCode: "ORDER_STATE " });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.orderState = data;
    },
    async getOperateStatus() {
      let { data }: any = await getDictionary({ dictCode: "zq_operate_status" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.operateStatus = data;
    },
    // 订单评价
    async getOrderScore() {
      let { data }: any = await getDictionary({ dictCode: "ORDER_SCORE " });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.orderScore = data;
    },
    // 产品类型
    async getProdType() {
      let { data }: any = await getDictionary({ dictCode: "ORDER_PRODTYPE " });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.orderProdType = data;
    },
    // 需求发布审核状态
    async getRequireReleaseStatus() {
      let { data }: any = await getDictionary({ dictCode: "opt_demand_audit_status_for_portal" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.requireReleaseStatus = data;
      return data;
    },
    // 需求发布审核状态
    async getRequireReleaseBusinessStatus() {
      let { data }: any = await getDictionary({ dictCode: "opt_demand_audit_status" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.requireReleaseBusinessStatus = data;
      return data;
    },
    // 需求类型
    async getRequireType() {
      let { data }: any = await getDictionary({ dictCode: "opt_demand_demand_type" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.requireType = data;
    },

    // 需求类型
    async getJbgsRequireType() {
      let { data }: any = await getDictionary({ dictCode: "opt_ocl_demand_type" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.jbgsRequireType = data;
    },
    // 实施地址
    async getDemandConstructionAddress() {
      let { data }: any = await getDictionary({ dictCode: "opt_demand_construction_address" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.demandConstructionAddress = data;
    },
    // 实施地址
    async getDemandAcceptStatus() {
      let { data }: any = await getDictionary({ dictCode: "opt_demand_accept_status" });
      data.forEach((item: any = {}) => {
        item.value = item.itemKey;
        item.label = item.itemValue;
      });
      this.demandAcceptStatus = data;
    }
  },

  persist: piniaPersistConfig("cnud-dictionary")
});
