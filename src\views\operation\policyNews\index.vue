<template>
  <div class="policy-box table-box">
    <pro-table
      ref="proTableRef"
      title="政策消息列表"
      :columns="columns"
      :init-param="initParam"
      :request-api="getTableList"
      @sort-change="sortChange"
      :tool-button="false"
    >
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => handleOpen({}, 'add')"> 新增政策消息 </el-button>
      </template>
      <template #status="{ row }">
        <custom-tag
          :type="statusTypeMap[row.status]"
          :status="row.status"
          :label="useDictLabel(news_status, row.status)"
        ></custom-tag>
      </template>
      <template #auditStatus="{ row }">
        <el-popover placement="top" title="" :width="400" trigger="hover">
          <div>
            <div>审核人：{{ row.auditName }}</div>
            <div>审核状态：{{ useDictLabel(policy_audit_status, row.auditStatus) }}</div>
            <div>审核意见：{{ row.auditMsg }}</div>
            <div>审核时间：{{ row.auditTime }}</div>
          </div>
          <template #reference>
            {{ useDictLabel(policy_audit_status, row.auditStatus) }}
          </template>
        </el-popover>
      </template>
      <template #operation="{ row }">
        <auth-button :row-data="row" :auth-list="authList"></auth-button>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="policyNews">
import { ref, watch, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { type Sort } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import { type ProTableInstance, type ColumnProps } from "@/components/ProTable/interface/index";
import { getPolicyPageList, revokePolicy, deletePolicy } from "@/api/modules/business/policyNews";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { IAuthButtonItem } from "@/components/AuthButton/typings";
import { useDict, useDictLabel } from "@/hooks/useDict";
import CustomTag from "@/components/CustomTag/index.vue";
import { policyNewsStorage } from "@/utils/storage/edit";

const { news_status, policy_audit_status } = useDict("news_status", "policy_audit_status");
const statusTypeMap: { [key: string]: string } = {
  "1": "blue",
  "2": "yellow",
  "3": "green",
  "4": "grey",
  "5": "red",
  "6": "blue"
};
enum StatusType {
  Saved = "1",
  Published = "3",
  Withdrawn = "4"
}

const { BUTTONS } = useAuthButtons();

const router = useRouter();
const route = useRoute();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTableRef = ref<ProTableInstance>();
const initParam = reactive({
  ascs: "",
  descs: ""
});

//排序
const sortChange = (data: Sort) => {
  const sortObject = {
    ascending: () => {
      initParam.ascs = data.prop;
      initParam.descs = "";
    },
    descending: () => {
      initParam.ascs = "";
      initParam.descs = data.prop;
    },
    default: () => {
      initParam.ascs = "";
      initParam.descs = "";
    }
  };
  sortObject[data.order || "default"]?.();
};

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return Promise.resolve();
  }
  const { title, status, auditStatus, creatorName, minCreateTime, maxCreateTime, minPublishTime, maxPublishTime, current, size } =
    search;
  return getPolicyPageList({
    search: title,
    status,
    auditStatus,
    searchCreatorName: creatorName,
    minCreateTime,
    maxCreateTime,
    minPublishTime,
    maxPublishTime,
    current,
    size,
    newsType: "2",
    ...initParam
  });
};

const authList: IAuthButtonItem[] = [
  {
    contentName: "查看",
    iconClass: "View",
    itemClick: scope => handleOpen(scope, "view"),
    authName: "policyNewsView"
  },
  {
    contentName: "撤回",
    iconClass: "Revoke",
    itemClick: scope => handleRevoke(scope),
    show: scope => scope.status === StatusType.Published,
    authName: "policyNewsRevoke"
  },
  {
    contentName: "编辑",
    iconClass: "Edit",
    itemClick: scope => handleOpen(scope, "edit"),
    show: scope => [StatusType.Saved, StatusType.Withdrawn].includes(scope.status),
    authName: "policyNewsEdit"
  },
  {
    contentName: "删除",
    iconClass: "Delete",
    itemClick: scope => handleDelete(scope),
    show: scope => [StatusType.Saved, StatusType.Withdrawn].includes(scope.status),
    authName: "policyNewsDelete"
  }
];

// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: "title",
    label: "政策标题",
    align: "left",
    search: { el: "input", props: { maxLength: 20 } }
  },
  {
    prop: "coverUrl",
    label: "政策封面",
    align: "left",
    width: 120,
    render: (scope: any) => {
      return <img class="row-img" src={scope.row.coverUrl} />;
    }
  },
  {
    prop: "status",
    label: "状态",
    align: "left",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {news_status.value.map((item: any) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    align: "left",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {policy_audit_status.value.map((item: any) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "creatorName",
    label: "创建人员",
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    width: 180,
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY/MM/DD HH:mm:ss",
        valueFormat: "YYYY/MM/DD HH:mm:ss"
      },
      transform: (value: any) => {
        return {
          minCreateTime: value[0],
          maxCreateTime: value[1]
        };
      }
    },
    sortable: true
  },
  {
    prop: "publishTime",
    align: "left",
    label: "发布时间",
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY/MM/DD HH:mm:ss",
        valueFormat: "YYYY/MM/DD HH:mm:ss"
      },
      transform: (value: any) => {
        return {
          minPublishTime: value[0],
          maxPublishTime: value[1]
        };
      }
    },
    width: 180,
    sortable: true
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 130 }
];

// 删除政策信息
const handleDelete = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此项政策消息？", "确认操作").then(async () => {
    await deletePolicy([data.id]);
    ElMessage.success("操作成功！");
    proTableRef.value?.getTableList();
  });
};

// 撤销政策信息
const handleRevoke = async (params: any) => {
  ElMessageBox.confirm("请确认是否撤回此项政策消息？", "确认操作").then(async () => {
    await revokePolicy(params.id);
    ElMessage.success("操作成功！");
    proTableRef.value?.getTableList();
  });
};

policyNewsStorage.clearExpired();

const handleOpen = (data: any, operate: string = "add") => {
  if (Object.keys(data).length !== 0) {
    data.isCheck = operate === "edit";
    policyNewsStorage.set(data.id, data);
    router.push({
      path: "/operation/policyNews/edit",
      query: {
        id: data.id,
        type: operate
      }
    });
  } else {
    router.push({
      path: "/operation/policyNews/edit",
      query: {
        type: operate
      }
    });
  }
};

watch(
  () => route.path,
  (path: string) => {
    if (path === "/operation/policyNews") {
      proTableRef.value?.getTableList();
    }
  }
);
</script>
<style lang="scss">
.policy-box .row-img {
  width: 100%;
}
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
