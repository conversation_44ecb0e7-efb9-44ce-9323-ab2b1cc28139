<template>
  <div class="info-box card">
    <div class="box-title">
      <div class="title">客户信息</div>
      <div class="card-btn">
        <slot name="button"> </slot>
      </div>
    </div>
    <div class="infos">
      <el-row>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">客户账号：</span>
            <span class="info-text">{{ customerInfo.portalUser?.account }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="1">
          <div class="info-item">
            <span class="info-label">客户名称：</span>
            <span class="info-text">{{
              customerInfo.portalUser?.verifyLevel === 3
                ? customerInfo.personalCertificate?.realName
                : customerInfo.enterpriseCertificate?.companyName
            }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="2">
          <div class="info-item">
            <span class="info-label">认证状态：</span>
            <span class="info-text">
              {{ useDictLabel(portal_verify_level, customerInfo.portalUser?.verifyLevel) }}
            </span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">联系人姓名：</span>
            <span class="info-text">{{
              customerInfo.portalUser?.verifyLevel === 3
                ? customerInfo.personalCertificate?.realName
                : customerInfo.enterpriseCertificate?.contactName
            }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="1">
          <div class="info-item">
            <span class="info-label">手机号码：</span>
            <!-- 2024.02.01 修改电话号码 -->
            <span class="info-text">{{ phone }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="2">
          <div class="info-item">
            <span class="info-label">用户邮箱：</span>
            <span class="info-text">{{ customerInfo.portalUser?.email }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDict, useDictLabel } from "@/hooks/useDict";
const { portal_verify_level } = useDict("portal_verify_level");

const props = defineProps({
  phone: {
    type: String,
    default: ""
  },
  customerInfo: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
</script>

<style scoped lang="scss">
@import "../order.module.scss";
</style>
