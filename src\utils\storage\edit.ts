import { commonKey } from "./storageKey";

interface Obj {
  [key: string]: any;
}

class EditStorage {
  private key: string = "";
  private expiredTimeStamp = 5 * 24 * 60 * 60 * 1000;
  private expiredKey = "storageSetedTime";
  private storage = window.localStorage;
  constructor(key: string) {
    this.key = key;
  }
  public set(id: string, value: Obj | undefined) {
    const data: { [key: string]: any } = this.getAll();
    if (value) {
      value[this.expiredKey] = new Date().getTime();
    }
    data[id] = value;
    this.storage.setItem(this.key, JSON.stringify(data));
  }
  public getAll(): Obj {
    const data = this.storage.getItem(this.key);
    if (data) {
      return JSON.parse(data);
    }
    return {};
  }
  public get(id: string): Obj | undefined {
    const data = this.storage.getItem(this.key);
    if (data) {
      return JSON.parse(data)[id];
    }
    return undefined;
  }
  public clearAll() {
    this.storage.removeItem(this.key);
  }
  public clearExpired() {
    const data = this.getAll();
    for (let key in data) {
      const item = data[key];
      if (item && item[this.expiredKey] < new Date().getTime() - this.expiredTimeStamp) {
        delete data[key];
      }
    }
    this.storage.setItem(this.key, JSON.stringify(data));
  }
  public clear(id: string) {
    this.set(id, undefined);
  }
}
export const bannerStorage = new EditStorage(commonKey + "Banner");
export const productAreaStorage = new EditStorage(commonKey + "ProductArea");
export const newsStorage = new EditStorage(commonKey + "NewsArea");
export const documentStorage = new EditStorage(commonKey + "DocumentArea");
export const messageStorage = new EditStorage(commonKey + "MessageArea");
export const productAgreementStorage = new EditStorage(commonKey + "ProductAgreement");
export const policyNewsStorage = new EditStorage(commonKey + "policyNews");

export const bannerReviewStorage = new EditStorage(commonKey + "BannerReview");
export const newsReviewStorage = new EditStorage(commonKey + "newsReview");
export const messageReviewStorage = new EditStorage(commonKey + "messageReview");
