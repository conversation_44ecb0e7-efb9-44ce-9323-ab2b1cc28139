<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="解决方案列表"
      :columns="columns"
      :request-api="getTableList"
      :request-auto="false"
      :tool-button="false"
      @sort-change="changeSort"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button v-auth="'add'" plain :icon="Plus" @click="creatSolution"> 发布 </el-button>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
        <el-button
          v-auth="'delete'"
          plain
          :icon="Delete"
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          删除
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toDetail(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'shelf'">
          <el-tooltip :content="scope.row.publishStatus === 2 ? '下架' : '上架'" placement="top">
            <i
              :class="['iconfont2 opera-icon', scope.row.publishStatus === 2 ? 'icon-lineicon_download' : 'icon-lineicon_upload']"
              @click="putOn(scope.row)"
            ></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="deleteSo(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
    <EditSortDialog
      v-if="saveItem && showEditDialog"
      v-model:show-dialog="showEditDialog"
      :sort="saveItem.sort"
      @save="saveSort"
    />
  </div>
</template>

<script setup lang="tsx" name="solutionList">
import { ref, onMounted, computed, onActivated } from "vue";
import { useRouter } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import { useHandleData } from "@/hooks/useHandleData";
import { useDownload } from "@/hooks/useDownload";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Plus, Delete, Download } from "@element-plus/icons-vue";
import {
  deleteSolution,
  exportSolution,
  getSolutionList,
  solutionPutOn,
  solutionCategoryTree,
  solutionUpdateSort
} from "@/api/modules/solution";
import { useProductStore } from "@/stores/modules/product";
import { useDicStore } from "@/stores/modules/dictionaty";
import { productStatus } from "@/utils/serviceDict";
import { ElMessage, ElMessageBox } from "element-plus";
import { Soluiton } from "@/api/interface/solution";
import CustomTag from "@/components/CustomTag/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import EditSortDialog from "@/components/EditSortDialog/index.vue";

const { BUTTONS } = useAuthButtons();

const router = useRouter();
const proTable = ref<ProTableInstance>();
const productStore = useProductStore();
const dictionaryStore = useDicStore();

const showEditDialog = ref<boolean>(false);
const saveItem = ref<any>({});

onMounted(() => {
  // 先获取字典
  dictionaryStore.getCooperateDicList(); // 合作类型
  // 产品服务商列表
  // productStore.getProviderList();
  // 然后
  // proTable.value?.getTableList();
});
// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  proTable.value?.getTableList();
});
// 合作类型字典-enum
const cooperateDic: any = computed(() => dictionaryStore.cooperateDic);
// 产品服务商列表
// const providerList: any = computed(() => productStore.providerList);
// 表格配置项
const columns: ColumnProps<Soluiton.SolutionItem>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "solutionCode", label: "方案编码", width: 110 },
  { prop: "solutionName", label: "方案名称", width: 160, search: { el: "input", props: { maxlength: "20" } } },
  { prop: "cooperateType", label: "合作类型", width: 90, enum: cooperateDic },
  { prop: "provider", label: "方案服务商", width: 100 },
  {
    prop: "categoryName",
    label: "方案分类",
    width: 290,
    enum: solutionCategoryTree,
    isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格
    search: { el: "cascader", key: "categoryIdList" },
    fieldNames: {
      value: "id",
      label: "name"
    }
  },
  {
    prop: "publishStatus",
    label: "上架状态",
    width: 110,
    search: { el: "select" },
    sortable: true,
    // 字典，可格式化单元格内容，还可以作为搜索框的下拉选项（字典可以为 API 请求函数）
    enum: productStatus, // 产品上架状态字典
    render: scope => {
      return (
        // <el-tag type={scope.row.publishStatus === 2 ? "success" : "danger"}>
        //   {scope.row.publishStatus === 2 ? "上架" : "下架"}
        // </el-tag>
        <CustomTag
          type={scope.row.publishStatus === 2 ? "success" : ""}
          status={String(scope.row.publishStatus)}
          label={scope.row.publishStatus === 2 ? "上架" : "下架"}
        ></CustomTag>
      );
    }
  },
  {
    prop: "sort",
    label: "排序",
    align: "left",
    render: (scope: any) => {
      return (
        <span class={"sort-btn"} onClick={() => setShowEditDialog(scope.row)}>
          {scope.row.sort}
        </span>
      );
    }
  },
  { prop: "creatorName", label: "创建人员", width: 95 },
  {
    prop: "createTime",
    label: "创建时间",
    sortable: true,
    width: 170,
    search: {
      el: "date-picker",
      span: 2,
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" }
    }
  },
  {
    prop: "updateTime",
    label: "上架时间",
    width: 170,
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 200 }
];

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
  newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
  delete newParams.createTime;
  if (newParams.categoryIdList?.length > 0) {
    newParams.categoryId = newParams.categoryIdList[newParams.categoryIdList.length - 1];
    delete newParams.categoryIdList;
  }
  newParams.descs = "createTime";
  return getSolutionList(newParams);
};
// 批量删除
const batchDelete = async (ids: string[]) => {
  await useHandleData(deleteSolution, { ids: ids.join(",") }, "删除所选解决方案");
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
const deleteSo = async (row: any = {}) => {
  if (row.publishStatus === 2) {
    ElMessageBox.alert("上架状态不可删除，请先下架后再操作。", "提示", { type: "warning" }).then(() => {});
    return;
  }
  await useHandleData(deleteSolution, { ids: row.id }, `删除<${row.solutionName}>`);
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
const changeSort = (data: any) => {
  console.log(data);
};

// 上下架操作
const putOn = async (row: any = {}) => {
  let handlePublishStr = row.publishStatus === 2 ? "下架" : "上架";
  let handlePublishCode = row.publishStatus === 2 ? 1 : 2;
  await useHandleData(
    solutionPutOn,
    { id: row.id, publishStatus: handlePublishCode },
    `${handlePublishStr} <${row.solutionName}>`
  );
  proTable.value?.getTableList();
};

const toDetail = (row: any = {}) => {
  if (row.publishStatus === 2) {
    ElMessageBox.alert("上架状态不可编辑，请先下架后再操作。", "提示", { type: "warning" }).then(() => {});
    return;
  }
  productStore.getSolutionDetail({ id: row.id });
  router.push(`/solution/solutionList/detail/${row.id}?pageType=edit`);
};
const toView = (row: any = {}) => {
  productStore.getSolutionDetail({ id: row.id });
  router.push(`/solution/solutionList/detail/${row.id}?pageType=view`);
};
const creatSolution = () => {
  router.push(`/solution/solutionList/detail/add?pageType=add`);
};
// 导出解决列表
const downloadFile = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
  newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
  delete newParams.createTime;
  if (newParams.categoryIdList?.length > 0) {
    newParams.categoryId = newParams.categoryIdList[newParams.categoryIdList.length - 1];
    delete newParams.categoryIdList;
  }
  newParams.descs = "createTime";
  ElMessageBox.confirm("确认导出解决方案列表?", "提示", { type: "warning" }).then(() => useDownload(exportSolution, newParams));
};
const setShowEditDialog = (data: any) => {
  saveItem.value = data;
  showEditDialog.value = true;
};
const saveSort = async (sort: number) => {
  await solutionUpdateSort({ id: saveItem.value.id, sort: sort });
  ElMessage.success("更改排序成功！");
  proTable.value?.getTableList();
};
</script>

<style lang="scss">
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
