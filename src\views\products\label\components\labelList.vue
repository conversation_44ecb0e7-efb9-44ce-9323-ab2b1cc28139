<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="列表"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      :request-auto="false"
      :tool-button="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button v-auth="'add'" plain :icon="Plus" @click="openDrawer(1, scope.rows)"> 新增 </el-button>
        <el-button
          v-auth="'delete'"
          plain
          :icon="Delete"
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          删除
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="openDrawer(2, scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i class="iconfont2 opera-icon icon-lajitong" @click="deleteLabel(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>
    <add-edit-label
      v-model:visible="labelDialogVisible"
      :params="labelDialogParams"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-label>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, PropType, watch } from "vue";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Plus, Delete, EditPen } from "@element-plus/icons-vue";
import addEditLabel from "./addEditLabel.vue";
import { editType, editTypeChinese } from "@/enums/dialogTypeEnum";
import { getTagList, deleteTag } from "@/api/modules/label";
import { Label } from "@/api/interface/label";
import { ElMessage } from "element-plus";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

// import { ResPage } from "@/api/interface/index";
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
interface labelDialog {
  id: string | number | undefined;
  title: string;
  type: number;
}
// interface labelData {
//   id: string;
//   name: string;
//   labelType: string;
//   labelTypeName: string;
//   linkProductNum: number;
// }
const proTable = ref<ProTableInstance>();
// const tableData = ref<Array<labelData>>([
//   {
//     id: "",
//     labelType: "1",
//     labelTypeName: "",
//     labelName: "测试12",
//     linkProductNum: 0
//   }
// ]);
let total = 0;
const props = defineProps({
  typeItem: {
    type: Object as PropType<Label.TagType>,
    default: () => {}
  }
});
watch(
  () => props.typeItem,
  () => {
    props.typeItem?.id && proTable.value?.getTableList();
  }
);
const onCancel = () => {};
const onConfirm = () => {
  proTable.value?.getTableList();
};
const dataCallback = (data: any) => {
  total = data.total || 0;
  return data;
  // return data.record;
};
const labelDialogVisible = ref(false);
const labelDialogParams = ref<labelDialog>({
  id: "",
  title: "新增",
  type: editType["add"]
});
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams = {
    typeId: props.typeItem.id,
    ...newParams
  };
  return getTagList(newParams);
};
const initParam = reactive({
  typeId: props.typeItem.id
});

// const searchParam = {};
// const search = () => {};
// const reset = () => {};
// 表格配置项
const columns: ColumnProps<Label.TagItem>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { type: "index", label: "序号", width: 80 },
  { prop: "typeName", label: "标签分类" },
  { prop: "name", label: "标签名称", search: { el: "input" } },
  { prop: "cnt", label: "关联产品数" },
  { prop: "operation", label: "操作", fixed: "right", width: 150 }
];

// 批量删除
const batchDelete = async (id: string[]) => {
  await useHandleData(deleteTag, { ids: id.join(",") }, "删除所选标签", "warning", { showRequestMsg: true });
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
const deleteLabel = async (data: any) => {
  await useHandleData(deleteTag, { ids: data.id as string }, `删除${data.name}标签信息`, "warning", { showRequestMsg: true });
  proTable.value?.getTableList();
};

const openDrawer = (operateType: number, row: Partial<Label.TagItem> = {}) => {
  if (operateType === 1 && total >= 20) {
    ElMessage.error("同个标签分类下最多存在20个标签");
    return;
  }
  const params = {
    title: editTypeChinese[operateType],
    type: operateType,
    typeId: props.typeItem.id,
    id: row.id,
    name: row.name
  };
  labelDialogVisible.value = true;
  labelDialogParams.value = params;
};
</script>
