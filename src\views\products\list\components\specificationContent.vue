<template>
  <div class="complex-content-box">
    <div class="name-box">
      <el-button type="primary" class="add-button" @click="addItem" :icon="CirclePlus" :disabled="viewControl">
        添加规格
      </el-button>
      <span class="tips"> <slot name="nameTips"></slot></span>
    </div>
    <div class="content-item" v-for="(item, index) in dataList" :key="index + 'complex'">
      <div class="item-title">
        <div class="name">{{ item.skuName || "规格名称" }}</div>
        <div class="right-btn">
          <el-button
            class="icon"
            link
            :disabled="viewControl"
            v-if="index !== dataList.length - 1"
            @click="xiayiItem(index, dataList)"
          >
            <i class="iconfont2 icon-xiayi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link :disabled="viewControl" v-if="index !== 0" @click="shangyiItem(index, dataList)">
            <i class="iconfont2 icon-shangyi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link :icon="Delete" @click="deleteItem(index)" :disabled="viewControl"></el-button>
        </div>
      </div>
      <el-form ref="skuRuleFormRef" class="item-form" :model="item" :rules="rules" label-width="140px">
        <el-form-item label="规格名称" prop="skuName">
          <el-input v-model="item.skuName" :disabled="viewControl" maxlength="30" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="外部产品编码" prop="externalCode" v-if="isExternal">
          <el-input v-model="item.externalCode" :disabled="viewControl" clearable />
        </el-form-item>
        <el-form-item label="规格配置" prop="skuConfig" v-if="!isExternal">
          <el-input v-model="item.skuConfig" :disabled="viewControl" clearable />
        </el-form-item>
        <!-- <el-form-item label="CBSS产品ID" prop="phoneBillPayCode" v-if="params.payType.indexOf('1') != -1">
          <el-input v-model="item.phoneBillPayCode" :disabled="viewControl" clearable />
        </el-form-item> -->
        <el-form-item label="规格单价" prop="skuPrice" v-if="!isExternal">
          <el-input-number
            v-model="item.skuPrice"
            :disabled="viewControl"
            :min="0"
            :max="********"
            :controls="false"
            :precision="3"
            auto-complete="off"
            clearable
            style="width: 150px; margin-right: 15px"
          ></el-input-number>
          <span>元</span>
        </el-form-item>
        <el-form-item label="折扣价" prop="skuDiscountPrice" v-if="!isExternal">
          <el-input-number
            v-model="item.skuDiscountPrice"
            :disabled="viewControl"
            :min="0"
            :max="********"
            :controls="false"
            :precision="3"
            auto-complete="off"
            clearable
            style="width: 150px; margin-right: 15px"
          />
          <span>元</span>
        </el-form-item>

        <!-- 产品支付方式为“话费支付”时，显示该选项，否则隐藏-->
        <el-form-item v-if="payTypeCurrent?.includes('1') && !isExternal" label="CBSS产品ID" prop="phoneBillPayCode">
          <el-input v-model="item.phoneBillPayCode" :disabled="viewControl" maxlength="20" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="服务商产品编码" v-if="!isExternal">
          <el-input v-model="item.providerSkuId" maxlength="200" :disabled="viewControl" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="计费模式" prop="billingMode" v-if="!isExternal">
          <el-radio-group v-model="item.billingMode" :disabled="viewControl" @change="changeBillingMode(item)">
            <el-radio
              v-for="(billingModeType, billingModeTypeIndex) in sku_billing_mode_type"
              :key="billingModeTypeIndex + 'billingMode'"
              :label="billingModeType.itemKey"
              >{{ billingModeType.itemValue }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="可选计费项" class="jf-item">
          <el-checkbox-group v-model="item.creatJiFeiList" :disabled="viewControl" @change="jfCheckbox(item)">
            <el-checkbox label="quantityCounting">购买数量</el-checkbox>
            <el-checkbox label="timeCounting">购买时长</el-checkbox>
          </el-checkbox-group>
          <div class="jf-box">
            <div>
              <span class="jf-label">购买数量限制：</span>
              <el-input
                v-model="item.minQuantity"
                :disabled="viewControl || !item.creatJiFeiList?.includes('quantityCounting')"
                placeholder="开始数值"
                v-d-input-int
                v-d-input-max="100000"
                maxlength="6"
                style="width: 80px"
                @blur="quantityChange(item)"
              />
              <span style="margin: 0 7px">~</span>
              <el-input
                v-model="item.maxQuantity"
                :disabled="viewControl || !item.creatJiFeiList?.includes('quantityCounting')"
                v-d-input-int
                v-d-input-max="100000"
                placeholder="结束数值"
                maxlength="6"
                style="width: 80px; margin-right: 20px"
                @blur="quantityChange(item)"
              />
              <span class="jf-label">数量步长：</span>
              <el-input
                v-model="item.quantityStep"
                placeholder="输入步长"
                :disabled="viewControl || !item.creatJiFeiList?.includes('quantityCounting')"
                v-d-input-int
                v-d-input-max="100000"
                maxlength="6"
                style="width: 80px; margin-right: 20px"
              />
              <span class="jf-label">数量单位：</span>
              <el-select
                v-model="item.skuUnit"
                placeholder="请选择"
                style="width: 100px"
                :disabled="viewControl || !item.creatJiFeiList?.includes('quantityCounting')"
              >
                <el-option
                  v-for="(quantityItem, quantityIndex) in quantityList"
                  :key="quantityIndex + 'skuUnit'"
                  :label="quantityItem.itemValue"
                  :value="quantityItem.itemKey"
                />
              </el-select>
            </div>
            <div style="margin-top: 10px">
              <span class="jf-label">购买时长限制：</span>
              <el-input
                v-model="item.minTime"
                :disabled="viewControl || !item.creatJiFeiList?.includes('timeCounting')"
                maxlength="6"
                v-d-input-int
                v-d-input-max="100000"
                placeholder="开始数值"
                style="width: 80px"
                @blur="timeChange(item)"
              />
              <span style="margin: 0 7px">~</span>
              <el-input
                v-model="item.maxTime"
                :disabled="viewControl || !item.creatJiFeiList?.includes('timeCounting')"
                v-d-input-int
                v-d-input-max="100000"
                maxlength="6"
                placeholder="结束数值"
                style="width: 80px; margin-right: 20px"
                @blur="timeChange(item)"
              />
              <span class="jf-label">时长步长：</span>
              <el-input
                v-model="item.timeStep"
                placeholder="输入步长"
                :disabled="viewControl || !item.creatJiFeiList?.includes('timeCounting')"
                v-d-input-int
                v-d-input-max="100000"
                maxlength="6"
                style="width: 80px; margin-right: 20px"
              />
              <span class="jf-label">时长单位：</span>
              <el-select
                v-model="item.timeUnit"
                :disabled="viewControl || !item.creatJiFeiList?.includes('timeCounting')"
                placeholder="请选择"
                style="width: 100px"
              >
                <el-option
                  v-for="(timeItem, timeIndex) in timeList"
                  :key="timeIndex + 'timeUnit'"
                  :label="timeItem.itemValue"
                  :value="timeItem.itemKey"
                />
              </el-select>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="是否支持试用" class="jf-item" prop="probationFlag" v-if="!isExternal">
          <el-radio-group v-model="item.probationFlag" :disabled="viewControl">
            <el-radio label="1"> 是 </el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="" class="jf-item" style="padding-left: 140px" v-if="item.probationFlag === '1'">
          <div class="jf-box" v-if="item.probationFlag === '1'">
            <span class="jf-label">试用有效期：</span>
            <el-input
              v-d-input-int
              v-d-input-max="30"
              style="width: 80px; margin-right: 20px"
              v-model="item.probationDate"
              :disabled="viewControl"
              placeholder="请输入"
            />
            <span style="margin-right: 100px">天</span>
            <!-- <span>试用次数限制：</span>
            <el-input
              v-d-input-int
              v-d-input-max="100000"
              style="width: 80px; margin-right: 20px"
              v-model="item.probationTime"
              :disabled="viewControl"
              placeholder="请输入"
            />
            <span>次</span> -->
          </div>
        </el-form-item>
        <el-form-item label="订购次数限制" class="jf-item" prop="probationTime">
          <el-input
            v-d-input-int
            v-d-input-max="100000"
            style="width: 80px; margin-right: 20px"
            v-model="item.probationTime"
            :disabled="viewControl"
            placeholder="请输入"
          />
          <span>次</span>
        </el-form-item>
        <el-form-item label="是否支持子账号" class="jf-item" prop="isSubAccountSharing">
          <el-radio-group v-model="item.isSubAccountSharing" :disabled="viewControl">
            <el-radio :label="true"> 是 </el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="" class="jf-item" style="padding-left: 140px" v-if="item.isSubAccountSharing === true">
          <div class="jf-box" v-if="item.isSubAccountSharing === true">
            <span class="jf-label">子账号数：</span>
            <el-input
              v-d-input-int
              v-d-input-max="100"
              style="width: 80px; margin-right: 20px"
              v-model="item.subAccountQuantity"
              :disabled="viewControl"
              placeholder="请输入"
            />
            <span>个</span>
          </div>
        </el-form-item>
        <el-form-item label="产品使用有效期" :rules="[{ validator: validApplyEffectTime(item, index) }]">
          <el-input
            v-d-input-int
            v-d-input-max="100000"
            style="width: 120px; margin-right: 20px"
            v-model="item.lifeSpan"
            :disabled="viewControl"
            placeholder="请输入"
          />
          <el-select v-model="item.lifeSpanUnit" placeholder="请选择" clearable :disabled="viewControl">
            <el-option
              v-for="(timeItem, timeIndex) in pms_time_uit"
              :key="timeIndex + 'pmpTimeUnit'"
              :label="timeItem.itemValue"
              :value="timeItem.itemKey"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="赠品" class="jf-item" prop="giftSkuId" v-if="!isGift">
          <div class="name-box">
            <el-button
              type="primary"
              class="add-button"
              @click="onAddGift(index, item.skuGiftList)"
              :icon="CirclePlus"
              :disabled="viewControl"
            >
              添加
            </el-button>
            <span class="tips"> <slot name="nameTips"></slot></span>
          </div>
          <GiftTable
            v-model:giftSkuId="item.giftSkuId"
            v-model:skuGiftList="item.skuGiftList"
            :sku-id-index="index"
            :disabled="viewControl"
          />
        </el-form-item>
        <el-form-item label="规格交付内容" v-if="!isExternal">
          <el-input
            v-model="item.skuContent"
            :disabled="viewControl"
            type="textarea"
            maxlength="1000"
            placeholder="请输入"
            show-word-limit
            clearable
          />
        </el-form-item>
        <el-form-item label="续订计算规则" prop="renewalRules">
          <el-radio-group v-model="item.renewalRules" :disabled="viewControl">
            <el-radio v-for="el in pms_renewal_rules" :key="el.itemKey" :label="el.itemKey">{{ el.itemValue }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="续订单赠品规则" prop="giftFlag">
          <el-radio-group v-model="item.giftFlag" :disabled="viewControl">
            <el-radio v-for="el in gift_flag_type" :key="el.itemKey" :label="el.itemKey">{{ el.itemValue }} </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </div>
  <ProductDrawer
    ref="drawerRef"
    :is-select="true"
    :selected-list="selectedList"
    :query-api="queryGiftSkuList"
    @on-comfirm="onSelectGift"
  />
</template>
<script setup lang="ts">
import { onMounted, ref, computed } from "vue";
import { CirclePlus, Delete } from "@element-plus/icons-vue";
// import { zNumber } from "@/utils/eleValidate";
import { getDictionary } from "@/api/modules/system";
import { ElMessageBox, FormInstance } from "element-plus";
import { useProductStore } from "@/stores/modules/product";
import { checkPhoneBillPayCode, queryGiftSkuList } from "@/api/modules/product";
const productStore = useProductStore();
import { useDict } from "@/hooks/useDict";
import { validApplyEffectTime } from "../validator";
import ProductDrawer from "@/views/operation/trial/components/productDrawer.vue";
import GiftTable from "./giftTable.vue";

const { sku_billing_mode_type, pms_time_uit, pms_renewal_rules, gift_flag_type } = useDict(
  "sku_billing_mode_type",
  "pms_time_uit",
  "pms_renewal_rules",
  "gift_flag_type"
);

// sku_billing_mode_type
interface QuantityItem {
  itemValue: string;
  itemKey: string;
}
const props = defineProps({
  productId: {
    type: String,
    default: ""
  },
  dataList: {
    type: Array,
    default: () => []
  },
  prefixion: {
    type: String,
    default: ""
  },
  viewControl: {
    type: Boolean,
    default: false // 查看详情就禁用新增和修改,由父组件的viewStatus传来
  },
  payType: {
    type: String,
    default: () => ""
  },
  isGift: {
    type: Boolean,
    default: false
  },
  isExternal: {
    // 是否是外接应用类产品
    type: Boolean,
    default: false
  }
});
const validatephoneBillPayCode = (rule: any, value: string, callback: any) => {
  checkPhoneBillPayCode(value).then((res: any) => {
    console.log(res.success);
    if (!res.success) {
      callback(new Error("同一个CBSS产品ID不能重复使用"));
    }
    callback();
  });
};

const timeCounting = (lifeSpan: string, lifeSpanUnit: string) => {
  if (lifeSpan !== null && lifeSpan !== undefined && lifeSpan !== "") {
    return true;
  }
  if (lifeSpanUnit !== null && lifeSpanUnit !== undefined && lifeSpanUnit !== "") {
    return true;
  }
  return false;
};

const emits = defineEmits(["addItem", "deleteItem"]);
const skuRuleFormRef = ref<FormInstance>();
const drawerRef = ref();
const giftTableData = ref([]);
const selectedList = ref<string[]>([]);
const selectSkuIndex = ref<number>(0);
// console.log("111111111");
// console.log(params.payType.indexOf("1") != -1);

const timeList = ref<QuantityItem[]>([]); // 时长单位列表
const quantityList = ref<QuantityItem[]>([]); // 数量单位列表
const rules = {
  skuName: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  externalCode: [{ required: true, message: "外部产品编码不能为空", trigger: "blur" }],
  skuPrice: [
    { required: true, message: "规格单价不能为空，限制输入0-********，最多保留3位小数", trigger: "blur" }
    // { validator: zNumber, message: "限制输入1-********的整数", trigger: "blur" }
  ],
  // skuDiscountPrice: [
  //   { message: "折扣价限制输入0-********，最多保留2位小数", trigger: "blur" }
  //   // { validator: zNumber, message: "限制输入1-********的整数", trigger: "blur" }
  // ],
  phoneBillPayCode: [
    { required: true, message: "CBSS产品ID不能为空", trigger: "blur" },
    { max: 20, message: "限制最长可输入20个字符", trigger: "blur" }
    // { validator: validatephoneBillPayCode, trigger: "blur" }
  ],
  probationFlag: [{ required: true, message: "请选择是否支持试用", trigger: "blur" }],
  billingMode: [{ required: true, message: "请选择计费模式", trigger: "blur" }],
  isSubAccountSharing: [{ required: true, message: "请选择是否支持子账号", trigger: "blur" }]
};
const sendProductDetailParam = computed(() => productStore.sendProductDetailParam?.[props.productId]);

const payTypeCurrent = computed(() => {
  if (!props.viewControl) {
    return sendProductDetailParam.value?.payType;
  } else {
    // 详情直接取props?.payType
    return props?.payType;
  }
});
onMounted(() => {
  // 时长字典
  getDictionary({ dictCode: "pms_time_uit" }).then((res: any) => {
    timeList.value = res.data;
  });
  // 数量单位字典
  getDictionary({ dictCode: "sku_quantity_uit" }).then((res: any) => {
    quantityList.value = res.data;
  });
});
const onAddGift = (skuIndex: number, list: any) => {
  selectedList.value = list;
  selectSkuIndex.value = skuIndex;
  drawerRef.value.visible = true;
};

const onSelectGift = (ids: string[], selectList: any) => {
  const selectSku = props.dataList[selectSkuIndex.value] as any;
  selectSku.skuGiftList = selectList;
  drawerRef.value.visible = false;
};
const addItem = () => {
  emits("addItem");
};
//删除元素
const deleteItem = (index: number) => {
  // if (contentDataList.value.length <= 1) {
  //   ElMessage.error("至少有一项内容");
  // } else {
  //   emits("deleteItem", index);
  // }
  emits("deleteItem", index);
};
const shangyiItem = (index: number, list: any[]) => {
  list[index] = list.splice(index - 1, 1, list[index])[0];
};
const xiayiItem = (index: number, list: any[]) => {
  list[index] = list.splice(index + 1, 1, list[index])[0];
};
const quantityChange = (item: object) => {
  if (item.minQuantity && item.maxQuantity && parseFloat(item.minQuantity) > parseFloat(item.maxQuantity)) {
    ElMessageBox.alert("购买数量开始数值不能大于结束数值，请重新设置", "提示", { type: "warning" }).then(() => {});
  }
};
const timeChange = (item: object) => {
  if (item.minTime && item.maxTime && parseFloat(item.minTime) > parseFloat(item.maxTime)) {
    ElMessageBox.alert("购买时长开始数值不能大于结束数值，请重新设置", "提示", { type: "warning" }).then(() => {});
  }
};
const changeBillingMode = (item: object) => {
  // if (item.billingMode !== "frequency") {
  //   item.lifeSpan = null;
  //   item.lifeSpanUnit = null;
  // }
};
const jfCheckbox = (item: object) => {
  if (!item.creatJiFeiList.includes("quantityCounting")) {
    item.minQuantity = null;
    item.maxQuantity = null;
    item.quantityStep = null;
    item.skuUnit = null;
  }
  if (!item.creatJiFeiList.includes("timeCounting")) {
    item.minTime = null;
    item.maxTime = null;
    item.timeStep = null;
    item.timeUnit = null;
  }
};
const ruleValidatePromise = () => {
  let promiseArr = [] as any;
  skuRuleFormRef.value?.forEach((itemref: any) => {
    promiseArr.push(
      new Promise((resolve, reject) => {
        itemref.validate((valid: any, fields: any) => {
          if (valid) {
            resolve("校验成功");
          } else {
            let obj = Object.keys(fields);
            itemref.scrollToField(obj[0]);
            reject("校验失败");
          }
        });
      })
    );
  });
  return promiseArr;
};
defineExpose({
  ruleValidatePromise
});
</script>

<style lang="scss" scoped>
.complex-content-box {
  width: 100%;
  .name-box {
    display: flex;
    width: fit-content;
    margin-bottom: 16px;
    .name-input {
      margin-right: 8px;
    }
    .add-button {
      margin-right: 8px;
    }
    .tips {
      margin-left: 15px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
    }
  }
  .content-item {
    margin-bottom: 16px;
    border: 1px solid #c4c6cc;
    &.no-border {
      border: none;
      .item-form {
        padding: 0;
      }
    }
    .item-title {
      display: flex;
      justify-content: space-between;
      padding: 4px 12px;
      background: #c4c6cc;
      .right-btn {
        .icon + .icon {
          margin-left: 0;
        }
      }
      .icon:hover {
        color: #3a3a3d;
      }
    }
  }
  .item-form {
    padding: 24px;
    .jf-item {
      :deep(.el-form-item__content) {
        display: block;
      }
      .jf-box {
        padding: 15px 0;
        .jf-label {
          font-size: var(--el-form-label-font-size);
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .nowrap {
    white-space: nowrap;
  }
}
</style>
