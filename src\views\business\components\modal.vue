<template>
  <el-dialog
    :append-to-body="true"
    custom-class="custom-modal"
    v-model="dialogVisible"
    top="0"
    width="996"
    :before-close="handleClose"
  >
    <template #title>商机详情 </template>
    <div class="info">
      <div class="title">客户信息</div>
      <div class="wrapper">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">客户账号： </span>
              <span class="info-text">{{ data?.contactName || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">手机号码： </span>
              <span class="info-text">{{ data?.phone || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">公司名称： </span>
              <span class="info-text">{{ data?.companyName || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">城市： </span>
              <span class="info-text">{{ data?.regionName || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">邮箱：</span>
              <span class="info-text">{{ data?.email || "-" }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="info">
      <div class="title">咨询信息</div>
      <div class="wrapper">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">咨询产品： </span>
              <span class="info-text">{{ data?.itemName || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="1">
            <div class="info-item">
              <span class="info-label">产品分类： </span>
              <span class="info-text">{{ data?.itemCategory || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="8" :push="2">
            <div class="info-item">
              <span class="info-label">咨询时间： </span>
              <span class="info-text">{{ data?.createTime || "-" }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">咨询问题： </span>
              <span class="info-text">{{ data?.consultMsg || "-" }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";

const dialogVisible = ref(false);
const data = ref<any>({});

const handleClose = (done: () => void) => {
  dialogVisible.value = false;
};
const handleOpen = props => {
  const { row } = props;
  data.value = row;
  dialogVisible.value = true;
};

defineExpose({
  handleOpen
});
</script>

<style scoped lang="scss">
:global(.custom-modal .el-dialog__header) {
  padding: 22px 24px;
  border-bottom: 1px solid #e1e3e6;
}
.info {
  margin-bottom: 10px;
  .title {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: bold;
    color: #3a3a3d;
  }
  .wrapper {
    .info-item {
      margin-bottom: 16px;
      .info-label {
        font-size: 14px;
        font-weight: 400;
        color: #73787f;
      }
      .info-text {
        font-size: 14px;
        font-weight: 400;
        color: #3a3a3d;
      }
    }
  }
}
</style>
