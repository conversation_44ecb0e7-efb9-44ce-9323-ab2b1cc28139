<template>
  <el-dialog v-model="dialogVisible" title="详情" align-center width="600px">
    <div class="dialog-content">
      <div class="info-box">
        <div class="info-item">
          <p class="info-item-label">企业名称</p>
          <p class="info-item-value">{{ demandInfo.enterpriseName || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">企业联系人</p>
          <p class="info-item-value">{{ demandInfo.linkman || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">联系方式</p>
          <p class="info-item-value">{{ demandInfo.contact || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">供应商名称</p>
          <p class="info-item-value">{{ demandInfo.providerName || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">产品名称</p>
          <p class="info-item-value">{{ demandInfo.productName || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">业务需求描述</p>
          <p class="info-item-value">{{ demandInfo.demandInfo || "-" }}</p>
        </div>
        <div class="info-item">
          <p class="info-item-label">创建时间</p>
          <p class="info-item-value">{{ demandInfo.createTime || "-" }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const dialogVisible = ref(false);

const demandInfo = ref({
  enterpriseName: "",
  linkman: "",
  contact: "",
  providerName: "",
  productName: "",
  demandInfo: "",
  createTime: ""
});

const show = (row: any) => {
  dialogVisible.value = true;
  demandInfo.value = row;
};

const close = () => {
  dialogVisible.value = false;
};

defineExpose({
  show
});
</script>

<style lang="scss" scoped>
.dialog-content {
  .info-box {
    .info-item {
      display: flex;
      align-items: flex-start;
      .info-item-label {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 136px;
        text-align: right;
        &::after {
          content: ":";
        }
      }
      .info-item-value {
        flex: 1;
        width: 0;
        margin-left: 10px;
      }
      .contract-file {
        color: var(--el-color-primary);
        cursor: pointer;
      }
    }
  }
}
</style>
