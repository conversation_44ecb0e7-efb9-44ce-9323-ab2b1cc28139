<!--账单管理（运营人员）-->
<template>
  <div class="table-box">
    <ProTable ref="proTable" title="账单列表" :columns="columns" :tool-button="false" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看明细" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(scope.row)"></i>
          </el-tooltip>
        </span>

        <span v-auth="'detail'">
          <el-tooltip content="拨付明细" placement="top" v-if="scope.row.status == '3' || scope.row.status == '5'">
            <i class="iconfont2 opera-icon icon-bofumingxi" @click="toDisbursement(scope.row)"></i>
          </el-tooltip>
        </span>

        <span v-auth="'delete'">
          <el-tooltip content="撤回" placement="top" v-if="scope.row.status == '1' || scope.row.status == '6'">
            <i class="iconfont2 opera-icon icon-lajitong" @click="withdraw(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="ts" name="billManage">
import { ref, reactive } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useRouter } from "vue-router";
import { getBillPageList, rejectBill, exportBillPageList } from "@/api/modules/billManage";
import { useUserStore } from "@/stores/modules/user";
import { ElMessage, ElMessageBox } from "element-plus";
import { useDownload } from "@/hooks/useDownload";

const { BUTTONS } = useAuthButtons();
// const router = useRouter();
const userStore = useUserStore();
// const isPartner = computed(() => userStore.userInfo.userType == UserType.partnerUsers);

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});
// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();

const router = useRouter();

const getTableList = (params: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  return getBillPageList(newParams);
};

const downloadFile = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  delete newParams.createTime;
  ElMessageBox.confirm("确认导出账单列表?", "提示", { type: "warning" }).then(() => useDownload(exportBillPageList, newParams));
};

const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "providerName", label: "合作商名称", search: { el: "input" } },
  {
    prop: "billDate",
    label: "账单日期",
    search: {
      el: "date-picker",
      props: {
        type: "month",
        // "range-separator": "-",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM"
      }
    }
  },
  { prop: "totalFee", label: "账单金额（元）" },
  {
    prop: "status",
    label: "状态",
    width: 130,
    enum: [
      { label: "账单待确认", value: "1" },
      { label: "拨付审核中", value: "2" },
      { label: "拨付处理中", value: "3" },
      { label: "拨付审核不通过", value: "4" },
      { label: "已拨付", value: "5" },
      { label: "账单驳回", value: "6" },
      { label: "账单已撤回", value: "7" }
    ],
    search: { el: "select" }
  },
  {
    prop: "rejectionReason",
    label: "账单驳回意见",
    align: "left"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 300 }
];

const toView = async (row: any) => {
  // console.log(row.id);
  router.push({ path: `/partner/billManage/billDetail`, query: { id: row.id } });
};

const toDisbursement = async (row: any) => {
  router.push({ path: `/partner/billManage/paymentDetail`, query: { id: row.id } });
};

const withdraw = async (row: any) => {
  ElMessageBox.confirm("是否确认撤回账单？撤回后账单将被删除，需要重新生成", "撤回账单").then(async () => {
    await rejectBill(row.id);
    ElMessage.success("撤回成功！");
    proTable.value?.getTableList();
  });
};
</script>

<style scoped>
.table-box {
  width: 100%;
}
</style>
