<template>
  <div>
    <div class="table-box">
      <pro-table ref="proTable" title="工单管理列表" :columns="columns" :request-api="getTableList" :tool-button="false">
        <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
        <template #tableHeader>
          <el-button v-auth="'export'" plain @click="downloadFile">
            <i class="iconfont2 icon-a-lineicon_share"></i>
            导出
          </el-button>
        </template>
        <template #orderStatus="{ row }">
          <CustomTag
            :type="orderStatus[row.orderStatus as OrderStatus]?.[0]"
            :label="useDictLabel(work_order_status, row.orderStatus)"
          ></CustomTag>
        </template>
        <template #orderType="{ row }">
          {{ useDictLabel(work_order_type, row?.orderType) }}
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span v-auth="'view'">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan" @click="toWorkOrderDetail(scope.row)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'deal'" v-if="scope.row.orderStatus === OrderStatus.PendingProcessing">
            <el-tooltip content="受理" placement="top">
              <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="toHandle(scope.row)"></i>
            </el-tooltip>
          </span>
          <span v-auth="'reply'" v-if="scope.row.orderStatus === OrderStatus.Processing || scope.row.orderStatus === '待处理'">
            <el-tooltip content="回复" placement="top">
              <i class="iconfont2 opera-icon icon-querenchuli1" @click="toWorkOrderDetail(scope.row, 'reply')"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>

    <Handle-modal ref="handleModalRef" @on-submit="handleSubmit" />
  </div>
</template>

<script setup lang="tsx" name="workOrder">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { cloneDeep } from "lodash";
import { ElMessageBox } from "element-plus";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getWorkOrderList, exportWorkOrderList, changeWorkOrderStatus } from "@/api/modules/workOrder";
import HandleModal from "./components/handleModal.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { orderStatus, OrderStatus } from "./type";
import CustomTag from "@/components/CustomTag/index.vue";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/modules/user";

const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const { work_order_status, work_order_type } = useDict("work_order_status", "work_order_type");

const { BUTTONS } = useAuthButtons();

const router = useRouter();
const proTable = ref<ProTableInstance>();

const handleModalRef = ref();
const handleItem = ref();

// 表格配置项
const columns: ColumnProps<any>[] = [
  // { type: "selection", fixed: "left", align: "left" },
  { prop: "code", align: "left", label: "工单编号", search: { el: "input" }, minWidth: 150 },
  { prop: "description", align: "left", label: "问题描述", search: { el: "input" }, minWidth: 100 },
  { prop: "orderType", label: "类型", align: "left", minWidth: 180 },
  {
    prop: "orderStatus",
    align: "left",
    label: "状态",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {work_order_status.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    minWidth: 100
  },
  { prop: "creatorName", align: "left", label: "提出人", search: { el: "input" }, minWidth: 100 },
  { prop: "createTime", align: "left", label: "提交时间", minWidth: 170 },
  { prop: "durationDesc", align: "left", label: "处理时长", minWidth: 170 },
  { prop: "handleParty", align: "left", label: "处理方", minWidth: 100 },
  { prop: "handleAccount", align: "left", label: "处理人", minWidth: 100 },
  { prop: "handleTime", align: "left", label: "处理时间", minWidth: 170 },
  { prop: "operation", align: "left", label: "操作", fixed: "right", width: 100 }
];

const getTableList = (params: any = {}): any => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = cloneDeep(params);
  return getWorkOrderList(newParams);
};

const toHandle = (row: any = {}) => {
  handleItem.value = row;
  handleModalRef.value.handleOpen();
};

const handleSubmit = () => {
  console.log({
    id: handleItem.value?.id,
    orderStatus: "2",
    userName: userInfo.value.userName
  });
  changeWorkOrderStatus({
    id: handleItem.value?.id,
    orderStatus: "2",
    userName: userInfo.value.userName
  }).then(res => {
    if (res.code === 200) {
      ElMessage.success("受理成功！");
      handleModalRef.value.handleClose();
      proTable.value?.getTableList();
    }
  });
};

const toWorkOrderDetail = (row: any = {}, type = "view") => {
  router.push({
    path: "/workOrder/detail",
    query: {
      pageType: type,
      id: row.id
    }
  });
};

const downloadFile = async () => {
  const params = {
    ...proTable.value?.searchParam,
    current: proTable.value?.pageable.current,
    size: proTable.value?.pageable.size
  };
  let newParams = JSON.parse(JSON.stringify(params));
  ElMessageBox.confirm("确认导出工单管理列表?", "提示", { type: "warning" }).then(() =>
    useDownload(exportWorkOrderList, newParams)
  );
};
</script>
