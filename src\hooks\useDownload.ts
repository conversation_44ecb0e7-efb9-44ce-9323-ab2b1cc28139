import { ElNotification } from "element-plus";

/**
 * @description 接收数据流生成 blob，创建链接，下载文件
 * @param {Function} api 导出表格的api方法 (必传)
 * @param {Object} params 导出的参数 (默认{})
 * @param {String} tempName 导出的文件名 (默认空字符串)
 * @param {Boolean} isNotify 是否有导出消息提示 (默认为 true)
 * @param {String} fileType 导出的文件格式 (默认为.xlsx)
 * */
export const useDownload = async (
  api: (param: any) => Promise<any>,
  params: any = {},
  tempName?: string,
  isNotify: boolean = true,
  fileType: string = ".xlsx"
) => {
  if (isNotify) {
    ElNotification({
      title: "温馨提示",
      message: "如果数据量过大会导致下载缓慢，请您耐心等待！",
      type: "info",
      duration: 3000
    });
  }
  try {
    const res = await api(params);

    const contentDisposition = res.headers?.["content-disposition"]?.split(";");
    const getKey = contentDisposition?.find((item: string) => item.indexOf("filename") !== -1) || `filename=下载文件${fileType}`;
    const disposition = new URLSearchParams(getKey);
    const fileName = tempName && tempName != "" ? `${tempName}${fileType}` : disposition.get("filename");
    const blob = new Blob([res.data], {
      type: res.data.type
    });

    // 兼容 edge 不支持 createObjectURL 方法
    if ("msSaveOrOpenBlob" in navigator) return window.navigator.msSaveOrOpenBlob(blob, fileName);
    const blobUrl = window.URL.createObjectURL(blob);
    const exportFile = document.createElement("a");
    exportFile.style.display = "none";
    exportFile.download = `${fileName}`;
    exportFile.href = blobUrl;
    document.body.appendChild(exportFile);
    exportFile.click();
    // 去除下载对 url 的影响
    document.body.removeChild(exportFile);
    window.URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.log(error);
  }
};
