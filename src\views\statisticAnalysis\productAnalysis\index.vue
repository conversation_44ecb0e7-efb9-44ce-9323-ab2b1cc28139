<template>
  <div class="order-analysis-container">
    <div class="order-header">
      <div class="header-box">
        <el-form :inline="true" ref="orderFormRef" :model="orderForm" :rules="rules">
          <el-form-item label="" prop="btn">
            <div>
              <el-button-group size="large" class="botton-box">
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.day, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.day)"
                >
                  日报表
                </el-button>
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.month, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.month)"
                >
                  月报表
                </el-button>
              </el-button-group>
            </div>
          </el-form-item>
          <el-form-item label="日期" prop="countDate">
            <el-date-picker
              v-if="dateType === DateType.day"
              class="data-picker-style"
              type="daterange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @focus="handleFocus"
              @calendar-change="handleChange"
              :disabled-date="disabledDate"
            />
            <el-date-picker
              v-else
              class="data-picker-style"
              type="monthrange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </el-form-item>
          <el-form-item label="产品名称" prop="itemId">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              :multiple="true"
              v-model="orderForm.itemId"
              placeholder="请选择产品名称"
              clearable
            >
              <el-option v-for="item in productOptions" :key="item.itemId" :label="item.itemName" :value="item.itemId" />
            </el-select>
          </el-form-item>
          <el-form-item label="合作商" prop="providerId">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              v-model="orderForm.providerId"
              placeholder="请选择合作商"
              clearable
            >
              <el-option
                v-for="item in providerOptions"
                :key="item.providerId"
                :label="item.providerName"
                :value="item.providerId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm(orderFormRef)"> 查询 </el-button>
            <el-button @click="resetForm(orderFormRef)"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="analysis-content">
      <div class="content-title">产品商机趋势</div>
      <div class="statistical-data">
        <div class="order-main">
          <indicator-button
            :indicator-title="'数据指标'"
            :list="orderIndex"
            :active="activeBtn"
            @item-click="onChangeOrder"
          ></indicator-button>

          <div class="chart-box">
            <bar-chart ref="orderLineChartRef" />
          </div>
        </div>
        <div class="order-aside">
          <ranking-list :ranking-title="'产品商机排名'" @on-more="onMore" :ranking-list="rankingData"></ranking-list>
        </div>
      </div>
    </div>
    <order-analysis-list
      :params="{ ...commonParams }"
      :table-data="tableInfo.tableData"
      :total-data="tableInfo.totalData"
    ></order-analysis-list>
  </div>
</template>

<script setup lang="ts" name="productAnalysis">
import { reactive, ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { ECharts } from "echarts";
import dayjs from "dayjs";
import type { FormInstance, FormRules } from "element-plus";
import rankingList from "@/views/statisticAnalysis/orderAnalysis/components/rankingList.vue";
import IndicatorButton from "@/components/IndicatorButton/index.vue";
import barChart from "./components/barChart.vue";
import orderAnalysisList from "./components/orderAnalysisList.vue";
import { getBusinessAnalysis, getBusinessAnalysisTop } from "@/api/modules/statisticAnalysis";
import { getBusinessNameList, getBusinessProviderList } from "@/api/modules/statisticAnalysis";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { startDate, endDate, startMonthDate, endMonthDate } from "../commonAnalysis";

enum IndexType {
  increase = "increase", // 新增
  accumulate = "accumulate" // 累计
}

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}

const { BUTTONS } = useAuthButtons();

// 获取子组件的ref
interface ChartExpose {
  initChart: (params: any) => ECharts;
}
const router = useRouter();
const orderLineChartRef = ref<ChartExpose>();
const rankingData = ref<any>([]);

const dateType = ref<DateType>(DateType.day);
const activeBtn = ref("");

const commonParams = reactive({
  countEndDate: endDate,
  countStartDate: startDate,
  dateType: dateType,
  itemId: [],
  providerId: "",
  accumulateOrIncrease: activeBtn.value,
  adviceType: 1 // 商机分析指标类型枚举：1-产品商机、2-解决方案商机，不传就全类型查
});
const tableInfo = reactive({
  tableData: [],
  totalData: []
});

// 月报表入参月份
const handleCountDate = () => {
  if (dateType.value === DateType.month) {
    return {
      countEndDate: dayjs(commonParams.countEndDate).format("YYYY-MM"),
      countStartDate: dayjs(commonParams.countStartDate).format("YYYY-MM")
    };
  }
  return {};
};
// 图表
const getChat = () => {
  if (!BUTTONS.value?.query) {
    return;
  }
  getBusinessAnalysis({
    ...commonParams,
    ...handleCountDate()
  }).then((res: any) => {
    if (res.code === 200) {
      const { list = [], total = {} } = res.data;
      tableInfo.totalData = total;
      tableInfo.tableData = list;

      // 图表数据处理
      const dateList: number[] = [],
        addList: number[] = [],
        accumulateList: string[] = [];
      if (list && list.length > 0) {
        list.forEach((item: any) => {
          dateList.push(item.dateWithoutYear);
          addList.push(item.netGrowthCount);
          accumulateList.push(item.accumulateCount);
        });
      }

      orderLineChartRef.value?.initChart({
        data1: activeBtn.value === IndexType.accumulate ? [] : addList,
        data2: activeBtn.value === IndexType.increase ? [] : accumulateList,
        nameList: dateList
      });
    } else {
      tableInfo.totalData = [];
      tableInfo.tableData = [];
    }
  });
};

// 排名
const getTop = () => {
  if (!BUTTONS.value?.query) {
    return;
  }
  getBusinessAnalysisTop({
    ...commonParams,
    ...handleCountDate(),
    accumulateOrIncrease: activeBtn.value ? activeBtn.value : IndexType.accumulate // 全部传“accumulate”
  }).then(res => {
    if (res.code === 200) {
      const ranking: any = res.data || [];
      rankingData.value = ranking.map((item: any) => {
        return {
          ...item,
          productName: item.itemName,
          count: activeBtn.value === IndexType.increase ? item.netGrowthCount : item.accumulateCount
        };
      });
    } else {
      rankingData.value = [];
    }
  });
};

const getAllData = () => {
  getTop();
  getChat();
};

onMounted(() => {
  getAllData();
  getSearchList();
});

const productOptions = ref([]);
const providerOptions = ref([]);
const getSearchList = async () => {
  getBusinessNameList({ adviceType: "1" }).then((res: any) => {
    productOptions.value = res?.data || [];
  });
  getBusinessProviderList({}).then((res: any) => {
    providerOptions.value = res?.data || [];
  });
};

const changeDate = (type: DateType) => {
  if (type === DateType.month) {
    commonParams.countStartDate = startMonthDate;
    commonParams.countEndDate = endMonthDate;
    orderForm.countDate = [startMonthDate, endMonthDate];
  } else {
    commonParams.countStartDate = startDate;
    commonParams.countEndDate = endDate;
    orderForm.countDate = [startDate, endDate];
  }
};
const onChangeDateType = (type: DateType) => {
  dateType.value = type;
  commonParams.dateType = type;
  changeDate(type);
  getAllData();
};

// 订单分析指标类型枚举：1-订购订单数、2-退订订单数、3-净增订单数、4-累计生效订单数、5-交易金额GMV、6-累计交易金额GMV
const orderIndex = [
  {
    label: "全部",
    value: ""
  },
  {
    label: "新增",
    value: "increase"
  },
  {
    label: "累计",
    value: "accumulate"
  }
];

const orderFormRef = ref<FormInstance>();
const orderForm = reactive({
  countDate: [startDate, endDate],
  itemId: [],
  providerId: ""
});

const checkDate = (_: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请选择日期"));
  } else {
    // 定义两个日期字符串
    const dateString1 = value[0];
    const dateString2 = value[1];
    // 将日期字符串转换为 dayjs 对象
    const date1 = dayjs(dateString1);
    const date2 = dayjs(dateString2);

    // 获取两个日期之间的月数差
    const monthDiff = Math.abs(date2.diff(date1, "month"));

    if (monthDiff > 11) {
      callback(new Error("最多只能查询12个月的数据"));
    }
  }
  callback();
};

const dateRule: any = computed(() =>
  dateType.value === DateType.day
    ? [{ required: true, message: "请选择日期", trigger: "blur" }]
    : [
        { required: true, message: "请选择日期", trigger: "blur" },
        { trigger: "blur", validator: checkDate }
      ]
);
const rules = reactive<FormRules>({
  countDate: dateRule
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      commonParams.countStartDate = orderForm.countDate[0];
      commonParams.countEndDate = orderForm.countDate[1];
      commonParams.itemId = orderForm.itemId || [];
      commonParams.providerId = orderForm.providerId || "";
      getAllData();
    } else {
      console.log("error submit!", fields);
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  // 处理重置日报表与月报表不相同
  changeDate(dateType.value);
};

const onChangeOrder = (values: any) => {
  activeBtn.value = values.value;
  commonParams.accumulateOrIncrease = values.value;
  getTop();
  getChat();
};

const onMore = () => {
  const dateArr = [commonParams.countStartDate, commonParams.countEndDate];
  router.push({
    path: "/statisticAnalysis/productAnalysis/productDimension",
    query: {
      dateType: dateType.value,
      countDate: dateArr,
      itemId: commonParams.itemId,
      providerId: commonParams.providerId
    }
  });
};

const chooseDay = ref<any>(null);
const handleChange = (val: Date[]) => {
  const [pointDay] = val;
  chooseDay.value = pointDay;
};
//取值方法
const handleFocus = () => {
  chooseDay.value = null;
};

const disabledDate = (time: number) => {
  if (!chooseDay.value) {
    return false;
  }
  let timeRange = 30;
  const con1 = new Date(chooseDay.value).getTime() - timeRange * 24 * 60 * 60 * 1000;
  const con2 = new Date(chooseDay.value).getTime() + timeRange * 24 * 60 * 60 * 1000;
  return time < con1 || time > con2;
};
</script>

<style lang="scss" scoped>
.cust-border {
  border-bottom: 1px solid #e1e3e6;
}
.order-analysis-container {
  background-color: #ffffff;
  .order-header {
    padding: 16px 0 0 24px;
    @extend.cust-border;
    .header-box {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      :deep(.el-form-item--default) {
        margin-bottom: 16px;
      }
      :deep(.el-form--inline .el-form-item) {
        margin-right: 12px;
      }
      :deep(.data-picker-style) {
        max-width: 210px;
      }
      .select-style {
        max-width: 190px;
      }
      .type-box {
        flex: 164px;
      }
    }
    .btn {
      height: 36px;
      &.active {
        color: var(--el-color-primary);
        background-color: var(--el-fill-color-blank);
        border-color: var(--el-color-primary);
      }
    }
  }
  .analysis-content {
    height: 470px;
    padding: 16px 24px 0;
    @extend.cust-border;
    .content-title {
      width: 100%;
      height: 24px;
      margin-bottom: 16px;
      font-family: "PingFangSC, PingFang SC,sans-serif";
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #000000d9;
    }
    .statistical-data {
      display: flex;
      width: 100%;
    }
    .order-main {
      flex: 1;
      .chart-box {
        width: 100%;
        height: 336px;
      }
    }
    .order-aside {
      width: 300px;
    }
  }
}
</style>
