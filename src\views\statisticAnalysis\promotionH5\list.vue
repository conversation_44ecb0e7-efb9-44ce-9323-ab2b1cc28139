<template>
  <div class="table-box">
    <ProTable @selection-change="changeSelect" ref="proTable" :request-api="getTableList" :columns="columns" :tool-button="false">
      <template #tableHeader>
        <el-button plain @click="handleExport" v-auth="'export:promotionH5'">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>
      <template #source="{ row }">
        {{ useDictLabel(demand_source, row.source) }}
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { exportPromotionH5, queryOrdDemandH5 } from "@/api/modules/statisticAnalysis";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { Statistic } from "@/api/interface/statisticAnalysis";
import { useDownload } from "@/hooks/useDownload";
import { ElMessageBox } from "element-plus";
import { useAuthButtons } from "@/hooks/useAuthButtons";
const { BUTTONS } = useAuthButtons();

const { demand_source } = useDict("demand_source");

const columns: ColumnProps[] = [
  { type: "selection" },
  {
    align: "center",
    prop: "time",
    label: "提交时间",
    isShow: false,
    search: {
      el: "date-picker",
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" },
      transform: (value: any) => {
        return {
          startDate: value[0],
          endDate: value[1]
        };
      }
    }
  },
  {
    align: "center",
    prop: "source",
    label: "来源"
  },
  {
    align: "center",
    prop: "contactName",
    label: "联系人"
  },
  {
    align: "center",
    prop: "phone",
    label: "联系电话"
  },
  {
    align: "center",
    prop: "demand",
    label: "使用需求"
  },
  {
    align: "center",
    prop: "industry",
    label: "行业领域"
  },
  {
    align: "center",
    prop: "createTime",
    label: "提交时间"
  }
];

const selectRows = ref<Statistic.promotionH5Item[]>([]);

const getTableList = async (params: Statistic.promotionH5Search) => {
  const searchParams = params;
  Reflect.deleteProperty(searchParams, "time");
  return Reflect.has(BUTTONS.value, "promotionH5:list") ? await queryOrdDemandH5(searchParams) : null;
};

const handleExport = () => {
  const exportSearch = proTable.value?.searchParam;
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() =>
    useDownload(exportPromotionH5, exportSearch)
  );
};

const changeSelect = (data: Statistic.promotionH5Item[]) => {
  selectRows.value = data;
};

const proTable = ref<ProTableInstance>();
</script>

<style scoped></style>
