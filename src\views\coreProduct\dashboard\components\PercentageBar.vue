<template>
  <div class="content">
    <div class="percentage-bar-container">
      <div ref="bar" :style="{ width: props.value + '%' }" class="percentage-bar"></div>
    </div>
    <span class="percentage-text">{{ props.value }}%</span>
  </div>
</template>

<script setup lang="ts" name="percentage">
const props = defineProps({
  value: {
    type: Number,
    default: 0
  }
});
</script>

<style scoped>
.content {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.percentage-bar-container {
  position: relative;
  display: inline-block;
  width: 140px;
  height: 20px;
  overflow: hidden;
  background-color: rgb(0 82 217 / 10%);
}
.percentage-bar {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  background-color: #0052d9;
  transition: width 0.3s;
}
.percentage-text {
  margin-left: 8px;
  font-family: PingFangSC, "PingFang SC";
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #3a3a3d;
}
</style>
