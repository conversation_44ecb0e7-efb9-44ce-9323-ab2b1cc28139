<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-16 16:18:06
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-11-27 15:33:41
 * @Description: file content
-->
<template>
  <div class="back-btn" @click="backToTopFun" v-if="baseMapName && baseMapName != 'china'">返回</div>
  <div id="mapContainer" style="width: 100%; height: 100%"></div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { BigScreenMap } from "./MapClass.js";
const baseMapName = ref("china");
let bigScreenMap: any = null;
onMounted(() => {
  bigScreenMap = new BigScreenMap({
    id: "mapContainer",
    clickCallback: (data: any) => {},
    mapChange: (data: any) => {
      baseMapName.value = data.baseMapName;
    }
  });
});
const backToTopFun = () => {
  bigScreenMap && bigScreenMap.backToTop();
};
</script>
<style lang="scss" scoped>
.back-btn {
  width: 102px;
  height: 43px;
  padding: 4px 12px;
  margin-left: 32px;
  font-family: PingFangSC, "PingFang SC";
  font-size: 18px;
  font-weight: 500;
  line-height: 43px;
  color: #a9e4ff;
  cursor: pointer;
  background: url("../../../../../assets/images/screen/bac.png") no-repeat;
  background-size: 100% 100%;
  &:hover {
    color: #ffffff;
    background-image: url("../../../../../assets/images/screen/bac-active.png");
  }
}
</style>
<style lang="scss">
.map-info-box {
  width: 256px;
  height: 192px;
  padding-bottom: 5px;
  font-family: PingFangSC, "PingFang SC";
  font-weight: 400;
  color: #ffffff;
  text-align: left;
  background: url("../../../../../assets/images/screen/box-bg.png") no-repeat;
  background-size: 100% 100%;

  // height: 192px;
  border: none;
  .title {
    margin-left: 15px;
    font-size: 18px;
    line-height: 32px;
  }
  .item {
    padding: 0 15px 5px;
    margin: 8px 0;
    font-size: 16px;
  }
}
</style>
