import http from "@/api";
import { CoreSystem } from "@/api/interface/coreSystem";

/*  用户资源池列表 */
export const queryList = async (params?: CoreSystem.userPlatformSearch) => {
  return await http.get<Array<CoreSystem.userPlatformItemDetail>>("/cnud-desk/admin/userPlatform/pageList", params);
};

/*  用户资源池查看 */
export const userPlatformView = async (userId: string) => {
  return await http.get<Array<CoreSystem.userPlatformItem>>("/cnud-desk/admin/userPlatform/view", { userId });
};

/*  用户资源池默认表 */
export const userPlatformDefault = async () => {
  return await http.get<Array<CoreSystem.userPlatformItemDetail>>("/cnud-desk/admin/defaultPlatform/list");
};

/*  用户资源池权限确认 */
export const userPlatformSave = async (params: CoreSystem.userPlatformItemDetail) => {
  return await http.post("/cnud-desk/admin/userPlatform/save", params);
};

/*  用户资源池默认权限确认 */
export const userPlatformDefaultSave = async (params: CoreSystem.userPlatformItemDetail) => {
  return await http.post("/cnud-desk/admin/defaultPlatform/save", params);
};
