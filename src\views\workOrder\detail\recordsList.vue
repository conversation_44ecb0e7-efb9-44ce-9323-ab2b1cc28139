<template>
  <div>
    <template v-if="dataSource && dataSource.length > 0">
      <ul class="record-list">
        <li class="record-list-item" v-for="item in dataSource" :key="item.id" @click="toDetail(item.id)">
          <template v-if="item.iconUrl">
            <img class="item-left" :src="item.iconUrl ? item.iconUrl : '@/assets/images/record_icon.png'" />
          </template>
          <template v-else>
            <img class="item-left" src="@/assets/images/record_icon.png" />
          </template>
          <div class="item-mid">
            <div class="item-title-box">
              <div class="item-mid-title sle">{{ item.creatorName }}</div>
              <div class="item-mid-rich">{{ item.createTime }}</div>
            </div>
            <div class="item-mid-main">
              <div>{{ item[descKeyName] }}</div>
              <div class="img-wrap" v-if="getImgList(item.images).length > 0">
                <UploadImgs
                  class="image-upload"
                  :file-list="getImgList(item.images)"
                  :limit="100"
                  :file-size="10"
                  height="68px"
                  width="68px"
                  :is-show-edit="false"
                  :is-show-view-text="false"
                  :is-show-del-text="false"
                  disabled
                >
                </UploadImgs>
              </div>
              <div class="file-wrap">
                <UploadFiles
                  v-if="getFileList(item.attachmentIds).length > 0"
                  class="custom-upload-styles"
                  :file-list="getFileList(item.attachmentIds)"
                  :file-type="'.doc, .xls, .docx, .xlsx, .rar, .zip, .jpg, .gif, .jpeg, .png, .pdf'"
                  :limit="100"
                  :file-size="10"
                  :show-file-list="true"
                  :is-show-download="true"
                  :drag="false"
                  list-type="text"
                  disabled
                >
                </UploadFiles>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </template>
    <el-empty v-else description="暂无数据 ~" />
  </div>
</template>

<script setup lang="ts">
import UploadImgs from "@/components/Upload/Imgs.vue";
import UploadFiles from "@/components/Upload/Files.vue";
import type { UploadFile } from "element-plus";

interface IProps {
  dataSource: any[];
  isProduct?: boolean;
  titleKeyName?: string;
  descKeyName?: string;
  searchKey?: string;
}

withDefaults(defineProps<IProps>(), {
  dataSource: () => [],
  isProduct: true,
  titleKeyName: "creatorName",
  descKeyName: "content",
  searchKey: ""
});

const getImgList = (images: string) => {
  if (images) {
    const imgList = JSON.parse(images);
    return imgList;
  }
  return [];
};
const getFileList = (files: string) => {
  if (files) {
    const imgList = JSON.parse(files);

    return imgList?.map((item: UploadFile) => {
      return {
        ...item,
        link: item.link
      };
    });
  }
  return [];
};

const emit = defineEmits(["toDetail"]);
const toDetail = (id: string) => {
  emit("toDetail", id);
};
</script>

<style lang="scss" scoped>
.record-list {
  padding: 0 24px;
  margin: 0 0 24px;
  .record-list-item {
    display: flex;
    width: 100%;
    padding: 24px 0 12px;
    overflow: hidden;
    background: #ffffff;
    border-bottom: 1px dashed #e1e3e6;
    &:first-child {
      margin-top: 0;
    }
    .item-mid {
      display: flex;
      flex: 1;
      flex-direction: column;
      width: 0;
      padding-left: 24px;
      .item-title-box {
        display: flex;
        flex: 0 0 auto;
        flex-wrap: nowrap;
        justify-content: flex-start;
        height: 25px;
        .item-mid-rich {
          flex-shrink: 0;
          margin-left: 16px;
          font-family: PingFangSC, "PingFang SC", sans-serif;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
          color: #909399;
        }
      }
      .item-mid-main {
        font-family: PingFangSC, "PingFang SC", sans-serif;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: #3a3a3d;
      }
      .item-mid-title {
        height: 22px;
        font-family: PingFangSC, "PingFang SC", sans-serif;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        color: #3a3a3d;
      }
    }
    .item-left {
      width: 40px;
      height: 40px;
      background: #0053d933;
      border-radius: 50%;
    }
  }
}
.img-wrap {
  margin-top: 8px;
  :deep(.image-upload .el-upload--picture-card) {
    display: none; /* 隐藏上传按钮 */
  }
  :deep(.image-upload .el-upload__tip) {
    display: none !important;
  }
}
.file-wrap {
  :deep(.custom-upload-styles .upload .el-upload) {
    display: none;
  }
  :deep(.custom-upload-styles .el-upload-list__item) {
    display: inline-block;
    width: auto !important;
    max-width: 100%;
    padding-right: 10px;
    margin-right: 24px;
    .el-upload-list__item-info {
      width: 100%;
      margin-left: 0;
    }
    .el-upload-list__item-file-name {
      flex: 1;
    }
    .el-upload-list__item-name {
      padding-left: 0 !important;
    }
  }
}
</style>
