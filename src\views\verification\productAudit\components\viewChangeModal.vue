<template>
  <div class="dialog-box">
    <el-dialog
      title="查看变更"
      width="1000"
      v-model="dialogVisible"
      @close="handleClose"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div style="height: 600px">
        <ProTable
          :request-auto="false"
          ref="proTable"
          :columns="columns"
          :tool-button="false"
          :data="changeList"
          :pagination="false"
        >
          <template #applicantOperation="{ row }">
            {{ useDictLabel(applicantOperationDict, row.applicantOperation) || "--" }}
          </template>

          <template #originalValue="{ row }">
            <template v-if="row.name === '跳转类型'">
              {{ useDictLabel(jumpTypeDic, row.originalValue) || "--" }}
            </template>
            <template v-else-if="row.name === '支付方式' && row.originalValue">
              {{
                row.originalValue
                  ?.split(",")
                  .map(item => useDictLabel(payDic, item))
                  ?.join(",") || "--"
              }}
            </template>
            <template v-else-if="row.name === '合作类型' && row.originalValue">
              {{ useDictLabel(cooperateDic, row.originalValue) || "--" }}
            </template>
            <template v-else-if="row.name === '支付方式'">
              {{ useDictLabel(payDic, row.originalValue) || "--" }}
            </template>
            <template v-else-if="row.name === '交付方式'">
              {{ useDictLabel(deliveryDic, row.originalValue) || "--" }}
            </template>
            <template v-else-if="row.name === '产品开通方式'">
              {{ useDictLabel(productOpenMethodDict, row.originalValue) || "--" }}
            </template>
            <template v-else-if="row.name === '销售对象'">
              {{ useDictLabel(proCustomerType, row.originalValue) || "--" }}
            </template>
            <template v-else-if="row.name === '规格类型'">
              {{ useDictLabel(skuTypeDic, row.originalValue) || "--" }}
            </template>
            <template v-else> {{ row.originalValue || "--" }}</template>
          </template>

          <template #newValue="{ row }">
            <template v-if="row.name === '跳转类型'">
              {{ useDictLabel(jumpTypeDic, row.newValue) || "--" }}
            </template>
            <template v-else-if="row.name === '支付方式' && row.newValue">
              {{
                row.newValue
                  ?.split(",")
                  .map(item => useDictLabel(payDic, item))
                  ?.join(",") || "--"
              }}
            </template>
            <template v-else-if="row.name === '合作类型' && row.newValue">
              {{ useDictLabel(cooperateDic, row.newValue) || "--" }}
            </template>
            <template v-else-if="row.name === '支付方式'">
              {{ useDictLabel(payDic, row.newValue) || "--" }}
            </template>
            <template v-else-if="row.name === '交付方式'">
              {{ useDictLabel(deliveryDic, row.newValue) || "--" }}
            </template>
            <template v-else-if="row.name === '产品开通方式'">
              {{ useDictLabel(productOpenMethodDict, row.newValue) || "--" }}
            </template>
            <template v-else-if="row.name === '销售对象'">
              {{ useDictLabel(proCustomerType, row.newValue) || "--" }}
            </template>
            <template v-else-if="row.name === '规格类型'">
              {{ useDictLabel(skuTypeDic, row.newValue) || "--" }}
            </template>
            <template v-else> {{ row.newValue || "--" }}</template>
          </template>
        </ProTable>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useDictLabel } from "@/hooks/useDict";

const dictionaryStore = useDicStore();

onMounted(() => {
  dictionaryStore.getAuditApplicantOperation();
});

const applicantOperationDict: any = computed(() => dictionaryStore.auditApplicantOperation);
const proCustomerType = computed(() => dictionaryStore.productCustomerType);
const cooperateDic = computed(() => dictionaryStore.cooperateDic);
const payDic = computed(() => dictionaryStore.orderPayWayDic);
const deliveryDic = computed(() => dictionaryStore.deliveryDic);
const jumpTypeDic = computed(() => dictionaryStore.jumpTypeDic);
const productOpenMethodDict = computed(() => dictionaryStore.productOpenMethodDic);
const skuTypeDic = computed(() => dictionaryStore.skuTypeDic);

defineProps({
  changeList: {
    type: Array,
    default: () => []
  }
});

const dialogVisible = ref(false);
const proTable = ref<ProTableInstance>();
const emit = defineEmits(["update:showDialog", "save"]);

const columns: ColumnProps<any>[] = [
  { prop: "name", label: "属性名称" },
  {
    prop: "applicantOperation",
    label: "变更类型"
  },
  { prop: "originalValue", label: "变更前的值" },
  { prop: "newValue", label: "变更值" }
];

const handleOpen = (rowData?: any) => {
  dialogVisible.value = true;
};

const handleClose = () => {
  dialogVisible.value = false;
};

defineExpose({
  handleOpen
});
</script>
