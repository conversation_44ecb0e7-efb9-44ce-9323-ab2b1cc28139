<template>
  <div class="label-main">
    <label-type-list
      :type-list="typeList"
      title="产品分类"
      :config-type="configType"
      :item-operate="itemOperate"
      v-model:active-index="activeIndex"
      @delete-item="onDelete"
    >
      <template #operateBtn>
        <div class="operate-btn">
          <el-button v-auth="'add'" :icon="Plus" plain @click="addLabelType">新增</el-button>
        </div>
      </template>
      <template #listContent>
        <el-tree
          class="classify-tree"
          :data="typeList"
          :props="defaultProps"
          :show-checkbox="false"
          :default-expanded-keys="['']"
          ref="treeRef"
          node-key="id"
          :allow-drop="canDrop"
          @node-drop="nodeDrop"
          draggable
          @current-change="changeTreeNode"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <!-- 子节点不可选中 -->
              <span
                v-if="data.parentId === undefined && data.children.length"
                class="tree-filed iconfont icon-a-filledicon_folder_opened"
              >
              </span>
              <span v-if="data.parentId" @click.stop class="disabled">{{ node.label }}</span>
              <span v-else>{{ node.label }}</span>
            </div>
          </template>
        </el-tree>
      </template>
    </label-type-list>
    <classify-list :parent-item="activeItem" @modify="updateTree"></classify-list>
    <add-edit-type-dialog
      v-model:visible="dialogVisible"
      :type="dialogType"
      :params="labelTypeParams"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-type-dialog>
  </div>
</template>
<script setup lang="ts" name="productClassify">
import { ref, onMounted } from "vue";
import labelTypeList from "@/components/TypeList/index.vue";
import classifyList from "./components/classifyList.vue";
import { Plus } from "@element-plus/icons-vue";
import addEditTypeDialog from "./components/addOrEditTypeDialog.vue";
import { Label } from "@/api/interface/label";
import {
  getTypeTree as getTypeTreeApi,
  deleteType as deleteTypeApi,
  sortType as sortTypeApi
} from "@/api/modules/productClassify";
import { editType } from "@/enums/dialogTypeEnum";
import { useHandleData } from "@/hooks/useHandleData";
import { ElMessage } from "element-plus";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

interface TreeData {
  name?: string;
  id: number | string;
  children?: TreeData[];
}
onMounted(() => {
  getTypeTree();
});
const canDrop = (draggingNode: any, dropNode: any, type: string) => {
  //同级拖拽
  return draggingNode.parent.id === dropNode.parent.id && type != "inner";
};
const nodeDrop = (draggingNode: any, dropNode: any) => {
  // //同级拖拽
  // return draggingNode.parent.id === dropNode.parent.id && type != "inner";
  const params = dropNode.parent.data.children.map((item: any, index: number) => {
    return {
      ...item,
      sort: index
    };
  });
  sortTypeApi(params).then((res: any) => {
    ElMessage.success(res.msg);
  });
};
const labelTypeParams = ref({});
const dialogType = ref<1 | 2>(1);
const activeIndex = ref(0);
const dialogVisible = ref(false);
const activeItem = ref<TreeData>({
  name: "全部分类",
  id: ""
});
//树节点改变
const changeTreeNode = (data: TreeData, node: any) => {
  if (data.parentId) {
    return;
  }
  activeItem.value = data;
};
const addLabelType = () => {
  if (activeItem.value.id) {
    //最多只有两级，所以不遍历了。如果后续增加层级，这里需要修改。
    labelTypeParams.value = {
      parentId: activeItem.value.id,
      parentList: ["全部分类", activeItem.value.name]
    };
  } else {
    labelTypeParams.value = {
      parentId: activeItem.value.id,
      parentList: ["一级分类"]
    };
  }

  dialogType.value = editType["add"];
  dialogVisible.value = true;
};

const defaultProps = {
  children: "children",
  label: "name",
  id: "id",
  disabled: function (data: any, node: any) {
    return node.isLeaf;
  }
};
const onConfirm = () => {
  dialogVisible.value = false;
  labelTypeParams.value = {};
  getTypeTree();
};
const onCancel = () => {
  dialogVisible.value = false;
  labelTypeParams.value = {};
};
const configType = {
  name: "name",
  key: "id"
};
const getTypeTree = () => {
  if (!BUTTONS.value?.query) {
    return;
  }
  getTypeTreeApi().then(res => {
    typeList.value = [
      {
        name: "全部分类",
        id: "",
        children: res.data as Array<TreeData>
      }
    ];
    activeIndex.value = 0;
  });
};

const itemOperate = ["delete"];
const typeList = ref<Array<TreeData>>([]);
const onDelete = async (item: Label.TagItem) => {
  await useHandleData(deleteTypeApi, { ids: item.id }, `删除${item.name}分类`, "warning", { showRequestMsg: true });
  getTypeTree();
};
const updateTree = () => {
  getTypeTree();
};
</script>
<style lang="scss" scoped>
.label-main {
  display: flex;
  height: 100%;
  overflow: auto;
  background: var(--el-bg-color-page);
}
.operate-btn {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  :deep(.el-button) {
    flex: 1;
  }
}
.disabled {
  cursor: not-allowed;
  opacity: 0.8;
}
.custom-tree-node {
  display: flex;
  align-items: center;
  .tree-filed {
    display: inline-block;
    margin: 0 4px;
    color: #9ca3ac;
  }
}
</style>
