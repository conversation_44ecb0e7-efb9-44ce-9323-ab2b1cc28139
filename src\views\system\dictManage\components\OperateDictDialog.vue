<template>
  <el-dialog v-model="dialogVisible" :title="title + '字典'" width="500px" @close="onCancel">
    <el-form ref="dialogFormRef" :model="dialogForm" :rules="rules">
      <el-form-item label="字典名称" prop="dictCode">
        <el-input
          placeholder="请输入字典名称"
          v-model="dialogForm.dictCode"
          type="text"
          clearable
          :disabled="isDisable"
          maxlength="36"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="字典键名" prop="itemKey">
        <el-input
          placeholder="请输入字典键名"
          v-model="dialogForm.itemKey"
          type="text"
          clearable
          :disabled="isDisable"
          maxlength="32"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="字典键值" prop="itemValue">
        <el-input
          placeholder="请输入字典键值"
          v-model="dialogForm.itemValue"
          type="text"
          clearable
          :disabled="isDisable"
          maxlength="4096"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number style="width: 160px" :min="0" placeholder="请输入排序" v-model="dialogForm.sort" :disabled="isDisable" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="dialogForm.status" :disabled="isDisable">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="dialogForm.remark"
          placeholder="请输入内容"
          type="textarea"
          clearable
          :disabled="isDisable"
          maxlength="255"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onCancel" size="large">取消</el-button>
        <el-button v-if="!isDisable" type="primary" @click="saveData(dialogFormRef)" size="large">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, watch, onMounted } from "vue";
import { FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";
import { saveDictionary, updateDictionary } from "@/api/modules/system";
import { ElTree } from "element-plus";

interface TreeData {
  name?: string;
  id: number | string;
  children?: TreeData[];
}

enum DialogType {
  add = "add",
  edit = "edit"
}
// 菜单类型（1目录 2菜单 3页面元素）
enum menuType {
  catalog = 1,
  menu = 2,
  page = 3
}
const treeRef = ref<InstanceType<typeof ElTree>>();

const props = defineProps({
  dialogType: {
    type: String,
    default: () => "details"
  },
  dialogData: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

const title = computed(() => (props.dialogType === "details" ? "查看" : props.dialogType === "edit" ? "编辑" : "新增"));
const isDisable = computed(() => props.dialogType === "details");

const dialogFormRef = ref();
const dialogForm = ref({
  dictType: "",
  remark: "",
  status: 1,
  dictCode: "",
  itemKey: "",
  itemValue: "",
  sort: undefined
});

const permsList = ref<Array<TreeData>>([]);
const defaultPermsTree = ref<Array<TreeData>>([]); // 存储默认的权限数据

const transformData = (treeData: TreeData[], permsValue = "all", roleId = null) => {
  let children: any = [];
  treeData.forEach(item => {
    if (item.children && item.children.length > 0) {
      children.push({
        ...item,
        children: transformData(item.children, permsValue, roleId),
        permissionCode: permsValue,
        roleId: roleId
      });
    } else {
      children.push({
        ...item,
        permissionCode: permsValue,
        roleId: roleId
      });
    }
  });
  return children;
};

watch(
  () => props.dialogData,
  () => {
    const { dictCode = "", itemKey = "", itemValue = "", remark = "", status = 1, sort = undefined } = props.dialogData || {};
    dialogForm.value.dictCode = dictCode;
    dialogForm.value.itemKey = itemKey;
    dialogForm.value.itemValue = itemValue;
    dialogForm.value.status = status;
    dialogForm.value.remark = remark;
    dialogForm.value.sort = sort;
  }
);

const emits = defineEmits(["update:visible", "cancel", "confirm"]);

const onCancel = () => {
  emits("cancel");
  dialogFormRef?.value?.clearValidate();
  dialogVisible.value = false;
};

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      let params: any = {
        dictCode: dialogForm.value.dictCode,
        itemKey: dialogForm.value.itemKey,
        itemValue: dialogForm.value.itemValue,
        remark: dialogForm.value.remark,
        status: dialogForm.value.status,
        sort: dialogForm.value.sort
      };
      if (!(props.dialogType === DialogType.add)) {
        params = {
          ...params,
          id: props.dialogData.id
        };
      }
      const api = props.dialogType == DialogType.add ? saveDictionary : updateDictionary;
      api(params).then((res: any) => {
        emits("confirm");
        ElMessage.success(res.msg);
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};

const rules = reactive<FormRules>({
  dictCode: [{ required: true, message: "请输入字典名称", trigger: "blur" }],
  itemKey: [{ required: true, message: "请输入字典键名", trigger: "blur" }],
  itemValue: [{ required: true, message: "请输入字典键值", trigger: "blur" }]
});
const dialogVisible = ref(false);
const openDialog = () => {
  permsList.value = JSON.parse(JSON.stringify(defaultPermsTree.value));
  dialogVisible.value = true;
};

defineExpose({ openDialog });
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item--default {
    margin-bottom: 38px;
  }
}
.tips {
  color: rgb(153 153 153);
  text-align: center;
}
.classify-tree {
  width: 100%;
  .custom-tree-node {
    width: 100%;
  }
  .custom-tree-node-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
