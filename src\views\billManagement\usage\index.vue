<template>
  <div class="table-box">
    <ProTable ref="proTable" title="账单列表" :columns="columns" :tool-button="false" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <!-- 表格操作 -->
      <template #operation="{ row }">
        <span v-auth="'view'">
          <el-tooltip content="查看明细" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="toView(row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="ts" name="usage">
import { ref } from "vue";
import { cloneDeep } from "lodash";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { getBillPage, exportBalanceUser } from "@/api/modules/billManagement";

const { BUTTONS } = useAuthButtons();

const proTable = ref<ProTableInstance>();
const router = useRouter();

const getTableList = (params: any) => {
  if (!BUTTONS.value?.query) {
    return Promise.resolve();
  }
  const newParams = cloneDeep(params);
  return getBillPage(newParams);
};

const downloadFile = async () => {
  const newParams = cloneDeep(proTable.value?.searchParam);
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() =>
    useDownload(exportBalanceUser, {
      account: newParams?.account,
      phone: newParams?.phone
    })
  );
};

const columns: ColumnProps<any>[] = [
  { prop: "balanceUpdateTime", label: "更新时间" },
  { prop: "account", label: "用户账号", search: { el: "input" } },
  { prop: "phone", label: "手机号码", search: { el: "input", order: 1 } },
  { prop: "buyQuantity", label: "订购核时" },
  { prop: "usedQuantity", label: "使用/过期核时" },
  { prop: "balance", label: "剩余核时" },
  { prop: "operation", label: "操作", fixed: "right", width: 120 }
];

const toView = async (row: any) => {
  router.push({ path: `/billManagement/usageDetail`, query: { id: row?.userId } });
};
</script>
