<template>
  <div class="fullscreen" @click="handleFullScreen">
    <!--<i :class="['iconfont', isFullscreen ? 'icon-suoxiao' : 'icon-fangda']" class="toolBar-icon" @click="handleFullScreen"></i>-->
    <i :class="['iconfont2', isFullscreen ? 'icon-suofang' : 'icon-quanping1']" class="toolBar-icon"></i>
    <span class="text">{{ isFullscreen ? "缩放" : "全屏" }}</span>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import screenfull from "screenfull";

const isFullscreen = ref(screenfull.isFullscreen);

onMounted(() => {
  screenfull.on("change", () => {
    if (screenfull.isFullscreen) isFullscreen.value = true;
    else isFullscreen.value = false;
  });
});

const handleFullScreen = () => {
  if (!screenfull.isEnabled) ElMessage.warning("当前您的浏览器不支持全屏 ❌");
  screenfull.toggle();
};
</script>

<style scoped lang="scss">
.fullscreen {
  cursor: pointer;
  .toolBar-icon {
    vertical-align: middle;
  }
  .text {
    font-size: 14px;
    vertical-align: middle;
  }
}
</style>
