<template>
  <div class="order-analysis-container">
    <div class="order-header">
      <div class="header-box">
        <el-form :inline="true" ref="orderFormRef" :model="orderForm" :rules="rules">
          <el-form-item label="" prop="btn">
            <div>
              <el-button-group size="large" class="botton-box">
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.day, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.day)"
                >
                  日报表
                </el-button>
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.month, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.month)"
                >
                  月报表
                </el-button>
              </el-button-group>
            </div>
          </el-form-item>
          <el-form-item label="日期" prop="countDate">
            <el-date-picker
              v-if="dateType === DateType.day"
              class="data-picker-style"
              type="daterange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @focus="handleFocus"
              @calendar-change="handleChange"
              :disabled-date="disabledDate"
            />
            <el-date-picker
              v-else
              class="data-picker-style"
              type="monthrange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm(orderFormRef)"> 查询 </el-button>
            <el-button @click="resetForm(orderFormRef)"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="analysis-content">
      <div class="content-title">门户访问量</div>
      <div class="statistical-data">
        <div class="order-main">
          <indicator-button
            :indicator-title="'数据维度'"
            :list="orderIndex"
            :active="activeBtn"
            @item-click="onChangeOrder"
          ></indicator-button>
          <indicator-button
            :indicator-title="'数据指标'"
            :list="dataIndicators"
            :active="activeIndicators"
            @item-click="onChangeIndicators"
          ></indicator-button>

          <div class="chart-box">
            <bar-and-line-chart ref="orderLineChartRef" :ratio-name="ratioName" />
          </div>
        </div>
      </div>
    </div>
    <order-analysis-list :params="{ ...commonParams }" ref="analysisListRef"></order-analysis-list>
  </div>
</template>

<script setup lang="ts" name="portalTrafficAnalysis">
import { reactive, ref, onMounted, computed } from "vue";
import { ECharts } from "echarts";
import dayjs from "dayjs";
import type { FormInstance, FormRules } from "element-plus";
import IndicatorButton from "@/components/IndicatorButton/index.vue";
import barAndLineChart from "./components/barAndLineChart.vue";
import orderAnalysisList from "./components/orderAnalysisList.vue";
import { getAccessChartData } from "@/api/modules/statisticAnalysis";
import { startDate, endDate, startMonthDate, endMonthDate } from "../commonAnalysis";

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}
// 获取子组件的ref
interface ChartExpose {
  initChart: (params: any) => ECharts;
}
const orderLineChartRef = ref<ChartExpose>();
const analysisListRef = ref();

const dateType = ref<DateType>(DateType.day);
const activeBtn = ref("pv");
const activeIndicators = ref("all");
const chartData = ref({
  nameList: [],
  data1: [],
  data2: [],
  data3: []
});

const ratioName = computed(() => `${dateType.value === DateType.day ? "日" : "月"}环比`);

const commonParams = reactive({
  endTime: endDate,
  startTime: startDate,
  indexUnit: dateType,
  indexType: activeBtn
});

// 月报表入参月份
const handleCountDate = () => {
  if (dateType.value === DateType.month) {
    // 门户访问量分析 --- 接口需要求入参日（默认1号）
    return {
      endTime: dayjs(commonParams.endTime).format("YYYY-MM-DD"),
      startTime: dayjs(commonParams.startTime).format("YYYY-MM-DD")
    };
  }
  return {};
};
// 图表
const getChat = () => {
  getAccessChartData({
    ...commonParams,
    ...handleCountDate()
  }).then((res: any) => {
    if (res.code === 200) {
      // na 新增 cum 累计 cum_mom环比
      const { na = [], cum = [], cum_mom = [] } = res.data || {};
      const nameList = na?.map((item: any) => item.indexLabel);
      const lineData: any = [];
      nameList?.forEach((item: string) => {
        const line = cum_mom?.find((monItem: any) => monItem.indexLabel === item) || {};
        if (Object.keys(line).length === 0) {
          lineData.push("");
        } else {
          lineData.push(line.indexValue?.replace(/%/g, ""));
        }
      });
      const params = {
        nameList,
        data1: na?.map((item: any) => item.indexValue),
        data2: cum?.map((item: any) => item.indexValue),
        data3: lineData
      };
      chartData.value = {
        ...params
      };
      orderLineChartRef.value?.initChart(params);
    }
  });
};

const getAllData = () => {
  getChat();
  analysisListRef.value.getTableList({ ...commonParams, ...handleCountDate() });
};

onMounted(() => {
  getAllData();
});

const changeDate = (type: DateType) => {
  if (type === DateType.month) {
    commonParams.startTime = startMonthDate;
    commonParams.endTime = endMonthDate;
    orderForm.countDate = [startMonthDate, endMonthDate];
  } else {
    commonParams.startTime = startDate;
    commonParams.endTime = endDate;
    orderForm.countDate = [startDate, endDate];
  }
};
const onChangeDateType = (type: DateType) => {
  dateType.value = type;
  commonParams.indexUnit = type;
  changeDate(type);
  getAllData();
};

const orderIndex = [
  {
    label: "PV",
    value: "pv"
  },
  {
    label: "UV",
    value: "uv"
  }
];

const dataIndicators = computed(() => [
  {
    label: "全部",
    value: "all"
  },
  {
    label: "新增",
    value: "add"
  },
  {
    label: "累计",
    value: "total"
  },
  {
    label: `${dateType.value === DateType.day ? "日" : "月"}环比`,
    value: "ratio"
  }
]);

const orderFormRef = ref<FormInstance>();
const orderForm = reactive({
  countDate: [startDate, endDate],
  productId: "",
  providerId: ""
});

const checkDate = (_: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请选择日期"));
  } else {
    // 定义两个日期字符串
    const dateString1 = value[0];
    const dateString2 = value[1];
    // 将日期字符串转换为 dayjs 对象
    const date1 = dayjs(dateString1);
    const date2 = dayjs(dateString2);

    // 获取两个日期之间的月数差
    const monthDiff = Math.abs(date2.diff(date1, "month"));

    if (monthDiff > 11) {
      callback(new Error("最多只能查询12个月的数据"));
    }
  }
  callback();
};

const dateRule: any = computed(() =>
  dateType.value === DateType.day
    ? [{ required: true, message: "请选择日期", trigger: "blur" }]
    : [
        { required: true, message: "请选择日期", trigger: "blur" },
        { trigger: "blur", validator: checkDate }
      ]
);
const rules = reactive<FormRules>({
  countDate: dateRule
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      commonParams.startTime = orderForm.countDate[0];
      commonParams.endTime = orderForm.countDate[1];
      getAllData();
    } else {
      console.log("error submit!", fields);
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  // 处理重置日报表与月报表不相同
  changeDate(dateType.value);
};

const onChangeOrder = (values: any) => {
  activeBtn.value = values.value;
  getChat();
};

const onChangeIndicators = (values: any) => {
  activeIndicators.value = values.value;
  let params = {
    nameList: [],
    data1: [],
    data2: [],
    data3: []
  };
  switch (values.value) {
    case "all":
      params = {
        nameList: chartData.value.nameList,
        data1: chartData.value.data1,
        data2: chartData.value.data2,
        data3: chartData.value.data3
      };
      break;
    case "add":
      params = {
        nameList: chartData.value.nameList,
        data1: chartData.value.data1,
        data2: [],
        data3: []
      };
      break;
    case "total":
      params = {
        nameList: chartData.value.nameList,
        data1: [],
        data2: chartData.value.data2,
        data3: []
      };
      break;
    case "ratio":
      params = {
        nameList: chartData.value.nameList,
        data1: [],
        data2: [],
        data3: chartData.value.data3
      };
      break;

    default:
      break;
  }
  orderLineChartRef.value?.initChart(params);
};

const chooseDay = ref<any>(null);
const handleChange = (val: Date[]) => {
  const [pointDay] = val;
  chooseDay.value = pointDay;
};
//取值方法
const handleFocus = () => {
  chooseDay.value = null;
};

const disabledDate = (time: number) => {
  if (!chooseDay.value) {
    return false;
  }
  let timeRange = 30;
  const con1 = new Date(chooseDay.value).getTime() - timeRange * 24 * 60 * 60 * 1000;
  const con2 = new Date(chooseDay.value).getTime() + timeRange * 24 * 60 * 60 * 1000;
  return time < con1 || time > con2;
};
</script>

<style lang="scss" scoped>
.cust-border {
  border-bottom: 1px solid #e1e3e6;
}
.order-analysis-container {
  background-color: #ffffff;
  .order-header {
    padding: 16px 0 0 24px;
    @extend.cust-border;
    .header-box {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      :deep(.el-form-item--default) {
        margin-bottom: 16px;
      }
      :deep(.el-form--inline .el-form-item) {
        margin-right: 12px;
      }
      :deep(.data-picker-style) {
        max-width: 210px;
      }
      .select-style {
        max-width: 190px;
      }
      .type-box {
        flex: 164px;
      }
    }
    .btn {
      height: 36px;
      &.active {
        color: var(--el-color-primary);
        background-color: var(--el-fill-color-blank);
        border-color: var(--el-color-primary);
      }
    }
  }
  .analysis-content {
    height: 470px;
    padding: 16px 24px 0;
    @extend.cust-border;
    .content-title {
      width: 100%;
      height: 24px;
      margin-bottom: 16px;
      font-family: "PingFangSC, PingFang SC,sans-serif";
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #000000d9;
    }
    .statistical-data {
      display: flex;
      width: 100%;
    }
    .order-main {
      flex: 1;
      .chart-box {
        width: 100%;
        height: 336px;
      }
    }
    .order-aside {
      width: 300px;
    }
  }
}
</style>
