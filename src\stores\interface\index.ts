/*
 * @Author: <EMAIL>
 * @Date: 2023-11-23 14:26:25
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-11-29 15:16:08
 * @Description: file content
 */
export type LayoutType = "vertical" | "classic" | "transverse" | "columns";

export type AssemblySizeType = "large" | "default" | "small";

export type LanguageType = "zh" | "en" | null;

/* GlobalState */
export interface GlobalState {
  layout: LayoutType;
  assemblySize: AssemblySizeType;
  language: LanguageType;
  maximize: boolean;
  primary: string;
  isDark: boolean;
  isGrey: boolean;
  isWeak: boolean;
  asideInverted: boolean;
  headerInverted: boolean;
  isCollapse: boolean;
  accordion: boolean;
  breadcrumb: boolean;
  breadcrumbIcon: boolean;
  tabs: boolean;
  tabsIcon: boolean;
  footer: boolean;
}

/* UserState */
export interface UserState {
  token: string;
  userInfo: {
    id?: string;
    name?: string;
    userName?: string;
    phone?: string;
    email?: string;
    account?: string;
    userType?: string; // 用户类型 [opt_user](1内部用户 2合作商用户)
  };
  sysInfo: any;
  publicKey: string;
  orgs: any[];
}

/* tabsMenuProps */
export interface TabsMenuProps {
  icon: string;
  customIconName: string;
  title: string;
  path: string;
  name: string;
  close: boolean;
}

/* TabsState */
export interface TabsState {
  tabsMenuList: TabsMenuProps[];
}

/* ProductState */
export interface ProductState {
  productDetailObj: any;
  sendProductDetailParam: any;
  providerList: any[];
  solutionDetailObj: object;
  productAuditDetailObj: object;
}

/* DictionaryState */
export interface DictionaryState {
  cooperateDic: any[];
  deliveryDic: any[];
  timeDic: any[];
  quantityDic: any[];
  bannerActionType: any[];
  newsStatus: any[];
  orderTypeDic: any[];
  orderStatusDic: any[];
  auditStatusDic: any[];
  orderPayWayDic: any[];
  jumpTypeDic: any[];
  productOpenMethodDic: any[];
  skuTypeDic: any[];
  cooperatePlatformDic: any[];
  resourcePoolStatus: any[];
  cloudPlatformList: any[];
  productCustomerType: any[];
  auditStatusEnterprise: any[];
  industryTypeEnterprise: any[];
  certificateApplyType: any[];
  applyAuditStatus: any[];
  introduceAuditStatus: any[];
  agreementAuditStatus: any[];
  providerAgreementStatus: any[];
  dataPermission: any[];
  auditOperation: any[];
  productAuditStatus: any[];
  bannerAuditAction: any[];
  auditApplicantOperation: any[];
  noticeStatus: any[];
  noticeAuditStatus: any[];
  orderRegion: any[];
  orderScore: any[];
  noticeTypeDic: any[];
  orderProdType: any[];
  orderState: any[];
  probationFlagDic: any[];
  operateStatus: any[];
  requireReleaseStatus: any[];
  requireReleaseBusinessStatus: any[];
  requireType: any[];
  jbgsRequireType: any[];
  demandConstructionAddress: any[];
  demandAcceptStatus: any[];
}

/* AuthState */
export interface AuthState {
  routeName: string;
  authButtonList: {
    [key: string]: string[];
  };
  authMenuList: Menu.MenuOptions[];
}

/* KeepAliveState */
export interface KeepAliveState {
  keepAliveName: string[];
}
