<template>
  <div class="card pl32">
    <div class="infos">
      <div class="header-title">
        审核状态：<span :class="`status-${detailData.auditStatus}`">{{
          useDictLabel(requireReleaseStatus, detailData.auditStatus)
        }}</span>
      </div>
      <el-form ref="formRef" label-width="110px" class="demo-reviewForm" :label-position="'left'">
        <el-row>
          <el-col v-for="item in baseInfo" :key="item.key" :span="8">
            <div class="info-item">
              <span class="info-label">{{ item.label }}:</span>
              <span v-if="item.key === 'releaseType'" class="info-text">{{ isRequireDetail ? "需求发布" : "需求应答" }}</span>
              <span v-else-if="item.key === 'auditStatus'" class="info-text">{{
                useDictLabel(requireReleaseStatus, detailData.auditStatus || "--")
              }}</span>
              <span v-else class="info-text">{{ detailData[item.key] || "--" }}</span>
            </div>
          </el-col>
          <el-col :span="24" class="header-title">审核信息</el-col>
          <el-col :span="24" class="pl20 mb20">需求方</el-col>
          <el-col :span="24" class="pl20">
            <el-row>
              <el-col v-for="item in demanderInfo" :key="item.key" :span="item.span || 6">
                <div class="info-item">
                  <span class="info-label">{{ item.label }}:</span>
                  <span v-if="item.key === 'demandType'" class="info-text">{{
                    useDictLabel(requireType, detailData.demandType || "--")
                  }}</span>
                  <span v-else-if="item.key === 'categoryName'" class="info-text">{{ formatCategoryName }}</span>
                  <span v-else-if="item.key === 'demandPicUrl'" class="info-text">
                    <img v-if="detailData?.demandPicUrl" :src="detailData?.demandPicUrl" alt="需求图片" class="demand-image" />
                    <span v-else>--</span>
                  </span>
                  <span v-else-if="item.key === 'price'" class="info-text">{{
                    detailData
                      ? detailData.isNegotiation === 1
                        ? "面谈"
                        : detailData.price
                        ? detailData.price + "万元"
                        : "--"
                      : "--"
                  }}</span>
                  <span v-else-if="item.key === 'demandUrl'" class="info-text">
                    <el-icon>
                      <Document />
                    </el-icon>
                    <!-- <a :href="detailData?.demandUrl" class="file-name">下载附件</a> -->
                    <a :href="detailData?.demandUrl" class="file-name" v-if="detailData?.demandUrl">下载附件</a>
                    <span v-else>暂无附件</span>
                  </span>
                  <!-- <span v-else-if="item.key === 'constructionAddress'" class="info-text">{{
                    useDictLabel(demandConstructionAddress, detailData.constructionAddress || "--")
                  }}</span> -->
                  <span v-else class="info-text">{{ detailData[item.key] || "--" }}</span>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <template v-if="!isRequireDetail">
            <el-col :span="24" class="pl20 mb20">供应方</el-col>
            <el-col :span="24" class="pl20">
              <el-row>
                <el-col v-for="item in supplierInfo" :key="item.key" :span="item.span || 6">
                  <div class="info-item">
                    <span class="info-label">{{ item.label }}:</span>
                    <span v-if="item.key === 'solutionUrl'" class="info-text">
                      <el-icon>
                        <Document />
                      </el-icon>
                      <a :href="detailData?.solutionUrl" class="file-name" v-if="detailData?.solutionUrl">下载附件</a>
                      <span v-else>暂无附件</span>
                    </span>
                    <span v-else class="info-text">{{ detailData[item.key] || "--" }}</span>
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </template>
          <el-col class="mt20">
            <Examine
              :verification-id="'id'"
              :audit-msg="auditMsg"
              :is-disabled="isReadOnly"
              :is-show-operate-btn="!isReadOnly"
              @on-confirm="onConfirm"
              @on-back="onBack"
            ></Examine>
          </el-col>
          <div class="last-next">
            <!-- 因为列表是时间逆序返回：after 对应上一条，brfore 对应下一条 -->
            <el-col v-if="beforeAndAfterData.afterName" @click="toView(beforeAndAfterData.afterId)">
              上一条 {{ beforeAndAfterData.afterName }}
            </el-col>
            <el-col v-else class="disable">上一条</el-col>
            <el-col v-if="beforeAndAfterData.beforeName" @click="toView(beforeAndAfterData.beforeId)">
              下一条 {{ beforeAndAfterData.beforeName }}
            </el-col>
            <el-col v-else class="disable">下一条</el-col>
          </div>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts" name="requireReleaseJbgsReviewDetail">
import { ref, reactive, computed, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import Examine from "@/views/verification/examine.vue";
import { closeTabItem } from "@/utils/closeTab";
import { useDictLabel } from "@/hooks/useDict";
import { useDicStore } from "@/stores/modules/dictionaty";
import { Document } from "@element-plus/icons-vue";
import {
  getRequireDetail,
  getSchemeDetail,
  getBeforeAndAfter,
  reviewRequire,
  reviewScheme,
  queryOclDemandCategory
} from "@/api/modules/requireReleaseJbgs";
import { OperateType, TabType, AuditStatusEnum } from "./type";

const route = useRoute();
const router = useRouter();
const dictionaryStore = useDicStore();
const isRequireDetail = route.query.pageType === TabType.REQUIRE;
const isReadOnly = ref(false);
isReadOnly.value = route.query.operateType === OperateType.DETAIL;
// 审核状态
const requireReleaseStatus: any = computed(() => dictionaryStore.requireReleaseStatus);
// 需求类型
const requireType: any = computed(() => dictionaryStore.jbgsRequireType);
// 实施地址
// const demandConstructionAddress: any = computed(() => dictionaryStore.demandConstructionAddress);

const baseInfo = [
  {
    label: "发布类型",
    key: "releaseType"
  },
  {
    label: "提交人员",
    key: isRequireDetail ? "creatorName" : "solutionCreatorName"
  },
  {
    label: "提交时间",
    key: "createTime"
  },
  {
    label: "审核状态",
    key: "auditStatus"
  },
  {
    label: "审核人",
    key: "auditorName"
  },
  {
    label: "审核时间",
    key: "auditTime"
  }
];
const demanderInfo = [
  {
    label: "企业名称",
    key: isRequireDetail ? "companyName" : "demandCompanyName"
  },
  {
    label: "企业地址",
    key: isRequireDetail ? "companyAddress" : "demandCompanyAddress"
  },
  {
    label: "联系人",
    key: isRequireDetail ? "contactName" : "demandContactName"
  },
  {
    label: "联系人电话",
    key: isRequireDetail ? "phone" : "demandPhone"
  },
  {
    label: "需求名称",
    key: isRequireDetail ? "name" : "demandName"
  },
  {
    label: "关键字",
    key: "keyWord"
  },
  {
    label: "需求地点",
    key: "constructionAddressName"
  },
  {
    label: "需求类别",
    key: "demandType"
  },
  {
    label: "需求预算",
    key: "price"
  },
  {
    label: "所属领域",
    key: "categoryName",
    formatter: () => formatCategoryName.value,
    span: 12
  },
  {
    label: "需求图片",
    key: "demandPicUrl",
    span: 6
  },

  // {
  //   label: "截止时间",
  //   key: "demandEndTime"
  // },
  // {
  //   label: "实施地址",
  //   key: "constructionAddress"
  // },
  {
    label: "需求说明",
    key: "description",
    span: 24
  },
  {
    label: "现有情况",
    key: "currentStatus",
    span: 24
  },
  {
    label: "需求附件",
    key: "demandUrl",
    span: 24
  }
];
const supplierInfo = [
  {
    label: "企业名称",
    key: "solutionCompanyName"
  },
  {
    label: "企业地址",
    key: "solutionCompanyAddress"
  },
  {
    label: "联系人",
    key: "solutionContactName"
  },
  {
    label: "联系人电话",
    key: "solutionPhone"
  },
  {
    label: "方案名称",
    key: "solutionName",
    span: 24
  },
  {
    label: "方案描述",
    key: "solutionDescription",
    span: 24
  },
  {
    label: "方案附件",
    key: "solutionUrl",
    span: 24
  }
];
const detailData = reactive<any>({
  auditStatus: "wait"
});
const auditMsg = ref();

const oclDemandCategory = ref<any[]>([]);

// 添加计算属性，根据categoryName字段的值从oclDemandCategory中获取对应的名称
const formatCategoryName = computed(() => {
  // 处理所属领域
  const categoryNames = [];

  // 查找一级分类名称
  const level1 = oclDemandCategory.value.find(cat => cat.id === detailData.categoryIdL1);
  if (level1) {
    categoryNames.push(level1.name);

    // 在一级分类的子分类中查找二级分类
    const level2 = level1.children?.find(cat => cat.id === detailData.categoryIdL2);
    if (level2 && level2.id !== detailData.categoryIdL1) {
      categoryNames.push(level2.name);

      // 在二级分类的子分类中查找三级分类
      const level3 = level2.children?.find(cat => cat.id === detailData.categoryId);
      if (level3 && level3.id !== detailData.categoryIdL2) {
        categoryNames.push(level3.name);
      }
    }
  }

  console.log(categoryNames, 1);

  return categoryNames.join("/") || "--";
});

const getDetail = async () => {
  try {
    const res = isRequireDetail ? await getRequireDetail({ id: route.query.id }) : await getSchemeDetail({ id: route.query.id });
    Object.assign(detailData, res.data);
    auditMsg.value = res.data?.auditMsg || "";
    if (!isReadOnly.value) {
      isReadOnly.value = detailData.auditStatus !== AuditStatusEnum.WAIT;
    }
  } catch (error) {}
};

const getOclDemandCategory = async () => {
  try {
    const res = await queryOclDemandCategory();
    oclDemandCategory.value = res.data as any[];
  } catch (error) {
    console.log(error);
  }
};

const onConfirm = async (value: any) => {
  auditMsg.value = value.auditRemark;
  const auditStatus = value.type == "pass" ? "pass" : "refuse";
  const params = {
    id: route.query.id,
    auditStatus: auditStatus,
    auditMsg: auditMsg.value
  };
  try {
    const res = isRequireDetail ? await reviewRequire(params) : await reviewScheme(params);
    ElMessage({
      message: value.type == "pass" ? "审核通过成功!" : "审核不通过完成!",
      type: "success"
    });
    closeTabItem(route);
    onBack();
  } catch (error) {}
};
const onBack = () => {
  router.push("/verification/requireReleaseJbgsReview");
};

const beforeAndAfterData = reactive<any>({});
const getBeforeAndAfterRecord = async () => {
  try {
    const res = await getBeforeAndAfter({ id: route.query.id, type: route.query.pageType });
    Object.assign(beforeAndAfterData, res.data);
  } catch (error) {}
};
const toView = (id: string) => {
  router.push(
    `/verification/requireReleaseJbgsReview/detail?id=${id}&operateType=${route.query.operateType}&pageType=${route.query.pageType}`
  );
};

onBeforeMount(() => {
  dictionaryStore.requireReleaseStatus.length === 0 && dictionaryStore.getRequireReleaseStatus();
  dictionaryStore.jbgsRequireType.length === 0 && dictionaryStore.getJbgsRequireType();
  // dictionaryStore.demandConstructionAddress.length === 0 && dictionaryStore.getDemandConstructionAddress();
  getOclDemandCategory();
  getDetail();
  getBeforeAndAfterRecord();
});
</script>
<style scoped lang="scss">
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
  .status-wait {
    color: #ffaa00;
  }
  .status-pass {
    color: #0052d9;
  }
  .status-refuse {
    color: #e5302f;
  }
}
.infos {
  margin-left: 10px;
  .info-item {
    margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      margin-right: 10px;
      color: #73787f;
      text-align: right;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: pre-wrap;
    }
  }
  .demo-reviewForm.el-form {
    .el-form-item {
      :deep(.el-form-item__label) {
        color: #73787f;
      }
    }
  }
}
.last-next {
  margin-top: 10px;
  font-size: 12px;
  color: var(--el-color-primary);
  cursor: pointer;
  .disable {
    color: #c0c4cc;
    cursor: auto;
  }
}
.demand-image {
  width: 240px;
  height: 180px;
  object-fit: fill;
  border-radius: 4px;
}
</style>
