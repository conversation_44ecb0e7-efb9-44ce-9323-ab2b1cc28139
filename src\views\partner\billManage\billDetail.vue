<template>
  <div class="table-box">
    <ProTable ref="proTable" title="账单明细表" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 #tableHeader左侧 #toolButton右侧 -->
      <template #tableHeader>
        <!-- 默认置灰,选择后支持点击，合作商类型账号不可见  -->
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="partnerManagement">
import { ref, reactive, onMounted, computed, onActivated } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { useDownload } from "@/hooks/useDownload";
import { useDicStore } from "@/stores/modules/dictionaty";

import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { exportBillDetailPageList, getBillDetailPageList } from "@/api/modules/billManage";

const { BUTTONS } = useAuthButtons();
const proTable = ref<ProTableInstance>();
const router = useRouter();
const route = useRoute();
let billId = route.query.id;

const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "providerName", label: "合作商", width: 130 },
  {
    prop: "billDate",
    label: "账单日期",
    width: 100
  },
  { prop: "orderId", label: "订单号", width: 200, search: { el: "input" } },
  {
    prop: "createTime",
    label: "订单时间",
    width: 180,
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY/MM/DD HH:mm:ss",
        valueFormat: "YYYY/MM/DD HH:mm:ss"
      }
    }
  },
  {
    prop: "productName",
    label: "产品名称",
    align: "left",
    width: 180
  },
  {
    prop: "unicomDividendProportion",
    label: "分成比例",
    align: "left",
    width: 180,
    render: (scope: any) => {
      return (
        <span>
          {scope.row.unicomDividendProportion} : {scope.row.partnerDividendProportion}
        </span>
      );
    }
  },
  {
    prop: "commissionFee",
    label: "手续费（元）",
    align: "left",
    width: 180
  },
  {
    prop: "unicomAccountFee",
    label: "平台金额（元）",
    align: "left",
    width: 180
  },
  {
    prop: "providerAccountFee",
    label: "合作方金额（元）",
    align: "left",
    width: 180
  }
];

const getTableList = (params: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.accountId = billId;
  newParams.createTime && (newParams.orderTimeOfStart = newParams.createTime[0]);
  newParams.createTime && (newParams.orderTimeOfEnd = newParams.createTime[1]);
  delete newParams.createTime;
  return getBillDetailPageList(newParams);
};

const downloadFile = async (row: any) => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.orderTimeOfEnd = newParams.createTime[0]);
  newParams.createTime && (newParams.orderTimeOfStart = newParams.createTime[1]);
  delete newParams.createTime;
  newParams.accountId = billId;
  ElMessageBox.confirm("确认导出账单明细列表?", "提示", { type: "warning" }).then(() =>
    useDownload(exportBillDetailPageList, newParams)
  );
};

onActivated(() => {
  proTable.value?.getTableList();
});
</script>

<style lang="scss" scoped>
.table-box {
  width: 100%;
}
</style>
