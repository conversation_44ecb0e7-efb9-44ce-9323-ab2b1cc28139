<!--
订单详情--客户经理信息
-->
<template>
  <div class="info-box card">
    <div class="box-title">
      <div class="title">客户经理信息</div>
    </div>
    <div class="infos" v-if="type === 'view'">
      <el-row>
        <el-col :span="8" v-for="(item, index) in dataInfo" :key="item.value + index" :push="index % 3">
          <div class="info-item">
            <span class="info-label">{{ item.label }}</span>
            <span class="info-text">{{ item.value }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="infos" v-else>
      <el-row>
        <el-col :span="8">
          <div class="info-item">
            <el-form-item
              prop="customerManagerName"
              label="客户经理姓名"
              :rules="[{ required: true, message: '请填写客户经理姓名', trigger: 'blur' }]"
            >
              <el-input maxlength="5" :model-value="customerManagerName" @input="$emit('update:customerManagerName', $event)" />
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <el-form-item
              prop="customerManagerDept"
              label="所属部门："
              :rules="[{ required: true, message: '请填写所属部门', trigger: 'blur' }]"
            >
              <el-input maxlength="5" :model-value="customerManagerDept" @input="$emit('update:customerManagerDept', $event)" />
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <el-form-item
              prop="customerManagerPhone"
              label="手机号码："
              :rules="[
                { required: true, message: '请填写联系人手机号码', trigger: 'blur' },
                { trigger: 'blur', validator: checkPhone }
              ]"
            >
              <el-input
                maxlength="11"
                :model-value="customerManagerPhone"
                @input="$emit('update:customerManagerPhone', $event)"
              />
            </el-form-item>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
interface DataInfo {
  label: string;
  value: string;
}

/**
 * type view是详情展示,edit是新增订单时输入
 */
interface InfoTemplate {
  dataInfo?: DataInfo[];
  type?: string;
  customerManagerName?: string;
  customerManagerDept?: string;
  customerManagerPhone?: string;
}

withDefaults(defineProps<InfoTemplate>(), {
  dataInfo: () => [],
  type: "view",
  customerManagerName: "",
  customerManagerDept: "",
  customerManagerPhone: ""
});

const emits = defineEmits(["update:customerManagerName", "update:customerManagerDept", "update:customerManagerPhone"]);
const checkPhone = (rule: any, value: any, callback: any) => {
  const reg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
  if (!reg.test(value)) {
    callback(new Error("请输入正确的手机号"));
  } else {
    callback();
  }
};
</script>

<style scoped lang="scss">
@import "../order.module.scss";
</style>
