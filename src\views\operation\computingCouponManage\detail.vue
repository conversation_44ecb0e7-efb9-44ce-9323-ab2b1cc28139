<template>
  <div class="card table-box info-box">
    <div class="header-title-common mb24">算力券申请详情信息</div>
    <div class="infos">
      <el-row>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">企业名称：</span>
            <span class="info-text">{{ couponDetail?.companyName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label center-label">社会信用统一代码：</span>
            <span class="info-text">{{ couponDetail?.usciCode }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">所属行业：</span>
            <span class="info-text">{{ useDictLabel(dict_industry_type, couponDetail?.industryType) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">公司注册地：</span>
            <span class="info-text">{{ couponDetail?.registeredAddress }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label center-label">注册时间：</span>
            <span class="info-text">{{ couponDetail?.registeredTime }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">通讯地址：</span>
            <span class="info-text">{{ couponDetail?.companyAddress }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">联系人姓名：</span>
            <span class="info-text">{{ couponDetail?.contactName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label center-label">手机号码：</span>
            <span class="info-text">{{ couponDetail?.phone }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">客户账号：</span>
            <span class="info-text">{{ couponDetail?.creatorName }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts" name="computingCouponDetail">
import { onMounted, reactive, ref } from "vue";
import { useRoute } from "vue-router";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { getCouponApplyDetail } from "@/api/modules/business/compCouponManage";

const { dict_industry_type, coupon_apply_status } = useDict("dict_industry_type", "coupon_apply_status");

const route = useRoute();
const id: string = (route.query.id as string) || "";

const couponDetail = reactive({
  companyName: "",
  usciCode: "",
  industryType: "",
  registeredAddress: "",
  registeredTime: "",
  companyAddress: "",
  contactName: "",
  phone: "",
  creatorName: ""
});

const getDetails = () => {
  getCouponApplyDetail({
    id
  }).then(res => {
    if (res.code === 200) {
      Object.assign(couponDetail, res?.data || {});
    }
  });
};
onMounted(() => {
  getDetails();
});
</script>

<style lang="scss" scoped>
.info-box {
  .title {
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #3a3a3d;
    border-bottom: 1px solid #e4e7ed;
  }
  .infos {
    margin-left: 10px;
  }
  .info-item {
    margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      width: 90px;
      margin-right: 10px;
      color: #73787f;
      text-align: right;
    }
    .center-label {
      width: 130px;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
}
</style>
