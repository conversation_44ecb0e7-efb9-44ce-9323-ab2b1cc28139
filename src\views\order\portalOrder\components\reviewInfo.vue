<template>
  <div class="info-box card">
    <div class="box-title">
      <div class="title">审核信息</div>
    </div>
    <div class="infos">
      <el-row>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">审核状态：</span>
            <span class="info-text">{{ useDictLabel(ord_audit_status, orderInfo?.auditStatus) }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="1">
          <div class="info-item">
            <span class="info-label">审核人：</span>
            <span class="info-text">{{ orderInfo?.auditor }}</span>
          </div>
        </el-col>
        <el-col :span="8" :push="2">
          <div class="info-item">
            <span class="info-label">审核时间：</span>
            <span class="info-text">{{ orderInfo?.auditTime }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="info-label">审核意见：</span>
            <span class="info-text">{{ orderInfo?.auditRemark }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDict, useDictLabel } from "@/hooks/useDict";
const { ord_audit_status } = useDict("ord_audit_status");

defineProps({
  orderInfo: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
</script>

<style scoped lang="scss">
@import "../order.module.scss";
</style>
