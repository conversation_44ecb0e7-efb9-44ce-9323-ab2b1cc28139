<template>
  <div>
    <el-dialog v-model="dialogVisible" title="关联用户" width="50%" @close="onCancel">
      <div class="content">
        <el-radio-group v-model="selectId">
          <ProTable
            ref="proTable"
            title="合作商协议管理列表"
            :request-auto="false"
            :columns="columns"
            :request-api="getTableList"
            :init-param="initParam"
            :tool-button="false"
          >
            <template #id="scope">
              <el-radio :label="scope.row.id">{{ `` }}</el-radio>
            </template>
          </ProTable>
        </el-radio-group>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="onConfirm" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, ref } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { getAllUserList } from "@/api/modules/system";
import { ElMessage } from "element-plus";

const emits = defineEmits(["confirm"]);

const dialogVisible = ref(false);

const proTable = ref<ProTableInstance>();

const columns: ColumnProps<any>[] = [
  {
    prop: "id",
    label: "选择",
    width: 100
  },
  {
    prop: "account",
    label: "用户账号",
    search: {
      el: "input"
    }
  },
  {
    prop: "phone",
    label: "手机号码"
  }
];

const selectId = ref<string>("");

const getTableList = (params: any) => {
  const queryData = {
    ...params
  };
  return getAllUserList(queryData);
};

const show = () => {
  dialogVisible.value = true;
  nextTick(() => {
    proTable.value?.reset();
  });
};

const close = () => {
  dialogVisible.value = false;
  selectId.value = "";
};

const onCancel = () => {
  close();
};

const onConfirm = () => {
  if (selectId.value) {
    const list = proTable.value?.tableData || [];
    const selectItem = list.find(item => item.id === selectId.value);
    if (selectItem) {
      const data = {
        userId: selectId.value,
        account: selectItem.account
      };
      emits("confirm", data);
      close();
      return;
    }
  }
  ElMessage.warning("请选择用户");
};

defineExpose({
  show,
  close
});
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
  .content {
    .el-radio-group {
      width: 100%;
    }
  }
}
</style>
