.cust-border {
  border-bottom: 1px solid #e1e3e6;
}
.home {
  font-family: PingFangSC, "PingFang SC";
  .board-box {
    padding: 0;
    margin-bottom: 16px;
    .content-title {
      height: 24px;
      padding: 16px 24px 14px 20px;
      @extend.cust-border;
      &.product-box {
        height: 51px;
        padding: 0 24px;
        .has-choice {
          line-height: 51px;
        }
      }
      .product-btn {
        display: inline-block;
        height: 51px;
        line-height: 51px;
        .product-option {
          display: inline-block;
          height: 50px;
          margin-right: 48px;
          cursor: pointer;
          &.active {
            color: var(--el-color-primary);
            border-bottom: 2px solid var(--el-color-primary);
          }
          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: rgb(0 0 0 / 85%);
      }
      .has-choice {
        float: right;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        .choice-option {
          margin-left: 20px;
          cursor: pointer;
          &:hover {
            color: #0052d9;
          }
        }
        .default {
          color: rgb(0 0 0 / 65%);
        }
        .active {
          color: #0052d9;
        }
      }
    }
    .data-box {
      padding: 24px 24px 0;
    }
    .rate {
      height: 22px;
      padding-left: 24px;
      font-size: 14px;
      color: #000000a6;
    }
    .product-data {
      display: flex;
      width: 100%;
      padding-bottom: 24px;
    }
    .order-main {
      flex: 1;
      .chart-box {
        width: 100%;
        height: 314px;
      }
    }
    .order-aside {
      width: 300px;
    }
  }
  .second-box {
    display: flex;
    gap: 16px;
    justify-content: space-around;
    .board-box {
      flex: 1;
    }
    .portal-line-box {
      width: 100%;
      height: 72px;
      margin-bottom: 24px;
    }
    .chart-box {
      width: 100%;
      height: 336px;
    }
  }
  .overview {
    display: flex;
    gap: 17px;
    justify-content: space-around;
    margin-bottom: 16px;
    .overview-box {
      flex: 1;
      height: 154px;
      padding: 24px 24px 12px;
      background: linear-gradient(270deg, #ffffff 0%, #e5edfb 100%);
      border: 1px solid #ffffff;
      border-radius: 4px;
      .overview-title {
        font-size: 14px;
        font-weight: 400;
        color: #3a3a3d;
        span {
          margin-right: 12px;
        }
      }
      .overview-num {
        padding: 10px 0;
        font-size: 32px;
        font-weight: 600;
        line-height: 45px;
        color: #3a3a3d;
      }
      .yesterday-add {
        padding-top: 12px;
        margin-top: 30px;
        font-size: 14px;
        border-top: 1px solid #ffffff;
        .add-label {
          margin-right: 8px;
          color: rgb(0 0 0 / 65%);
        }
        .add-num {
          color: rgb(0 0 0 / 85%);
        }
      }
    }
  }
}
.custom-list {
  :deep(.card.table-main) {
    padding: 0;
    border: none !important;
    box-shadow: none !important;
  }
}
