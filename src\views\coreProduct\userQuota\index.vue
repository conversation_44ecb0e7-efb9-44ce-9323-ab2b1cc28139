<template>
  <div class="table-box">
    <ProTable :request-auto="false" ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <template #tableHeader>
        <el-button v-auth="'setThreshold'" type="primary" @click="handleControlOpen"> 用户信控默认配置</el-button>
      </template>
      <template #operation="{ row }">
        <!-- <el-button text type="primary" @click="handleOpen(row)">调整 </el-button> -->
        <auth-button :row-data="row" :auth-list="authList"></auth-button>
      </template>
    </ProTable>
    <el-dialog
      v-model="dialogVisible"
      title="编辑配额信息"
      width="800"
      :before-close="handleClose"
      destroy-on-close
      append-to-body
    >
      <el-form ref="FormRef" :model="form" label-width="auto">
        <el-form-item label="用户账号："> {{ form.account }} </el-form-item>
        <el-form-item label="手机号码："> {{ form.phone }} </el-form-item>
        <el-form-item label="用户名称："> {{ form.userName }} </el-form-item>
        <el-form-item
          label="CPU配额(核):"
          prop="totalCpuCount"
          :rules="[
            { required: true, message: '请输入CPU配额(核)' },
            { validator: validateInput, trigger: 'blur' }
          ]"
        >
          <el-input v-model="form.totalCpuCount" />
        </el-form-item>
        <el-form-item
          label="GPU配额(卡):"
          prop="totalGpuCount"
          :rules="[
            { required: true, message: '请输入GPU配额(卡)' },
            { validator: validateInput, trigger: 'blur' }
          ]"
        >
          <el-input v-model="form.totalGpuCount" />
        </el-form-item>
        <el-form-item
          label="内存配额(GB):"
          prop="totalMemoryCount"
          :rules="[
            { required: true, message: '请输入内存配额(GB)' },
            { validator: validateInput, trigger: 'blur' }
          ]"
        >
          <el-input v-model="form.totalMemoryCount" />
        </el-form-item>
        <el-form-item
          label="欠费核时量阈值:"
          prop="thresholdBalance"
          :rules="[
            { required: true, message: '请输入信控欠费核时量' },
            { validator: validateInput, trigger: 'blur' }
          ]"
        >
          <el-input v-model="form.thresholdBalance">
            <template #append>核时</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="submitForm(FormRef)" :loading="loading"> 确认 </el-button>
        <el-button @click="resetForm(FormRef)">取消</el-button>
      </template>
    </el-dialog>
    <!-- 用户信控管理 -->
    <el-dialog
      v-model="userControlVisible"
      title="用户信控管理"
      width="800"
      :before-close="handleControlClose"
      destroy-on-close
      append-to-body
    >
      <el-form ref="userControlFormRef" :model="controlForm" label-width="auto">
        <el-form-item
          label="CPU配额(核):"
          prop="totalCpuCount"
          :rules="[
            { required: true, message: '请输入CPU配额(核)' },
            { validator: validateInput, trigger: 'blur' }
          ]"
        >
          <el-input v-model="controlForm.totalCpuCount" />
        </el-form-item>
        <el-form-item
          label="GPU配额(卡):"
          prop="totalGpuCount"
          :rules="[
            { required: true, message: '请输入GPU配额(卡)' },
            { validator: validateInput, trigger: 'blur' }
          ]"
        >
          <el-input v-model="controlForm.totalGpuCount" />
        </el-form-item>
        <el-form-item
          label="内存配额(GB):"
          prop="totalMemoryCount"
          :rules="[
            { required: true, message: '请输入内存配额(GB)' },
            { validator: validateInput, trigger: 'blur' }
          ]"
        >
          <el-input v-model="controlForm.totalMemoryCount" />
        </el-form-item>
        <el-form-item
          label="欠费核时量阈值:"
          prop="thresholdBalance"
          :rules="[
            { required: true, message: '请输入信控欠费核时量' },
            { validator: validateInput, trigger: 'blur' }
          ]"
        >
          <el-input v-model="controlForm.thresholdBalance">
            <template #append>核时</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="controlSubmit(userControlFormRef)" :loading="loading"> 确认 </el-button>
        <el-button @click="controlResetForm(userControlFormRef)">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import type { CoreSystem } from "@/api/interface/coreSystem";
import {
  queryGetThreshold,
  queryUserQuotaDetail,
  queryUserQuotaList,
  queryUserQuotaUpdate,
  updateUserQuotaThreshold
} from "@/api/modules/coreSystem/userQuota";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { onMounted, onRenderTracked, onRenderTriggered, reactive, ref } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { IAuthButtonItem } from "@/components/AuthButton/typings";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const FormRef = ref<FormInstance>();

const userControlVisible = ref(false);

const userControlFormRef = ref<FormInstance>();

const regex = new RegExp(/^(?:[1-9]\d{0,4}|100000)$/);

const loading = ref(false);

const authList: IAuthButtonItem<CoreSystem.UserQuotaItemData>[] = [
  {
    toolTipProps: {
      content: "调整"
    },
    iconClass: "Edit",
    itemClick: scope => handleOpen(scope),
    authName: "userQuotaEdit"
  }
];

const validateInput = (rule: any, value: any, callback: any) => {
  if (regex.test(value)) {
    callback();
  } else {
    callback(new Error("输入1-100000的整数"));
  }
};

const proTable = ref<ProTableInstance>();

const dialogVisible = ref(false);

const columns: ColumnProps[] = [
  { prop: "account", label: "用户账号", search: { el: "input", props: { maxlength: 20 } } },
  { prop: "phone", label: "手机号码", search: { el: "input", props: { maxlength: 20 } } },
  { prop: "userName", label: "用户名称" },
  {
    prop: "useCpuCount",
    label: "CPU已用/配额（核）",
    render: ({ row }) => (
      <>
        {row.useCpuCount}/{row.totalCpuCount}
      </>
    )
  },
  {
    prop: "useGpuCount",
    label: "GPU已用/配额（卡）",
    render: ({ row }) => (
      <>
        {row.useGpuCount}/{row.totalGpuCount}
      </>
    )
  },
  {
    prop: "useMemoryCount",
    label: "内存已用/配额（GB）",
    render: ({ row }) => (
      <>
        {row.useMemoryCount}/{row.totalMemoryCount}
      </>
    )
  },
  {
    prop: "thresholdBalance",
    label: "信控欠费核时量（核时）"
  },
  { prop: "operation", label: "操作", fixed: "right" }
];
const form = reactive<CoreSystem.UserQuotaItemData>({
  totalCpuCount: 128,
  totalGpuCount: 8,
  totalMemoryCount: 200,
  userName: "",
  account: "",
  phone: "",
  id: null,
  thresholdBalance: 5200
});

const controlForm = reactive({
  thresholdBalance: 5200,
  totalCpuCount: 500,
  totalGpuCount: 16,
  totalMemoryCount: 5000
});

const controlSubmit = (formEl: FormInstance | undefined) => {
  if (formEl) {
    formEl
      .validate()
      .then(() => {
        loading.value = true;
        updateUserQuotaThreshold({
          thresholdBalance: controlForm.thresholdBalance,
          totalCpuCount: controlForm.totalCpuCount,
          totalGpuCount: controlForm.totalGpuCount,
          totalMemoryCount: controlForm.totalMemoryCount
        })
          .then(res => {
            ElMessage({
              message: "调整成功！",
              type: "success"
            });
            proTable.value?.getTableList();
            handleControlClose();
          })
          .catch(error => {})
          .finally(() => {
            loading.value = false;
          });
      })
      .catch(() => {});
  }
};

const controlResetForm = (formEl: FormInstance | undefined) => {
  if (formEl) {
    formEl.resetFields();
  }
  handleControlClose();
};

const handleControlClose = () => {
  userControlVisible.value = false;
};

const getTableList = async (params?: CoreSystem.UserQuotaSearch) => {
  return Reflect.has(BUTTONS.value, "userQuotaList") ? await queryUserQuotaList({ ...params }) : null;
};

const handleControlOpen = () => {
  queryGetThreshold()
    .then(result => {
      controlForm.thresholdBalance = result.data.thresholdBalance;
      controlForm.totalCpuCount = result.data.totalCpuCount;
      controlForm.totalGpuCount = result.data.totalGpuCount;
      controlForm.totalMemoryCount = result.data.totalMemoryCount;
      userControlVisible.value = true;
    })
    .catch(err => {});
};

const handleClose = () => {
  dialogVisible.value = false;
};

const submitForm = (formEl: FormInstance | undefined) => {
  formEl?.validate().then(() => {
    loading.value = true;
    queryUserQuotaUpdate({ ...form })
      .then(res => {
        if (res.code === 200) {
          handleClose();
          ElMessage({
            message: "调整成功！",
            type: "success"
          });
          proTable.value?.getTableList();
        } else {
          ElMessage({
            showClose: true,
            message: "调整失败",
            type: "error"
          });
        }
      })
      .catch(error => {
        console.log(error);
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  dialogVisible.value = false;
};

const handleOpen = (item: CoreSystem.UserQuotaItemData) => {
  queryUserQuotaDetail(item.id as string).then(res => {
    const { phone, account, userName, id, totalMemoryCount, totalGpuCount, totalCpuCount, thresholdBalance } = res.data;
    form.phone = phone;
    form.account = account;
    form.userName = userName;
    form.totalCpuCount = totalCpuCount;
    form.totalGpuCount = totalGpuCount;
    form.totalMemoryCount = totalMemoryCount;
    form.thresholdBalance = thresholdBalance;
    form.id = id;
    dialogVisible.value = true;
  });
};

onMounted(() => {
  proTable.value?.getTableList();
});
</script>

<style scoped></style>
