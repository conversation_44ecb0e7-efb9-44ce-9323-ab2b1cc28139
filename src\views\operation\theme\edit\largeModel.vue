<template>
  <div class="complex-content-box">
    <div class="name-box">
      <el-button type="primary" class="add-button" @click="addLargeModel" :icon="CirclePlus"> 添加 </el-button>
      <span class="tips"> <slot name="nameTips"></slot></span>
    </div>
    <div class="content-item" v-for="(item, index) in dataList" :key="index + 'complex'">
      <div class="item-title">
        <div class="name">{{ item.title || "模型名称" }}</div>
        <div class="right-btn">
          <el-button class="icon" link v-if="index !== dataList.length - 1" @click="xiayiItem(index, dataList)">
            <i class="iconfont2 icon-xiayi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link v-if="index !== 0" @click="shangyiItem(index, dataList)">
            <i class="iconfont2 icon-shangyi" style="font-size: 15px"></i>
          </el-button>
          <el-button class="icon" link :icon="Delete" @click="deleteLargeModel(index)"></el-button>
        </div>
      </div>
      <el-form ref="largeModelFormRef" class="item-form" :model="item" :rules="rules" label-width="120px">
        <el-form-item label="模型名称" prop="name" required>
          <el-input v-model="item.name" maxlength="20" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="模型选择" prop="modelId" v-if="!isCase" required>
          <el-select v-model="item.modelId" placeholder="请选择" @change="handleLink(item.modelId, index)" clearable>
            <el-option
              v-for="(timeItem, timeIndex) in model_list"
              :key="timeIndex + 'modelListUnit'"
              :label="timeItem.itemValue"
              :value="timeItem.itemKey"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模型分类" prop="category" required>
          <el-select v-model="item.category" placeholder="请选择" clearable>
            <el-option v-for="item1 in categoryList" :key="item1" :label="item1" :value="item1" />
          </el-select>
        </el-form-item>
        <el-form-item label="模型标签" prop="tags" v-if="!isCase">
          <el-select multiple :multiple-limit="3" v-model="item.tags" placeholder="请选择">
            <el-option v-for="item1 in tags" :key="item1" :label="item1" :value="item1" />
          </el-select>
          <span class="button-text" @click="tagShow(tags, index)">管理</span>
        </el-form-item>
        <el-form-item label="模型介绍" prop="description" v-if="!isCase" required>
          <el-input v-model="item.description" type="textarea" maxlength="500" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="核心优势" prop="advantage" v-if="isCase" required>
          <el-input v-model="item.advantage" type="textarea" maxlength="100" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="相关简介" prop="description" v-if="isCase" required>
          <el-input v-model="item.description" type="textarea" maxlength="500" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="模型厂家" prop="supplier" required>
          <el-input v-model="item.supplier" maxlength="10" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="模型图标" prop="iconUrl" v-if="!isCase" required>
          <UploadImg :file-size="2" v-model:image-url="item.iconUrl" width="135px" height="135px">
            <template #tip>建议图片尺寸128px*128px，图片大小不超过1M</template>
          </UploadImg>
        </el-form-item>
        <el-form-item label="跳转链接" prop="link" required>
          <el-input v-model="item.link" maxlength="150" show-word-limit clearable :disabled="!isCase" />
          <p class="tips-text">
            支持单点登录，手动填写跳转链接时请添加协议头，建议添加如：https或其他已定制的协议头，若添加http协议头有一定链接被篡改风险
          </p>
        </el-form-item>
        <el-form-item label="背景图片" prop="iconUrl" v-if="isCase" required>
          <UploadImg :file-size="2" v-model:image-url="item.iconUrl" width="135px" height="135px">
            <template #tip>建议图片尺寸128px*128px，图片大小不超过1M</template>
          </UploadImg>
        </el-form-item>
      </el-form>
    </div>
  </div>
  <TagDialog ref="tagDialogRef" @update-tags="updateTags" />
</template>
<script setup lang="ts">
import { onMounted, ref, computed, reactive, watch } from "vue";
import { CirclePlus, Delete } from "@element-plus/icons-vue";
import UploadImg from "@/components/Upload/Img.vue";
import { FormInstance } from "element-plus";
import { useDict } from "@/hooks/useDict";
import TagDialog from "./tagDialog.vue";
import { modelChildren } from "@/api/interface/business/theme";
const { model_list, model_link } = useDict("model_list", "model_link");
// const { sku_billing_mode_type, pms_time_uit, pms_renewal_rules } = useDict(
//   "sku_billing_mode_type",
// );

const props = defineProps({
  dataList: {
    type: Array<modelChildren>,
    default: () => []
  },
  modelIndex: {
    type: Number,
    default: -1
  },

  isCase: {
    type: Boolean,
    default: false
  },
  categoryList: {
    type: Array as () => string[],
    default: () => []
  },
  tags: {
    type: Array as () => string[],
    default: () => []
  }
});

const emits = defineEmits(["addLargeModel", "deleteLargeModel", "updateLink", "updateListTags", "updateTags"]);
const largeModelFormRef = ref<FormInstance>();
const tagDialogRef = ref();
// const tagList = ref(props.dataList.map(item => item.tags));

const rules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  modelId: [{ required: true, message: "请选择模型", trigger: "blur" }],
  category: [{ required: true, message: "请选择分类", trigger: "blur" }],
  description: [{ required: true, message: "请输入介绍", trigger: "blur" }],
  advantage: [{ required: true, message: "请输入核心优势", trigger: "blur" }],
  // relatedDescription: [{ required: true, message: "请输入相关简介", trigger: "blur" }],
  supplier: [{ required: true, message: "请输入厂家", trigger: "blur" }],
  iconUrl: [{ required: true, message: "请上传图标", trigger: "blur" }],
  link: [{ required: true, message: "请输入跳转链接", trigger: "blur" }]
  // backgroundUrl: [{ required: true, message: "请上传背景图片", trigger: "blur" }]
};
// const sendProductDetailParam = computed(() => productStore.sendProductDetailParam?.[props.productId]);

// const onAddGift = (skuIndex: number, list: any) => {
//   selectedList.value = list;
//   selectSkuIndex.value = skuIndex;
//   drawerRef.value.visible = true;
// };

// const onSelectGift = (ids: string[], selectList: any) => {
//   const selectSku = props.dataList[selectSkuIndex.value] as any;
//   selectSku.skuGiftList = selectList;
//   drawerRef.value.visible = false;
// };
const tagShow = (data: any, index: number) => {
  tagDialogRef.value.openDialog(data, index);
};

const addLargeModel = () => {
  emits("addLargeModel");
};
//删除元素
const deleteLargeModel = (cardIndex: number) => {
  // if (contentDataList.value.length <= 1) {
  //   ElMessage.error("至少有一项内容");
  // } else {
  //   emits("deleteLargeModel", index);
  // }
  emits("deleteLargeModel", props.modelIndex, cardIndex);
};
const shangyiItem = (index: number, list: any[]) => {
  list[index] = list.splice(index - 1, 1, list[index])[0];
};
const xiayiItem = (index: number, list: any[]) => {
  list[index] = list.splice(index + 1, 1, list[index])[0];
};

const updateTags = (data: any) => {
  // tagList.value[index] = data;
  if (props.tags.length > 0) {
    props.dataList.forEach((item, largeModelIndex) => {
      if (item.tags.length > 0) {
        item.tags.forEach((tag, tagIndex) => {
          if (!data.includes(tag)) {
            emits("updateListTags", props.modelIndex, largeModelIndex, tagIndex);
          }
        });
      }
    });
  }
  emits("updateTags", data, props.modelIndex);
};

const handleLink = (data: string, index: number) => {
  const link = ref("");
  model_link.value.forEach((item: any) => {
    if (data === item.itemKey) link.value = item.itemValue;
  });
  emits("updateLink", link.value, props.modelIndex, index);
};

const ruleValidatePromise = () => {
  let promiseArr = [] as any;
  largeModelFormRef.value?.forEach((itemref: any) => {
    promiseArr.push(
      new Promise((resolve, reject) => {
        itemref.validate((valid: any, fields: any) => {
          if (valid) {
            resolve("校验成功");
          } else {
            let obj = Object.keys(fields);
            itemref.scrollToField(obj[0]);
            reject("校验失败");
          }
        });
      })
    );
  });
  return promiseArr;
};

watch(
  () => props.tags,
  () => {
    // emit(
    //   "updateSkuIds",
    //   tableList.value
    //   // tableList.value.map((item: any) => item.id)
    // );
  },
  { deep: true }
);

watch(
  () => props.dataList,
  () => {
    // emit(
    //   "updateSkuIds",
    //   tableList.value
    //   // tableList.value.map((item: any) => item.id)
    // );
  },
  { deep: true }
);

defineExpose({
  ruleValidatePromise
});

onMounted(() => {});
</script>

<style lang="scss" scoped>
.complex-content-box {
  width: 100%;
  .name-box {
    display: flex;
    width: fit-content;
    margin-bottom: 16px;
    .name-input {
      margin-right: 8px;
    }
    .add-button {
      margin-right: 8px;
    }
    .tips {
      margin-left: 15px;
      color: var(--el-text-color-regular);
      white-space: nowrap;
    }
  }
  .content-item {
    margin-bottom: 16px;
    border: 1px solid #c4c6cc;
    .item-title {
      display: flex;
      justify-content: space-between;
      padding: 4px 12px;
      background: #c4c6cc;
      .right-btn {
        .icon + .icon {
          margin-left: 0;
        }
      }
      .icon:hover {
        color: #3a3a3d;
      }
    }
  }
  .item-form {
    padding: 24px;
    .item-small-card {
      display: flex;
      flex-direction: column;
      width: 100%;
      border: #c4c6cc;
      border-style: solid;
      border-width: 1px;
      .item-small-card-item {
        &:first-child {
          margin-top: 10px;
        }

        margin-bottom: 10px;
        margin-left: 10px;
      }
    }
    .item-big-card {
      display: flex;
      flex-direction: row;
      width: 100%;
      padding: 10px;
      border: #c4c6cc;
      border-style: solid;
      border-width: 1px;
      .item-big-card-left {
        flex-direction: column;
      }
      .item-big-card-additionalTitles {
        flex-direction: column;
        margin-left: 10px;
        .item-big-card-title {
          height: 50px;
          margin-bottom: 10px;
        }
        .item-big-card-context {
          // height: 80px;
          margin-bottom: 10px;
        }
      }
    }
    .jf-item {
      :deep(.el-form-item__content) {
        display: block;
      }
      .jf-box {
        padding: 15px 0;
        .jf-label {
          font-size: var(--el-form-label-font-size);
          color: var(--el-text-color-regular);
        }
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .nowrap {
    white-space: nowrap;
  }
}
.tips-text {
  margin: 0;
  font-size: 12px;
  color: #909399;
}
.button-text {
  margin-left: 10px;
  color: #2b85e4;
  cursor: pointer;
}
</style>
