import type { Customer } from "../interface/customer";
import { System } from "../interface/system";
import http from "@/api";

// 客户管理（门户用户）列表
export const getPortalUserList = (params: System.PortalUserParams) => {
  return http.get(`/system/portalUser/list`, params);
};
/**
 * 用户来源统计
 * @returns
 */
export const querySourceCount = () => {
  return http.get<Customer.SourceCountItem>(`/system/portalUser/sourceCount`);
};

/**
 * 客户管理启动停用
 * @param params
 * @returns
 */
export const querySetStatus = (params: { id: string; status: number }) => {
  return http.post(`/system/portalUser/status`, params);
};
/**
 * 客户管理详情
 * @param params
 * @returns
 */
export const queryPortalUserInfo = (params: { id: any }) => {
  return http.get<System.PortalUserParams>(`/system/portalUser/info`, params);
};

/**
 * 客户商机和方案分页接口
 * @param params
 * @returns
 */
export const queryBusiness = (params: { userId?: string; current?: number; size?: number }) => {
  return http.get("/cnud-order/order-service-business/page", params);
};

/**
 * 客户中心-客户产品查询
 * @param params
 * @returns
 */
export const queryCustomerProducts = (params: { userId?: string; current?: number; size?: number }) => {
  return http.get("/cnud-order/order-manage/customer/products", params);
};

/**
 * 订单导出
 * @param params
 * @returns
 */
export const queryCustomerListExcel = (params: any) => {
  return http.download(`/system/portalUser/export`, params);
};
