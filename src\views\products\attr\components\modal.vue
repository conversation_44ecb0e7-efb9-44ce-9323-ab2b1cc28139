<template>
  <el-dialog v-model="dialogVisible" :title="modalTitle" width="800" :before-close="handleClose" :destroy-on-close="true">
    <attrForm ref="attrFormRef"></attrForm>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
export type MODALACTION = "edit" | "add" | "view";

import { Product } from "@/api/interface/product";
import attrForm from "./attrForm.vue";
import { ref, unref, computed, nextTick } from "vue";

const attrFormRef = ref();

const modalTitle = computed(() => {
  switch (unref(modalAction)) {
    case "add":
      return "新增产品属性";
    case "edit":
      return "编辑产品属性";
    case "view":
      return "查看产品属性";
    default:
      return "";
  }
});
const dialogVisible = ref(false);
const modalAction = ref("add");

const emit = defineEmits(["on-submit"]);

const handleOpen = (action: MODALACTION, rowData?: Product.ProductAttrListItem) => {
  modalAction.value = action;
  dialogVisible.value = true;
  nextTick(() => {
    if (action === "edit") {
      attrFormRef.value?.formSetValues(rowData);
    }
  });
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleSubmit = () => {
  attrFormRef.value
    ?.submit()
    .then(() => {
      dialogVisible.value = false;
      // eslint-disable-next-line vue/custom-event-name-casing
      emit("on-submit");
    })
    .catch(err => {
      console.log(err);
    });
};

defineExpose({
  handleOpen
});
</script>

<style scoped></style>
