<template>
  <el-table :data="data" class="w100" border :header-cell-style="{ fontWeight: 'bold', color: '#303133', background: '#f5f7fa' }">
    <el-table-column align="left" prop="subOrderNo" label="子订单编号">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" :content="row?.subOrderNo" placement="top-start">
            {{ row?.subOrderNo }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="providerSkuId" label="外系统订单编号" width="130">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="row?.providerSkuId" placement="top-start">
            {{ row?.providerSkuId }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" prop="productName" label="产品名称">
      <template #default="{ row }">
        <div class="text">
          <el-tooltip class="box-item" effect="dark" :content="row?.productName" placement="top-start">
            {{ row?.productName }}
          </el-tooltip>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" width="240" prop="skuName" label="产品规格">
      <template #default="{ row }">
        <div v-html="row?.skuName"></div>
      </template>
    </el-table-column>
    <el-table-column align="left" width="85" prop="orderType" label="订单类型">
      <template #default="{ row }">
        {{ useDictLabel(orderTypeDic, row?.orderType) }}
      </template>
    </el-table-column>
    <el-table-column align="right" width="85" prop="count" label="购买数量">
      <template #default="{ row }">
        <div v-if="row?.count">
          {{ row?.count }}
          {{ useDictLabel(quantityDic, row?.countUnit) }}
        </div>
        <div v-else>/</div>
      </template>
    </el-table-column>
    <el-table-column align="right" width="85" prop="buyDuration" label="购买时长">
      <template #default="{ row }">
        <div v-if="row?.buyDuration">
          {{ row?.buyDuration }}
        </div>
        <div v-else>/</div>
      </template>
    </el-table-column>
    <el-table-column align="right" width="95" prop="skuPrice" label="单价（元）">
      <template #default="{ row }">{{ toPrice(row.skuPrice) }} </template>
    </el-table-column>
    <el-table-column align="left" prop="effective" label="有效期" width="180">
      <template #default="{ row }">
        <div class="time-text">
          <span>{{ row.effectTime }}</span> <span> -</span> <span>{{ row.endTime }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="left" width="95" prop="productProviderName" label="产品服务商"> </el-table-column>
    <el-table-column align="right" prop="payFee" label="订单金额（元）" width="130">
      <template #default="{ row }">
        <div class="pay-fee">{{ toPrice(row.payFee) }}</div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
interface TablePropType {
  data?: any[];
}
import { useDictLabel } from "@/hooks/useDict";
import { useDicStore } from "@/stores/modules/dictionaty";
import { storeToRefs } from "pinia";
import { toPrice } from "@/utils/index";
import accountDialogTable from "./accountDialogTable.vue";
import { ref } from "vue";
const dictionaryStore = useDicStore();
const { orderTypeDic, timeDic, quantityDic } = storeToRefs(dictionaryStore);
defineProps<TablePropType>();
</script>

<style scoped lang="scss">
.text {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.time-text {
  span {
    display: block;
  }
}
.w100 {
  width: 100%;
}
.pay-fee {
  font-size: 16px;
  color: #0052d9;
}
</style>
