<template>
  <div class="main-box">
    <label-type-list
      :type-list="typeList"
      title="部门树"
      :config-type="configType"
      :props="defaultProps"
      :item-operate="itemOperate"
      v-model:active-index="activeIndex"
      @delete-item="onDelete"
    >
      <template #operateBtn>
        <div class="operate-btn">
          <el-button v-auth="'department_add'" :icon="Plus" plain @click="accountOperate(editType.add)">新增</el-button>
          <el-button
            v-auth="'department_edit'"
            :icon="EditPen"
            plain
            @click="accountOperate(editType.edit)"
            :disabled="!activeItem.id"
          >
            修改
          </el-button>
          <!-- <el-button @click="accountOperate(editType.remove)" :disabled="!activeItem.id">删除</el-button> -->
        </div>
      </template>
      <template #listContent>
        <div style="overflow-x: auto">
          <el-tree
            class="department-tree"
            :data="typeList"
            :props="defaultProps"
            :show-checkbox="false"
            ref="treeRef"
            node-key="id"
            :allow-drop="canDrop"
            @node-drop="nodeDrop"
            draggable
            @current-change="changeTreeNode"
            :default-expand-all="true"
            highlight-current
            :current-node-key="selected"
          >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <div class="name-box">
                  <span
                    v-if="(!data.parentId || data.parentId === '0') && data.children.length"
                    class="tree-filed iconfont icon-a-filledicon_folder_opened"
                  >
                  </span>
                  <span :title="node.label">{{ node.label }}</span>
                </div>
                <more-operate
                  v-if="data.orgCode !== 'gdUnicom'"
                  :operate-type="itemOperate"
                  @on-delete="onDelete(data)"
                  @on-edit="onEdit(data)"
                  @on-add="onAdd(data)"
                >
                </more-operate>
              </div>
            </template>
          </el-tree>
        </div>
      </template>
    </label-type-list>
    <department-list :parent-item="activeItem"></department-list>
    <add-edit-type-dialog
      v-model:visible="dialogVisible"
      :type="dialogType"
      :params="labelTypeParams"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-type-dialog>
  </div>
</template>
<script setup lang="ts" name="accountManage">
import { ref, onMounted, computed } from "vue";
import labelTypeList from "@/components/TypeList/index.vue";
import departmentList from "./components/departmentList.vue";
import addEditTypeDialog from "./components/addOrEditTypeDialog.vue";
import { getOrgTreeList, deleteOrg as deleteTypeApi, sortOrg as sortOrgApi } from "@/api/modules/system";
import { editType } from "@/enums/dialogTypeEnum";
import { useHandleData } from "@/hooks/useHandleData";
import { ElMessage, ElTree } from "element-plus";
import moreOperate from "@/components/TypeList/moreOperate.vue";
import { Plus, EditPen } from "@element-plus/icons-vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

interface TreeData {
  orgName?: string;
  orgCode?: string;
  description?: string;
  parentId?: string;
  id: number | string;
  orgId?: string;
  children?: TreeData[];
}
onMounted(() => {
  getTypeTree();
});

const selected = computed(() => activeItem.value.id);

const canDrop = (draggingNode: any, dropNode: any, type: string) => {
  //同级拖拽
  return draggingNode.parent.id === dropNode.parent.id && type != "inner";
};
const nodeDrop = (draggingNode: any, dropNode: any) => {
  // //同级拖拽
  // return draggingNode.parent.id === dropNode.parent.id && type != "inner";
  const params = (dropNode.parent.data?.children || dropNode.parent.data).map((item: any, index: number) => {
    return {
      ...item,
      sort: index
    };
  });
  sortOrgApi(params).then((res: any) => {
    ElMessage.success(res.msg);
  });
};
const labelTypeParams = ref({});
const dialogType = ref<editType>(editType.add);
const activeIndex = ref(0);
const dialogVisible = ref(false);

const defActiveItem = {
  orgName: "",
  id: "",
  orgCode: "",
  description: ""
};
const activeItem = ref<TreeData>(defActiveItem);

const treeRef = ref<InstanceType<typeof ElTree>>();

//树节点改变
const changeTreeNode = (data: TreeData, node: any) => {
  activeItem.value = data;
};
const accountOperate = (type = editType.add) => {
  if (type === editType.remove) {
    onDelete(activeItem.value);
    return;
  }

  if (activeItem.value.id) {
    if (type === editType.add) {
      labelTypeParams.value = {
        id: activeItem.value.id,
        orgId: activeItem.value.id,
        parentOrgName: activeItem.value.orgName
      };
    } else {
      if (activeItem.value.orgCode === "gdUnicom") {
        ElMessage.info("不可编辑");
        return;
      }
      labelTypeParams.value = {
        orgName: activeItem.value.orgName,
        orgCode: activeItem.value.orgCode,
        description: activeItem.value.description,
        parentId: activeItem.value.parentId,
        id: activeItem.value.id
      };
    }
  } else {
    labelTypeParams.value = {
      id: activeItem.value.id,
      orgId: activeItem.value.id,
      parentId: activeItem.value.parentId
    };
  }

  dialogType.value = type;
  dialogVisible.value = true;
};

const defaultProps = {
  children: "children",
  label: "orgName",
  id: "id",
  disabled: function (data: any, node: any) {
    return node.isLeaf;
  }
};
const onConfirm = () => {
  dialogVisible.value = false;
  labelTypeParams.value = {};
  getTypeTree();
};
const onCancel = () => {
  dialogVisible.value = false;
  labelTypeParams.value = {};
};
const configType = {
  name: "orgName",
  key: "id"
};
const getTypeTree = () => {
  activeItem.value = defActiveItem;
  if (!BUTTONS.value?.department_query) {
    return;
  }
  getOrgTreeList({}).then(res => {
    typeList.value = res.data as Array<TreeData>;
    const list: TreeData[] = res.data as Array<TreeData>;
    treeRef.value?.setCheckedKeys(list[0] ? [list[0].id] : []);
    activeItem.value = list[0] || {};
    activeIndex.value = 0;
  });
};

const itemOperate = BUTTONS.value.department_delete ? ["delete"] : [];
const typeList = ref<Array<TreeData>>([]);
const onDelete = async (item: TreeData) => {
  if (item.orgCode === "gdUnicom") {
    ElMessage.info("不可删除");
    return;
  }
  await useHandleData(
    deleteTypeApi,
    { ids: [item.id] },
    { msg: `是否确认删除该记录，删除后其子部门和所属用户将被同步删除，不可恢复。`, successMsg: "删除" }
  );
  getTypeTree();
};
const onAdd = (item: TreeData) => {
  accountOperate(editType.add);
};
const onEdit = (item: TreeData) => {
  activeItem.value = item;
  accountOperate(editType.edit);
};
</script>
<style lang="scss" scoped>
.label-main {
  display: flex;
  height: 100%;
  overflow: auto;
  background: var(--el-bg-color-page);
}
.operate-btn {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  :deep(.el-button) {
    flex: 1;
  }
}
.disabled {
  cursor: not-allowed;
  opacity: 0.8;
}
:deep(.department-tree.el-tree .el-tree-node) {
  min-width: max-content !important;
}
.department-tree {
  &.el-tree {
    min-width: 300px;

    // overflow: auto;
    .el-tree-node__children {
      display: inline;
    }
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    cursor: pointer;
    .name-box {
      display: flex;
      align-items: center;
      > span {
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .tree-filed {
        display: inline-block;
        margin: 0 4px;
        color: #9ca3ac;
      }
    }
  }
}
</style>
