<template>
  <div class="banner-box table-box">
    <ProTable ref="proTable" title="用户列表" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => toEdit()"> 新增 </el-button>
      </template>
      <template #auditStatus="{ row }">
        <el-popover placement="top" title="" :width="400" trigger="hover">
          <div>
            <div>审核人：{{ row.auditName }}</div>
            <div>审核状态：{{ useDictLabel(statusDict, row.auditStatus) }}</div>
            <div>审核意见：{{ row.auditMsg }}</div>
            <div>审核时间：{{ row.auditTime }}</div>
          </div>
          <template #reference>
            {{ useDictLabel(statusDict, row.auditStatus) }}
          </template>
        </el-popover>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'edit'">
          <el-tooltip v-if="scope.row.auditStatus !== '1'" effect="dark" content="编辑" placement="top">
            <i class="iconfont2 icon-a-lineicon_edit opera-icon" @click="() => toEdit(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip v-if="!scope.row.enabled && scope.row.auditStatus === '3'" effect="dark" content="删除" placement="top">
            <i class="iconfont2 icon-lajitong opera-icon" @click="delBanner(scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <EditSortDialog
      v-if="bannerItem && showEditDialog"
      v-model:show-dialog="showEditDialog"
      :sort="bannerItem.sort"
      @save="saveSort"
    />
  </div>
</template>

<script setup lang="tsx" name="banner">
import { ref, onMounted, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { bannerStorage } from "@/utils/storage/edit";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import EditSortDialog from "@/components/EditSortDialog/index.vue";
import { getBannerPageList, deleteBanner, changeBanner } from "@/api/modules/business/banner";
import { type ProTableInstance, type ColumnProps } from "@/components/ProTable/interface/index";
import { Plus } from "@element-plus/icons-vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useDicStore } from "@/stores/modules/dictionaty";
import { useDictLabel } from "@/hooks/useDict";

enum Status {
  review = "1",
  pass = "2",
  noPass = "3"
}
const dictionaryStore = useDicStore();
const { BUTTONS } = useAuthButtons();

const router = useRouter();
const route = useRoute();

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
const proTable = ref<ProTableInstance>();
const showEditDialog = ref<boolean>(false);
const bannerItem = ref<any>({});

bannerStorage.clearExpired();

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const { bannerName, enabled, publishTime = [], current, size, auditStatus } = search;
  return getBannerPageList({
    search: bannerName,
    enabled,
    maxPublishTime: publishTime[1],
    minPublishTime: publishTime[0],
    current,
    size,
    descs: "sort",
    auditStatus
  });
};
onMounted(() => {
  dictionaryStore.getProductAuditStatus();
});
const statusDict: any = computed(() => {
  const status = dictionaryStore.productAuditStatus;
  status?.forEach((item: any = {}) => {
    item.value = String(item.itemKey);
  });
  return status;
});
// 表格配置项
const columns: ColumnProps<any>[] = [
  {
    prop: "bannerName",
    label: "项目名称",
    align: "left",
    search: { el: "input", props: { maxLength: 10 } }
  },
  {
    prop: "mobileImageUrl",
    label: "移动端轮播图片",
    align: "left",
    width: 120,
    render: (scope: any) => {
      return scope.row.mobileImageUrl ? <img class="row-img" src={scope.row.mobileImageUrl} /> : "--";
    }
  },
  {
    prop: "imageUrl",
    label: "轮播图片",
    align: "left",
    width: 120,
    render: (scope: any) => {
      return <img class="row-img" src={scope.row.imageUrl} />;
    }
  },
  {
    prop: "enabled",
    label: "启用状态",
    align: "left",
    enum: [
      { label: "启用", value: true },
      { label: "未启用", value: false }
    ],
    search: { el: "tree-select" },
    render: (scope: any) => {
      const disabledStatus = scope.row.auditStatus === Status.review || scope.row.auditStatus === Status.noPass;
      return (
        <el-switch
          disabled={disabledStatus}
          model-value={scope.row.enabled}
          active-value={true}
          inactive-value={false}
          onClick={() => changeEnabled(scope.row)}
        />
      );
    }
  },
  {
    prop: "sort",
    label: "排序",
    align: "left",
    render: (scope: any) => {
      return (
        <span class={"sort-btn"} onClick={() => setShowEditDialog(scope.row)}>
          {scope.row.sort}
        </span>
      );
    }
  },
  {
    prop: "startTime",
    label: "有效期",
    align: "left",
    render: ({ row: { startTime, endTime } }: any) => {
      return startTime ? startTime + "至" + endTime : "";
    }
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    align: "left",
    search: { el: "select" },
    enum: statusDict
  },
  {
    prop: "creatorName",
    label: "创建人员",
    align: "left"
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    width: 180
  },
  {
    prop: "publishTime",
    align: "left",
    label: "发布时间",
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "-",
        format: "YYYY/MM/DD",
        valueFormat: "YYYY/MM/DD HH:mm:ss"
      }
    },
    width: 180,
    sortable: true
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 95 }
];

// 删除用户信息
const delBanner = async (data: any) => {
  ElMessageBox.confirm("请确认是否删除此项banner配置？", "确认操作").then(async () => {
    await deleteBanner(data.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

const setShowEditDialog = (data: any) => {
  bannerItem.value = data;
  showEditDialog.value = true;
};
const saveSort = async (sort: number) => {
  await changeBanner({ ...bannerItem.value, sort: sort });
  ElMessage.success("操作成功！");
  proTable.value?.getTableList();
};

// 切换用户状态
const changeEnabled = async (data: any) => {
  if (data.auditStatus === Status.review || data.auditStatus === Status.noPass) {
    return;
  }
  ElMessageBox.confirm("是否修改启用状态？", "确认操作").then(async () => {
    await changeBanner({ ...data, enabled: !data.enabled });
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

watch(
  () => route.path,
  (path: string) => {
    if (path === "/operation/banner") {
      proTable.value?.getTableList();
    }
  }
);

const toEdit = (data?: any) => {
  if (data) {
    bannerStorage.set(data.id, data);
    router.push({
      path: "/operation/banner/edit",
      query: {
        id: data.id
      }
    });
  } else {
    router.push({
      path: "/operation/banner/edit"
    });
  }
};
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
.table-box .sort-btn {
  padding: 0 10px;
  color: #0052d9;
  cursor: pointer;
  background-color: #f1f6ff;
}
</style>
