<template>
  <div class="order-analysis-container">
    <div class="order-header">
      <div class="header-box">
        <el-form :inline="true" ref="orderFormRef" :model="orderForm" :rules="rules">
          <el-form-item label="" prop="btn">
            <div>
              <el-button-group size="large" class="botton-box">
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.day, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.day)"
                >
                  日报表
                </el-button>
                <el-button
                  type="default"
                  :class="{ active: dateType === DateType.month, btn: true }"
                  plain
                  @click="onChangeDateType(DateType.month)"
                >
                  月报表
                </el-button>
              </el-button-group>
            </div>
          </el-form-item>
          <el-form-item label="日期" prop="countDate">
            <el-date-picker
              v-if="dateType === DateType.day"
              class="data-picker-style"
              type="daterange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @focus="handleFocus"
              @calendar-change="handleChange"
              :disabled-date="disabledDate"
            />
            <el-date-picker
              v-else
              class="data-picker-style"
              type="monthrange"
              v-model="orderForm.countDate"
              range-separator="-"
              start-placeholder="请选择开始时间"
              end-placeholder="请选择结束时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              :rule="[{}]"
            />
          </el-form-item>
          <el-form-item label="产品名称" prop="productIdList">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              :multiple="true"
              v-model="orderForm.productIdList"
              placeholder="请选择产品名称"
              clearable
            >
              <el-option v-for="item in productOptions" :key="item.productId" :label="item.productName" :value="item.productId" />
            </el-select>
          </el-form-item>
          <el-form-item label="合作商" prop="providerId">
            <el-select
              class="select-style"
              collapse-tags
              collapse-tags-tooltip
              filterable
              v-model="orderForm.providerId"
              placeholder="请选择合作商"
              clearable
            >
              <el-option
                v-for="item in providerOptions"
                :key="item.providerId"
                :label="item.providerName"
                :value="item.providerId"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm(orderFormRef)"> 查询 </el-button>
            <el-button @click="resetForm(orderFormRef)"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="analysis-content">
      <div class="content-title">统计数据</div>
      <div class="statistical-data">
        <div class="order-main">
          <indicator-button
            :indicator-title="'订单指标'"
            :list="orderIndex"
            :active="activeBtn"
            @item-click="onChangeOrder"
          ></indicator-button>
          <indicator-button
            :indicator-title="'财务指标'"
            :list="financialIndex"
            :active="activeBtn"
            @item-click="onChangeOrder"
          ></indicator-button>
          <div class="chart-box">
            <line-chart ref="orderLineChartRef" />
          </div>
        </div>
        <div class="order-aside">
          <ranking-list :ranking-title="'产品排名'" @on-more="onMore" :ranking-list="rankingData"></ranking-list>
        </div>
      </div>
    </div>
    <order-analysis-list :params="{ ...commonParams }" ref="analysisListRef"></order-analysis-list>
  </div>
</template>

<script setup lang="ts" name="orderAnalysis">
import { reactive, ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { ECharts } from "echarts";
import dayjs from "dayjs";
import type { FormInstance, FormRules } from "element-plus";
import rankingList from "./components/rankingList.vue";
import IndicatorButton from "@/components/IndicatorButton/index.vue";
import lineChart from "./components/lineChart.vue";
import orderAnalysisList from "./components/orderAnalysisList.vue";
import { getOrderDateChat, getOrderProductTop, getProductList, getProviderList } from "@/api/modules/statisticAnalysis";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { startDate, endDate, startMonthDate, endMonthDate } from "../commonAnalysis";

const { BUTTONS } = useAuthButtons();

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}

interface IndexType {
  label: string;
  value: string;
}

// 获取子组件的ref
interface ChartExpose {
  initChart: (params: any, name?: string) => ECharts;
}

const router = useRouter();
const orderLineChartRef = ref<ChartExpose>();
const rankingData = ref<any>([]);
const analysisListRef = ref();

const dateType = ref<DateType>(DateType.day);
const activeBtn = ref("1");

const commonParams = reactive({
  countEndDate: endDate,
  countStartDate: startDate,
  dateType: dateType,
  orderAnalysisTypeKey: activeBtn,
  providerId: "",
  productIds: ""
});

// 订单分析指标类型枚举：1-订购订单数、2-退订订单数、3-净增订单数、4-累计生效订单数、5-交易金额GMV、6-累计交易金额GMV
const orderIndex = [
  {
    label: "订购订单数",
    value: "1"
  },
  {
    label: "退订订单数",
    value: "2"
  },
  {
    label: "净增订单数",
    value: "3"
  },
  {
    label: "累计生效订单数",
    value: "4"
  }
];
const financialIndex = [
  {
    label: "交易金额GMV",
    value: "5"
  },
  {
    label: "累计交易金额GMV",
    value: "6"
  }
];
const nameList: IndexType[] = [...orderIndex, ...financialIndex];

// 月报表入参月份
const handleCountDate = () => {
  if (dateType.value === DateType.month) {
    return {
      countEndDate: dayjs(commonParams.countEndDate).format("YYYY-MM"),
      countStartDate: dayjs(commonParams.countStartDate).format("YYYY-MM")
    };
  }
  return {};
};
// 图表
const getChat = () => {
  if (!BUTTONS.value?.query) {
    return;
  }
  getOrderDateChat({
    ...commonParams,
    ...handleCountDate()
  }).then((res: any) => {
    if (res.code === 200) {
      const { dateList = [], countList = [] } = res.data;

      const currentItem = nameList.filter(item => item.value === activeBtn.value);
      orderLineChartRef.value?.initChart(
        {
          data: countList,
          nameList: dateList
        },
        currentItem.length ? currentItem[0].label : ""
      );
    }
  });
};

// 排名
const getTop = () => {
  if (!BUTTONS.value?.query) {
    return;
  }
  getOrderProductTop({
    ...commonParams,
    ...handleCountDate()
  }).then(res => {
    if (res.code === 200) {
      rankingData.value = res.data;
    }
  });
};

const getAllData = () => {
  getTop();
  getChat();
  analysisListRef.value.getTableList({ ...commonParams, ...handleCountDate() });
};

onMounted(() => {
  getAllData();
  getSearchList();
});

const productOptions = ref([]);
const providerOptions = ref([]);
const getSearchList = async () => {
  getProductList().then((res: any) => {
    productOptions.value = res?.data || [];
  });
  getProviderList().then((res: any) => {
    providerOptions.value = res?.data || [];
  });
};

const changeDate = (type: DateType) => {
  if (type === DateType.month) {
    commonParams.countStartDate = startMonthDate;
    commonParams.countEndDate = endMonthDate;
    orderForm.countDate = [startMonthDate, endMonthDate];
  } else {
    commonParams.countStartDate = startDate;
    commonParams.countEndDate = endDate;
    orderForm.countDate = [startDate, endDate];
  }
};
const onChangeDateType = (type: DateType) => {
  dateType.value = type;
  commonParams.dateType = type;
  changeDate(type);
  getAllData();
};

const orderFormRef = ref<FormInstance>();
const orderForm = reactive({
  countDate: [startDate, endDate],
  productIdList: [],
  providerId: ""
});

const checkDate = (_: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("请选择日期"));
  } else {
    // 定义两个日期字符串
    const dateString1 = value[0];
    const dateString2 = value[1];
    // 将日期字符串转换为 dayjs 对象
    const date1 = dayjs(dateString1);
    const date2 = dayjs(dateString2);

    // 获取两个日期之间的月数差
    const monthDiff = Math.abs(date2.diff(date1, "month"));

    if (monthDiff > 11) {
      callback(new Error("最多只能查询12个月的数据"));
    }
  }
  callback();
};

const dateRule: any = computed(() =>
  dateType.value === DateType.day
    ? [{ required: true, message: "请选择日期", trigger: "blur" }]
    : [
        { required: true, message: "请选择日期", trigger: "blur" },
        { trigger: "blur", validator: checkDate }
      ]
);
const rules = reactive<FormRules>({
  countDate: dateRule
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      commonParams.countStartDate = orderForm.countDate[0];
      commonParams.countEndDate = orderForm.countDate[1];
      commonParams.productIds = (orderForm.productIdList || []).join(",");
      commonParams.providerId = orderForm.providerId || "";
      getAllData();
    } else {
      console.log("error submit!", fields);
    }
  });
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  // 处理重置日报表与月报表不相同
  changeDate(dateType.value);
  commonParams.productIds = "";
  commonParams.providerId = "";

  getAllData();
};

const onChangeOrder = (values: any) => {
  activeBtn.value = values.value;
  getTop();
  getChat();
};

const onMore = () => {
  const dateArr = [commonParams.countStartDate, commonParams.countEndDate];
  router.push({
    path: "/statisticAnalysis/orderAnalysis/orderDimension",
    query: {
      dateType: dateType.value,
      countDate: dateArr,
      productIds: commonParams.productIds ? commonParams.productIds?.split(",") : commonParams.productIds,
      providerId: commonParams.providerId
    }
  });
};

const chooseDay = ref<any>(null);
const handleChange = (val: Date[]) => {
  const [pointDay] = val;
  chooseDay.value = pointDay;
};
//取值方法
const handleFocus = () => {
  chooseDay.value = null;
};

const disabledDate = (time: number) => {
  if (!chooseDay.value) {
    return false;
  }
  let timeRange = 30;
  const con1 = new Date(chooseDay.value).getTime() - timeRange * 24 * 60 * 60 * 1000;
  const con2 = new Date(chooseDay.value).getTime() + timeRange * 24 * 60 * 60 * 1000;
  return time < con1 || time > con2;
};
</script>

<style lang="scss" scoped>
.cust-border {
  border-bottom: 1px solid #e1e3e6;
}
.order-analysis-container {
  background-color: #ffffff;
  .order-header {
    padding: 16px 0 0 24px;
    @extend.cust-border;
    .header-box {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      :deep(.el-form-item--default) {
        margin-bottom: 16px;
      }
      :deep(.el-form--inline .el-form-item) {
        margin-right: 12px;
      }
      :deep(.data-picker-style) {
        max-width: 210px;
      }
      .select-style {
        max-width: 190px;
      }
      .type-box {
        flex: 164px;
      }
    }
    .btn {
      height: 36px;
      &.active {
        color: var(--el-color-primary);
        background-color: var(--el-fill-color-blank);
        border-color: var(--el-color-primary);
      }
    }
  }
  .analysis-content {
    height: 470px;
    padding: 16px 24px 0;
    @extend.cust-border;
    .content-title {
      width: 100%;
      height: 24px;
      margin-bottom: 16px;
      font-family: "PingFangSC, PingFang SC,sans-serif";
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #000000d9;
    }
    .statistical-data {
      display: flex;
      width: 100%;
    }
    .order-main {
      flex: 1;
      .chart-box {
        width: 100%;
        height: 336px;
      }
    }
    .order-aside {
      width: 300px;
    }
  }
}
</style>
