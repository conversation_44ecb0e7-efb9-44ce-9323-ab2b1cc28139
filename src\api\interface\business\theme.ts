export interface themeItem {
  name: string;
  creatorName: string;
  createTime: string;
  id: string;
}

export interface cardItem {
  title: string;
  context: string;
}

export interface modelChildren {
  name: string;
  title: string;
  // type: string;
  // productIds: [];
  uploadType: string;
  videoUrl: string;
  iconUrl: string;
  link: string;
  operationButtons: buttonItemType[];
  additionalTitles: cardItem[];
  modelId: string;
  category: string;
  tags: string[];
  description: string;
  advantage: string;
  supplier: string;
}

export interface modelItem {
  name: string;
  title: string;
  type: string;
  skuIds: string[];
  products?: productItem[]; //详情产品列表
  categoryList: string[];
  tags: string[];
  iconUrl: string;
  videoUrl: string;
  richText: string;
  noticeContent: string;
  link: string;
  description: string;
  modelId: string;
  children: modelChildren[];
}

export interface themeLDetail {
  name: string;
  id: string;
  description: string;
  iconUrl: string;
  operationButtons: buttonItemType[];
  children: modelItem[];
}

export interface buttonItemType {
  name: string;
  context: string;
}

// export interface tagItem {
//   // label: string;
//   value: string;
// }

export interface productItem {
  productName: string;
  categoryName: string;
  // categoryFullName: string;
  skuName: string;
  skuPrice: string;
  skuDiscountPrice: string;
  id: string;
}
