/*
 * @Author: <EMAIL>
 * @Date: 2023-11-20 10:50:24
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-11-21 15:13:33
 * @Description: file content
 */
import http from "@/api";
// 获取地图的JSON
export const getMapJson = (name: string) => {
  return http.get(`/cnud/mapdata/${name}.json`, {}, { baseURL: "/minio" });
};
// 根据所选省份提供对应省份的云池信息
export const getPrivincePoint = (params: any) => {
  return http.get(`/cnud-smartscreen/cloud/privincecloudInformation`, params);
};
// top指标
export const getTopIndicatInfo = () => {
  return http.get(`/cnud-smartscreen/cloud/cloudPowerInformation`);
};
// 近7天的算力使用情况
export const getSuanLiUse = () => {
  return http.get(`/cnud-smartscreen/cloud/computePowerHistory`);
};
// 算力接入总数月份数据
export const getSuanLiTotal = () => {
  return http.get(`/cnud-smartscreen/cloud/increaseMonthHistory`);
};
// 总算力分布占比接口
export const getSuanLiDistr = () => {
  return http.get(`/cnud-smartscreen/cloud/privenceComputePower`);
};
// 资源型号数量分布
export const getResourceModel = () => {
  return http.get(`/cnud-smartscreen/processor/allProcessorInformation`);
};
// 近一天的服务器负载数据接口
export const getServerLoad = () => {
  return http.get(`/cnud-smartscreen/serverLoad/serverLoadInformation`);
};
