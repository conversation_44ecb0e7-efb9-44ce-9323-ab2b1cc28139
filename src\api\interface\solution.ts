export namespace Soluiton {
  export interface SolutionListParams {
    ascs?: string; // 根据该字段排正序
    descs?: string; // 根据该字段排倒序
    categoryIdL1?: string; // 一级分类ID
    categoryId?: string; // 方案分类ID
    cooperateType?: string; // 产品合作类型
    createTimeStart?: string;
    createTimeEnd?: string;
    createTime?: string;
    current?: number; // 当前页
    size?: number; // 每页的数量
    solutionName?: string; // 解决方案名
    solutionStatus?: number; // 上架状态，上架2 下架1
  }

  export interface SolutionItem {
    categoryId?: string;
    categoryIdL1?: string;
    categoryName?: string;
    cooperateType?: string;
    cooperateTypeName?: string;
    createTime?: Date;
    creatorId?: string;
    creatorName?: string;
    description?: string;
    iconUrl?: string;
    id?: string;
    introduce?: string;
    isDel?: boolean;
    mainUrl?: string;
    provider?: string;
    providerId?: string;
    publisher?: string;
    publishStatus?: number;
    publishTime?: string;
    solutionCode?: string;
    solutionName?: string;
    tenantId?: string;
    updaterId?: string;
    updaterName?: string;
    updateTime?: string;
    [property: string]: any;
  }
  export interface PutOnItem {
    id?: string;
    publishStatus?: number;
  }
  export interface DetailItem {
    id?: string;
  }
  export interface JudgeNameItem {
    solutionName?: string;
    id?: string;
  }
  export interface SolutionCategoryItem {
    cnt?: number;
    code?: string;
    createTime?: Date;
    creatorName?: string;
    description?: string;
    icon?: string;
    id?: string;
    level?: number;
    name?: string;
    parentId?: string;
    pic?: string;
    sort?: number;
    [property: string]: any;
  }
}
