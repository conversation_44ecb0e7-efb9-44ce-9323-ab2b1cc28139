<template>
  <div class="table-box">
    <pro-table ref="proTable" :request-api="getTableList" :columns="columns" :tool-button="false">
      <template #operation="{ row }">
        <el-tooltip content="下载" placement="top" v-if="row.status === '2'">
          <el-icon v-auth="'downloadExport'" class="download" size="20px" color="#0052D9">
            <Download @click="handleDownload(row)" />
          </el-icon>
        </el-tooltip>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx">
import { computed } from "vue";
import { Download } from "@element-plus/icons-vue";
import { ColumnProps } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { downloadManage, exportDownload } from "@/api/modules/upload";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import CustomTag from "@/components/CustomTag/index.vue";
import { downloadMange } from "@/api/interface/upload";
import { useDownload } from "@/hooks/useDownload";
import { downloadUtil } from "@/utils/download";

const { BUTTONS } = useAuthButtons();

const { audit_status } = useDict("audit_status");

const columns: ColumnProps[] = [
  {
    prop: "fileOriginalName",
    label: "导出文件"
  },
  {
    prop: "columSize",
    label: "导出数量"
  },
  {
    prop: "status",
    label: "审核状态",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {audit_status.value.map(item => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    render: (scope: any) => (
      <CustomTag
        type="audit"
        status={`download_${scope.row.status}`}
        label={useDictLabel(audit_status.value, scope.row.status)}
      ></CustomTag>
    )
  },
  {
    prop: "creatorName",
    label: "提交人员"
  },
  {
    prop: "createTime",
    label: "提交时间",
    sortable: true
  },
  {
    prop: "advice",
    label: "审核意见"
  },
  { prop: "auditorName", label: "审核人" },
  {
    prop: "auditTime",
    label: "审核时间",
    sortable: true
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const getTableList = async (params: downloadMange.IDownLoadSearch) => {
  return Reflect.has(BUTTONS.value, "downloadList") ? await downloadManage(params) : null;
};

const handleDownload = (row: downloadMange.IExportFile) => {
  exportDownload({ id: row.id }).then(res => {
    const contentDisposition = res?.headers.get("Content-Disposition");
    const _filename = decodeURIComponent(contentDisposition.split(";")[1].trim().split("=")[1]);

    let filename = _filename.replace(/\"/g, "");

    filename = filename.replace(/'/g, "");

    const blob = new Blob([res.data], {
      type: res.data.type
    });

    const blobUrl = window.URL.createObjectURL(blob);
    const exportFile = document.createElement("a");
    exportFile.style.display = "none";
    exportFile.download = `${filename}`;
    exportFile.href = blobUrl;
    document.body.appendChild(exportFile);
    exportFile.click();
    // 去除下载对 url 的影响
    document.body.removeChild(exportFile);
    window.URL.revokeObjectURL(blobUrl);
  });
};
</script>

<style scoped lang="scss">
.download {
  cursor: pointer;
}
</style>
