<template>
  <!-- 步骤条 -->
  <div class="step-bar">
    <step-tab :list="stepList" :step-value="stepValue" @item-click="handleStep"></step-tab>
  </div>
</template>

<script setup lang="ts" name="mystep">
import { ref } from "vue";
import StepTab from "@/components/StepTab/index.vue";
import mittBus from "@/utils/mittBus";

const props = defineProps({
  stepValue: {
    type: Number,
    default: 1
  },
  viewStatus: {
    type: Boolean,
    default: false
  }
});

let emits = defineEmits(["getStepValue"]);

const stepList = ref([
  {
    title: "基本信息",
    icon: "icon-jibenxinxi"
  },
  {
    title: "产品信息",
    icon: "icon-chanpinxinxi"
  },
  {
    title: "销售信息",
    icon: "icon-xiaoshouxinxi1"
  }
]);

const handleStep = (index: number) => {
  // 查看详情&跳转第一步，不需要验证
  if (index === 1 || props.viewStatus) {
    emits("getStepValue", index);
    return;
  }
  // 跳转到第二步和第三步要调用表单验证
  if (index === 2) {
    mittBus.emit("handleStepTwo");
  }
  if (index === 3) {
    if (props.stepValue === 1) {
      mittBus.emit("oneJumpToThree");
    } else {
      mittBus.emit("handleStepThree");
    }
  }
};
</script>
