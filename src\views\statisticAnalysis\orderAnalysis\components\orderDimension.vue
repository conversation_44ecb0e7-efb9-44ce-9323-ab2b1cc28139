<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="产品订单纬度"
      :columns="columns"
      :request-auto="false"
      :tool-button="false"
      :pagination="false"
      :data="tableData"
      :init-param="initParam"
      :request-api="getTableList"
      :summary-method="getSummaries"
      show-summary
      :collapsed="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button plain @click="downloadFile"> <i class="iconfont2 iconfont icon-a-lineicon_share"></i> 导出 </el-button>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="orderDimension">
import { ref, onMounted, reactive } from "vue";
import { useRoute } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { useDownload } from "@/hooks/useDownload";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getOrderProductList, exportOrderProductList, getProductList, getProviderList } from "@/api/modules/statisticAnalysis";
import { getTypeTree } from "@/api/modules/productClassify";

enum DateType {
  day = "day", // day-日报表
  month = "month" // month-月报表
}
interface DataType {
  list?: any;
  total?: any;
}

const route = useRoute();
const { countDate, dateType, productIds = [], providerId = "" } = route.query;

// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({
  dateType: dateType as string
});

const proTable = ref<ProTableInstance>();
const tableData = ref([]);
const totalData = ref([]);

onMounted(() => {
  proTable.value?.getTableList();
});

// 表格配置项
const columns: ColumnProps<any>[] = [
  { prop: "index", label: "序号", type: "index", width: 80 },
  {
    prop: "countDate",
    label: "日期",
    isShow: false,
    search: {
      el: "date-picker",
      props: {
        type: dateType === DateType.day ? "daterange" : "monthrange",
        valueFormat: dateType === DateType.day ? "YYYY-MM-DD" : "YYYY-MM"
      },
      defaultValue: countDate || ["", ""]
    }
  },
  {
    prop: "productName",
    label: "产品名称",
    enum: getProductList,
    isFilterEnum: false,
    fieldNames: { label: "productName", value: "productId" },
    search: {
      el: "select",
      key: "productIdList",
      props: { multiple: true, "collapse-tags": true, "collapse-tags-tooltip": true, filterable: true },
      defaultValue: productIds || []
    }
  },
  {
    prop: "productCategory",
    label: "产品分类",
    enum: getTypeTree,
    isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格
    search: { el: "cascader", key: "categoryIdList" },
    fieldNames: {
      value: "id",
      label: "name"
    }
  },
  {
    prop: "providerName",
    label: "合作商"
  },
  {
    prop: "providerId",
    label: "合作商",
    enum: getProviderList,
    fieldNames: { label: "providerName", value: "providerId" },
    search: {
      el: "select",
      props: { "collapse-tags": true, "collapse-tags-tooltip": true, filterable: true },
      defaultValue: providerId || ""
    },
    isShow: false
  },
  { prop: "subscribeCount", label: "订购订单数", sortable: true, minWidth: 100 },
  { prop: "unsubscribeCount", label: "退订订单数", sortable: true, minWidth: 100 },
  { prop: "netGrowthCount", label: "净增订单数", sortable: true, minWidth: 100 },
  { prop: "accumulateCount", label: "累计生效订单数", sortable: true, minWidth: 150 },
  { prop: "gmv", label: "交易金额GMV(元）", sortable: true, minWidth: 180 }
];

const getTableList = async (search: any = {}) => {
  const { countDate, productIdList = "", providerId = "", categoryIdList = "" } = proTable.value?.searchParam || {};
  let categoryId = "";
  if (categoryIdList?.length > 0) {
    categoryId = categoryIdList[categoryIdList.length - 1];
  }
  const responeData = await getOrderProductList({
    ...initParam,
    countStartDate: (countDate && countDate[0]) || "",
    countEndDate: (countDate && countDate[1]) || "",
    providerId,
    categoryId,
    productIds: (productIdList || []).join(",")
  });
  if (responeData.code === 200) {
    const data: DataType = responeData.data || {};
    tableData.value = data.list || [];
    totalData.value = data.total || [];
    return new Promise(resolve => {
      resolve({
        data: {
          records: data.list
        }
      });
    });
  } else {
    return new Promise(resolve => {
      resolve({
        data: {
          records: []
        }
      });
    });
  }
};
const getSummaries = (param: any) => {
  const { columns } = param;
  const sums: string[] = [];
  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = "总计";
      return;
    }
    sums[index] = totalData.value[column.property];
  });

  return sums;
};
const downloadFile = () => {
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() => {
    const { countDate, productIdList = "", providerId = "", categoryIdList = "" } = proTable.value?.searchParam || {};
    let categoryId = "";
    if (categoryIdList?.length > 0) {
      categoryId = categoryIdList[categoryIdList.length - 1];
    }
    const params = {
      ...initParam,
      countStartDate: (countDate && countDate[0]) || "",
      countEndDate: (countDate && countDate[1]) || "",
      providerId,
      categoryId,
      productIds: (productIdList || []).join(",")
    };
    useDownload(exportOrderProductList, params);
  });
};
</script>

<style lang="scss" scoped>
:deep(.el-table__footer-wrapper tbody td.el-table__cell, .el-table__header-wrapper tbody td.el-table__cell) {
  color: var(--el-color-primary);
  background-color: #ffffff;
}
</style>
