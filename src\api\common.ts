/*
 * @Author: <EMAIL>
 * @Date: 2023-12-15 16:41:08
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-21 15:23:04
 * @Description: file content
 */
import http from "@/api";

const queryAllDicListUrl: string = "/system/common/dict/allList";

export const queryAllDicList = () => {
  return http.get(queryAllDicListUrl);
};

/**
 * 下载
 * @param url
 * @param params
 */
export const downloadFileUrl = (url, params: any) => {
  return http.download(url, params);
};
