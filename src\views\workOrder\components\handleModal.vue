<template>
  <el-dialog v-model="dialogVisible" :title="modalTitle" width="488" :before-close="handleClose" :destroy-on-close="true">
    <div class="content">
      <div class="text">请确认是否受理该工单！</div>
      <div class="tips">受理后工单状态更新为处理中，请您及时处理</div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";

const modalTitle = "系统提示";
const dialogVisible = ref(false);

const emit = defineEmits(["onSubmit"]);

const handleOpen = () => {
  dialogVisible.value = true;
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleSubmit = () => {
  emit("onSubmit");
};

defineExpose({
  handleOpen,
  handleClose
});
</script>

<style scoped lang="scss">
.content {
  font-family: PingFangSC, "PingFang SC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  .text {
    color: #3a3a3d;
  }
  .tips {
    color: #9ca3ac;
  }
}
</style>
