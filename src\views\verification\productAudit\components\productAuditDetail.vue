<template>
  <div class="card table-box" style="padding-left: 32px">
    <!-- 待审核、审核通过、审核不通过 -->
    <product-status :product-status="baseInfo.auditStatus ?? '1'" />
    <audit-base-info :audit-info="baseInfo" />
    <div class="header-title">审核信息</div>

    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane v-for="item in tabs" :label="item.label" :name="item.value" :key="item.value"></el-tab-pane>
    </el-tabs>
    <!-- 1.基本信息 -->
    <div v-show="activeName === '1'">
      <step-one
        @get-step-value="handleShowStep"
        :passdata="productDetailObj"
        :page-type="baseInfoStatus"
        :view-status="true"
        :is-show-operate-btn="false"
        :is-audit="true"
      ></step-one>
    </div>
    <div v-show="activeName === '2'">
      <step-two
        @get-step-value="handleShowStep"
        :passdata="productDetailObj"
        :page-type="baseInfoStatus"
        :view-status="true"
        :is-audit="true"
        :is-show-operate-btn="false"
      ></step-two>
    </div>
    <div v-show="activeName === '3'">
      <step-three
        @get-step-value="handleShowStep"
        :passdata="productDetailObj"
        :page-type="baseInfoStatus"
        :view-status="true"
        :is-audit="true"
        :is-show-operate-btn="false"
      ></step-three>
    </div>

    <Examine
      :verification-id="'id'"
      :audit-msg="reviewForm.auditAdvice"
      :is-disabled="viewStatus"
      :is-show-operate-btn="!viewStatus"
      :is-show-backed-btn="false"
      @on-confirm="onConfirm"
    >
      <template #operateBtn>
        <!-- 审核类型为“产品变更”时，展示该按钮，点击弹出查看变更弹窗 -->
        <el-button v-if="baseInfo.changeList && baseInfo.changeList.length > 0" type="default" @click="toViewChangeList()">
          查看变更列表
        </el-button>
      </template>
    </Examine>
    <view-change-modal ref="viewChangeModalRef" :change-list="baseInfo.changeList" />
  </div>
</template>

<script setup lang="ts" name="productAuditDetail">
import { computed, ref, onMounted } from "vue";
import router from "@/routers";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import type { TabsPaneContext } from "element-plus";
import productStatus from "./productStatus.vue";
import auditBaseInfo from "./auditBaseInfo.vue";
import stepOne from "@/views/products/list/components/stepOne.vue";
import stepTwo from "@/views/products/list/components/stepTwo.vue";
import stepThree from "@/views/products/list/components/stepThree.vue";
import Examine from "@/views/verification/examine.vue";
import viewChangeModal from "./viewChangeModal.vue";
import { productAuditHistory } from "@/api/modules/product";
import { useProductStore } from "@/stores/modules/product";
import { closeTabItem } from "@/utils/closeTab";
import { useDicStore } from "@/stores/modules/dictionaty";

const dictionaryStore = useDicStore();
enum OperateType {
  "detail" = "1",
  "review" = "2"
}
const route = useRoute();
const productStore = useProductStore();
const activeStep = ref(1); // 进来默认在第一步
const { pageType = "", id = "" } = route.query;

const baseInfoStatus = "view"; // 只查看
const viewStatus = pageType == OperateType.detail; // 是否是查看详情，用来控制输入框的disabled

const tabs = [
  {
    label: "基本信息",
    value: "1"
  },
  {
    label: "产品信息",
    value: "2"
  },
  {
    label: "销售信息",
    value: "3"
  }
];
const activeName = ref("1");

const onConfirm = (values: any) => {
  productAuditHistory({
    id: id,
    auditAdvice: values.auditRemark,
    auditStatus: values.type == "pass" ? 2 : 3
  }).then(res => {
    if (res.code === 200) {
      ElMessage({
        message: values.type == "pass" ? "审核通过成功!" : "审核不通过完成!",
        type: "success"
      });
      closeTabItem(route);
      router.push("/verification/productAudit");
    } else {
      ElMessage({
        type: "error",
        message: res.msg
      });
    }
  });
};

const viewChangeModalRef = ref();
const toViewChangeList = () => {
  viewChangeModalRef.value.handleOpen();
};

const handleClick = (tab: TabsPaneContext, event: Event) => {
  activeName.value = tab.props.name as string;
};
const productDetailObj = computed(() => productStore.productAuditDetailObj.newValue); // 详情obj传给子组件，避免三个子组件拿三次
const baseInfo: any = computed(() => productStore.productAuditDetailObj); // 详情obj传给子组件，避免三个子组件拿三次

const reviewForm = ref({
  auditAdvice: baseInfo.value.auditAdvice || ""
});
const handleShowStep = (value: number) => {
  activeStep.value = value;
};
onMounted(() => {
  // 先获取字典
  dictionaryStore.queryProdCustomerType(); // 产品销售对象
  dictionaryStore.getCooperateDicList(); // 合作类型
  dictionaryStore.getOrderPayDicList(); // 支付方式
  dictionaryStore.getDeliveryDicList(); // 交付方式
  dictionaryStore.getJumpTypeDicList(); // 获取跳转类型字典
  dictionaryStore.getProductOpenMethodDicList(); // 获取产品-产品开通接口字典

  dictionaryStore.getskuTypeDicList(); //规格类型字典
  // 产品服务商列表
  // productStore.getProviderList();
  // 然后
  // proTable.value?.getTableList();
});
</script>

<style lang="scss" scoped>
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
</style>
