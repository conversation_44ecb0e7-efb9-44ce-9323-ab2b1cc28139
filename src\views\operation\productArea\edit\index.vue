<template>
  <div class="card content-box">
    <div class="edit-title">{{ route.meta.title }}</div>
    <el-form ref="ruleFormRef" :model="ruleForm" label-position="left" :rules="rules" label-width="83px">
      <el-form-item label="专区名称" placeholder="输入内容" prop="zoneName" required>
        <el-input :max="10" v-model="ruleForm.zoneName" />
      </el-form-item>
      <el-form-item label="专区图标" prop="iconUrl" required>
        <UploadImg :file-size="2" v-model:image-url="ruleForm.iconUrl" width="135px" height="135px">
          <template #tip>建议图片尺寸 60px x 60px，支持图片格式jpg/jpeg/png</template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="展示产品" prop="productIdList">
        <el-button type="primary" class="mb-10" @click="selectProduct">选择产品</el-button>
        <product-table v-model:productIdList="ruleForm.productIdList"></product-table>
      </el-form-item>
      <el-form-item label="查看更多" prop="moreEnabled">
        <el-switch v-model="ruleForm.moreEnabled" />
      </el-form-item>
      <el-form-item v-if="ruleForm.moreEnabled" label="跳转链接" prop="moreUrl" required>
        <el-input v-model="ruleForm.moreUrl" />
      </el-form-item>
      <el-form-item label="专区介绍" prop="description" placeholder="请输入内容">
        <el-input :max="50" v-model="ruleForm.description" type="textarea" />
      </el-form-item>
      <el-form-item label="排序" prop="sort" required>
        <el-input-number :controls="false" v-model="ruleForm.sort" />
      </el-form-item>
      <el-form-item label="启用状态" prop="enabled">
        <el-switch v-model="ruleForm.enabled" />
      </el-form-item>
      <div>
        <el-button type="primary" @click="submitForm(ruleFormRef)">确定</el-button>
        <el-button @click="goBack()">取消</el-button>
      </div>
    </el-form>
    <ProductDrawer ref="drawerRef" />
  </div>
</template>

<script setup lang="ts" name="productEdit">
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import ProductTable from "./productTable.vue";
import ProductDrawer from "./productDrawer.vue";
import { useRouter, useRoute } from "vue-router";
import { productAreaStorage } from "@/utils/storage/edit";
import UploadImg from "@/components/Upload/Img.vue";
import { change, add } from "@/api/modules/business/productArea";
import { useProductAreaStore } from "@/stores/modules/productArea";

const route = useRoute();

const productStore = useProductAreaStore();
const router = useRouter();
const drawerRef = ref<InstanceType<typeof ProductDrawer> | null>(null);

let isEdit = false;
const id: string = (route.query.id as string) || "";

const getInitForm = () => {
  const item = productAreaStorage.get(id);
  if (item) {
    isEdit = true;
    item.productIdList = item.productIdList || [];
    return {
      ...item
    };
  } else {
    return {
      zoneName: "",
      productIdList: [],
      iconUrl: "",
      description: "",
      moreEnabled: true,
      moreUrl: "",
      enabled: true,
      sort: 0
    };
  }
};

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive(getInitForm());

const validateMoreUrl = (rule: any, value: any, callback: any) => {
  if (ruleForm.moreEnabled && !ruleForm.moreUrl) {
    callback(new Error("请输入查看更多跳转链接"));
  }
  callback();
};

const validatorProductIdList = (rule: any, value: any, callback: any) => {
  if (!productStore.productDetailList.length) {
    callback(new Error("产品不能为空"));
  }
  callback();
};

const validateSort = (rule: any, value: any, callback: any) => {
  if (ruleForm.sort === 0) {
    callback(new Error("请输入排序"));
  }
  callback();
};

const rules = reactive<FormRules>({
  zoneName: [
    { required: true, message: "请输入专区名称", trigger: "blur" },
    { max: 10, message: "最多输入10个字符", trigger: "blur" }
  ],
  productIdList: [{ validator: validatorProductIdList, trigger: "blur" }],
  iconUrl: [
    {
      required: true,
      message: "专区图标不能为空",
      trigger: "blur"
    }
  ],
  description: [
    {
      required: true,
      message: "专区介绍不能为空",
      trigger: "blur"
    },
    { max: 50, message: "最多输入50个字符", trigger: "blur" }
  ],
  moreUrl: [{ validator: validateMoreUrl, trigger: "blur" }],
  sort: [{ validator: validateSort, trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    const data = { ...ruleForm, productIdList: productStore.productDetailList.map((item: any) => item.id) };
    data.productIds = JSON.stringify(data.productIdList);
    if (valid) {
      if (isEdit) {
        await change(data);
      } else {
        await add(data);
      }
      goBack();
    }
  });
};

const goBack = () => {
  productAreaStorage.clear();
  router.push("/operation/productArea");
};

const selectProduct = () => {
  if (drawerRef.value) {
    drawerRef.value.visible = true;
  }
};
</script>

<style scoped lang="scss">
.el-form {
  width: 100%;
  .text-center {
    text-align: center;
  }
}
.content-box {
  align-items: start;
}
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
</style>
