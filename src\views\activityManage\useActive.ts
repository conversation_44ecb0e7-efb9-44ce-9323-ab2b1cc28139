import { activityH5 } from "@/api/interface/activityH5";
import { queryMarketingDetail } from "@/api/modules/activityH5";
import { ref } from "vue";
import { useRoute } from "vue-router";

export default () => {
  const route = useRoute();

  const cList = ref<Array<any>>([]);

  const formData = ref<activityH5.activityParams>({
    name: "",
    startTime: "",
    endTime: "",
    joinUserType: "",
    rules: "",
    marketingProductList: [],
    shareContent: "",
    shareIcon: "",
    shareTitle: "",
    backgroundImage: "",
    headImage: ""
  });

  const pageType = ref("create");

  const activeTime = ref<string | [Date, Date] | [string, string]>("");

  if (Reflect.has(route.query, "pageType")) {
    if (Reflect.get(route.query, "pageType") === "edit" || Reflect.get(route.query, "pageType") === "view") {
      pageType.value = route.query.pageType as string;

      queryMarketingDetail({ marketingId: route.query.marketingId as string }).then(res => {
        formData.value = res.data;

        activeTime.value = [formData.value.startTime, formData.value.endTime];
        formData.value.marketingProductList = res.data.marketingProductList;
      });
    }
  }

  return { pageType, cList, formData, activeTime };
};
