<!--
 * @Author: <EMAIL>
 * @Date: 2023-11-20 17:29:21
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-28 09:06:35
 * @Description: file content
-->
<template>
  <el-drawer
    v-model="drawerVisible"
    title="绑定账号"
    direction="rtl"
    @close="drawerVisible = false"
    size="50%"
    class="account-drawer"
  >
    <template #default>
      <div class="h100">
        <partner-account-manage
          :search-params="searchParams"
          v-if="drawerVisible"
          :is-page="false"
          :platform-disabled="true"
        ></partner-account-manage>
      </div>
    </template>
    <template #footer>
      <div class="flex-auto">
        <el-button @click="drawerVisible = false">关闭</el-button>
      </div>
    </template>
  </el-drawer>
  <!-- <account-bind-dialog></account-bind-dialog> -->
</template>
<script setup lang="tsx">
import partnerAccountManage from "@/views/partner/partnerAccountManage/index.vue";
import { computed } from "vue";
interface TablePropType {
  data?: any[];
}
const emits = defineEmits(["update:visible"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: true
  },
  searchParams: {
    type: Object,
    default: () => {}
  }
});

const drawerVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    emits("update:visible", value);
  }
});
</script>
<style lang="scss">
.account-drawer {
  .el-select {
    width: auto;
  }
}
.h100 {
  height: 100%;
}
.flex-auto {
  flex: auto;
}
</style>
