<template>
  <div class="policy-content-box">
    <el-form ref="policyFormRef" :model="policyForm" :rules="rules" label-position="left" label-width="110px" :disabled="!isEdit">
      <el-form-item label="政策标题" prop="title" required>
        <el-input :maxlength="50" v-model="policyForm.title" placeholder="请输入政策标题" />
      </el-form-item>
      <el-form-item label="政策内容" prop="detail" required>
        <WangEditor :disabled="!isEdit" :max="20000" v-model:value="policyForm.detail" height="200px" />
      </el-form-item>
      <el-form-item label="跳转详情URL" prop="detailUrl">
        <el-input v-model="policyForm.detailUrl" :maxlength="128" placeholder="请输入跳转详情URL" />
      </el-form-item>
      <el-form-item label="封面" prop="coverUrl" required>
        <upload-img :file-size="2" v-model:image-url="policyForm.coverUrl" width="135px" height="135px">
          <template #tip>建议图片宽度为750px,高度为750px，支持图片格式jpg/jpeg/png</template>
        </upload-img>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="newsEdit">
import { reactive, ref } from "vue";
import { useRoute } from "vue-router";
import type { FormInstance, FormRules } from "element-plus";
import { policyNewsStorage } from "@/utils/storage/edit";
import UploadImg from "@/components/Upload/Img.vue";
import WangEditor from "@/components/WangEditor/index.vue";

defineProps({
  isEdit: {
    type: Boolean,
    default: false
  }
});

const route = useRoute();
const id: string = (route.query.id as string) || "";

const getInitForm = () => {
  const item = policyNewsStorage.get(id);
  if (item) {
    delete item.isCheck;
    return {
      title: "",
      detail: "",
      detailUrl: "",
      coverUrl: "",
      ...item
    };
  } else {
    return {
      title: "",
      detail: "",
      detailUrl: "",
      coverUrl: ""
    };
  }
};

const policyFormRef = ref<FormInstance>();
const policyForm = reactive(getInitForm());

const rules = reactive<FormRules>({
  title: [
    { required: true, message: "请输入政策标题", trigger: "blur" },
    { max: 50, message: "最多输入20个字符", trigger: "blur" }
  ],
  detail: [
    { required: true, message: "请输入政策消息内容", trigger: "blur" },
    { max: 20000, message: "最多输入20000个字符", trigger: "blur" }
  ],
  detailUrl: [{ required: true, message: "请输入跳转详情URL", trigger: "blur" }],
  coverUrl: [{ required: true, message: "请上传封面", trigger: "blur" }]
});

defineExpose({ policyFormRef, policyForm });
</script>

<style scoped lang="scss">
.policy-content-box {
  align-items: start;
}
</style>
