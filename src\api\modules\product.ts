/*
 * @Author: <EMAIL>
 * @Date: 2023-10-20 17:10:22
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-11-10 18:16:05
 * @Description: file content
 */
import { Product } from "@/api/interface/product";
import http from "@/api";

/**
 * @name 产品模块
 */
// 产品列表
export const getProductList = (params: Product.ProductListParams) => {
  return http.get(`/cnud-product/pmsProduct/queryPage`, params);
};

// 产品上下架
export const putOnOff = (params: Product.PutOnItem) => {
  return http.post(`/cnud-product/pmsProduct/publish`, params);
};

// 产品编辑
export const productEdit = (params: Object) => {
  return http.post(`/cnud-product/pmsProduct/update`, params);
};

// 产品新增
export const productSave = (params: Object) => {
  return http.post(`/cnud-product/pmsProduct/save`, params);
};

// 产品列表排序
export const productUpdateSort = (params: Object) => {
  return http.post(`/cnud-product/pmsProduct/updateSort`, params);
};

// 删除
export const deleteProduct = (params: Object) => {
  return http.post(`/cnud-product/pmsProduct/remove`, params);
};

// 产品详情
export const productDetail = (params: Product.DetailItem) => {
  return http.post(`/cnud-product/pmsProduct/detail`, params);
};

// 判断产品名称是否重复
export const judgeName = (params: Product.JudgeNameItem) => {
  return http.post(`/cnud-product/pmsProduct/judgeName`, params);
};

// 产品渠道接口（发布触点可用）
export const getChannelList = (params: Product.ChannelListParams) => {
  return http.get(`/cnud-product/pmsChannel/list`, params);
};

// 产品服务商列表
export const getProviderList = (params: Object) => {
  return http.get(`/cnud-product/pmsProvider/list`, params);
};
// 产品服务商列表(分页）
export const getProviderPageList = (params: Object) => {
  return http.get(`/cnud-product/pmsProvider/pageList`, params);
};
// 服务商详情
export const providerDeatil = (params: Object) => {
  return http.get(`/cnud-product/pmsProvider/detail`, params);
};
// 服务商新增
export const providerSave = (params: Object) => {
  return http.post(`/cnud-product/pmsProvider/save`, params);
};
// 服务商编辑
export const providerUpdate = (params: Object) => {
  return http.post(`/cnud-product/pmsProvider/update`, params);
};
// 服务商删除
export const providerRemove = (params: Object) => {
  return http.post(`/cnud-product/pmsProvider/delete`, params);
};

// 批量打标签
export const batchAddLabel = (params: Product.LabelAddItem) => {
  return http.post(`/cnud-product/pmsProduct/addTagsBatch`, params);
};

// 导出产品列表
export const exportProduct = (params: Object) => {
  return http.get(`/cnud-product/pmsProduct/export`, params, { responseType: "blob" });
};

/* 产品权益表-列表查询 */
export const productPmsRightsList = () => {
  return http.get<Product.pmsRightsListItem[]>(`/cnud-product/pmsRights/list`);
};
//获取产品属性，不分页
export const getProductAttrList = () => {
  return http.get(`/cnud-product/pmsProductAttr/list`);
};
/**
 * 产品属性定义表-列表查询(分页)
 * @param params
 * @returns
 */
export const pmsProductAttrList = (params?: Product.ProductAttrSearch) => {
  return http.get<{ current: number; pages: number; records: Product.ProductAttrListItem[]; size: number; total: number }>(
    `/cnud-product/pmsProductAttr/pageList`,
    params
  );
};
/**
 * 新增产品属性
 * @param params
 * @returns
 */
export const pmsProductAttrSave = (params: Product.ProductAttrListItem) => {
  return http.post<{ id: string }>(`/cnud-product/pmsProductAttr/save`, params);
};

/**
 * 编辑产品属性
 * @param params
 * @returns
 */
export const pmsProductAttrUpdate = (params: Product.ProductAttrListItem) => {
  return http.post(`/cnud-product/pmsProductAttr/update`, params);
};

/**
 * 产品属性详情
 * @param params
 * @returns
 */
export const pmsProductAttrDetail = (params?: { id: string | number }) => {
  return http.get<Product.ProductAttrListItem>(`/cnud-product/pmsProductAttr/detail`, params);
};

/**
 * 删除产品属性
 * @param params
 * @returns
 */
export const pmsProductAttrDelete = (params: { ids: string[] }) => {
  return http.post(`/cnud-product/pmsProductAttr/delete`, params);
};

/**
 * 管理后台-合作商管理表-列表查询(分页)
 * @param params
 * @returns
 */
export const pmsProviderList = (params?: any) => {
  return http.get<{ current: number; pages: number; records: any[]; size: number; total: number }>(
    `/cnud-product/operate/pmsProvider-pageList`,
    params
  );
};

/** 产品审核信息 start */
/**
 * 产品审核信息-列表查询
 * @param params
 * @returns
 */
export const getProductAuditList = (params: any) => {
  return http.get(`/cnud-product/admin/productAuditHistory/productAuditHistory-list`, params);
};
/**
 * 产品审核信息-详情
 * @param params
 * @returns
 */
export const getProductAuditDetail = (params: { id: string | number }) => {
  return http.get(`/cnud-product/admin/productAuditHistory/productAuditHistory-detail`, params);
};
/**
 * 产品审核处理
 * @param params
 * @returns
 */
export const productAuditHistory = (params: any) => {
  return http.post(`/cnud-product/admin/productAuditHistory/productAuditHistory-handle`, params);
};

/** CBSS产品ID查重 */
export const checkPhoneBillPayCode = (phoneBillPayCode: string) => {
  return http.post(`/cnud-product/admin/pmsProduct/phoneBillPayCode`, { phoneBillPayCode: phoneBillPayCode });
};

/** 产品协议管理 start */
//协议列表
export const productAgreementList = (params: any) => {
  return http.get(`/cnud-product/admin/productProtocol/queryPage`, params);
};

//协议新增
export const productAgreementAdd = (params: any) => {
  return http.post(`/cnud-product/admin/productProtocol/save`, params);
};

//协议编辑
export const productAgreementEdit = (params: any) => {
  return http.post(`/cnud-product/admin/productProtocol/update`, params);
};
/** 产品协议管理 end */

//id查询产品协议详情
export const productAgreementQuery = (id: string) => {
  return http.post(`/cnud-product/admin/productProtocol/detail`, { id: id });
};

//删除产品协议
export const productAgreementdelete = (id: string) => {
  return http.post(`/cnud-product/admin/productProtocol/remove`, { id: id });
};

// 赠品产品下拉选择列表
export const queryGiftSkuList = (params: Product.QueryGiftReq) => {
  return http.get(`/cnud-product/admin/pmsProduct/queryGiftSkuList`, params);
};
// 推广词
export const updatePromotionWord = (data: { ids: Array<string>; promotionWordSave: string }) => {
  return http.post(`/cnud-product/admin/pmsProduct/updatePromotionWord`, data);
};

// 上架产品规则下拉选择列表
export const queryProdSkuPage = (params: any) => {
  return http.get(`/cnud-product/admin/pmsProduct/queryProdSkuPage`, params);
};
