<template>
  <div class="form-item">
    <el-form-item label="二维码" v-if="typeVal === '3'" :prop="sourceType.qrCodeUrl">
      <UploadImg :file-size="2" :image-url="qrCodeUrl" @update:image-url="handleImgUrl" width="135px" height="135px">
        <template #tip>建议上传图片尺寸为50px×50px,图片大小不超过2M，支持图片格式jpg/jpeg/png</template>
      </UploadImg>
    </el-form-item>
    <el-form-item label="二维码使用说明" v-if="typeVal === '3'" :prop="sourceType.qrCodeDesc">
      <el-input :max="500" :model-value="qrCodeDesc" placeholder="输入内容" @input="handleQrCodeDesc" />
    </el-form-item>
    <el-form-item
      label="跳转链接"
      v-if="typeVal === '1' || typeVal === '2' || typeVal === '3'"
      :prop="sourceType.linkUrl"
      :rules="[{ required: typeVal === '1' || typeVal === '2', message: '请输入跳转链接', trigger: 'blur' }]"
    >
      <el-input :max="150" :model-value="linkUrl" placeholder="输入内容" @input="handleLinkUrl" />
    </el-form-item>
    <el-form-item>
      <div class="tips">
        <span v-if="typeVal !== '4'">
          手动填写跳转链接时请添加协议头，建议添加如：https或其他已定制的协议头，若添加http协议头有一定链接被篡改风险
        </span>
        <span v-show="typeVal === '3'">二维码和跳转链接只可选择1种方式输入</span>
      </div>
    </el-form-item>
    <el-form-item label="使用说明指引" v-if="typeVal === '4'" :prop="sourceType.descVal">
      <el-input
        type="textarea"
        :max="500"
        style="width: 600px"
        :model-value="descVal"
        @input="handleInputDesc"
        placeholder="输入内容"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import UploadImg from "@/components/Upload/Img.vue";
import { computed } from "vue";

/* 平台类型source miniApps:小程序/h5  portal:门户  */

const props = defineProps({
  source: String,
  typeVal: String,
  descVal: String,
  qrCodeUrl: String,
  qrCodeDesc: String,
  linkUrl: String
});
const emits = defineEmits(["update:descVal", "update:qrCodeDesc", "update:linkUrl", "update:qrCodeUrl"]);

const sourceType = computed(() => {
  if (props.source === "portal") {
    return {
      qrCodeUrl: "portalQRCodeUrl",
      qrCodeDesc: "portalQRCodeDescription",
      linkUrl: "url",
      descVal: "portalUseDescription"
    };
  } else {
    return {
      qrCodeUrl: "miniAppsQRCodeUrl",
      qrCodeDesc: "miniAppsQRCodeDescription",
      linkUrl: "miniAppsUrl",
      descVal: "miniAppsUseDescription"
    };
  }
});

const handleInputDesc = (event: any) => {
  emits("update:descVal", event);
};
const handleQrCodeDesc = (event: any) => {
  emits("update:qrCodeDesc", event);
};
const handleLinkUrl = (event: any) => {
  emits("update:linkUrl", event);
};
const handleImgUrl = e => {
  emits("update:qrCodeUrl", e);
};
</script>

<style scoped lang="scss">
.tips {
  color: var(--el-text-color-regular);
}
</style>
