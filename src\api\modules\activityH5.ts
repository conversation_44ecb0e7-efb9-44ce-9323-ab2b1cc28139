import http from "@/api";
import { activityH5 } from "../interface/activityH5";

/* 新增营销活动H5 */
export const queryAddMarketing = (data: activityH5.activityParams) => {
  return http.post<any>(`/cnud-product/admin/marketing/save`, data);
};

/* 营销活动H5列表 */
export const queryActivityH5Page = (params: activityH5.activitySearchParams) => {
  return http.get<Array<activityH5.marketingProductListItem>>(`/cnud-product/admin/marketing/queryPage`, params);
};

/* 营销活动H5详情 */
export const queryMarketingDetail = (data: { marketingId: string }) => {
  return http.post<activityH5.activityParams>(`/cnud-product/admin/marketing/detail`, data);
};

/* 营销活动H5更新 */
export const queryMarketingUpdate = (data: activityH5.activityParams) => {
  return http.post<Array<activityH5.marketingProductListItem>>(`/cnud-product/admin/marketing/update`, data);
};

/* 营销活动H5启用停止 */
export const queryMarketingStar = (data: { marketingId: string }) => {
  return http.post(`/cnud-product/admin/marketing/stop`, data);
};

/* 营销活动H5删除 */
export const queryMarketingDelete = (data: { marketingId: string }) => {
  return http.post(`/cnud-product/admin/marketing/remove`, data);
};

/* 营销活动详情-奖品领取情况 */
export const queryMarketingGetAnalysis = (data: { marketingId: string }) => {
  return http.post<activityH5.getAnalysisData>(`/cnud-order/admin/marketing/getAnalysis`, data);
};

/* 营销活动详情-参与明细 */
export const queryJoinDetailPage = (params: { marketingId: string; phone: string; startTime: string; endTime: string }) => {
  return http.get<Array<activityH5.JoinDetailPageItem>>(`/cnud-order/admin/marketing/joinDetailPage`, params);
};
/* 营销活动导出-参与明细 */
export const queryJoinDetailExport = (params: { marketingId: string; phone: string; startTime: string; endTime: string }) => {
  return http.download(`/cnud-order/admin/marketing/export`, params);
};
// /* 访问与参与数 */
// export const queryGetAccessAnalysis = (params: { marketingId: string }) => {
//   return http.get(`/cnud-product/admin/marketing/getAccessAnalysis`, params);
// };

/* 预览地址 */
export const generateMarketingPreviewUrl = (data: { marketingId: string }) => {
  return http.post<string>("/cnud-product/admin/marketing/generateMarketingPreviewUrl", data);
};
