###
 # @Author: <EMAIL>
 # @Date: 2023-09-08 09:49:57
 # @LastEditors: linzewei4270
 # @LastEditTime: 2023-11-30 10:58:48
 # @Description: file content
### 
# 本地环境
VITE_USER_NODE_ENV = development

# 公共基础路径
VITE_PUBLIC_PATH = ./

# 打包时是否删除 console
VITE_DROP_CONSOLE = true

# 是否开启 VitePWA
VITE_PWA = false

# 开发环境接口地址
VITE_API_URL = /api

# 开发环境跨域代理，支持配置多个
# 天穹对接 http://*************:8001/
VITE_PROXY = [["/api","http://*************:8083/"],["/minio","http://*************:8083/"]]

