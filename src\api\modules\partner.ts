import http from "@/api";
import { Partner } from "@/api/interface/partner";

/**
 * 合作商协议表-列表查询(分页)
 * @param params
 */
export const getProviderAgreementList = (params: any) => {
  return http.get(`/cnud-product/pmsProviderAgreement-pageList`, params);
};

/**
 * 合作商协议表-详情
 * @param params
 */
export const getProviderAgreementDetail = (params: { id: string }) => {
  return http.get<Partner.ProviderAgreementDetail>(`/cnud-product/pmsProviderAgreement-detail`, params);
};

/**
 * 合作商协议表-新增(返回id)
 * @param params
 */
export const addProviderAgreement = (params: Partner.AddAgreementParams) => {
  return http.post(`/cnud-product/pmsProviderAgreement-save`, params);
};

/**
 * 合作商协议表-修改
 * @param params
 */
export const updateProviderAgreement = (params: Partner.AddAgreementParams) => {
  return http.post(`/cnud-product/pmsProviderAgreement-update`, params);
};

/**
 * 合作商协议表-撤销
 * @param params
 */
export const revokeProviderAgreement = (params: { id: string }) => {
  return http.post(`/cnud-product/pmsProviderAgreement-revoke`, params);
};
/**
 * 合作商管理表-当前用户合作商详情
 * @param params
 */
export const getUserProvider = () => {
  return http.get(`/cnud-product/operate/pmsProvider-userProvider`);
};

/**
 * 合作商申请表-列表查询(分页)
 * @param params
 */
export const getProviderApplyList = (params: any) => {
  return http.get(`/cnud-product/pmsProviderApply-pageList`, params);
};

/**
 * 合作商申请表-审核
 * @param params
 */
export const auditProviderApply = (params: Partner.AuditParams) => {
  return http.post(`/cnud-product/pmsProviderApply-audit`, params);
};

/**
 * 合作商申请表-详情
 * @param params
 */
export const getProviderApplyDetail = (params: any) => {
  return http.get(`/cnud-product/pmsProviderApply-detail`, params);
};

/**
 * 合作商申请表-扩展企业信息详情
 * @param params
 */
export const getProviderApplyDetailExt = (params: any) => {
  return http.get(`/cnud-product/pmsProviderApply-detailExt`, params);
};

/**
 * 合作商申请表-审核接口
 * @param params
 */
export const getProviderApplyAuditDetailExt = (params: any) => {
  return http.get(`/cnud-product/admin/pmsProviderApply-auditDetailExt`, params);
};

export const queryRegionList = (params: any) => {
  return http.get(`/system/common/region/listByParentRegionCode`, params);
};

// export const pmsProviderAgreement = (params?: any) => {
//   return http.get<{ current: number; pages: number; records: Partner.ProviderAgreement[]; size: number; total: number }>(
//     `/cnud-product/pmsProviderAgreement-pageList`,
//     params
//   );
// };
/** 合作商管理 start */

/**
 * 新增
 * @param params
 */
export const addProvider = (params: any) => {
  return http.post(`/cnud-product/operate/pmsProvider-saveExt`, params);
};

/**
 * 编辑
 * @param params
 */
export const updateProvider = (params: any) => {
  return http.post(`/cnud-product/operate/pmsProvider-update`, params);
};

/**
 * 编辑
 * @param params
 */
export const updateExtProvider = (params: any) => {
  return http.post(`/cnud-product/operate/pmsProvider-updateExt`, params);
};

/**
 * 详情
 * @param params
 */
export const getProviderDetail = (params: { id: string }) => {
  return http.get(`/cnud-product/operate/pmsProvider-detailExt`, params);
};

/**
 * 导出
 * @param params
 */
export const exportProvider = (params: any) => {
  return http.get(`/cnud-product/operate/export`, params, { responseType: "blob" });
};
/** 合作商管理 end */

/** 合作商协议审核表接口 start */
/**
 * 合作商协议审核表-列表查询(分页)
 * @param params
 * @returns
 */
export const getProviderAgreementAudit = (params: any) => {
  return http.get(`/cnud-product/pmsProviderAgreementAudit-pageList`, params);
};
/**
 * 合作商协议审核表-详情
 * @param params
 * @returns
 */
export const getAgreementAuditDetail = (params: { id: string }) => {
  return http.get(`/cnud-product/pmsProviderAgreementAudit-detail`, params);
};
/**
 * 合作商协议审核表-审核
 * @param params
 * @returns
 */
export const auditProviderAgreement = (params: Partner.AuditParams) => {
  return http.post(`/cnud-product/pmsProviderAgreementAudit-audit`, params);
};

/** 合作商协议审核表接口 end */

/** 合作商引入管理表 start */
/**
 * 合作商引入管理表-列表查询(分页)
 * @param params
 * @returns
 */
export const getProviderIntroduce = (params: any) => {
  return http.get(`/cnud-product/pmsProviderIntroduce-pageList`, params);
};
/**
 * 合作商申请表-审核
 * @param params
 * @returns
 */
export const auditProviderIntroduce = (params: any) => {
  return http.post(`/cnud-product/pmsProviderIntroduce-audit`, params);
};

/**
 * 合作商引入管理表-详情
 * @param params
 * @returns
 */
export const getProviderIntroduceDetail = (params: any) => {
  return http.get(`/cnud-product/pmsProviderIntroduce-detail`, params);
};

/**
 * 合作商引入管理表-详情
 * @param params
 * @returns
 */
export const getProviderIntroduceDetailExt = (params: any) => {
  return http.get(`/cnud-product/pmsProviderIntroduce-detailExt`, params);
};

/**
 * 合作商引入管理表-审核详情
 * @param params
 * @returns
 */
export const queryProviderIntroduceAuditDetailExt = (params: any) => {
  return http.get(`/cnud-product/admin/pmsProviderIntroduce-auditDetailExt`, params);
};

/** 合作商引入管理表 end */

/**
 * 合作商管理-设置子商户号
 * @param data
 * @returns
 */
export const setMerchantNumber = (data: any) => {
  return http.post(`/cnud-product/admin/operate/setMerchantNumber`, data);
};

// 拨付审核分页查询
export const queryAuditPage = (data: any) => {
  return http.get(`/cnud-order/admin/order-account/account/audit-page`, data);
};

// 拨付审核详情查询
export const queryAuditDetail = (data: any) => {
  return http.post(`/cnud-order/admin/order-account/account/audit-detail`, data);
};

// 拨付审核一级审核
export const auditPrimary = (data: any) => {
  return http.post(`/cnud-order/admin/order-account/account/audit-primary`, data);
};

// 拨付审核二级审核
export const auditSecondary = (data: any) => {
  return http.post(`/cnud-order/admin/order-account/account/audit-secondary`, data);
};
