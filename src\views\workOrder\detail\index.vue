<template>
  <div>
    <div class="detail-box">
      <div class="msg-detail">
        <div class="detail-title">
          <span>工单详情-工单编号:{{ baseDetail?.code }}</span>
          <div v-if="pageType !== 'view'">
            <el-button
              v-if="baseDetail?.orderStatus == OrderStatus.Processing"
              class="default-status-btn default-btn"
              type="default"
              plain
              :icon="CircleCheck"
              color="#73787F"
              @click="onToHandle"
            >
              确认解决
            </el-button>
          </div>
        </div>

        <work-order-info :base-detail="baseDetail" title="工单信息"></work-order-info>
        <processing-records title="处理记录" :list-data="orderDetailList"></processing-records>
        <div v-if="pageType !== 'view' && baseDetail?.orderStatus !== OrderStatus.Close">
          <el-form ref="ruleFormRef" :model="recordsForm" :rules="rules" status-icon>
            <el-form-item label="" prop="records">
              <div class="dropzone" @drop.prevent="handleDrop" @click="inputRef.focus()">
                <el-input
                  ref="inputRef"
                  class="input-layout"
                  type="textarea"
                  resize="none"
                  :autosize="{
                    minRows: 5,
                    maxRows: 8
                  }"
                  maxlength="1200"
                  placeholder="支持拖动照片、附件到此上传"
                  v-model="recordsForm.records"
                  @paste="handlePaste"
                >
                </el-input>

                <div class="img-wrap">
                  <UploadImgs
                    class="image-upload"
                    v-if="imageList.length > 0"
                    v-model:file-list="imageList"
                    :limit="100"
                    :file-size="10"
                    height="68px"
                    width="68px"
                    :is-show-edit="false"
                    :is-show-view-text="false"
                    :is-show-del-text="false"
                    drag
                  >
                    <template #empty></template>
                  </UploadImgs>
                </div>
              </div>
            </el-form-item>
          </el-form>
          <div class="submit-wrap">
            <UploadFiles
              class="custom-upload-styles"
              v-model:file-list="productDocs"
              :file-type="'.doc, .xls, .docx, .xlsx, .rar, .zip, .jpg, .gif, .jpeg, .png, .pdf'"
              :limit="100"
              :file-size="10"
              :show-file-list="true"
              :drag="false"
            >
              <template #trigger>
                <el-button type="default"> <i class="iconfont2 icon-lineicon_upload"></i><span>上传文件</span> </el-button>
              </template>
            </UploadFiles>

            <div class="submit-box">
              <span>{{ recordsForm.records?.length || 0 }}/1200</span>
              <el-button type="primary" @click="confirmClick(ruleFormRef)">发送</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <confirm-Modal ref="confirmModalRef" @on-submit="onSubmit" />
  </div>
</template>

<script setup lang="tsx" name="workOrderDetail">
import { ref, computed, onMounted, reactive } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, ElNotification, FormRules, FormInstance, type UploadFile } from "element-plus";
import { CircleCheck } from "@element-plus/icons-vue";
import UploadFiles from "@/components/Upload/Files.vue";
import UploadImgs from "@/components/Upload/Imgs.vue";
import WorkOrderInfo from "./workOrderInfo.vue";
import ProcessingRecords from "./processingRecords.vue";
import { uploadImg } from "@/api/modules/upload";
import ConfirmModal from "../components/confirmModal.vue";
import { getWorkOrderDetail, saveWorkOrderLog, confirmWorkOrder } from "@/api/modules/workOrder";
import { useUserStore } from "@/stores/modules/user";
import { OrderStatus } from "../type";

const route = useRoute();

const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const id: string = (route.query.id as string) || "";
const pageType: string = (route.query.pageType as string) || "";
const confirmModalRef = ref();
const ruleFormRef = ref();

const recordsForm = ref({ records: "" });
const orderDetailList = ref<any>([]);
const inputRef = ref<any>([]);
const fileType = ".doc, .xls, .docx, .xlsx, .rar, .zip, .jpg, .gif, .jpeg, .png, .pdf";
const fileSize = 10;

const imageList = ref<any>([]);
const productDocs = ref<any>([]);
const baseDetail = ref();

// const orderTableData = ref<OrderDetail[]>([]);
// const fileList = computed<{ attrValue: string; id: string | number; attrName: string }[]>(() => {
//   if (orderDetail.value?.subOrderDetailList && orderDetail.value?.subOrderDetailList.length > 0) {
//     return orderDetail.value?.subOrderDetailList[0].attributes[0].children;
//   } else if (orderDetail.value?.attributes) {
//     return orderDetail.value?.attributes[0].children;
//   }
//   return [];
// });
const onToHandle = () => {
  confirmModalRef.value.handleOpen("confirm", {});
};

// const handleDragOver = (event: DragEvent) => {
//   // 这里可以添加一些视觉反馈，比如改变样式表示可以放置
//   // console.log("event.dataTransfer.files", event.dataTransfer.files);
// };

const handleDrop = (event: DragEvent) => {
  const files = event.dataTransfer?.files;
  uploadFiles(files);
};

const getDetails = (orderId: string) => {
  getWorkOrderDetail({ id: orderId }).then(res => {
    const { workOrderLogList = [], ...other } = res.data;
    baseDetail.value = other;
    orderDetailList.value = workOrderLogList;
    console.log(orderDetailList.value);
  });
};
const uploadFiles = async (files: any) => {
  // 这里编写文件上传的逻辑，例如使用FormData发送POST请求
  for (let i = 0; i < files.length; i++) {
    const fileSizes = files[i].size / 1024 / 1024 < fileSize;
    const type = files[i].name.substring(files[i].name.lastIndexOf("."));
    const fileTypes = fileType.includes(type);
    if (!fileTypes) {
      ElNotification({
        title: "温馨提示",
        message: `上传的文件不符合所需的格式,仅支持的文件格式为${fileType}！`,
        type: "warning"
      });
      return;
    }
    if (!fileSizes) {
      setTimeout(() => {
        ElNotification({
          title: "温馨提示",
          message: `上传的文件大小不能超过 ${fileSize}M！`,
          type: "warning"
        });
      }, 0);
      return;
    }
    let formData = new FormData();
    formData.append("file", files[i]);
    try {
      const api = uploadImg;
      const { data }: any = await api(formData);
      if (files[i].type.includes("image")) {
        imageList.value = [...imageList.value, { ...data, url: data?.link }];
      } else {
        productDocs.value = [...productDocs.value, { ...data, name: data?.originalName, link: data?.link }];
      }
    } catch (error) {
      console.log("error:", error);
    }
  }
};

const handlePaste = async (value: any) => {
  let file = value.clipboardData.items[0];
  if (file.type.includes("image")) {
    let imgFile = file.getAsFile();
    const formData = new FormData();
    formData.append("file", imgFile);
    try {
      const api = uploadImg;
      const { data }: any = await api(formData);
      imageList.value = [...imageList.value, { ...data, url: data.link }];
    } catch (error) {
      console.log("error:", error);
    }
  }
};
const confirmClick = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid: any) => {
    if (valid) {
      const proDocs = productDocs.value?.map((item: UploadFile) => {
        return {
          name: item.name,
          uid: item.uid,
          link: item.link
        };
      });
      const images = imageList.value?.map((item: any) => {
        return {
          name: item.name,
          url: item.url
        };
      });
      console.log(proDocs);

      saveWorkOrderLog({
        attachmentIds: JSON.stringify(proDocs),
        content: recordsForm.value.records,
        id,
        images: JSON.stringify(images),
        workOrderId: baseDetail.value?.id
      }).then(res => {
        if (res.code === 200) {
          ElMessage.success("发送成功！");
          // 重置
          recordsForm.value.records = "";
          productDocs.value = [];
          imageList.value = [];
          getDetails(id);
        }
      });
    }
  });
};

const onSubmit = (value: string) => {
  confirmWorkOrder({
    content: value,
    id,
    orderStatus: "3",
    userId: userInfo.value.id,
    userName: userInfo.value.userName
  }).then(res => {
    if (res.code === 200) {
      ElMessage.success("确认解决成功！");
      confirmModalRef.value.handleClose();
      getDetails(id);
    }
  });
};

const rules = reactive<FormRules>({
  records: [
    {
      required: true,
      message: "请输入回复内容",
      trigger: "blur"
    }
  ]
});

onMounted(() => {
  getDetails(id);
});
</script>
<style lang="scss" scoped>
.detail-box {
  padding: 24px 24px 19px;
  background: #ffffff;
}
.detail-title {
  display: flex;
  justify-content: space-between;
  height: 52px;
  font-family: PingFangSC, "PingFang SC", sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px;
  color: #3a3a3d;
  text-align: left;
  border-bottom: 1px solid #e1e3e6;
  :deep(.status-btn) {
    border: none;
    > span {
      font-family: PingFangSC, "PingFang SC", sans-serif;
      font-size: 14px;
      color: #3a3a3d;
    }
  }
  :deep(.default-status-btn) {
    background-color: #ffffff;
    border: 1px solid #73787f !important;
    > span {
      font-family: PingFangSC, "PingFang SC", sans-serif;
      font-size: 14px;
      color: #73787f;
    }
  }
  :deep(.resolved-btn) {
    border: none;
    .el-icon {
      color: #19be6b;
    }
  }
  :deep(.default-btn) {
    border: none;
    .el-icon {
      color: #73787f;
    }
  }
  :deep(.unresolved-btn) {
    border: none;
    .el-icon {
      color: var(--el-color-primary);
    }
  }
}
.bottom {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 1200px;
  margin: 0 auto;
  margin-top: 20px;
  font-size: 16px;
  .money {
    margin-left: 10px;
    font-size: 24px;
    font-weight: bold;
    color: var(--el-color-primary);
  }
}
.section-box {
  // width: 1200px;
  margin: 36px auto 0;
  .file-link {
    display: flex;
    align-items: center;
    margin: 5px 0;
    color: var(--el-text-color-regular);
    text-decoration: none;
    span {
      margin-left: 10px;
    }
  }
  &--remark {
    display: flex;
    margin-top: 24px;
    span {
      width: 43px;
      font-size: 14px;
      font-weight: 400;
      color: #73787f;
    }
    p {
      flex: 1;
      font-size: 14px;
    }
  }
  .section-title {
    position: relative;
    height: 28px;
    padding-left: 12px;
    font-family: PingFangSC-Semibold, "PingFang SC", sans-serif;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    color: #3a3a3d;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 20px;
      content: " ";
      background: var(--el-color-primary);
      transform: translate(0, -50%);
    }
  }
}
.to-pay-btn {
  margin-left: 10px;
  &.el-link.is-underline::after {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 0;
    content: "";
    border-bottom: 1px solid #1890ff;
  }
}
.dropzone {
  position: relative;
  width: 100%;
  margin: 16px 0 8px;
  color: #cccccc;
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  .img-wrap {
    padding: 0 16px;
    :deep(.image-upload .el-upload--picture-card) {
      display: none; /* 隐藏上传按钮 */
    }
  }
  :deep(.input-layout .el-textarea__inner) {
    border: none;
    box-shadow: none;
  }
  :deep(.input-layout .el-textarea__inner:focus, .input-layout .el-textarea__inner:hover) {
    border: none;
    box-shadow: none;
  }
}
:deep(.custom-upload-styles) {
  .upload .el-upload {
    display: inline-block;
    width: auto;
    height: auto;
    border-radius: 4px !important;
  }
  .upload .el-upload .el-upload-dragger {
    border: none;
    border-radius: 4px;
    &:hover {
      border: none;
    }
  }
  .el-upload-list__item {
    width: calc(100% + 30px);
  }
  &.upload-box .el-upload__tip {
    margin: 0 !important;
  }
  .upload .el-upload-list {
    margin: 0;
  }
}
.submit-wrap {
  display: flex;
  justify-content: space-between;
}
.submit-box {
  span {
    height: 22px;
    margin-right: 16px;
    font-family: PingFangSC, "PingFang SC", sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #c1c4cb;
  }
}
</style>
