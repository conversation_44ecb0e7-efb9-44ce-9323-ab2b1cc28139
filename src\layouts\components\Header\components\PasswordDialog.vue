<template>
  <el-dialog v-model="dialogVisible" title="修改密码" width="500px">
    <el-form ref="resetWordFormRef" :model="resetWordForm" :rules="rules" label-width="100px">
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input placeholder="请输入旧密码" v-model="resetWordForm.oldPassword" type="password" clearable show-password>
        </el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="certificate">
        <el-input placeholder="请输入新密码" v-model="resetWordForm.certificate" type="password" clearable show-password>
        </el-input>
      </el-form-item>
      <el-form-item label="确认新密码" prop="checkCertificate">
        <el-input placeholder="请再次输入新密码" v-model="resetWordForm.checkCertificate" type="password" clearable show-password>
        </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sureModify">确认修改</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { modifyPassword } from "@/api/modules/user";
import { FormRules } from "element-plus";
import { ElMessage } from "element-plus";

const resetWordForm = reactive({
  oldPassword: "",
  certificate: "",
  checkCertificate: ""
});
const rules = reactive<FormRules>({
  oldPassword: [
    {
      validator: (rule, value, cb) => {
        if (!value) {
          cb(new Error("请输入旧密码"));
        }

        cb();
      }
    }
  ],
  certificate: [
    {
      validator(rule, value, cb) {
        if (!value) {
          cb(new Error("请输入新密码"));
        }
        const reg = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*,\._])[0-9a-zA-Z!@#$%^&*,\\._]{8,20}$/;
        if (!reg.test(value)) {
          cb(new Error("请输入8-20位大小写字母+数字+特殊字符组合。"));
        } else {
          cb();
        }
      }
    }
  ],
  checkCertificate: [
    {
      validator: (rule, value, cb) => {
        if (!value) {
          cb(new Error("请再次输入新密码"));
        }
        if (value !== resetWordForm.certificate) {
          cb(new Error("两次密码输入不一致"));
        }
        cb();
      }
    }
  ]
});
const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
};
const sureModify = () => {
  modifyPassword({ verifyContent: 3, oldPassword: resetWordForm.oldPassword, newPassword: resetWordForm.certificate }).then(
    res => {
      if (res.code === 200) {
        ElMessagme.success("修改密码成功");
      }
    }
  );
};

defineExpose({ openDialog });
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item--default {
    margin-bottom: 38px;
  }
}
</style>
