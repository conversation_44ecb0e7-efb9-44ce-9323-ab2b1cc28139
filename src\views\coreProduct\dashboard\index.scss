.cust-border {
  border-bottom: 1px solid #e1e3e6;
}
.dataVisualize-box {
  background-color: #ffffff;
  .top-box {
    height: 39px;
    padding: 16px 0 0 24px;
    @extend.cust-border;
    .top-tab {
      display: flex;
      flex-direction: row;
      .top-title {
        width: 72px;
        margin-right: 30px;
        font-family: PingFangSC, "PingFang SC";
        font-size: 18px;
        font-weight: 600;
        line-height: 25px;
        color: #3a3a3d;
      }
      .demo-tabs {
        width: 900px;
        height: 58px;
      }
    }
  }
  .bottom-box {
    position: relative;

    // padding: 20px 0 0;
    margin-top: 16px;
    .bottom-title {
      position: absolute;
      top: 75px;
      left: 50px;
      font-family: DIN;
      font-size: 18px;
      font-weight: bold;
    }
    .bottom-tabs {
      padding: 0 50px;
    }
    .curve-echarts {
      box-sizing: border-box;
      height: 400px;
      padding: 0 50px;
    }
  }
}
.cluster-detail {
  background-color: #f5f7fa;

  // @extend.cust-border;
  .detail-chart {
    display: flex;
    flex-direction: row;
    background-color: #ffffff;

    // justify-content: space-between;
    @extend.cust-border;
    .card-title-box {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .card-chart {
      width: 178px;
      height: 178px;
      margin: 19px 20px 16px;
    }
    .card-title {
      width: auto;
      font-family: PingFangSC, "PingFang SC";
      font-size: 16px;

      // font-style: normal;
      font-weight: 500;
      line-height: 22px;
      color: #3a3a3d;
    }
    .card-count {
      // width: 56px;
      height: 20px;
      font-family: PingFangSC, "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      color: #3a3a3d;
      text-align: right;
    }
    .card-work-count {
      // width: 22px;
      height: 50px;
      font-family: PingFangSC, "PingFang SC";
      font-size: 36px;

      // font-style: normal;
      font-weight: 600;
      line-height: 50px;
      color: #0052d9;

      // text-align: left;
    }
    .card-text {
      width: 42px;
      height: 20px;
      margin-top: 21px;
      margin-bottom: 9px;
      margin-left: 30px;
      font-family: PingFangSC, "PingFang SC";
      font-size: 14px;

      // font-style: normal;
      font-weight: 400;
      line-height: 20px;
      color: #3a3a3d;

      // text-align: left;
    }
    .card-box {
      box-sizing: border-box;
      width: 267px;
      height: 267px;
      padding: 16px 24px;
      margin: 24px 0 24px 24px;
      border: 1px solid #e1e3e6;
      border-radius: 8px;
      .card-work-up {
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        width: 177px;
        height: 98px;
        padding: 24px 36px 24px 31px;
        margin: 10px 44px 0 46px;
        @extend.cust-border;
      }
      .card-work-down {
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        width: 177px;
        height: 98px;
        padding: 24px 36px 24px 31px;
        margin: 0 44px 0 46px;
      }
    }
  }
}
