<template>
  <!-- 步骤条 -->
  <div class="step-bar">
    <el-row class="step-box">
      <span class="step-box-item step-box-item_one">1.选择产品<i class="after" /></span>
      <span :class="[stepValue >= 2 ? 'success' : '', 'step-box-item']"> <i class="before" />2.选择规格 </span>
    </el-row>
  </div>
</template>

<script setup lang="ts" name="mystep">
defineProps({
  stepValue: {
    type: Number,
    default: 1
  }
});
</script>

<style lang="scss" scoped>
.step-box {
  display: flex;
  justify-content: space-between;
  width: 430px;
  margin: 0 auto;
  margin-bottom: 25px;
  &-item {
    position: relative;
    display: inline-block;
    width: 200px;
    height: 50px;
    font-size: 14px;
    line-height: 50px;
    color: #2d2d33;
    text-align: center;
    pointer-events: none !important;

    // cursor: pointer;
    background: #f0f1f5;
    .before {
      position: absolute;
      left: 0;
      z-index: 1;
      width: 0;
      height: 0;
      content: "";
      border: 25px solid transparent;
      border-left: 25px solid #ffffff;
    }
    .after {
      position: absolute;
      right: -50px;
      z-index: 1;
      width: 0;
      height: 0;
      content: "";
      border: 25px solid transparent;
      border-left: 25px solid #f0f1f5;
    }
    &.success {
      color: #ffffff;
      background: var(--el-color-primary);
      .before {
        border-left: 25px solid #ffffff;
      }
      .after {
        border-left: 25px solid var(--el-color-primary);
      }
    }
    &_one {
      width: 176px;
      color: #ffffff;
      background: var(--el-color-primary);
      .after {
        border-left: 25px solid var(--el-color-primary);
      }
    }
  }
}

// 禁用点击切换，因为需要一步一步往下走去处理数据，除非针对性做了处理，没时间改，先禁用了
// .step-box-item {
//   pointer-events: none !important;
// }
</style>
