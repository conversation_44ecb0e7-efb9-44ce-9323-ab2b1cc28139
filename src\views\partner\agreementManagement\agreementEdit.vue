<template>
  <div class="card">
    <div class="edit-title">合作协议信息</div>
    <el-form ref="agreeFormRef" :model="agreeForm" :rules="rules" label-position="left" label-width="150px">
      <el-row :gutter="48">
        <el-col :span="12">
          <el-form-item label="合作商名称" prop="name" required>
            <el-input
              v-model="agreeForm.name"
              placeholder="请选择合作商名称"
              :disabled="viewStatus || isPartner || isEdit"
              @click="selectEnterprise"
              readonly
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合作协议名称" prop="agreementName" required>
            <el-input :max="50" v-model="agreeForm.agreementName" placeholder="请输入合作协议名称" :disabled="viewStatus" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="有效期" prop="validTime">
            <el-date-picker
              type="daterange"
              v-model="agreeForm.validTime"
              start-placeholder="有效期开始时间"
              end-placeholder="有效期结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled="viewStatus"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="附件" prop="agreementUrl">
            <uploadFiles
              :is-private="true"
              v-model:file-list="agreeForm.agreementUrl"
              width="135px"
              height="135px"
              :file-type="'.doc,.docx,.pdf'"
              :limit="10"
              :show-file-list="true"
              :disabled="viewStatus"
            >
              <template #tip>
                <div class="upload-tips">上传的文件格式要求word、pdf，不超过10M。</div>
              </template>
            </uploadFiles>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否关联旧合作协议" prop="isRelated">
            <el-select
              v-model="agreeForm.isRelated"
              placeholder="请选择是否关联旧合作协议"
              :disabled="viewStatus"
              style="width: 230px"
              clearable
            >
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="hasRelated">
          <el-form-item label="关联旧合作协议" prop="relateAgreement">
            <el-button class="card-btn" plain @click="selectAgreement" style="margin-bottom: 16px" :disabled="viewStatus">
              选择
            </el-button>
            <template v-if="agreeForm.relateAgreement.length">
              <el-select
                v-model="agreeForm.relateAgreement"
                class="select-none"
                placeholder="请选择"
                style="width: 100%"
                filterable
                multiple
                ref="multiSelect"
                @focus="onFocus"
                @click.prevent="onFocus"
                :disabled="viewStatus"
              >
                <el-option v-for="item in relateList" :key="item.id" :label="item.agreementName" :value="item.id" />
              </el-select>
            </template>
          </el-form-item>
          <div class="tips">注意：如果选择关联旧合作协议，审核通过后，旧合作协议将无效。</div>
        </el-col>
      </el-row>
      <div>
        <el-button type="primary" @click="submitForm(agreeFormRef)" v-if="!viewStatus"> 确定 </el-button>
        <el-button @click="goBack()"> 取消 </el-button>
      </div>
    </el-form>

    <partnerModal ref="partnerRef" @selected-partner="getPartnerInfo"></partnerModal>
    <agreementModal
      ref="agreementRef"
      :provider-id="partnerInfo?.providerId"
      @selected-agreement="getAgreementInfo"
    ></agreementModal>
  </div>
</template>

<script setup lang="ts" name="agreementEdit">
import { reactive, ref, computed, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import agreementModal from "./agreementModal.vue";
import partnerModal from "./partnerModal.vue";
import uploadFiles from "@/components/Upload/Files.vue";
import {
  addProviderAgreement,
  updateProviderAgreement,
  getProviderAgreementDetail,
  getUserProvider
} from "@/api/modules/partner";
import { closeTabItem } from "@/utils/closeTab";
import { useUserStore } from "@/stores/modules/user";
import { UserType } from "@/enums/userInfo";
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const isPartner = computed(() => userStore.userInfo.userType == UserType.partnerUsers);

enum editType {
  "add" = 1,
  "edit" = 2,
  "revoke" = 3,
  "details" = 4
}

const route = useRoute();
const viewStatus = computed(() => route.query.type === `${editType.details}`);
let isEdit = route.query.type === `${editType.edit}`;

const partnerInfo = ref<any>();

const agreementRef = ref();
const partnerRef = ref();
const relateList = ref<any>([]);
const multiSelect = ref();

const router = useRouter();

const agreeFormRef = ref<FormInstance>();
let agreeForm = reactive({
  name: "",
  agreementName: "",
  validTime: [],
  agreementUrl: [],
  isRelated: "0",
  relateAgreement: []
});
const hasRelated = computed(() => agreeForm.isRelated === "1");

onMounted(() => {
  if (isPartner.value) {
    getUserProviderInfo();
  }
  if (route.query.type === `${editType.details}` || isEdit) {
    const id = JSON.parse(JSON.stringify((route.query.id as any) || ""));
    getProviderAgreementDetail({ id }).then(result => {
      if (result.code === 200) {
        const detailData: any = result.data;
        agreeForm = Object.assign(agreeForm, detailData);
        if (detailData?.validStartTime) {
          let date: any = [];
          date.push(detailData.validStartTime, detailData.validEndTime);
          agreeForm.validTime = date;
        }
        const agreementUrl = JSON.parse(detailData.agreementUrl);
        agreeForm.agreementUrl = agreementUrl.map((item: any) => {
          return { ...item, name: item.originalName, url: item.link };
        });

        const relate = JSON.parse(detailData.relateAgreement);
        const select = relate?.map((item: any) => item.id);
        agreeForm.relateAgreement = select;
        relateList.value = relate;
        partnerInfo.value = {
          providerId: detailData.providerId,
          name: detailData.name,
          code: detailData.code
        };
      }
    });
  }
});

const getUserProviderInfo = async () => {
  await getUserProvider().then(resp => {
    const data: any = resp.data;
    agreeForm.name = data?.name;
    partnerInfo.value = {
      providerId: data?.id,
      name: data?.name,
      code: data?.code
    };
  });
};
const selectAgreement = () => {
  if (!partnerInfo.value || Object.keys(partnerInfo.value).length == 0) {
    return ElMessage.info("请先选择合作商名称");
  }
  agreementRef.value.handleOpen();
};

const selectEnterprise = () => {
  if (isPartner.value) return;
  partnerRef.value.handleOpen();
};
const onFocus = () => {
  multiSelect.value.blur();
};

const getPartnerInfo = (values: any) => {
  if (partnerInfo.value?.providerId !== values.providerId) {
    agreeForm.relateAgreement = [];
  }
  agreeForm.name = values.name;
  partnerInfo.value = values;
};

const getAgreementInfo = (data: any) => {
  const list = data ? JSON.parse(JSON.stringify(data)) : [];
  relateList.value = list;
  const select = list?.map((item: any) => item.id);
  agreeForm.relateAgreement = select;
};
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入合作商名称", trigger: "blur" }],
  agreementName: [
    { required: true, message: "请输入合作协议名称", trigger: "blur" },
    { max: 50, message: "最多输入50个字符", trigger: "blur" }
  ],
  validTime: [{ required: true, message: "请选择有效期", trigger: "blur" }],
  agreementUrl: [{ required: true, message: "请上传附件", trigger: "blur" }],
  isRelated: [{ required: true, message: "请选择是否关联旧合作协议", trigger: "blur" }],
  relateAgreement: [{ required: true, message: "请选择关联旧合作协议", trigger: "blur,change" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async valid => {
    if (valid) {
      const { validTime, ...other } = agreeForm;
      const fileList = agreeForm.agreementUrl?.map((item: { url: string; uid: number; name: string; link: string }) => {
        return { link: item.link, uid: item.uid, originalName: item.name };
      });
      console.log(fileList);

      const agreementUrl = JSON.stringify(fileList);

      const relateAgree: any = [];
      if (agreeForm.relateAgreement && agreeForm.relateAgreement.length > 0) {
        agreeForm.relateAgreement.forEach(item => {
          const index = relateList.value?.findIndex((items: any) => items.id === item);
          if (index !== -1) {
            const relateItem = relateList.value[index];
            relateAgree.push({
              agreementName: relateItem.agreementName,
              id: relateItem.id,
              validEndTime: relateItem.validEndTime,
              validStartTime: relateItem.validStartTime
            });
          }
        });
      }
      const relateAgreement = JSON.stringify(relateAgree);

      const data = {
        ...other,
        code: partnerInfo.value.code,
        providerId: partnerInfo.value.providerId,
        agreementUrl,
        relateAgreement,
        isRelated: Number(agreeForm.isRelated),
        validStartTime: validTime && validTime.length ? validTime[0] : undefined,
        validEndTime: validTime && validTime.length ? validTime[1] : undefined
      };

      const api = isEdit ? updateProviderAgreement : addProviderAgreement;
      const text = isEdit ? "更新" : "新增";
      await api(data).then(res => {
        if (res.code == 200) {
          ElMessage({
            message: `合作协议${text}成功`,
            type: "success"
          });

          closeTabItem(route);
          goBack();
        } else {
          ElMessage({
            message: `合作协议${text}失败`,
            type: "error"
          });
        }
      });
    }
  });
};

const goBack = () => {
  router.push("/partner/agreementManagement");
};
</script>

<style scoped lang="scss">
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
.mt10 {
  margin-top: 10px;
}
.tips {
  margin-bottom: 16px;
  font-size: var(--el-form-label-font-size);
  color: #3a3a3d;
}

/* 为select框添加class */
.select-none {
  // pointer-events: none;

  /* 取消选择框最右边的图标事件 */
  :deep(.el-input__suffix) {
    display: none;
  }
}
</style>
