<template>
  <div class="detailed">
    <div class="mb20 header-title-common">参与明细</div>
    <div class="table-box">
      <ProTable ref="proTable" :request-api="getTableList" :columns="columns" :tool-button="false">
        <template #tableHeader>
          <el-button plain @click="handleExport">
            <i class="iconfont2 icon-a-lineicon_share"></i>
            导出
          </el-button>
        </template>
      </ProTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { nextTick, onMounted, ref } from "vue";
import { queryJoinDetailPage, queryJoinDetailExport } from "@/api/modules/activityH5";
import { useRoute } from "vue-router";
import { useDownload } from "@/hooks/useDownload";
import { ElMessage } from "element-plus";

const route = useRoute();

const columns: ColumnProps[] = [
  {
    prop: "phone",
    label: "手机号码",
    align: "center",
    search: { el: "input" }
  },
  {
    prop: "productName",
    label: "奖品名称",
    align: "center"
  },
  {
    prop: "skuName",
    label: "产品规格",
    align: "center"
  },
  {
    prop: "time",
    label: "领取时间",
    align: "center",
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        valueFormat: "YYYY-MM-DD HH:mm:ss"
      },
      transform: (value: any) => {
        return {
          startTime: value[0],
          endTime: value[1]
        };
      }
    },
    render: (scope: any) => `${scope.row.createTime}`
  }
];

const proTable = ref<ProTableInstance>();

const getTableList = async params => {
  Reflect.deleteProperty(params, "time");
  return await queryJoinDetailPage({ ...params, marketingId: route.query.marketingId as string });
};

const handleExport = () => {
  let params = Object.assign({}, proTable.value?.searchParam);
  const values = params;
  values.size = 10000;
  values.current = 1;
  values.marketingId = route.query.marketingId;
  let newParams = JSON.parse(JSON.stringify(values));
  console.log(newParams);

  // useDownload(queryJoinDetailExport, newParams);
  queryJoinDetailExport(newParams)
    .then(res => {
      ElMessage.success("请前往下载管理中下载文件");
    })
    .catch(() => {
      ElMessage.error("下载有误，请重新下载");
    });
};

onMounted(() => {
  // proTable.value?.getTableList();
});
</script>

<style scoped lang="scss">
.detailed {
  margin-top: 30px;
}
</style>
