<template>
  <div class="card content-box">
    <div class="edit-title">{{ route.meta.title }}</div>
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-position="left" label-width="83px">
      <el-form-item label="项目名称" prop="bannerName" required>
        <el-input :max="20" v-model="ruleForm.bannerName" placeholder="输入内容" />
      </el-form-item>
      <el-form-item label="轮播图片" prop="imageUrl" required>
        <UploadImg :file-size="2" v-model:image-url="ruleForm.imageUrl" width="135px" height="135px">
          <template #tip>建议上传图片尺寸为7680px x 960px，支持图片格式jpg/jpeg/png</template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="移动端轮播图片" prop="mobileImageUrl">
        <UploadImg :file-size="1" v-model:image-url="ruleForm.mobileImageUrl" width="135px" height="135px">
          <template #tip>建议上传图片尺寸为750 px× 714px，图片大小不超过1M，支持图片格式jpg/jpeg/png</template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="交互形式" prop="actionType" required>
        <el-radio-group v-model="ruleForm.actionType">
          <el-radio v-for="item in banner_action" :key="item.itemKey" :label="item.itemKey">{{ item.itemValue }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="跳转链接" prop="linkUrl" v-if="ruleForm.actionType === jumpUrl">
        <el-input v-model="ruleForm.linkUrl" :max="150" placeholder="输入内容" />
      </el-form-item>
      <el-form-item label="播放视频" prop="videoUrl" v-else>
        <UploadVideo v-model:video-url="ruleForm.videoUrl">
          <template #tip>视频的格式为mp4,视频不能大于100M</template>
        </UploadVideo>
      </el-form-item>
      <el-form-item label="定时">
        <el-form-item prop="date">
          <el-date-picker
            type="datetimerange"
            v-model="ruleForm.date"
            :disabled-date="disabledDate"
            range-separator="-"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
      </el-form-item>
      <el-form-item label="启用状态" prop="enabled">
        <el-switch v-model="ruleForm.enabled" />
      </el-form-item>
      <el-form-item label="排序" prop="sort" required>
        <el-input-number :controls="false" v-model="ruleForm.sort" />
      </el-form-item>
      <div>
        <el-button type="primary" @click="submitForm(ruleFormRef)"> 提交发布 </el-button>
        <el-button @click="goBack()"> 取消 </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="bannerEdit">
import { reactive, ref, computed, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import UploadVideo from "@/components/Upload/Video.vue";
import UploadImg from "@/components/Upload/Img.vue";
import { bannerStorage } from "@/utils/storage/edit";
// import { useDicStore } from "@/stores/modules/dictionaty";
import { changeBanner, addBanner } from "@/api/modules/business/banner";
import { useRoute, useRouter } from "vue-router";
import { useDict } from "@/hooks/useDict";

const { banner_action } = useDict("banner_action");

const route = useRoute();

const id: string = (route.query.id as string) || "";

let isEdit = false;

const getInitForm = () => {
  const banner = bannerStorage.get(id);
  if (banner) {
    isEdit = true;
    return {
      ...banner,
      date: [banner.startTime, banner.endTime],
      videoUrl: "",
      linkUrl: ""
    };
  } else {
    return {
      bannerName: "",
      mobileImageUrl: "",
      imageUrl: "",
      actionType: "",
      date: [undefined, undefined],
      actionUrl: "",
      sort: 0,
      enabled: true,
      videoUrl: "",
      linkUrl: ""
    };
  }
};

const router = useRouter();

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive(getInitForm());
const disabledDate = (time: Date) => time.getTime() < Date.now() - 60 * 60 * 24 * 1000;

//  banner_action
// const distStore = useDicStore();
// const { bannerActionType } = distStore;
// distStore.getBannerActionType().then(() => {
//   if (ruleForm.actionType === bannerActionType[0].itemKey) {
//     ruleForm.linkUrl = ruleForm.actionUrl;
//   } else {
//     ruleForm.videoUrl = ruleForm.actionUrl;
//   }
// });
watch(
  () => banner_action.value,
  () => {
    if (ruleForm.actionType === banner_action.value[0]?.itemKey) {
      ruleForm.linkUrl = ruleForm.actionUrl || "";
    } else {
      ruleForm.videoUrl = ruleForm.actionUrl || "";
    }
  },
  {
    immediate: true,
    deep: true
  }
);
const jumpUrl = computed(() => {
  return banner_action.value[0]?.itemKey || "";
});

const validateActionUrl = (rule: any, value: any, callback: any) => {
  if (value === "") {
    banner_action.value?.forEach((item: any) => {
      if (ruleForm.actionType === item.itemKey) {
        callback(new Error(`请输入${item.itemValue}`));
      }
    });
  }
  callback();
};

const validateSort = (rule: any, value: any, callback: any) => {
  if (ruleForm.sort === 0) {
    callback(new Error("请输入排序"));
  }
  callback();
};

const rules = reactive<FormRules>({
  bannerName: [
    { required: true, message: "请输入项目名称", trigger: "blur" },
    { max: 20, message: "最多输入20个字符", trigger: "blur" }
  ],
  imageUrl: [{ required: true, message: "请上传轮播图片", trigger: "blur" }],
  actionType: [{ required: true, message: "请选择交互形式", trigger: "blur" }],
  videoUrl: [{ validator: validateActionUrl, trigger: "blur" }],
  linkUrl: [{ validator: validateActionUrl, trigger: "blur" }],
  sort: [{ validator: validateSort, trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const data = {
        ...ruleForm,
        startTime: ruleForm.date && ruleForm.date.length ? ruleForm.date[0] : undefined,
        endTime: ruleForm.date && ruleForm.date.length ? ruleForm.date[1] : undefined,
        date: undefined,
        actionUrl:
          banner_action.value && banner_action.value.length && banner_action.value[0].itemKey === ruleForm.actionType
            ? ruleForm.linkUrl
            : ruleForm.videoUrl
      };
      if (isEdit) {
        await changeBanner(data);
      } else {
        await addBanner(data);
      }
      goBack();
    }
  });
};

const goBack = () => {
  bannerStorage.clear(id);
  router.push("/operation/banner");
};
</script>

<style scoped lang="scss">
.content-box {
  align-items: start;
}
.edit-title {
  padding-left: 3px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
</style>
