import http from "@/api";
import { CoreSystem } from "@/api/interface/coreSystem";

/*  硬件资源权重配置列表 */
export const queryHardwareWeightConfigList = async (params?: CoreSystem.hardwareWeightSearch) => {
  return await http.get<Array<CoreSystem.hardwareWeightItem>>("/cnud-desk/admin/hardware/hardwareWeight/pageList", params);
};

/*  新增硬件资源权重配置 */
export const addHardwareWeightConfig = async (params: any) => {
  return await http.post("/cnud-desk/admin/hardware/hardwareWeight/save", params);
};

/*  删除硬件资源权重配置 */
export const deleteHardwareWeightConfig = async (id: string) => {
  return await http.post("/cnud-desk/admin/hardware/hardwareWeight/delete", { ids: [id] });
};

/*  修改硬件资源权重配置 */
export const updateHardwareWeightConfig = async (params: any) => {
  return await http.post("/cnud-desk/admin/hardware/hardwareWeight/update", params);
};

// /*  集群下拉列表 */
// export const getClusterList = async () => {
//   return await http.get("/cnud-desk/portal/cluster");
// };

/*  硬件资源下拉列表 */
export const getHardwareCodeList = async (id: string) => {
  return await http.get<string[]>("/cnud-desk/admin/hardware/hardwareWeight/hardwareCodeList", { platformCode: id });
};
