<template>
  <!--  <el-icon class="collapse-icon" @click="changeCollapse">-->
  <!--    <component :is="globalStore.isCollapse ? 'expand' : 'fold'"></component>-->
  <!--  </el-icon>-->
  <div class="collapse-icon" @click="changeCollapse">
    <i :class="['iconfont2', globalStore.isCollapse ? 'icon-lineicon_outdent1' : 'icon-lineicon_outdent']"></i>
  </div>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/modules/global";

const globalStore = useGlobalStore();
const changeCollapse = () => globalStore.setGlobalState("isCollapse", !globalStore.isCollapse);
</script>

<style scoped lang="scss">
.collapse-icon {
  margin-right: 20px;

  // color: var(--el-header-text-color);
  color: #ffffff;
  cursor: pointer;
}
</style>
