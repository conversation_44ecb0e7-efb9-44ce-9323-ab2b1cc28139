import http from "@/api";
import { array } from "@amcharts/amcharts4/core";

// 获取试用管理列表
export const getPageList = (data: any) => {
  return http.get("/cnud-product/admin/productDiscount/queryProbationPage", data);
};

// 获取试用产品列表
export const getProductList = (data: any) => {
  return http.get("/cnud-product/admin/pmsProduct/queryProbationPage", data);
};

//删除试用产品
export const deleteItem = (id: string) => {
  return http.post("/cnud-product/admin/productDiscount/delProbation", { id });
};

//新增试用产品
export const add = (ids: Array<string>) => {
  return http.post("/cnud-product/admin/productDiscount/addProbation", { ids: ids });
};
// export const getCategory = () => {
//   return http.get("/cnud-product/admin/pmsProduct/queryDiscountPage");
// };
//排序
export const sortDiscount = (ids: string, sort: number) => {
  return http.post("/cnud-product/admin/productDiscount/sortProbation", { id: ids, sort: sort });
};
