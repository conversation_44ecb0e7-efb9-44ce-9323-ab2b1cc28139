<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="列表"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
      :request-auto="false"
      :tool-button="false"
      :is-keep-selected="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button v-auth="'add'" plain :icon="Plus" @click="openDrawer(editType.add, null)"> 新增 </el-button>
        <el-button
          v-auth="'delete'"
          plain
          :icon="Delete"
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          删除
        </el-button>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 iconfont icon-a-lineicon_share"></i> 导出
        </el-button>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <!-- 超级管理员账号不允许被停用。启停按钮不可见 -->
        <span v-auth="'enable'">
          <el-tooltip v-if="!isAdmin" :content="scope.row.status ? '停用' : '启用'" placement="top">
            <i
              :class="['iconfont2 opera-icon', scope.row.status ? 'icon-tingyong' : 'icon-qiyong']"
              @click="changeStatus(scope.row)"
            ></i>
          </el-tooltip>
        </span>
        <span v-auth="'convert'">
          <!-- 超级管理员用户不可部门转移 -->
          <el-tooltip content="部门转换" placement="top" v-if="!isAdmin">
            <i class="iconfont2 opera-icon icon-zhuanhuan" @click="openDrawer(DepartDialogType.transfer, scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'resetpwd'">
          <el-tooltip content="登录密码重置" placement="top" v-if="userInfo?.id !== scope.row.id">
            <i class="iconfont2 opera-icon icon-mimazhongzhi" @click="resetPassword(scope.row)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i class="iconfont2 opera-icon icon-a-lineicon_edit" @click="openDrawer(editType.edit, scope.row)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>

    <add-edit-user-dialog
      v-model:visible="labelDialogVisible"
      :type="labelDialogParams.type"
      :params="labelDialogParams.params"
      @cancel="onCancel"
      @confirm="onConfirm"
    ></add-edit-user-dialog>

    <el-dialog v-model="dialogVisible" title="" width="400" center>
      <el-result icon="success" title="登录密码重置成功" :style="{ paddingTop: 0, paddingBottom: 0 }">
        <template #extra>
          重置后密码为：{{ resetPassWordValue }}
          <el-text v-copy="resetPassWordValue" :style="{ cursor: 'pointer', color: 'red' }">
            <el-icon><DocumentCopy color="var(--el-color-primary)" /></el-icon>
          </el-text>
        </template>
      </el-result>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, watch, onMounted, computed } from "vue";
import { useHandleData } from "@/hooks/useHandleData";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Delete, Plus, Download } from "@element-plus/icons-vue";
import { editType } from "@/enums/dialogTypeEnum";
import { deleteUser as deleteTypeApi, statusUser } from "@/api/modules/system";
import { getOperateUserList, resetPassWord, getRoleList, exportOperateUser } from "@/api/modules/system";
import addEditUserDialog from "./addOrEditUserDialog.vue";
import { ElMessageBox } from "element-plus";
import { ResPage } from "@/api/interface/index";
import { useDownload } from "@/hooks/useDownload";
import { useUserStore } from "@/stores/modules/user";
import CustomTag from "@/components/CustomTag/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

const isAdmin = computed(() => userInfo.value?.account === "admin");

enum DepartDialogType {
  transfer = 4
}

interface typeDialog {
  type: number;
  params: Object;
}

const proTable = ref<ProTableInstance>();
const props = defineProps({
  parentItem: {
    type: Object,
    default: () => {}
  }
});
watch(
  () => props.parentItem,
  () => {
    initParam.orgId = props.parentItem.id;
  }
);

const dialogVisible = ref(false);
const resetPassWordValue = ref("");

const roleDict = ref<any>([]);

const getRoleDicts = (params: any = { current: 1, size: 10000 }) => {
  let newParams = JSON.parse(JSON.stringify(params));
  getRoleList(newParams).then(res => {
    if (res.code === 200) {
      (res.data as ResPage<any>).records?.forEach(item => {
        roleDict.value.push({ label: item.roleName, value: item.id });
      });
    }
  });
};

onMounted(() => {
  // 先获取字典
  getRoleDicts();
});

const onCancel = () => {};
const onConfirm = () => {
  proTable.value?.getTableList();
};

const dataCallback = (data: any) => {
  return data;
  // return data.record;
};

const labelDialogVisible = ref(false);
const labelDialogParams = ref<typeDialog>({
  params: {},
  type: editType.add
});
const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  // props.parentItem.orgId ? (newParams.orgId = props.parentItem.id) : delete newParams.parentId;
  return getOperateUserList(newParams);
};
const initParam = reactive({
  orgId: props.parentItem.id, // orgId 取组织的id
  containSubOrg: "1"
});

// 表格配置项
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "containSubOrg",
    label: "查询类型",
    search: {
      render: () => {
        return (
          <el-checkbox v-model={initParam.containSubOrg} true-label="1" false-label="0">
            包含子部门
          </el-checkbox>
        );
      }
    },
    isShow: false
  },
  {
    prop: "account",
    label: "用户账号",
    search: { el: "input" },
    render: scope => {
      return (
        <el-link type="primary" underline={false} style="cursor: auto;font-weight:normal">
          {scope.row.account}
        </el-link>
      );
    },
    width: 100
  },
  { prop: "userName", label: "用户姓名", search: { el: "input" }, width: 100 },
  { prop: "orgName", label: "所属部门", width: 100 },
  {
    prop: "userType",
    label: "账号类型",
    search: { el: "select" },
    width: 100,
    enum: [
      {
        label: "内部用户",
        value: "1"
      },
      {
        label: "合作商用户",
        value: "2"
      }
    ]
  },
  {
    prop: "validType",
    label: "账号有效期",
    search: { el: "select" },
    width: 100,
    enum: [
      {
        label: "长期",
        value: 1
      },
      {
        label: "短期",
        value: 0
      }
    ]
  },
  {
    prop: "validStatus",
    label: "账号有效状态",
    search: { el: "select" },
    width: 120,
    render: scope => {
      return (
        <CustomTag
          type={scope.row.validStatus === 1 ? "success" : "error"}
          status={String(scope.row.validStatus)}
          label={scope.row.validStatus === 1 ? "生效中" : "已失效"}
        ></CustomTag>
      );
    },
    enum: [
      {
        label: "生效中",
        value: 1
      },
      {
        label: "已失效",
        value: 0
      }
    ]
  },
  {
    prop: "roles",
    label: "角色",
    search: {
      el: "select",
      key: "roles",
      props: {
        multiple: true,
        "collapse-tags": true,
        placeholder: "请选择（可多选）"
      }
    },
    width: 100,
    enum: roleDict,
    isShow: false
    // fieldNames: {
    //   label: "roleName",
    //   value: "id"
    // }
  },
  {
    prop: "roleNames",
    label: "角色"
  },
  {
    prop: "status",
    label: "状态",
    search: { el: "select" },
    width: 100,
    render: scope => {
      return (
        <CustomTag
          type={scope.row.status === 1 ? "success" : "error"}
          status={String(scope.row.status)}
          label={scope.row.status === 1 ? "正常" : "停用"}
        ></CustomTag>
      );
    },
    enum: [
      {
        label: "正常",
        value: 1
      },
      {
        label: "停用",
        value: 0
      }
    ]
  },
  {
    prop: "silentState",
    label: "沉默用户",
    search: { el: "select" },
    width: 100,
    enum: [
      {
        label: "沉默用户",
        value: 1
      },
      {
        label: "非沉默用户",
        value: 0
      }
    ]
  },
  { prop: "operation", label: "操作", fixed: "right", width: 170 }
];

// 批量删除
const batchDelete = async (id: string[]) => {
  await useHandleData(deleteTypeApi, { ids: id }, { msg: "是否确认删除该记录，删除后不可恢复。", successMsg: "删除" });
  proTable.value?.clearSelection();
  proTable.value?.getTableList();
};
// 导出列表
const downloadFile = async () => {
  const params = {
    ...initParam,
    ...proTable.value?.searchParam,
    current: proTable.value?.pageable.current,
    size: proTable.value?.pageable.size
  };
  let newParams = JSON.parse(JSON.stringify(params));
  ElMessageBox.confirm("确认导出账号列表?", "提示", { type: "warning" }).then(() => useDownload(exportOperateUser, newParams));
};

const resetPassword = async (row: any) => {
  ElMessageBox.confirm(`请确认是否要重置 ${row.userName}（${row.account}）账号的登录密码？`, "温馨提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    draggable: true
  }).then(async () => {
    const res = await resetPassWord({ id: row.id });
    if (!(res?.code === 200)) return;
    resetPassWordValue.value = res?.data as string;
    dialogVisible.value = true;

    // ElMessageBox.confirm(`重置后密码为：${res?.data}`, "登录密码重置成功", {
    //   type: "success",
    //   draggable: true,
    //   showCancelButton: false,
    //   showConfirmButton: false,
    //   center: true
    // });
  });
  proTable.value?.getTableList();
};

const openDrawer = (operateType: editType | DepartDialogType, row: any) => {
  //  新增-所属部门---部门树选择项
  //  编辑-所属部门---表格当前列

  const dialogParams = {
    type: operateType,
    params: {
      // name: row?.name,
      // description: row?.description,
      // id: row?.id,
      // parentId: props.parentItem.id,
      // orgName: props.parentItem.orgName,
      // parentList: ["全部部门"]
      ...row
    }
  };
  if (!row) {
    dialogParams.params = {
      orgId: props.parentItem.id,
      orgName: props.parentItem.orgName
    };
  }
  // props.parentItem.id ? dialogParams.params.parentList.push(props.parentItem.name) : "";
  labelDialogVisible.value = true;
  labelDialogParams.value = dialogParams;
};

const INEFFECT = 1; // 生效中
// 停用/启用
const changeStatus = async (data: any) => {
  const isEnable = data.validStatus === INEFFECT;
  if (!isEnable) {
    ElMessageBox.alert("账号已失效不可启用，请检查", "提示", { type: "warning" });
    return;
  }
  const toStop = data.status; // 正常--->去停用
  await useHandleData(statusUser, { id: data?.id, status: toStop ? 0 : 1 }, toStop ? "停用" : "启用");
  proTable.value?.getTableList();
};
</script>
