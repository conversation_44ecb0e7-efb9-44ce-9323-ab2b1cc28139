<template>
  <el-form ref="oneFormRef" :model="oneForm" :rules="rules" label-width="120px" label-position="left">
    <el-form-item label="产品名称" prop="productName">
      <el-input v-model="oneForm.productName" :disabled="viewStatus" @blur="checkName" maxlength="50" clearable show-word-limit />
    </el-form-item>
    <el-form-item label="产品分类" prop="classify">
      <el-cascader
        v-model="oneForm.classify"
        placeholder="请选择产品分类"
        :disabled="viewStatus"
        :options="typeOptions"
        filterable
        clearable
        style="width: 380px; margin-right: 20px"
      ></el-cascader>
      <p class="tips">产品需配置在产品二级分类下</p>
    </el-form-item>
    <el-form-item label="订购/咨询入口" prop="entrance">
      <el-checkbox-group v-model="oneForm.entrance" :disabled="viewStatus">
        <el-checkbox label="showBuy">立即购买</el-checkbox>
        <el-checkbox label="showConsult">咨询详情</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="合作类型" prop="cooperateType">
      <el-radio-group v-model="oneForm.cooperateType" :disabled="viewStatus">
        <el-radio v-for="(item, index) in cooperateDic" :key="index + 'cooperationType'" :label="item.itemKey">
          {{ item.itemValue }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item
      v-if="oneForm.cooperateType !== 'Z' && oneForm.entrance.includes('showBuy')"
      label="合作商"
      prop="providerId"
      :rules="[{ required: true, trigger: 'change', message: '请选择产品合作商' }]"
    >
      <el-input disabled style="width: 300px; margin-right: 20px" v-model="oneForm.provider" placeholder="请选择合作商">
      </el-input>
      <el-button type="primary" :disabled="viewStatus" @click="selectPartner">选择合作商</el-button>
      <!-- <el-select
        ref="providerRef"
        v-model="oneForm.providerId"
        :disabled="viewStatus"
        placeholder="请选择产品服务商"
        style="width: 300px"
        clearable
      >
        <el-option v-for="(item, index) in providerList" :key="index + 'providerList'" :label="item.name" :value="item.id" />
      </el-select> -->
    </el-form-item>
    <el-form-item
      v-if="oneForm.cooperateType !== 'Z' && oneForm.entrance.includes('showBuy')"
      label="产品分成比例"
      prop="unicomDividendProportion"
      :rules="[{ required: true, trigger: 'blur', validator: validateNumber }]"
    >
      平台 : 合作商 =
      <el-input
        :disabled="viewStatus"
        placeholder="平台比例"
        style="width: 150px; margin: 0 10px"
        v-model="oneForm.unicomDividendProportion"
      >
        <template #append>%</template>
      </el-input>
      :
      <el-input
        :disabled="viewStatus"
        placeholder="合作商比例"
        style="width: 150px; margin: 0 10px"
        v-model="oneForm.partnerDividendProportion"
      >
        <template #append>%</template>
      </el-input>
    </el-form-item>
    <el-form-item
      v-if="oneForm.cooperateType !== 'Z' && oneForm.entrance.includes('showBuy')"
      label="分成合同依据"
      prop="contractUrl"
      :rules="[{ required: true, trigger: 'change', message: '请选择分成合同依据' }]"
    >
      <div class="file-list">
        <el-button type="primary" :disabled="oneForm.provider === '' || viewStatus" @click="handleContract">选择</el-button>
        <div class="link-list" v-if="contractFileList.length > 0">
          <div class="link-list--item" v-for="(item, index) in contractFileList" :key="item.link">
            <el-link :underline="false" :href="item.link">{{ item.originalName }} </el-link>
            <el-button :disabled="viewStatus" type="danger" size="small" style="margin-left: 10px" @click="handleDelFile(index)">
              删除
            </el-button>
          </div>
        </div>
        <!-- <el-button type="danger" v-if="contractFileList.length > 0">删除</el-button> -->
      </div>
    </el-form-item>

    <el-form-item label="销售对象" prop="targetCustomer">
      <!-- <el-checkbox-group v-model="oneForm.contactor" :disabled="viewStatus">
        <el-checkbox v-for="(item, index) in contactorList as any[]" :key="index + 'contactor'" :label="item.id">
          {{ item.name }}
        </el-checkbox>
      </el-checkbox-group> -->
      <el-radio-group v-model="oneForm.targetCustomer" :disabled="viewStatus">
        <el-radio v-for="item in proCustomerType" :key="item.itemKey" :label="item.itemKey">{{ item?.itemValue }}</el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="发布触点" prop="contactor">
      <el-checkbox-group v-model="oneForm.contactor" :disabled="viewStatus">
        <el-checkbox v-for="(item, index) in contactorList as any[]" :key="index + 'contactor'" :label="item.id">
          {{ item.name }}
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>

    <el-form-item label="支付方式" prop="pay">
      <el-checkbox-group v-model="oneForm.pay" :disabled="viewStatus">
        <el-checkbox v-for="(item, index) in payDic as any[]" :key="index + 'payDic'" :label="item.itemKey">
          {{ item.itemValue }}
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="交付方式">
      <el-select v-model="oneForm.delivery" placeholder="请选择" :disabled="viewStatus" style="width: 300px" clearable>
        <el-option
          v-for="(item, index) in deliveryDic"
          :key="index + 'deliveryDic'"
          :label="item.itemValue"
          :value="item.itemKey"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="产品标签">
      <div :class="['label-select', { 'forbid-click': viewStatus }]" @click="showLabelDawer">
        <el-icon class="arrow-icon"><arrow-right /></el-icon>
        <div v-if="oneForm.choiceLabelList.length > 0">
          <span v-for="(item, index) in oneForm.choiceLabelList as any[]" :key="index + 'label'" class="label-box">
            {{ item.name }}
            <el-icon class="close-icon" @click.stop="removeLabel(index)"> <circle-close-filled /></el-icon>
          </span>
        </div>
      </div>
    </el-form-item>
    <el-form-item label="产品开通方式" prop="productOpenMethod">
      <el-radio-group v-model="oneForm.productOpenMethod" :disabled="viewStatus">
        <el-radio v-for="(item, index) in productOpenMethodDict as any[]" :key="index + 'jumpType'" :label="item.itemKey">
          {{ item.itemValue }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="产品使用类型" prop="jumpType">
      <el-radio-group v-model="oneForm.jumpType" :disabled="viewStatus">
        <el-radio v-for="(item, index) in jumpTypeDic as any[]" :key="index + 'jumpType'" :label="item.itemKey">
          {{ item.itemValue }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="oneForm.jumpType === 'showInfo' ? '使用说明指引' : '跳转链接'" prop="jumpInfo" v-if="oneForm.jumpType">
      <el-input
        v-model="oneForm.jumpInfo"
        placeholder="输入内容"
        :disabled="viewStatus"
        :type="oneForm.jumpType === 'showInfo' ? 'textarea' : 'text'"
        :maxlength="oneForm.jumpType === 'showInfo' ? 500 : 150"
        clearable
        show-word-limit
      />
      <p class="tips" v-if="oneForm.jumpType === 'outLink' || oneForm.jumpType === 'innerLink'">
        手动填写跳转链接时请添加协议头，建议添加如：https或其他已定制的协议头，若添加http协议头有一定链接被篡改风险
      </p>
    </el-form-item>
    <el-form-item label="下载信息" v-if="oneForm.jumpType === 'showInfo'">
      <download-info
        ref="downloadInfoRef"
        prefixion="下载文件"
        :view-control="viewStatus"
        :limit-num="5"
        v-model:dataList="oneForm.attachmentList"
        @delete-item="deleteAttachment"
      >
        <template #nameTips>
          {{ oneForm.attachmentList.length > 0 ? `(${oneForm.attachmentList.length}/5)` : "最多可添加5个" }}
        </template>
      </download-info>
    </el-form-item>
    <el-form-item label="免费权益">
      <el-select multiple v-model="oneForm.rights" placeholder="请选择" :disabled="viewStatus" style="width: 300px" clearable>
        <el-option v-for="(item, index) in pmsRightsList" :key="+index" :label="item.name" :value="item.id" />
      </el-select>
    </el-form-item>

    <el-form-item label="订单免审" prop="orderAudit">
      <el-radio-group v-model="oneForm.orderAudit" :disabled="viewStatus">
        <el-radio :label="true">是</el-radio>
        <el-radio :label="false">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="产品类型" prop="productType">
      <el-radio-group v-model="oneForm.productType" :disabled="viewStatus">
        <el-radio :label="item.itemKey" v-for="item in pms_product_type" :key="item.itemKey">
          {{ item.itemValue }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
  </el-form>
  <div class="step-btn" v-if="isShowOperateBtn">
    <el-button plain @click="backto"> 返回 </el-button>
    <el-button type="primary" @click="submitForm(oneFormRef, false)"> 下一步 </el-button>
  </div>

  <label-drawer ref="labelRef" @selected-list-ids="getSelectedListIds" @selected-list="getSelectedList"></label-drawer>
  <Modal ref="modalRef" @on-select="handleSelect"></Modal>
</template>

<script setup lang="ts" name="stepOne">
import { reactive, ref, nextTick, onMounted, computed } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { getChannelList, judgeName } from "@/api/modules/product";
import labelDrawer from "./labelDrawer.vue";
import { getTypeTree } from "@/api/modules/productClassify";
import { useProductStore } from "@/stores/modules/product";
import { useDicStore } from "@/stores/modules/dictionaty";
import mittBus from "@/utils/mittBus";
import { productPmsRightsList } from "@/api/modules/product";
import type { Product } from "@/api/interface/product";
import Modal, { ActionType, ModalProps } from "./modal.vue";
import { useDict } from "@/hooks/useDict";
import downloadInfo from "./downloadInfo.vue";

const { pms_product_type } = useDict("pms_product_type");

const validateNumber = (rule: any, value: any, callback: any) => {
  const reg = /^\+?[0-9]*$/;
  const value1 = oneForm.unicomDividendProportion;
  const value2 = oneForm.partnerDividendProportion;
  if (reg.test(value1) && reg.test(value2)) {
    if (Number(value1) + Number(value2) === 100) {
      callback();
    } else {
      callback(new Error("产品分成比例加起来必须等于100%"));
    }
  } else {
    callback(new Error("只能输入大于等于0的整数"));
  }
};
const props = defineProps({
  passdata: {
    type: Object,
    default: () => {}
  },
  pageType: {
    type: String,
    default: ""
  },
  viewStatus: {
    type: Boolean,
    default: false
  },
  isShowOperateBtn: {
    type: Boolean,
    default: () => true
  },
  isAudit: {
    type: Boolean,
    default: () => false // 产品审核页面id是通过query传递的
  }
});
let emits = defineEmits(["getStepValue"]);
const productStore = useProductStore();
const dictionaryStore = useDicStore();
const router = useRouter();
const route = useRoute();
const id = props.isAudit ? (route.query.id as string) : (route.params.id as string);
const productId = props.pageType !== "add" ? id : "add_product";

const labelRef = ref();
const oneFormRef = ref<FormInstance>();
const providerRef = ref();
const downloadInfoRef = ref();
const oneForm = reactive({
  productName: "",
  classify: [],
  cooperateType: "",
  providerId: "",
  provider: "",
  contactor: [],
  entrance: [],
  pay: [],
  delivery: "",
  choiceLabelList: [],
  orderAudit: "",
  productType: "1", // 初始值为主产品
  rights: [],
  jumpType: "",
  jumpInfo: "",
  productOpenMethod: "",
  contractUrl: "",
  unicomDividendProportion: "",
  partnerDividendProportion: "",
  /* 销售类型 */
  targetCustomer: "",
  attachmentList: []
});
let choiceLabelListIds = ref([]); // 选择的产品标签ids数组
/* 产品权益列表 */
const pmsRightsList = ref<Product.pmsRightsListItem[]>([]);
const repetJudgeFlag = ref(true);
const contractFileList = ref<{ link: string; originalName: string }[]>([]);
const rules = reactive<FormRules>({
  productName: [{ required: true, message: "请输入产品名称", trigger: "blur" }],
  classify: [{ required: true, message: "请选择产品分类", trigger: "change" }],
  cooperateType: [{ required: true, message: "请选择合作类型", trigger: "change" }],
  // providerId: [{ required: true, message: "请选择产品服务商", trigger: "change" }],
  contactor: [{ required: true, message: "请至少选择一个发布触点", trigger: "change" }],
  entrance: [{ required: true, message: "请至少选择一个订购/咨询入口", trigger: "change" }],
  pay: [{ required: true, message: "请至少选择一个支付方式", trigger: "change" }],
  orderAudit: [{ required: true, message: "请选择是否订单免审", trigger: "change" }],
  productType: [{ required: true, message: "请选择产品类型", trigger: "change" }],
  jumpType: [{ required: true, message: "请选择产品使用类型", trigger: "change" }],
  jumpInfo: [{ required: true, message: "请输入", trigger: "blur" }],
  productOpenMethod: [{ required: true, message: "请选择产品开通方式", trigger: "blur" }],
  targetCustomer: [{ required: true, message: "请选择销售对象", trigger: "blur" }]
});

const proCustomerType = computed(() => dictionaryStore.productCustomerType);
const cooperateDic = computed(() => dictionaryStore.cooperateDic);
const payDic = computed(() => dictionaryStore.orderPayWayDic);
const deliveryDic = computed(() => dictionaryStore.deliveryDic);
const jumpTypeDic = computed(() => dictionaryStore.jumpTypeDic);
const productOpenMethodDict = computed(() => dictionaryStore.productOpenMethodDic);
const providerList = computed(() => productStore.providerList); // 产品服务商列表

const typeOptions = ref([]); // 产品分类
const contactorList = ref([]); // 渠道列表（发布触点用）

const modalRef = ref<ModalProps>();

onMounted(() => {
  // 获取渠道列表-渲染发布触点的单选框
  getChannelList({ needPublish: true }).then((res: any) => {
    contactorList.value = res.data;
  });
  // 产品分类下拉列表
  getTypeTreeOption();
  // 编辑和查看的回显：
  if (props.pageType !== "add") {
    nextTick(() => {
      let rightsId = [];
      if (Reflect.has(props.passdata, "rights") && props.passdata.rights.length > 0) {
        rightsId = props.passdata.rights.map((item: { rightsId: string }) => item.rightsId);
      }
      contractFileList.value =
        props.passdata.contractUrl === "" || props.passdata.contractUrl === null ? [] : JSON.parse(props.passdata.contractUrl);
      Object.assign(oneForm, {
        productName: props.passdata.productName,
        classify: [props.passdata.categoryIdL1, props.passdata.categoryId], // 产品分类
        cooperateType: props.passdata.cooperateType, // 合作类型
        providerId: props.passdata.providerId, // 产品服务商id
        provider: props.passdata.provider,
        contactor: props.passdata.publishChannel.map((data: any = {}) => data.id), // 接口返回的publishChannel:[{name:xx, id:xx}]
        pay: props.passdata.payType.split(","), // 支付方式
        delivery: props.passdata.deliveryMethod, // 交付方式
        choiceLabelList: props.passdata.tagList, // 产品标签
        orderAudit: props.passdata.orderNoVerify, // 订单免审
        productType: props.passdata?.productType || "1", // 产品类型
        rights: rightsId, // 权益
        jumpType: props.passdata.jumpType || "", // 产品使用类型
        jumpInfo: props.passdata.jumpInfo || "",
        productOpenMethod: props.passdata.productOpenMethod || "", //产品打开方式
        targetCustomer: props.passdata.targetCustomer,
        unicomDividendProportion: props.passdata.unicomDividendProportion,
        partnerDividendProportion: props.passdata.partnerDividendProportion,
        contractUrl: props.passdata.contractUrl,
        attachmentList: props.passdata.attachmentList
      });
      // 处理产品标签ids数组
      choiceLabelListIds.value = props.passdata.tagList.map((data: any) => data.id);
      // 处理订购/咨询入口
      if (props.passdata.showBuy) {
        oneForm.entrance.push("showBuy");
      }
      if (props.passdata.showConsult) {
        oneForm.entrance.push("showConsult");
      }
    });
  }
  /* 产品权益列表 */
  productPmsRightsList().then((res: any) => {
    pmsRightsList.value = res?.data;
  });
});
const submitForm = async (formEl: FormInstance | undefined, jump: boolean) => {
  if (!formEl) return;
  if (!repetJudgeFlag.value) {
    ElMessageBox.alert("产品名称重复，请重新命名", "提示", { type: "error" }).then(() => {});
    return;
  }
  let downloadPromise = downloadInfoRef.value?.ruleValidatePromise();
  let flag = true;
  if (downloadPromise) {
    flag = false;
    await Promise.all([...downloadPromise]).then((res: any) => {
      flag = true;
    });
    if (!flag) return;
  }
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      // 立即购买才入参合作商/产品分成比例/分成合同依据
      const showBuyParams =
        oneForm.entrance.includes("showBuy") && oneForm.cooperateType !== "Z"
          ? {
              providerId: oneForm.providerId, // 产品服务商id
              provider: oneForm.provider,
              // 产品分成比例
              unicomDividendProportion: oneForm.unicomDividendProportion,
              partnerDividendProportion: oneForm.partnerDividendProportion,
              contractUrl: oneForm.contractUrl
            }
          : {
              providerId: "",
              provider: "",
              unicomDividendProportion: "",
              partnerDividendProportion: "",
              contractUrl: ""
            };
      const attachmentArray = oneForm.jumpType === "showInfo" ? oneForm.attachmentList : [];
      // 处理第一步的oneForm传值
      let newParams = {
        productName: oneForm.productName,
        categoryIdL1: oneForm.classify[0], // 产品分类第一级
        categoryId: oneForm.classify[1], // 产品分类第二级
        cooperateType: oneForm.cooperateType, // 合作类型
        // provider: providerRef.value.selectedLabel, // 产品服务商name
        publishChannel: oneForm.contactor, // 发布触点--ids数组
        showBuy: oneForm.entrance.includes("showBuy"), // 立即购买
        showConsult: oneForm.entrance.includes("showConsult"), // 咨询详情
        payType: oneForm.pay.join(","), // 支付方式
        deliveryMethod: oneForm.delivery, // 交付方式
        tagList: choiceLabelListIds, // 产品标签--详情和传值是不一样的（详情返回了name，编辑只需要ids数组）,注意区分
        orderNoVerify: oneForm.orderAudit, // 订单免审
        productType: oneForm.productType, // 产品类型
        rights: oneForm.rights, // 权益（可以多选 ）
        jumpType: oneForm.jumpType, // 产品使用类型
        jumpInfo: oneForm.jumpInfo,
        targetCustomer: oneForm.targetCustomer,
        productOpenMethod: oneForm.productOpenMethod, //产品打开方式
        attachmentList: attachmentArray, // 下载信息
        ...showBuyParams
      };
      productStore.setProductDetail(productId, newParams);
      emits("getStepValue", 2);
      if (jump) {
        // 从第一步跳转到第三步，需要第二步验证通过
        mittBus.emit("handleStepThree");
      }
    } else {
      let obj = Object.keys(fields);
      formEl.scrollToField(obj[0]);
      console.log("error submit!", fields);
      return;
    }
  });
};
mittBus.on("handleStepTwo", () => {
  submitForm(oneFormRef.value, false);
});
mittBus.on("oneJumpToThree", () => {
  submitForm(oneFormRef.value, true);
});
// 重置表单
// const resetForm = (formEl: FormInstance | undefined) => {
//   if (!formEl) return;
//   formEl.resetFields();
// };

const getTypeTreeOption = () => {
  getTypeTree().then((res: any) => {
    typeOptions.value = res.data;
    typeOptions.value.forEach((item: any = {}) => {
      item.value = item.id;
      item.label = item.name;
      if (item.children.length > 0) {
        item.children.forEach((child: any = {}) => {
          child.value = child.id;
          child.label = child.name;
        });
      } else if (!item.children || item.children.length === 0) {
        item.disabled = true;
      }
    });
  });
};
const backto = () => {
  router.push("/productManage/list");
};

// 校验产品名称是否重复
const checkName = () => {
  let param = { productName: oneForm.productName };
  if (props.pageType === "edit") {
    param.id = id;
  }

  // productName为空不调用接口校验
  if (!oneForm.productName.trim()) {
    return;
  }

  judgeName(param).then((res: any) => {
    if (!res.data?.canUse) {
      repetJudgeFlag.value = false;
      ElMessageBox.alert("产品名称重复，请重新命名", "提示", { type: "error" }).then(() => {});
    } else {
      repetJudgeFlag.value = true;
      // ElMessageBox.alert("产品名称查重校验通过", "提示", { type: "success" }).then(() => {});
    }
  });
};
const showLabelDawer = () => {
  if (props.viewStatus) return;
  labelRef.value?.showLabel();
};
const getSelectedListIds = (data: any) => {
  choiceLabelListIds.value = data;
};
const getSelectedList = (data: any) => {
  oneForm.choiceLabelList = data;
};

const removeLabel = (index: number) => {
  if (props.viewStatus) return;
  oneForm.choiceLabelList.splice(index, 1);
  choiceLabelListIds.value.splice(index, 1);
};

/* 选择作合商 */
const selectPartner = () => {
  modalRef.value?.handleOpen("partner", oneForm);
};
const handleSelect = (actionType: ActionType, value: any) => {
  if (actionType === "partner") {
    // 选择合作商时，清空产品分成比例、分成合同依据。
    if (oneForm.providerId && value.id && value.id !== oneForm.providerId) {
      oneForm.unicomDividendProportion = "";
      oneForm.partnerDividendProportion = "";
      oneForm.contractUrl = "";
      contractFileList.value = [];
    }
    oneForm.provider = value.name;
    oneForm.providerId = value.id;
  } else {
    oneForm.contractUrl = value.agreementUrl;
    contractFileList.value = JSON.parse(value.agreementUrl);
  }
};

/* 合同依据 */
const handleContract = () => {
  modalRef.value?.handleOpen("contract", oneForm);
};

const handleDelFile = (idx: number) => {
  contractFileList.value.splice(idx, 1);
  // 合同依据删除后contractUrl变更
  oneForm.contractUrl = JSON.stringify(contractFileList.value);
};

const deleteAttachment = (index: number) => {
  oneForm.attachmentList.splice(index, 1);
};
</script>

<style scoped lang="scss">
.el-form {
  width: 90%;
  .text-center {
    text-align: center;
  }
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
.step-btn {
  margin-top: 30px;
  margin-bottom: 10px;
  text-align: left;
  :deep(.el-button) {
    height: 36px;
    margin-right: 16px;
  }
}
.label-select {
  position: relative;
  width: 450px;
  min-height: 34px;
  padding-right: 30px;
  padding-left: 10px;
  cursor: pointer;
  background: #ffffff;
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  .arrow-icon {
    position: absolute;
    top: 50%;
    right: 7px;
    transform: translateY(-50%);
  }
  .label-box {
    padding: 2px 4px 2px 8px;
    margin-right: 10px;
    background: #f4f7fb;
    border: 1px solid #e9edf5;
    border-radius: 4px;
    .close-icon {
      color: #aaaaaa;
      &:hover {
        color: #3a3a3d;
      }
    }
  }
}
.forbid-click {
  color: var(--el-disabled-text-color);
  cursor: not-allowed;
  background: var(--el-fill-color-light);
}
.tips {
  font-size: 14px;
  line-height: 16px;
  color: #73787f;
}
.file-list {
  display: flex;
  justify-content: flex-start;
}
.link-list {
  margin: 0 10px;
  a {
    display: block;
  }
  &--item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
}
</style>
