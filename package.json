{"name": "cnud-admin", "private": true, "version": "1.0.0", "type": "module", "description": "cnud-admin open source management system", "license": "MIT", "homepage": "http://gitlab.*************.nip.io/cnud-frontend/cnud-web-template", "repository": {"type": "git", "url": "http://gitlab.*************.nip.io/cnud-frontend/cnud-web-template.git"}, "bugs": {"url": "http://gitlab.*************.nip.io/cnud-frontend/cnud-web-template/issues"}, "scripts": {"dev": "vite", "serve": "vite", "dev:test": "vite --mode test", "build:dev": " vite build --mode development", "build:test": "vite build --mode test", "build:pro": "vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "npm run build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "prepare": "husky install", "release": "standard-version", "commit": "git add -A && czg && git push", "install:ci": "pnpm install --no-frozen-lockfile"}, "dependencies": {"@amcharts/amcharts4": "^4.10.20", "@element-plus/icons-vue": "^2.1.0", "@vueuse/core": "^10.3.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.4.0", "dayjs": "^1.11.9", "dompurify": "^3.1.6", "driver.js": "^0.9.7", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "2.3.4", "jszip": "^3.10.1", "lodash": "^4.17.21", "md5": "^2.3.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "print-js": "^1.6.0", "qrcode-parser": "^2.1.3", "qs": "^6.11.2", "screenfull": "^6.0.2", "sm-crypto": "^0.3.13", "sortablejs": "^1.15.0", "vue": "^3.3.4", "vue-i18n": "9.2.2", "vue-input-directive": "^1.1.5", "vue-qrcode": "^2.2.2", "vue-router": "^4.2.4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^17.6.7", "@types/jszip": "^3.4.0", "@types/md5": "^2.3.2", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@types/sm-crypto": "^0.3.3", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "autoprefixer": "^10.4.14", "cz-git": "^1.7.0", "czg": "^1.7.0", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.16.1", "husky": "^8.0.3", "lint-staged": "^13.2.3", "postcss": "^8.4.27", "postcss-html": "^1.5.0", "prettier": "^3.0.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.64.2", "standard-version": "^9.5.0", "stylelint": "^15.10.2", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^10.0.0", "typescript": "^5.1.6", "unplugin-vue-setup-extend-plus": "^1.0.0", "vite": "^4.4.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-pwa": "^0.16.4", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.8"}, "engines": {"node": ">=16.0.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}