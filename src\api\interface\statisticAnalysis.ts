export namespace Statistic {
  export interface OrderDateListParams {
    countEndDate?: string;
    countStartDate?: string; // 统计开始时间
    dateType?: string; // 日期类型：day-日报表，month-月报表
    orderAnalysisTypeKey?: string; // 订单分析指标类型枚举：1-订购订单数、2-退订订单数、3-净增订单数、4-累计生效订单数、5-交易金额GMV、6-累计交易金额GMV
    productId?: string; // 产品id
    productName?: string; // 产品名称
    providerId?: string; // 合作商id
    productIdList?: string[]; // 产品id list
    productIds?: string; // 产品id list
    categoryId?: string; // 产品分类id
  }
  export interface OrderDateItem {
    accumulateCount: number; // 累计生效订单数
    accumulateGmv: number; // 累计交易金额GMV
    accumulateGmvHB: string; // 累计交易金额GMV日/月环比
    accumulateGmvHBArrow: string; // 累计交易金额GMV日/月环比箭头: 1-向上 2-向下
    accumulateHB: string; // 累计生效数日/月环比
    accumulateHBArrow: string; // 累计生效数日/月环比箭头: 1-向上 2-向下
    countDate: string; // 统计时间
    gmv: number; //  交易金额GMV(元）
    gmvHB: string; // 交易金额GMV日/月环比
    gmvHBArrow: string; // 交易金额GMV日/月环比箭头: 1-向上 2-向下
    netGrowthCount: number; //  净增订单数 integer
    subscribeCount: number; //  订购订单数 integer
    unsubscribeCount: number; //  退订订单数
  }
  export interface PortalTrafficParams {
    endTime?: string; // 结束时间
    indexType?: string; // 指标维度
    indexUnit?: string; // 指标单位
    startTime?: string; // 开始时间
  }
  export interface BusinessParams {
    countEndDate?: string; // 统计结束时间，这个一定要给
    countStartDate?: string; // 统计开始时间,为了后续可能的可变日期区间做了兼容，该值就是新增的起始日期，日报或者月报就传当月第一天日期，月报就传当年1月的日期
    dateType?: string; // 日期类型：day-日报表，month-月报表
    accumulateOrIncrease?: string; // 累计或者新增说明:increase、新增；accumulate、累计，图表可不传，top要给
    adviceType?: number; // 商机分析指标类型枚举：1-产品商机、2-解决方案商机，不传就全类型查
    handleTime?: string; // 受理时间
    handler?: string; // 受理人
    handlerStatus?: string; // 受理状态 1-已受理 2-待受理
    isDel?: string; // is_del
    itemCategory?: string; // 产品或方案分类
    itemName?: string; // 产品或方案名称
    providerId?: string; // 合作商id
    providerName?: string; // 服务商名
  }
  export interface PortalParams {
    endTime?: string; // 	结束时间
    indexUnit: string; // 	指标单位, 枚举值: day month
    startTime?: string;
  }
  export interface SoluitonRepParams {
    accumulateCount?: number; // 累计数
    accumulateHB?: string; // 累计产品商机数日/月环比
    accumulateHBArrow?: string; // 累计数日/月环比箭头: 1:向上 2:向下 "":不变
    dateWithYear?: string; // 日期（年月日/年月），列表用
    dateWithoutYear: string; // 日期（月日/月），图用
    netGrowthCount?: number; // 新增数
  }

  export interface promotionH5Search {
    adProductType?: string;
    startDate?: string;
    endDate?: string;
    adProductTypes?: any;
  }
  export interface promotionH5Item {
    adProductType?: string;
    contactName: string;
    createTime: string;
    creatorId: null;
    creatorName: null;
    demand: string;
    id: string;
    industry: string;
    phone: string;
    source: string;
    updateTime: string;
  }
  export interface QueryOrdDemandParams {
    ascs?: string;
    current?: number;
    descs?: string;
    size?: number;
    adProductType?: string; // 推广产品类型
    cityCode?: string; // 城市编号
    cityName?: string; // 城市名称
    companyName?: string; // 企业名称
    contactName?: string; // 联系人
    demand?: string; // 使用需求
    endDate?: string; // 截止时间
    id?: string; // id
    industry?: string; // 行业领域
    phone?: string; // 手机号,不支持模糊匹配
    provinceCode?: string; // 省份编号
    provinceName?: string; // 省份名称
    source?: string; // 来源，字典【demand_source】
    startDate?: string; // 起始时间
    verifyCode?: string; // 验证码
  }
}
