<template>
  <div class="table-box">
    <el-tabs v-model="activeName" class="tab-box" @tab-click="handleTabClick">
      <el-tab-pane label="企业需求" :name="TabType.ENTERPRISE"></el-tab-pane>
      <el-tab-pane label="服务商方案" :name="TabType.SERVICE"> </el-tab-pane>
    </el-tabs>

    <div v-show="activeName === TabType.ENTERPRISE" class="list-box">
      <pro-table
        ref="enterpriseTableRef"
        title="企业需求"
        :request-auto="false"
        :columns="tableColumnsEnterprise"
        :request-api="getEnterpriseList"
        :tool-button="false"
      >
        <!-- 审核状态 -->
        <template #auditStatus="{ row }">
          <custom-tag
            :type="statusTypeMap[row.auditStatus]"
            :status="row.auditStatus"
            :label="useDictLabel(requireReleaseBusinessStatus, row.auditStatus) || '--'"
          ></custom-tag>
        </template>
        <template #solutionCount="scope">
          <span v-if="scope.row.solutionCount" class="response-plan" @click="toView(scope.row)">
            详情列表({{ scope.row.solutionCount }})
          </span>
          <span v-else>--</span>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span v-auth="'enterpriseView'" @click="openDetailDialog(scope.row)">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan"></i>
            </el-tooltip>
          </span>
          <span v-auth="'enterpriseClose'" @click="handleClose(scope.row)">
            <el-tooltip v-if="scope.row.auditStatus == AuditStatusEnum.PASS" content="关闭" placement="top">
              <i class="iconfont2 opera-icon icon-tingyong"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>
    <div v-show="activeName === TabType.SERVICE" class="list-box">
      <pro-table
        ref="serviceTable"
        title="服务商方案"
        :request-auto="false"
        :columns="tableColumnsService"
        :request-api="getServiceList"
        :tool-button="false"
      >
        <!-- 审核状态 -->
        <template #auditStatus="{ row }">
          <custom-tag
            :type="statusTypeMap[row.auditStatus]"
            :status="row.auditStatus"
            :label="useDictLabel(requireReleaseBusinessStatus, row.auditStatus) || '--'"
          ></custom-tag>
        </template>
        <!-- 采纳状态 -->
        <template #acceptStatus="{ row }">
          <custom-tag
            :type="acceptStatusTypeMap[row.acceptStatus]"
            :status="row.acceptStatus"
            :label="useDictLabel(demandAcceptStatus, row.acceptStatus) || '--'"
          ></custom-tag>
        </template>
        <!-- 表格操作 -->
        <template #operation="scope">
          <span v-auth="'serviceView'" @click="openDetailDialog(scope.row)">
            <el-tooltip content="查看" placement="top">
              <i class="iconfont2 opera-icon icon-chakan"></i>
            </el-tooltip>
          </span>
        </template>
      </pro-table>
    </div>
    <detail-dialog ref="detailDialogRef"></detail-dialog>
  </div>
</template>
<script setup lang="tsx" name="demandJbgsSupplyBusiness">
import { ref, onMounted, computed, onBeforeMount } from "vue";
import { useRouter } from "vue-router";
import type { TabsPaneContext } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import CustomTag from "@/components/CustomTag/index.vue";
import detailDialog from "./components/detailDialog.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useHandleData } from "@/hooks/useHandleData";
import { useDictLabel } from "@/hooks/useDict";
import { useDicStore } from "@/stores/modules/dictionaty";
import { TabType, acceptStatusTypeMap, AuditStatusEnum } from "./type";
import { getBusinessRequireList, getBusinessSchemeList, closeDemand } from "@/api/modules/requireReleaseJbgs";

const router = useRouter();
const dictionaryStore = useDicStore();
const { BUTTONS } = useAuthButtons();

// 审核状态
const requireReleaseBusinessStatus: any = computed(() => dictionaryStore.requireReleaseBusinessStatus);
// 采纳状态
const demandAcceptStatus: any = computed(() => dictionaryStore.demandAcceptStatus);
// 需求类型
const requireType: any = computed(() => dictionaryStore.jbgsRequireType);
// 实施地址
const demandConstructionAddress: any = computed(() => dictionaryStore.demandConstructionAddress);
const statusTypeMap: { [key: string]: string } = {
  wait: "yellow",
  pass: "blue",
  refuse: "red",
  close: "grey"
};

const enterpriseTableRef = ref<ProTableInstance>();
const tableColumnsEnterprise: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "name", label: "需求名称", minWidth: 90, search: { el: "input", key: "demandName", props: { maxlength: "20" } } },
  {
    prop: "demandType",
    label: "需求类型",
    minWidth: 90,
    enum: requireType,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireType.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "companyName", label: "需方企业名称", minWidth: 110 },
  { prop: "companyAddress", label: "需方企业地址", minWidth: 110 },
  { prop: "contactName", label: "需方联系人", minWidth: 100 },
  { prop: "phone", label: "需方联系电话", minWidth: 110 },
  {
    prop: "constructionAddressName",
    minWidth: 120,
    label: "需求地点"
  },
  // { prop: "demandEndTime", label: "响应截止时间", minWidth: 110 },
  // { prop: "constructionAddress", label: "实施地址", minWidth: 90, enum: demandConstructionAddress },
  {
    prop: "auditStatus",
    label: "审核结果",
    minWidth: 110,
    enum: requireReleaseBusinessStatus,
    search: {
      label: "审核状态",
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireReleaseBusinessStatus.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "createTime", label: "提交时间", minWidth: 110, sortable: true },
  { prop: "auditTime", label: "审核时间", minWidth: 110, sortable: true },
  { prop: "solutionCount", label: "应答方案", minWidth: 110 },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];
const getEnterpriseList = (params: any = {}) => {
  if (!BUTTONS.value?.enterpriseQuery) {
    return;
  }
  return getBusinessRequireList(params);
};
const toView = (row: any) => {
  router.push({
    path: "/business/demandJbgsSupplyBusiness/responsePlanList",
    query: {
      id: row.id,
      demandName: row.name
    }
  });
};
const handleClose = async (row: any) => {
  await useHandleData(closeDemand, { id: row.id }, `关闭<${row.name}>`);
  enterpriseTableRef.value?.getTableList();
};

const serviceTable = ref<ProTableInstance>();
const tableColumnsService: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "demandName", label: "需求名称", minWidth: 90, search: { el: "input", props: { maxlength: "20" } } },
  {
    prop: "demandType",
    label: "需求类型",
    minWidth: 90,
    enum: requireType,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireType.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "demandCompanyName", label: "需方企业名称", minWidth: 110 },
  { prop: "demandCompanyAddress", label: "需方企业地址", minWidth: 110 },
  { prop: "demandContactName", label: "需方联系人", minWidth: 100 },
  { prop: "demandPhone", label: "需方联系电话", minWidth: 110 },
  {
    prop: "constructionAddressName",
    minWidth: 120,
    label: "需求地点"
  },
  // { prop: "demandEndTime", label: "响应截止时间", minWidth: 110 },
  // { prop: "constructionAddress", label: "实施地址", minWidth: 90, enum: demandConstructionAddress },
  { prop: "solutionName", label: "方案名称", minWidth: 90, search: { el: "input", props: { maxlength: "20" } } },
  { prop: "solutionCompanyName", label: "供方企业名称", minWidth: 110 },
  { prop: "solutionCompanyAddress", label: "供方企业地址", minWidth: 110 },
  { prop: "solutionContactName", label: "供方联系人", minWidth: 100 },
  { prop: "solutionPhone", label: "供方联系电话", minWidth: 110 },
  {
    prop: "auditStatus",
    label: "审核结果",
    minWidth: 90,
    enum: requireReleaseBusinessStatus,
    search: {
      label: "审核状态",
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {requireReleaseBusinessStatus.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "acceptStatus", label: "采纳结果", minWidth: 90 },
  { prop: "createTime", label: "提交时间", minWidth: 110, sortable: true },
  { prop: "auditTime", label: "审核时间", minWidth: 110, sortable: true },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];
const getServiceList = (params: any = {}) => {
  if (!BUTTONS.value?.serviceQuery) {
    return;
  }
  return getBusinessSchemeList(params);
};

const activeName = ref(TabType.ENTERPRISE);
const handleTabClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name;
  if (tab.props.name === TabType.ENTERPRISE) {
    enterpriseTableRef.value?.getTableList();
  } else {
    serviceTable.value?.getTableList();
  }
};

const detailDialogRef = ref();
const openDetailDialog = (row: any) => {
  if (activeName.value === TabType.ENTERPRISE) {
    detailDialogRef.value?.handleOpen("需求信息", activeName.value, row);
  } else {
    detailDialogRef.value?.handleOpen("需求应答信息", activeName.value, row);
  }
};

onBeforeMount(() => {
  dictionaryStore.requireReleaseBusinessStatus.length === 0 && dictionaryStore.getRequireReleaseBusinessStatus();
  dictionaryStore.demandAcceptStatus.length === 0 && dictionaryStore.getDemandAcceptStatus();
  // dictionaryStore.requireType.length === 0 && dictionaryStore.getRequireType();
  dictionaryStore.jbgsRequireType.length === 0 && dictionaryStore.getJbgsRequireType();

  // dictionaryStore.demandConstructionAddress.length === 0 && dictionaryStore.getDemandConstructionAddress();
});
onMounted(() => {
  handleTabClick({ props: { name: TabType.ENTERPRISE } });
});
</script>
<style scoped lang="scss">
.tab-box {
  background-color: #ffffff;
}
.list-box {
  display: flex;
  flex-direction: column;
  height: calc(100% - 54px);
}
.response-plan {
  color: var(--el-color-primary);
  cursor: pointer;
}
::v-deep(.el-tabs__item) {
  width: 100px !important;
}
</style>
