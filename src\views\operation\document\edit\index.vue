<template>
  <el-dialog
    :model-value="true"
    :show-close="false"
    width="708"
    :title="isEdit ? '编辑文档' : '新增文档'"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="card content-box">
      <el-form ref="ruleFormRef" :model="ruleForm" label-position="left" :rules="rules" label-width="100px">
        <el-form-item label="文档名称" prop="docName" required>
          <el-input :max="20" v-model="ruleForm.docName" placeholder="输入内容" />
        </el-form-item>
        <el-form-item label="文件上传" prop="docUrl" required>
          <UploadFile
            v-model:file-url="ruleForm.docUrl"
            v-model:file-id="ruleForm.docId"
            :file-size="20"
            width="135px"
            height="135px"
          >
            <template #tip>支持格式：.rar .zip .doc .docx .pdf ，单个文件不能超过20MB</template>
          </UploadFile>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submitForm(ruleFormRef)"> 确定 </el-button>
      <el-button @click="goBack()"> 取消 </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="dynamicForm">
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import UploadFile from "@/components/Upload/File.vue";
import { documentStorage } from "@/utils/storage/obj";
import { change, add } from "@/api/modules/business/document";

const emit = defineEmits(["update:visible", "refresh"]);
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  }
});

const isEdit = ref<boolean>(false);

const getInitForm = () => {
  const item = documentStorage.get() as any;
  if (item) {
    isEdit.value = true;
    return {
      ...item
    };
  } else {
    return {
      docName: "",
      docUrl: "",
      docId: ""
    };
  }
};

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive(getInitForm());

const rules = reactive<FormRules>({
  docName: [
    { required: true, message: "请输入文档名称", trigger: "blur" },
    { max: 20, message: "最多输入20个字符", trigger: "blur" }
  ],
  docUrl: [{ required: true, message: "请上传文件", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      const data = {
        ...ruleForm
      };
      if (isEdit.value) {
        await change(data);
      } else {
        await add(data);
      }
      emit("refresh");
      goBack();
    }
  });
};

const goBack = () => {
  documentStorage.clear();
  emit("update:visible", false);
};
</script>

<style scoped lang="scss">
.content-box {
  align-items: start;
}
</style>
