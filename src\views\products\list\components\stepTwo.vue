<template>
  <el-form ref="twoFormRef" :model="twoForm" :rules="rules" label-width="120px" label-position="left">
    <el-form-item label="产品主图" prop="mainUrl">
      <UploadImg v-model:image-url="twoForm.mainUrl" width="135px" height="135px" :file-size="1" :disabled="viewStatus">
        <template #tip> 尺寸建议800*800像素，图片大小不能超过 1M，支持图片格式jpg/jpeg/png</template>
      </UploadImg>
    </el-form-item>
    <el-form-item label="产品icon" prop="iconUrl">
      <UploadImg v-model:image-url="twoForm.iconUrl" width="135px" height="135px" :file-size="0.3" :disabled="viewStatus">
        <template #tip> 尺寸建议128*128像素，图片大小不能超过 0.3M，支持图片格式jpg/jpeg/png</template>
      </UploadImg>
    </el-form-item>
    <el-form-item label="产品简介" prop="description">
      <el-input
        v-model="twoForm.description"
        :disabled="viewStatus"
        type="textarea"
        placeholder="请输入产品简介"
        maxlength="512"
        show-word-limit
      />
    </el-form-item>
    <el-form-item label="产品展示图">
      <UploadImgs
        v-model:file-list="twoForm.showPics"
        :limit="10"
        :file-size="1"
        :disabled="viewStatus"
        height="140px"
        width="140px"
      >
        <template #tip> 尺寸建议800*800像素，图片大小不能超过 1M，最多可上传10张图片 </template>
      </UploadImgs>
    </el-form-item>
    <el-form-item label="产品功能">
      <complex-content
        ref="featuresComplex"
        prefixion="功能"
        :file-size="0.3"
        img-tips="建议图片尺寸 176px x 176px，图片大小不超过0.3M，支持图片格式jpg/jpeg/png"
        :view-control="viewStatus"
        :limit-num="500"
        v-model:dataList="twoForm.productFunctionList"
        @add-item="addProductFeatures"
        @delete-item="deleteProductFeatures"
      >
        <template #nameTips>最多可添加8个</template>
      </complex-content>
    </el-form-item>
    <el-form-item label="产品优势">
      <complex-content
        ref="advantageRef"
        prefixion="优势"
        :file-size="0.3"
        img-tips="建议图片尺寸 160px x 200px，图片大小不超过0.3M，支持图片格式jpg/jpeg/png"
        :view-control="viewStatus"
        v-model:dataList="twoForm.productAdvantage"
        @add-item="addAdvantages"
        @delete-item="deleteAdvantages"
      >
        <template #nameTips>最多可添加8个</template>
      </complex-content>
    </el-form-item>
    <el-form-item label="产品参数">
      <WangEditor v-model:value="twoForm.productParams" height="200px" :disabled="viewStatus" />
    </el-form-item>
    <el-form-item label="产品详情">
      <WangEditor v-model:value="twoForm.details" height="200px" :disabled="viewStatus" />
    </el-form-item>
    <el-form-item label="应用场景">
      <complex-content
        ref="applyScenesRef"
        prefixion="场景"
        :file-size="1"
        img-tips="建议图片尺寸 1320px x 832px，图片大小不超过1M，支持图片格式jpg/jpeg/png"
        :view-control="viewStatus"
        :limit-num="500"
        v-model:dataList="twoForm.applyScenes"
        @add-item="addAppScenarios"
        @delete-item="deleteAppScenarios"
      >
        <template #nameTips>最多可添加8个</template>
      </complex-content>
    </el-form-item>
    <el-form-item label="客户案例">
      <complex-content
        ref="casesRef"
        prefixion="案例"
        :file-size="0.3"
        img-tips="建议图片尺寸 256px x 256px，图片大小不超过0.3M，支持图片格式jpg/jpeg/png"
        :view-control="viewStatus"
        v-model:dataList="twoForm.customerCases"
        @add-item="addCustomerCase"
        @delete-item="deleteCustomerCase"
      >
        <template #nameTips>最多可添加8个</template>
      </complex-content>
    </el-form-item>
    <el-form-item label="服务和支持">
      <el-input
        v-model="twoForm.custSupport"
        type="textarea"
        :disabled="viewStatus"
        placeholder="请输入"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>
  </el-form>
  <div class="step-btn" v-if="isShowOperateBtn">
    <el-button plain @click="backtoList"> 返回 </el-button>
    <el-button plain @click="backto"> 上一步 </el-button>
    <el-button type="primary" @click="submitForm(twoFormRef)"> 下一步 </el-button>
  </div>
</template>

<script setup lang="ts" name="stepTwo">
import { reactive, ref, onMounted, nextTick } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import UploadImg from "@/components/Upload/Img.vue";
import UploadImgs from "@/components/Upload/Imgs.vue";
import complexContent from "./complexContent.vue";
import WangEditor from "@/components/WangEditor/index.vue";
import { useProductStore } from "@/stores/modules/product";
import mittBus from "@/utils/mittBus";

const props = defineProps({
  passdata: {
    type: Object,
    default: () => {}
  },
  pageType: {
    type: String,
    default: ""
  },
  viewStatus: {
    type: Boolean,
    default: false
  },
  isShowOperateBtn: {
    type: Boolean,
    default: () => true
  },
  isAudit: {
    type: Boolean,
    default: () => false // 产品审核页面id是通过query传递的
  }
});
let emits = defineEmits(["getStepValue"]);
const productStore = useProductStore();
const router = useRouter();
const route = useRoute();
const id = props.isAudit ? (route.query.id as string) : (route.params.id as string);
const productId = props.pageType !== "add" ? id : "add_product";

const twoFormRef = ref<FormInstance>();
const featuresComplex = ref();
const advantageRef = ref();
const applyScenesRef = ref();
const casesRef = ref();
const twoForm = reactive({
  mainUrl: "",
  iconUrl: "",
  description: "",
  showPics: [],
  productFunctionList: [],
  productAdvantage: [],
  productParams: "",
  details: "",
  applyScenes: [],
  customerCases: [],
  custSupport: ""
});
// const complexValidate = (rule: any, value: any, callback: any) => {
//   if (twoForm.productFeatures.length < 1) {
//     callback(new Error("请添加产品功能"));
//   } else {
//     callback();
//   }
// };
const rules = reactive<FormRules>({
  mainUrl: [{ required: true, message: "请上传产品主图", trigger: "blur" }],
  iconUrl: [{ required: true, message: "请上传产品icon", trigger: "blur" }],
  description: [{ required: true, message: "请输入产品简介", trigger: "blur" }]
});

onMounted(() => {
  if (props.pageType !== "add") {
    // 等待各组件（例如富文本）先加载完，再赋值：
    nextTick(() => {
      // 编辑和查看回显处理：
      Object.assign(twoForm, {
        mainUrl: props.passdata.mainUrl || "",
        iconUrl: props.passdata.iconUrl || "",
        description: props.passdata.description || "",
        productFunctionList: props.passdata.productFunctionList,
        productAdvantage: props.passdata.productAdvantage,
        productParams: props.passdata.productParams || "",
        details: props.passdata.details || "",
        applyScenes: props.passdata.applyScenes,
        customerCases: props.passdata.customerCases,
        custSupport: props.passdata.custSupport || "",
        showPics: props.passdata.showPics
      });
      // 处理展示图（多张图片组件）
      twoForm.showPics.forEach((item: any = {}) => {
        item.url = item.picUrl;
        item.name = item.id;
      });
      console.log("twoForm", twoForm);
    });
  }
});
const addProductFeatures = () => {
  if (twoForm.productFunctionList.length >= 8) {
    ElMessage.error("最多添加八个产品功能");
    return;
  }
  twoForm.productFunctionList.push({ title: "", description: "", picUrl: "" });
};
const deleteProductFeatures = (index: number) => {
  // if (twoForm.productFunctionList.length < 3) {
  //   ElMessage.error("最少需添加二个产品功能");
  //   return;
  // }
  twoForm.productFunctionList.splice(index, 1);
  // twoFormRef.value?.acceptParams(twoForm.applicationScene.list);
};
const addAdvantages = () => {
  if (twoForm.productAdvantage.length >= 8) {
    ElMessage.error("最多添加八个产品优势");
    return;
  }
  twoForm.productAdvantage.push({ title: "", description: "", picUrl: "" });
};
const deleteAdvantages = (index: number) => {
  twoForm.productAdvantage.splice(index, 1);
};
const addAppScenarios = () => {
  if (twoForm.applyScenes.length >= 8) {
    ElMessage.error("最多添加八个应用场景");
    return;
  }
  twoForm.applyScenes.push({ title: "", description: "", picUrl: "" });
};
const deleteAppScenarios = (index: number) => {
  twoForm.applyScenes.splice(index, 1);
};
const addCustomerCase = () => {
  if (twoForm.customerCases.length >= 8) {
    ElMessage.error("最多添加八个客户案例");
    return;
  }
  twoForm.customerCases.push({ title: "", description: "", picUrl: "" });
};
const deleteCustomerCase = (index: number) => {
  twoForm.customerCases.splice(index, 1);
};
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid: any, fields: any) => {
    if (valid) {
      // 处理第二步的twoForm传值
      // 处理产品展示图多张
      const picsList = twoForm.showPics.map((item: any = {}) => {
        return {
          picUrl: item.url,
          title: item.name,
          description: item.name // 因为暂无description,所以沟通后决定先传name值
        };
      });
      let featurePromise = featuresComplex.value?.ruleValidatePromise();
      let advantagePromise = advantageRef.value?.ruleValidatePromise();
      let applyScenesPromise = applyScenesRef.value?.ruleValidatePromise();
      let casesPromise = casesRef.value?.ruleValidatePromise();
      Promise.all([...featurePromise, ...advantagePromise, ...applyScenesPromise, ...casesPromise])
        .then(result => {
          console.log(result);
          productStore.setProductDetail(productId, { ...twoForm, showPics: picsList });
          emits("getStepValue", 3);
        })
        .catch(error => {
          console.log(error);
        });
    } else {
      let obj = Object.keys(fields);
      formEl.scrollToField(obj[0]);
      console.log("error submit!", fields);
    }
  });
};
mittBus.on("handleStepThree", () => {
  submitForm(twoFormRef.value);
});

// const resetForm = (formEl: FormInstance | undefined) => {
//   if (!formEl) return;
//   formEl.resetFields();
// };
const backto = () => {
  emits("getStepValue", 1);
};
const backtoList = () => {
  router.push("/productManage/list");
};
</script>

<style scoped lang="scss">
.el-form {
  width: 90%;
  .text-center {
    text-align: center;
  }
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
.step-btn {
  margin-top: 30px;
  margin-bottom: 10px;
  text-align: left;
  :deep(.el-button) {
    height: 36px;
    margin-right: 16px;
  }
}
</style>
