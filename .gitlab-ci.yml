code-check:
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  tags:
   - code-check
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - "sonar-scanner
        -Dsonar.projectKey=cnud-portal-manage \
        -D sonar.qualitygate.wait=true \
        -Dsonar.host.url=${SONAR_HOST_URL} \
        -Dsonar.login=${SONAR_TOKEN} \
        -Dsonar.pullrequest.key=${CI_MERGE_REQUEST_IID} \
        -Dsonar.pullrequest.branch=${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME} \
        -Dsonar.pullrequest.base=${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}"
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^project\/.*$/'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release\/.*$/'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
    - if: '$CI_COMMIT_BRANCH =~ /^codecheck\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^project\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
    - if: '$CI_COMMIT_BRANCH == "master"'
