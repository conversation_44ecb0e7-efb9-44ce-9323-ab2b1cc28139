# 定义阶段
stages:
  - code-check
  - build
  - deploy

# 代码检查阶段
code-check:
  stage: code-check
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  tags:
    - code-check
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - "sonar-scanner
      -Dsonar.projectKey=cnud-portal-manage \
      -D sonar.qualitygate.wait=true \
      -Dsonar.host.url=${SONAR_HOST_URL} \
      -Dsonar.login=${SONAR_TOKEN} \
      -Dsonar.pullrequest.key=${CI_MERGE_REQUEST_IID} \
      -Dsonar.pullrequest.branch=${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME} \
      -Dsonar.pullrequest.base=${CI_MERGE_REQUEST_TARGET_BRANCH_NAME}"
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^project\/.*$/'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^release\/.*$/'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
    - if: '$CI_COMMIT_BRANCH =~ /^codecheck\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^project\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
    - if: '$CI_COMMIT_BRANCH == "master"'

# 前端构建阶段
build-frontend:
  stage: build
  image: node:18-alpine
  tags:
    - docker
  variables:
    NODE_OPTIONS: "--max-old-space-size=4096"
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - node_modules/
      - .pnpm-store/
  before_script:
    # 安装 pnpm
    - npm install -g pnpm
    # 设置 pnpm 存储路径
    - pnpm config set store-dir .pnpm-store
  script:
    # 使用 --no-frozen-lockfile 参数安装依赖
    - pnpm install --no-frozen-lockfile
    # 运行构建
    - pnpm run build:pro
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^project\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
    - if: '$CI_COMMIT_BRANCH == "master"'

# 部署阶段
deploy-frontend:
  stage: deploy
  image: alpine:latest
  tags:
    - docker
  dependencies:
    - build-frontend
  before_script:
    - apk add --no-cache rsync openssh-client
  script:
    # 这里添加你的部署脚本
    - echo "部署前端应用到服务器..."
    # 示例：使用 rsync 同步文件到服务器
    # - rsync -avz --delete dist/ user@server:/path/to/web/root/
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual # 手动触发部署
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
      when: manual
