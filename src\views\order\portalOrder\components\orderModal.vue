<!-- 
2023-3-22
选择产品页面，删除选择“支付方式”字段，用户提单后，订单编号生成，订单进入待支付/待审核状态
统一通过在前台的控制台的【支付】按钮进行选择支付方式
 -->

<template>
  <el-dialog
    :append-to-body="true"
    :destroy-on-close="true"
    class="custom-modal"
    v-model="dialogVisible"
    :width="activeStep === 1 ? 1282 : 800"
    :before-close="handleClose"
    title="选择产品"
  >
    <div class="table-box" v-show="activeStep === 1">
      <pro-table
        ref="proTable"
        title="产品列表"
        :columns="columns"
        :request-api="getTableList"
        :request-auto="false"
        :tool-button="false"
      ></pro-table>
    </div>
    <div v-show="activeStep === 2">
      <div class="sku-box">
        <div class="sku-box-body">
          <div class="sku-item title">{{ selectedProDetail?.productName }}</div>
          <div class="sku-item des">
            {{ selectedProDetail?.description }}
          </div>
          <div class="sku-item">
            <span class="info-label">总计</span>
            <span class="info-text price">¥&nbsp;{{ allPrice }}</span>
          </div>
          <div class="sku-item">
            <span class="info-label">产品服务商</span>
            <span class="info-text">{{ selectedProDetail?.provider }}</span>
          </div>
          <div class="sku-item">
            <span class="info-label">交付方式</span>
            <span class="info-text">{{ useDictLabel(pms_delivery_method, selectedProDetail.deliveryMethod) || "-" }}</span>
          </div>
          <div class="sku-item" v-if="selectedSkuConfig">
            <span class="info-label">规格配置</span>
            <span class="info-text">{{ selectedSkuConfig || "-" }}</span>
          </div>
          <div class="sku-item prod-sku">
            <span class="info-label">产品规格</span>
            <el-radio-group v-model="selectedSize" @change="selectSizeChange">
              <el-radio v-for="(item, index) in sizeList" :key="index + 'size'" :label="item.id" border>
                <div v-html="item.skuName"></div>
              </el-radio>
            </el-radio-group>
          </div>
          <div class="sku-item" v-if="hasQuantity">
            <span class="info-label">购买数量</span>
            <el-input-number
              v-model="quantityNum"
              :step="quantityStep"
              :min="minQuantity"
              :max="maxQuantity"
              step-strictly
            ></el-input-number>
            <span class="unit">{{ quantityFilter[skuUnit] }}</span>
          </div>
          <div class="sku-item" v-if="hasTime">
            <span class="info-label">购买时长</span>
            <el-input-number v-model="timeNum" :step="timeStep" :min="minTime" :max="maxTime" step-strictly></el-input-number>
            <span class="unit">{{ timeFilter[timeUnit] }}</span>
          </div>

          <div class="sku-item" v-if="hasTrial">
            <span class="info-label">购买方式</span>
            <span class="info-text type">&nbsp;试用{{ probationDate }}天</span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleStep">{{ activeStep === 1 ? "取消" : "上一步" }}</el-button>
        <el-button v-if="activeStep === 1" :disabled="!proTable?.selectedList.length > 0" @click="selectProduct" type="primary">
          下一步
        </el-button>
        <el-button v-if="activeStep === 2" @click="sureSize" type="primary"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import ProTable from "@/components/ProTable/index.vue";
import { Product } from "@/api/interface/product";
import { Ref, computed, nextTick, onMounted, ref } from "vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import { getProductList, productDetail } from "@/api/modules/product";
import { useProductStore } from "@/stores/modules/product";
import { productStatus } from "@/utils/serviceDict";
import CustomTag from "@/components/CustomTag/index.vue";
import { ElMessage } from "element-plus";
import { getDictionary } from "@/api/modules/system";
import { TargetCustomer } from "@/config/order";
import { useDict, useDictLabel } from "@/hooks/useDict";
const { pms_delivery_method } = useDict("pms_delivery_method");

const dictionaryStore = useDicStore();
const productStore = useProductStore();
const dialogVisible = ref(false);
const proTable = ref<ProTableInstance>();
const selectedProDetail = ref({});
const payDicLocal = ref([]);
// const deliveryName = ref("");
const selectedSize = ref("");
const hasTime = ref();
const hasTrial = ref(false);
const probationDate = ref("");
const minTime = ref();
const maxTime = ref();
const timeStep = ref();
const timeUnit = ref();
const skuUnit = ref();
const minQuantity = ref();
const maxQuantity = ref();
const sizeList = ref([]);
const quantityNum = ref();
const activeStep = ref(1);
const unitPrice = ref();

const allPrice = computed(() => {
  if (selectedProDetail.value.skuType === "2") {
    return selectedProDetail.value.price;
  } else {
    return unitPrice.value * (quantityNum.value || 1) * (timeNum.value || 1);
  }
});
const timeNum = ref();
const selectedSizeName = ref("");
const selectedSkuConfig = ref("");
const hasQuantity = ref();
const quantityStep = ref();
const quantityFilter = ref({});
const probationFlag = ref();
// const payType = ref("");
const timeFilter = ref({});

const cooperateDic = computed(() => dictionaryStore.cooperateDic);
const providerList = computed(() => productStore.providerList);
// const deliveryDic = computed(() => dictionaryStore.deliveryDic);
const payDic = computed(() => dictionaryStore.orderPayWayDic);
const selectableFun = (row: any) => {
  return row.skuType === "1";
};
// 表格配置项
const columns: ColumnProps<Product.ProductListParams>[] = [
  { type: "selection", fixed: "left", width: 50, selectable: selectableFun },
  { prop: "productCode", label: "产品编码", width: 110, search: { el: "input" } },
  { prop: "productName", label: "产品名称", search: { el: "input" } },
  { prop: "cooperateType", label: "合作类型", width: 90, enum: cooperateDic, search: { el: "select" } },
  { prop: "providerId", label: "产品服务商", width: 100, enum: providerList, search: { el: "select" } },
  { prop: "categoryName", label: "产品分类", width: 180 },
  {
    prop: "publishStatus",
    label: "上架状态",
    width: 110,
    enum: productStatus, // 产品上架状态字典
    render: scope => {
      return (
        <CustomTag
          type={scope.row.publishStatus ? "success" : "danger"}
          label={scope.row.publishStatus === 2 ? "上架" : "下架"}
        ></CustomTag>
      );
    }
  },
  { prop: "creatorName", label: "创建人员", width: 85, search: { el: "input" } },
  {
    prop: "createTime",
    label: "创建时间",
    width: 170,
    sortable: true,
    search: {
      el: "date-picker",
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" }
    }
  },
  {
    prop: "updateTime",
    label: "上架时间",
    width: 170,
    sortable: true
  }
];

const emits = defineEmits(["selectedProductInfo"]);

onMounted(() => {
  getDictionary({ dictCode: "pms_time_uit" }).then((res: any) => {
    res.data.forEach(item => {
      timeFilter.value[item.itemKey] = item.itemValue;
    });
  });
  // 对数量单位字典进行处理
  getDictionary({ dictCode: "sku_quantity_uit" }).then((res: any) => {
    res.data.forEach(item => {
      quantityFilter.value[item.itemKey] = item.itemValue;
    });
  });
  dictionaryStore.getCooperateDicList(); // 合作类型
  // dictionaryStore.getDeliveryDicList(); // 交付方式
});

/* 关闭 */
const handleClose = () => {
  dialogVisible.value = false;
};

/* 打开 */
const handleOpen = () => {
  dialogVisible.value = true;
  activeStep.value = 1;
  nextTick(() => {
    proTable.value?.getTableList();
  });
};

const handleStep = () => {
  if (activeStep.value === 1) {
    handleClose();
  } else {
    activeStep.value = 1;
  }
};

const selectProduct = () => {
  if ((proTable.value?.selectedList as Array<any>).length > 1) {
    ElMessage.error("只能选择1个产品");
  } else {
    if (proTable.value?.selectedList.length === 0) return;
    productDetail({ id: proTable.value?.selectedList[0].id }).then((res: any) => {
      quantityNum.value = 0;
      timeNum.value = 0;
      selectedProDetail.value = res.data;
      // let ppList = selectedProDetail.value.payType.split(",");
      let productAttr = null;
      try {
        productAttr = JSON.parse(selectedProDetail.value.productAttr);
      } catch (error) {}
      // payDicLocal.value = payDic.value.filter(item => ppList.includes(item.itemKey));
      // deliveryDic.value.forEach(item => {
      //   if (item.itemKey === selectedProDetail.value.deliveryMethod) {
      //     deliveryName.value = item.itemValue;
      //   }
      // });

      sizeList.value =
        selectedProDetail.value.skuType == "1"
          ? selectedProDetail.value.skus
          : [
              {
                skuPrice: "--",
                id: "",
                skuName: "--"
              }
            ];
      unitPrice.value = sizeList.value[0].skuPrice; // sku单价

      // 购买数量、时长初始值：
      selectedSize.value = sizeList.value[0].id;
      selectedSizeName.value = sizeList.value[0].skuName;
      selectedSkuConfig.value = sizeList.value[0].skuConfig;
      hasQuantity.value = sizeList.value[0].quantityCounting;
      hasTrial.value = sizeList.value[0].probationFlag == "1";
      probationDate.value = sizeList.value[0].probationDate;
      probationFlag.value = sizeList.value[0].probationFlag;
      if (hasQuantity.value) {
        quantityStep.value = sizeList.value[0].quantityStep;
        skuUnit.value = sizeList.value[0].skuUnit;
        minQuantity.value = sizeList.value[0].minQuantity;
        maxQuantity.value = sizeList.value[0].maxQuantity;
        quantityNum.value = sizeList.value[0].minQuantity; // 初始默认值
      }
      hasTime.value = sizeList.value[0].timeCounting;
      if (hasTime.value) {
        timeStep.value = sizeList.value[0].timeStep;
        timeUnit.value = sizeList.value[0].timeUnit;
        minTime.value = sizeList.value[0].minTime;
        maxTime.value = sizeList.value[0].maxTime;
        timeNum.value = sizeList.value[0].minTime; // 初始默认值
      }

      activeStep.value = 2;
    });

    // emits("selectedListIds", proTable.value?.selectedListIds); // 后端需要的是这个
  }
};

const selectSizeChange = (value: any) => {
  quantityNum.value = 0;
  timeNum.value = 0;

  sizeList.value.forEach(item => {
    if (value === item.id) {
      unitPrice.value = item.skuPrice; // sku单价
      selectedSizeName.value = item.skuName;
      selectedSkuConfig.value = item.skuConfig;
      hasQuantity.value = item.quantityCounting;
      hasTime.value = item.timeCounting;
      probationDate.value = item.probationDate;
      probationFlag.value = item.probationFlag;
      if (hasQuantity.value) {
        quantityStep.value = item.quantityStep;
        skuUnit.value = item.skuUnit;
        minQuantity.value = item.minQuantity;
        maxQuantity.value = item.maxQuantity;
        quantityNum.value = item.minQuantity; // 初始默认值
      }
      if (hasTime.value) {
        timeStep.value = item.timeStep;
        timeUnit.value = item.timeUnit;
        minTime.value = item.minTime;
        maxTime.value = item.maxTime;
        timeNum.value = item.minTime; // 初始默认值
      }
    }
  });
};

const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.createTimeStart = newParams.createTime[0]);
  newParams.createTime && (newParams.createTimeEnd = newParams.createTime[1]);
  delete newParams.createTime;
  newParams.publishStatus = 2;
  newParams.skuType = "1";
  newParams.targetCustomers = `${TargetCustomer.ENTERPRISE},${TargetCustomer.ALL}`;
  return getProductList(newParams);
};

const sureSize = () => {
  // if (!payType.value) {
  //   ElMessage.error("请选择支付方式");
  //   return;
  // }
  if (hasQuantity.value && !quantityNum.value) {
    ElMessage.error("请选择购买数量");
    return;
  }
  if (hasTime.value && !timeNum.value) {
    ElMessage.error("请选择购买时长");
    return;
  }

  let infos = {
    productId: selectedProDetail.value.id,
    productCode: selectedProDetail.value.productCode,
    productName: selectedProDetail.value.productName,
    skyType: selectedProDetail.value.skyType,
    skuId: selectedSize.value,
    skuName: selectedSizeName.value,
    skuConfig: selectedSkuConfig.value,
    quantityNum: quantityNum.value,
    timeNum: timeNum.value,
    // payType: payType.value,
    deliveryMethod: selectedProDetail.value.deliveryMethod,
    price: unitPrice.value, // 单价
    wholeMoney: allPrice.value, // 订单总金额
    skuType: selectedProDetail.value.skuType,
    timeUnit: timeUnit.value,
    probationFlag: probationFlag.value
  };
  emits("selectedProductInfo", infos);
  handleClose();
};

defineExpose({
  handleOpen
});
</script>

<style scoped lang="scss">
:global(.el-overlay-dialog) {
  // display: block;
}
:global(.el-dialog) {
  // margin: auto;
  // margin-top: 10vh;
}
:global(.custom-modal .el-dialog__header) {
  padding: 22px 24px;
  border-bottom: 1px solid #e1e3e6;
}
:deep(.prod-sku) {
  .el-radio__input {
    display: none;
  }
}
:global(.prod-sku .el-radio) {
  margin-right: 16px;
  &.is-bordered {
    border: 1px solid #73787f;
  }
}
.sku-box {
  display: flex;
  justify-content: center;
  .sku-item {
    display: flex;
    align-items: center;
    margin-bottom: 22px;
    font-size: 16px;
    .info-label {
      min-width: 104px;
      color: #73787f;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
    .unit {
      margin-left: 10px;
    }
    .price {
      font-size: 24px;
      color: var(--el-color-primary);
    }
    .type {
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }
  .title {
    margin-bottom: 12px;
    font-size: 20px;
    font-weight: 600;
    color: #3a3a3d;
  }
  .des {
    color: #3a3a3d;
  }
}
</style>
