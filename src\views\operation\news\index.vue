<template>
  <div class="banner-box table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :tool-button="false">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'add'" plain :icon="Plus" @click="() => toEdit()"> 新增 </el-button>
      </template>

      <template #auditStatus="{ row }">
        <el-popover placement="top" title="" :width="400" trigger="hover">
          <div>
            <div>审核人：{{ row.auditName }}</div>
            <div>审核状态：{{ useDictLabel(statusDict, row.auditStatus) }}</div>
            <div>审核意见：{{ row.auditMsg }}</div>
            <div>审核时间：{{ row.auditTime }}</div>
          </div>
          <template #reference>
            {{ useDictLabel(statusDict, row.auditStatus) }}
          </template>
        </el-popover>
      </template>

      <template #status="{ row }">
        <CustomTag
          :type="statusTypeMap[row.status]"
          :status="row.status"
          :label="useDictLabel(dictStore.newsStatus, row.status)"
        ></CustomTag>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan" @click="() => toEdit(scope.row, true)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'edit'">
          <el-tooltip content="编辑" placement="top">
            <i
              class="iconfont2 opera-icon icon-a-lineicon_edit"
              v-if="scope.row.status === getItemKey('已保存') || scope.row.status === getItemKey('已撤回')"
              @click="() => toEdit(scope.row)"
            ></i>
          </el-tooltip>
        </span>
        <span v-auth="'revoke'">
          <el-tooltip content="撤回" placement="top">
            <i
              class="iconfont2 opera-icon icon-chexiao"
              v-if="scope.row.status === getItemKey('已发布')"
              link
              @click="() => revoke(scope.row)"
            ></i>
          </el-tooltip>
        </span>
        <span v-auth="'unpublish'">
          <el-tooltip content="取消发布" placement="top">
            <i
              class="iconfont2 opera-icon icon-quxiaofabu"
              v-if="scope.row.status === getItemKey('待发布')"
              @click="() => cancelPublish(scope.row)"
            ></i>
          </el-tooltip>
        </span>
        <span v-auth="'delete'">
          <el-tooltip content="删除" placement="top">
            <i
              class="iconfont2 opera-icon icon-lajitong"
              v-if="scope.row.status === getItemKey('已保存') || scope.row.status === getItemKey('已撤回')"
              @click="() => del(scope.row)"
            ></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="news">
import { ref, watch, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { type ProTableInstance, type ColumnProps, type HeaderRenderScope } from "@/components/ProTable/interface/index";
import { Plus } from "@element-plus/icons-vue";
import { getPageList, cancel, newsDelete, change, revoke as revokeAjax } from "@/api/modules/business/news";
import { useDicStore } from "@/stores/modules/dictionaty";
import { newsStorage } from "@/utils/storage/edit";
import { getDictionary } from "@/api/modules/system";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDictLabel } from "@/hooks/useDict";

const dictionaryStore = useDicStore();
const statusTypeMap: { [key: string]: string } = {
  "1": "blue",
  "2": "yellow",
  "3": "green",
  "4": "grey",
  "5": "red"
};
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const route = useRoute();

const dictStore = useDicStore();

const router = useRouter();

const proTable = ref<ProTableInstance>();
const newsStatus = ref([]);

const getTableList = (search: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const { title, publishTime = [], createTime = [], creatorName, status, current, size, auditStatus } = search;
  return getPageList({
    search: title,
    minPublishTime: publishTime[0],
    maxPublishTime: publishTime[1],
    minCreateTime: createTime[0],
    maxCreateTime: createTime[1],
    searchCreatorName: creatorName,
    status,
    current,
    size,
    auditStatus,
    newsType: "1"
  });
};

onMounted(() => {
  dictionaryStore.getProductAuditStatus();
});
const statusDict: any = computed(() => {
  const status = dictionaryStore.productAuditStatus;
  status?.forEach((item: any = {}) => {
    item.value = String(item.itemKey);
  });
  return status;
});

const columns = ref<ColumnProps[]>([
  {
    prop: "title",
    label: "最新动态标题",
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "coverUrl",
    label: "最新动态封面",
    align: "left",
    width: 120,
    render: (scope: any) => {
      return <img class="row-img" src={scope.row.coverUrl} />;
    }
  },
  {
    prop: "status",
    align: "left",
    label: "状态",
    enum: () => {
      return getDictionary({ dictCode: "news_status" });
    },
    fieldNames: { label: "itemValue", value: "itemKey" },
    search: { el: "tree-select", props: { filterable: true } }
  },
  {
    prop: "auditStatus",
    label: "审核状态",
    align: "left",
    search: { el: "select" },
    enum: statusDict
  },
  {
    prop: "creatorName",
    align: "left",
    label: "创建人员",
    search: { el: "input" }
  },
  {
    prop: "createTime",
    label: "创建时间",
    align: "left",
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY/MM/DD HH:mm:ss",
        valueFormat: "YYYY/MM/DD HH:mm:ss"
      }
    },
    width: 180,
    sortable: true
  },
  {
    prop: "publishTime",
    label: "发布时间",
    align: "left",
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY/MM/DD HH:mm:ss",
        valueFormat: "YYYY/MM/DD HH:mm:ss"
      }
    },
    width: 180,
    sortable: true
  },
  { prop: "operation", label: "操作", align: "left", fixed: "right", width: 130 }
]);

const getItemKey = (label: string) => {
  const item = newsStatus.value.filter((item: any) => item.label === label);
  if (item.length) {
    return (item as any)[0].value;
  }
  return "";
};

dictStore.getNewsStatus().then((data: any) => {
  newsStatus.value = data.map((item: any) => ({ value: item.itemKey, label: item.itemValue }));
});

const cancelPublish = async (params: any) => {
  ElMessageBox.confirm("请确认是否取消发布？", "确认操作").then(async () => {
    await cancel(params.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

const del = async (params: any) => {
  ElMessageBox.confirm("请确认是否删除此项动态配置？", "确认操作").then(async () => {
    await newsDelete(params.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

const revoke = async (params: any) => {
  ElMessageBox.confirm("请确认是否撤回此项动态配置？", "确认操作").then(async () => {
    await revokeAjax(params.id);
    ElMessage.success("操作成功！");
    proTable.value?.getTableList();
  });
};

newsStorage.clearExpired();

const toEdit = (data?: any, isCheck = false) => {
  if (data) {
    data.isCheck = isCheck;
    newsStorage.set(data.id, data);
    router.push({
      path: "/operation/news/edit",
      query: {
        id: data.id
      }
    });
  } else {
    router.push({
      path: "/operation/news/edit"
    });
  }
};

watch(
  () => route.path,
  (path: string) => {
    if (path === "/operation/news") {
      proTable.value?.getTableList();
    }
  }
);
</script>
<style lang="scss">
.banner-box .row-img {
  width: 100%;
}
</style>
