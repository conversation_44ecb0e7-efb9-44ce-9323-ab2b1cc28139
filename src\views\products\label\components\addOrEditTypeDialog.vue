<!--
 * @Author: <EMAIL>
 * @Date: 2023-09-08 10:11:24
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-19 11:26:07
 * @Description: file content
-->
<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="editTypeChinese[props.type] + '标签分类'" top="0" width="30%">
      <el-form ref="ruleFormRef" :model="labelForm" :rules="rules" label-width="120px" status-icon label-position="top">
        <el-form-item label="标签分类" prop="name">
          <el-input class="label-name" v-model="labelForm.name" maxlength="15" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="标签名称" prop="list" class="label-content">
          <template #label>
            <div class="form-title-name">
              <span>标签名称</span>
              <el-button v-if="props.type === 1" plain :icon="Plus" @click="addLabel">添加标签</el-button>
              <!-- 编辑标签分类需校验添加标签按钮权限 -->
              <el-button v-else v-auth="'add'" plain :icon="Plus" @click="addLabel">添加标签</el-button>
            </div>
          </template>
          <template #default>
            <div class="label-box" ref="labelBox">
              <draggable v-model="labelForm.list" handle=".sortBtn" animation="300" item-key="id">
                <template #item="{ element, index }">
                  <div class="lable-item" :key="element.id">
                    <div class="label-name" v-if="element.id != editingId" @click="changeEditElementId(element.id, element.name)">
                      {{ element.name }}
                    </div>
                    <el-input
                      class="label-name"
                      v-if="element.id == editingId"
                      maxlength="15"
                      show-word-limit
                      @keydown.enter="changeEditElementId('-1', element.name)"
                      v-model="element.name"
                      @blur="changeEditElementId('-1', element.name)"
                    ></el-input>
                    <div class="label-btns">
                      <el-button v-if="props.type === 1" link @click="deleteItem(index)" text plain :icon="Delete"></el-button>
                      <el-button v-else v-auth="'delete'" link @click="deleteItem(index)" text plain :icon="Delete"></el-button>
                      <el-button link class="sortBtn" text plain :icon="Operation"></el-button>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </template>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import { uniqueId } from "lodash";
import { computed, ref, reactive, watch } from "vue";
import type { FormInstance } from "element-plus";
import { Plus, Delete, Operation, ElementPlus } from "@element-plus/icons-vue";
import draggable from "vuedraggable";
import { addTagType, updateTagType } from "@/api/modules/label";
import { ResPage } from "@/api/interface/index";
import { Label } from "@/api/interface/label";
import { ElMessage } from "element-plus";
import { getTagList } from "@/api/modules/label";
// import { Label } from "@/api/interface/label";
const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  type: {
    type: Number,
    default: 1
  },
  params: {
    type: Object,
    default: () => {}
  }
});

let oldLabelId = "";
const labelBox = ref();
const editingId = ref("-1");
const ruleFormRef = ref();
const labelForm = ref<any>({
  name: "",
  list: []
});

const emits = defineEmits(["update:visible", "cancel", "confirm"]);
watch(
  () => props.params,
  () => {
    labelForm.value.name = props.params.name || "";
    if (props.params.id) {
      getTagList({ typeId: props.params.id, size: 10000 }).then(res => {
        if (res.code === 200) {
          labelForm.value.list = (res.data as ResPage<any>).records;
        }
      });
    } else {
      //默认三项
      labelForm.value.list = [];
      for (let i = 0; i < 3; i++) {
        addLabel();
      }
    }
  }
);

const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
};

const changeEditElementId = (value: string, labelName: string) => {
  if (!checkLabelName()) {
    ElMessage.error("标签名字不能超出15个字符或者空");
    return;
  }
  if (!checkSameName()) {
    editingId.value = oldLabelId;
    ElMessage.error("标签名字不能重复");
  } else {
    editingId.value = value;
    oldLabelId = value;
  }
};

const checkSameName = (): boolean => {
  const newListLength = new Set(labelForm.value.list.map((item: any) => item.name)).size;
  const listLength = labelForm.value.list.length;

  //true则说明不存在同名
  return listLength === newListLength;
};

const typeValidatePass = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("标签分类不能为空"));
  } else if (value.length > 15) {
    callback(new Error("标签分类名称不能超过15个字符"));
  } else {
    callback();
  }
};

const labelValidatePass = (rule: any, value: any, callback: any) => {
  if (value.length === 0) {
    callback(new Error("标签不能为空"));
  } else {
    callback();
  }
};

const rules = reactive({
  name: [{ validator: typeValidatePass, trigger: "blur", required: true }],
  list: [{ validator: labelValidatePass, trigger: "change", required: true }]
});

const getNewLabelName = (itemIndex: any): string => {
  let sameNumber = 0;
  const name = `新标签${itemIndex}`;
  labelForm.value.list.forEach((item: any) => {
    item.name === name ? sameNumber++ : void 0;
  });
  //
  if (sameNumber >= 1) {
    return getNewLabelName(++itemIndex);
  } else {
    return name;
  }
};

const addLabel = () => {
  if (labelForm.value.list.length >= 20) {
    ElMessage.error("同个分类下的标签最多存在20个");
    return;
  }
  if (!checkLabelName()) {
    ElMessage.error("请检查标签名字，不能为空或者大于15个字符");
    return;
  }

  const id = uniqueId();
  const labelName = getNewLabelName(labelForm.value.list.length + 1);
  const labelId = `newLabel${id}`;
  const newItem = { name: labelName, id: labelId };
  labelForm.value.list.unshift(newItem);
  setTimeout(() => {
    changeEditElementId(labelId, labelName);
    const container = labelBox.value;
    if (container.scrollTop < container.scrollHeight) {
      container.scrollTop = 0;
    }
  }, 0);
};

const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    labelForm.value = {
      name: "",
      list: []
    };
    emits("update:visible", value);
  }
});

const deleteItem = (index: number) => {
  const item = labelForm.value.list[index];
  labelForm.value.list.splice(index, 1);
};

const checkLabelName = () => {
  let result = true;
  labelForm.value.list.forEach((item: any) => {
    if (!item.name || item.name.length > 15) {
      result = false;
    }
  });
  return result;
};

const saveData = (formEl: FormInstance | undefined) => {
  if (!checkSameName()) {
    ElMessage.error("标签名字不能重复");
    return;
  }
  if (!checkLabelName()) {
    ElMessage.error("请检查标签名字，不能为空或者大于15个字符");
    return;
  }
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiObject = {
        [editType["add"]]: addTagType,
        [editType["edit"]]: updateTagType
      };
      const requestParamsObject = {
        [editType["add"]]: () => {
          return {
            name: labelForm.value.name,
            tagNames: labelForm.value.list.map((item: Label.TagItem) => {
              return item.name;
            })
          };
        },
        [editType["edit"]]: () => {
          return {
            id: props.params.id,
            name: labelForm.value.name,
            tags: labelForm.value.list.map((item: Label.TagItem) => {
              const id = item.id!.indexOf("newLabel") != -1 ? undefined : item.id;
              return { id, name: item.name, typeId: props.params.id };
            })
          };
        }
      };
      apiObject[props.type as 1 | 2](requestParamsObject[props.type as 1 | 2]()).then((res: any) => {
        emits("confirm");
        ElMessage.success(res.msg);
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding-bottom: 0;
}
.label-content {
  :deep(.el-form-item__label) {
    display: flex;
    justify-content: space-between;
    padding-right: 0;
  }
}
.label-box {
  width: 100%;
  min-height: 200px;
  max-height: 380px;
  padding: 20px;
  overflow: auto;
  background: var(--el-color-white);
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  scroll-behavior: smooth;
  :deep(.el-form-item) {
    margin-bottom: 22px;
  }
  .lable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    :deep(.el-input) {
      flex: 1;
      margin-right: 20px;
    }
  }
}
.form-title-name {
  display: inline-flex;

  // width: 100%;
  flex: 1%;
  justify-content: space-between;
}
</style>
