/*
 * @Author: <EMAIL>
 * @Date: 2023-09-25 09:58:31
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-15 16:45:08
 * @Description: file content
 */
import { Upload } from "@/api/interface/index";
import { PORT1 } from "@/api/config/servicePort";
import http from "@/api";
import type { downloadMange } from "@/api/interface/upload";

/**
 * @name 文件上传模块
 */
// 图片上传
export const uploadImg = (params: FormData) => {
  return http.post<Upload.ResFileUrl>(`cnud-file/common/oss/endpoint/put-file`, params);
};

// 删除图片
export const deleteImg = (fileName: string, fileId?: string) => {
  return http.post(`cnud-file/common/oss/endpoint/remove-file?fileName=${fileName}&id=${fileId}`);
};

// 视频上传
export const uploadVideo = (params: FormData) => {
  // return http.post<Upload.ResFileUrl>(PORT1 + `/file/upload/video`, params);
};

//私密图片上传
export const uploadImgPrivate = (params: FormData) => {
  return http.post<Upload.ResFileUrl>(`cnud-file/common/oss/endpoint/put-private-file`, params);
};

/* 下载管理列表 */
export const downloadManage = (data: downloadMange.IDownLoadSearch) => {
  return http.post("/cnud-file/admin/export-audit/download_manage", data);
};

/* 下载文件 */
export const exportDownload = (params: { id: string }) => {
  return http.download("/cnud-file/admin/export-audit/download", params);
};
