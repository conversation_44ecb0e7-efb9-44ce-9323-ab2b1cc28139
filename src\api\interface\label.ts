/*
 * @Author: <EMAIL>
 * @Date: 2023-09-15 10:26:13
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-09-15 17:40:17
 * @Description: file content
 */
//标签模块
export namespace Label {
  export interface TagListParams {
    ascs?: string;
    current?: number;
    descs?: string;
    name?: string;
    size?: number;
    typeId?: string;
    typeName?: string;
  }
  export interface TagItem {
    name?: string;
    typeId?: string;
    id?: string;
  }
  export interface TagType {
    createTime?: string;
    updateTime?: string;
    creatorId?: string;
    id?: string;
    isDel?: boolean;
    name?: string;
    updaterId?: string;
    typeId?: string;
  }
  export interface TagTypeListParams {
    ascs?: string;
    createTime?: string;
    current?: number;
    descs?: string;
    size?: number;
    updateTime?: string;
    creatorId?: string;
    id?: string;
    isDel?: string;
    name?: string;
    updaterId?: string;
  }
  export interface deleteParams {
    ids: string;
  }
}
