<template>
  <el-drawer
    :destroy-on-close="true"
    :append-to-body="true"
    v-model="drawer"
    title="绑定合作平台"
    size="1200"
    direction="rtl"
    :before-close="handleClose"
    class="'account-drawer'"
  >
    <slot name="search"></slot>
    <div class="table-box">
      <ProTable
        :request-api="getTableList"
        :tool-button="false"
        ref="proTable"
        :columns="columns"
        :request-auto="false"
      ></ProTable>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { nextTick, ref } from "vue";
import ProTable from "@/components/ProTable/index.vue";

import { ProTableInstance } from "@/components/ProTable/interface";
import { getPlatformAccountList } from "@/api/modules/platform";

const props = defineProps({
  columns: {
    type: Array,
    default: () => {
      return [];
    }
  },

  searchParams: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

const drawer = ref(false);

const proTable = ref<ProTableInstance>();

const handleClose = () => {
  drawer.value = false;
};

const handleOpen = () => {
  drawer.value = true;
  nextTick(() => {
    proTable.value?.getTableList();
  });
};

const getTableList = async (params: any = {}) => {
  const platform = props.searchParams.platform !== "" ? props.searchParams.platform : "1";
  return await getPlatformAccountList({ ...params, platform, isBind: 0 });
};

const searchTable = () => {
  proTable.value?.getTableList();
};

defineExpose({
  handleOpen,
  handleClose,
  searchTable
});
</script>

<style scoped lang="scss"></style>
