<template>
  <div class="page-verification-detail">
    <div class="verification-info">
      <div class="header-title">审核信息</div>
      <div class="form">
        <el-form ref="formRef" :model="examineForm" label-width="83px" class="demo-ruleForm">
          <el-form-item label="审核意见" prop="auditRemark" :rules="[{ required: validRequired, message: '请输入驳回意见' }]">
            <el-input
              rows="5"
              v-model="examineForm.auditRemark"
              placeholder="驳回时必须填写审核意见，通过默认审核意见为同意"
              type="textarea"
              maxlength="200"
              show-word-limit
              autocomplete="off"
              :disabled="props.isDisabled"
            />
          </el-form-item>
          <el-form-item> </el-form-item>
          <div class="button">
            <slot name="operateBtn"></slot>
            <el-button v-if="isShowBackedBtn" plain @click="handleBack">返回</el-button>
            <el-button type="primary" @click="handleTask('pass')" v-if="props.isShowOperateBtn">通过</el-button>
            <el-button type="danger" @click="handleTask('noPass')" v-if="props.isShowOperateBtn">不通过</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from "vue";
import type { Action, FormInstance } from "element-plus";
import { ElMessageBox } from "element-plus";
// import { useRouter } from "vue-router";

const props = defineProps({
  auditMsg: {
    type: String,
    require: false,
    default: ""
  },
  isDisabled: {
    type: Boolean,
    require: false,
    default: false
  },
  isShowOperateBtn: {
    type: Boolean,
    require: false,
    default: false
  },
  isShowBackedBtn: {
    type: Boolean,
    default: () => true
  }
});

// const router = useRouter();
const formRef = ref<FormInstance>();
const validType = ref<string>("pass");

const validRequired = computed(() => {
  return validType.value === "noPass";
});

const examineForm = reactive({
  auditRemark: props.auditMsg || ""
});

watch(
  () => props.auditMsg,
  () => {
    examineForm.auditRemark = props.auditMsg;
  }
);

const emit = defineEmits(["onConfirm", "onBack"]);

const handleTask = (index: string) => {
  validType.value = index;
  let tips = "";
  if (index === "pass") {
    tips = "是否确认审核通过";
  } else {
    tips = "是否确认驳回审核";
  }

  nextTick(() => {
    formRef.value?.validate().then(valid => {
      ElMessageBox.alert(tips, "操作提示", {
        confirmButtonText: "确定",
        callback: (action: Action) => {
          if (action === "confirm") {
            const auditRemark = examineForm.auditRemark === "" ? "同意" : examineForm.auditRemark;
            emit("onConfirm", { type: index, auditRemark });
          }
        }
      });
    });
  });
};

const handleBack = () => {
  emit("onBack");
};
</script>

<style scoped lang="scss">
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
</style>
