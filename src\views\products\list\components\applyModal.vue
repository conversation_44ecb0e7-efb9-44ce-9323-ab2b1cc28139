<template>
  <el-dialog v-model="dialogVisible" :title="modalTitle" width="800" :before-close="handleClose" :destroy-on-close="true">
    <el-alert type="warning" :closable="false">
      <template #title>
        <div style="display: flex">
          <el-icon :size="24"><QuestionFilled /></el-icon>
          <span style="margin-left: 10px; line-height: 24px">{{ tips }}</span>
        </div>
      </template>
    </el-alert>

    <div class="content">
      <el-form ref="applyRef" :model="applyForm" label-width="120px">
        <template v-if="modalAction !== 'update'">
          <el-form-item label="产品名称：" prop="productName">
            <span>{{ applyForm.productName }}</span>
          </el-form-item>
          <el-form-item label="产品分类：" prop="categoryName">
            <span>{{ applyForm.categoryName }}</span>
          </el-form-item>
        </template>
        <template v-if="modalAction === 'off'">
          <el-form-item
            label="下架原因："
            prop="applicationMessage"
            :rules="[{ trigger: 'blur', required: true, message: '请输入下架原因' }]"
          >
            <el-input
              v-model="applyForm.applicationMessage"
              type="textarea"
              placeholder="请输入内容"
              maxlength="250"
              rows="5"
              show-word-limit
            ></el-input>
          </el-form-item>
        </template>
        <template v-else-if="modalAction === 'update'">
          <el-form-item
            label="修改信息："
            prop="applicationMessage"
            :rules="[{ trigger: 'blur', required: true, message: '请输入修改信息' }]"
          >
            <el-input
              v-model="applyForm.applicationMessage"
              type="textarea"
              placeholder="请输入内容"
              maxlength="250"
              rows="5"
              show-word-limit
            ></el-input>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="备注：" prop="applicationMessage">
            <el-input
              v-model="applyForm.applicationMessage"
              type="textarea"
              placeholder="请输入内容"
              maxlength="250"
              rows="5"
              show-word-limit
            ></el-input>
          </el-form-item>
        </template>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, unref, computed, nextTick } from "vue";
import { Product } from "@/api/interface/product";
type MODALACTION = "on" | "off" | "update";

const modalTitle = computed(() => {
  switch (unref(modalAction)) {
    case "on":
      return "上架申请";
    case "off":
      return "下架申请";
    case "update":
      return "产品修改申请";
    default:
      return "";
  }
});

const tips = computed(() => {
  switch (unref(modalAction)) {
    case "on":
      return "产品上架需平台运营审核通过后方可上架";
    case "off":
      return "产品下架需平台运营审核通过后方可下架";
    case "update":
      return "已上架产品的信息修改需平台运营审核通过后方可生效";
    default:
      return "";
  }
});
const dialogVisible = ref(false);
const modalAction = ref("on");
const applyRef = ref();

const applyForm = ref<any>({
  productName: "",
  categoryName: "",
  applicationMessage: ""
});

const emit = defineEmits(["on-submit"]);

const handleOpen = (action: MODALACTION, rowData?: Product.ProductAttrListItem) => {
  modalAction.value = action;
  dialogVisible.value = true;
  nextTick(() => {
    if (action === "on" || action === "off") {
      applyForm.value = {
        ...rowData
      };
    }
  });
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleSubmit = () => {
  applyRef.value
    ?.validate()
    .then(() => {
      // dialogVisible.value = false;
      // eslint-disable-next-line vue/custom-event-name-casing
      emit("on-submit", applyForm.value);
    })
    .catch((err: string) => {
      console.log(err);
    });
};

defineExpose({
  handleOpen,
  handleClose
});
</script>

<style scoped lang="scss">
.content {
  margin-top: 20px;
}
</style>
