<template>
  <el-dialog v-model="visible" title="选择产品" width="1200" align-center @close="handleCancel" destroy-on-close>
    <div class="table-box">
      <ProTable
        @selection-change="changeSelect"
        ref="ProTableRef"
        :request-api="getTableList"
        :columns="columns"
        :tool-button="false"
        :row-key="'skuId'"
      >
        <template #tableHeader>
          <div class="mb20">
            <span>已选中</span>
            <span class="has-choose">{{ diffArray.length }}</span>
            <span>项</span>
          </div>
        </template>
      </ProTable>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button :disabled="diffArray.length === 0" type="primary" @click="handleConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { computed, nextTick, ref, watch } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { queryProdSkuPage } from "@/api/modules/product";
import { Product } from "@/api/interface/product";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getTypeTree } from "@/api/modules/productClassify";
import { useRoute } from "vue-router";
import { productItem } from "@/api/interface/business/theme";
const props = defineProps({
  maxItem: {
    type: Number,
    default: 20
  },
  modelValue: {
    type: Boolean,
    default: false
  },
  modelIndex: {
    type: Number,
    default: 0
  },
  cList: {
    type: Array<productItem>,
    default: () => {
      return [];
    }
  }
});
const emits = defineEmits(["update:modelValue", "updateSkuIds"]);

const route = useRoute();

const ProTableRef = ref<ProTableInstance>();

const visible = ref(false);

/* 新选的产品 */
const diffArray = ref<Array<Product.ProductListParams>>([]);

const columns: ColumnProps<Product.ProductListParams>[] = [
  {
    type: "selection",
    fixed: "left",
    width: 50,
    selectable: (row: any, index: number) => {
      if (diffArray.value.length >= props.maxItem) {
        return diffArray.value.some((el: any) => el.skuId === row.skuId);
      } else {
        return true;
      }
    }
  },
  { prop: "productName", label: "产品名称", search: { el: "input" } },
  {
    prop: "categoryName",
    label: "产品分类",

    enum: getTypeTree,
    isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格
    search: { el: "cascader", key: "categoryIdList" },
    fieldNames: {
      value: "id",
      label: "name"
    }
  },
  { prop: "skuName", label: "产品规格" },
  { prop: "skuPrice", label: "规格单价" },
  { prop: "skuDiscountPrice", label: "折扣价" }
];

const changeSelect = (data: Product.ProductListParams[]) => {
  // if (data.length <= 20) {
  //   diffArray.value = data;
  // } else {
  //   ElMessage.warning("最多选择20个");
  // }

  if (data.length > props.maxItem) {
    data.splice(props.maxItem);
    ProTableRef.value?.element.clearSelection();
    data.forEach(row => {
      ProTableRef.value?.element.toggleRowSelection(row, true);
    });
  }

  diffArray.value = data;
};

const getTableList = async (params: any = {}) => {
  const newParams = JSON.parse(JSON.stringify(params));

  if (Reflect.has(newParams, "categoryIdList")) {
    newParams.categoryId = params.categoryIdList[params.categoryIdList.length - 1];
    Reflect.deleteProperty(newParams, "categoryIdList");
  }

  return await queryProdSkuPage({ ...newParams, publishStatus: 2, productType: "1" });
};

const handleConfirm = () => {
  emits("updateSkuIds", diffArray.value, props.modelIndex);
  emits("update:modelValue", false);
};

const handleCancel = () => {
  emits("update:modelValue", false);
};

watch(
  () => props.modelValue,
  nVal => {
    visible.value = nVal;
    diffArray.value = [];
    if (nVal) {
      nextTick(() => {
        props.cList.forEach((row: any) => {
          ProTableRef.value?.element.toggleRowSelection({ skuId: row.id, ...row }, true);
        });
      });
    } else {
      // nextTick(() => {
      //   ProTableRef.value?.element!.clearSelection();
      // });
    }
  }
);
</script>

<style scoped lang="scss">
.has-choose {
  padding: 0 5px;
  color: var(--el-color-primary);
}
</style>
