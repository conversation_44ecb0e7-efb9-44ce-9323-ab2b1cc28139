<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="生态触点订单列表"
      :columns="columns"
      :tool-button="false"
      :request-api="getTableList"
      :request-auto="false"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>
      <template #status="{ row }">
        <CustomTag
          :type="'order'"
          :status="row.status"
          :label="useDictLabel(dictionaryStore?.orderStatusDic, row.status)"
        ></CustomTag>
      </template>
      <!-- 表格操作 -->
      <template #operation="{ row }">
        <span v-auth="'review'">
          <el-tooltip v-if="row?.auditStatus === '2'" content="审核" placement="top">
            <i class="iconfont2 opera-icon icon-shenhe" @click="verify(row)"></i>
          </el-tooltip>
        </span>
      </template>
      <template #skuName="{ row }">
        <div v-html="row.skuName"></div>
      </template>
    </pro-table>
  </div>
</template>

<script setup lang="tsx" name="ecologyOrder">
import { ref, onMounted, onActivated, computed } from "vue";
import { useRouter } from "vue-router";
import { CirclePlus, Download } from "@element-plus/icons-vue";
// import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
// import { useDownload } from "@/hooks/useDownload";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getOrderList, queryOrderListExcel } from "@/api/modules/order";
import { useDicStore } from "@/stores/modules/dictionaty";
import { getChannelList } from "@/api/modules/product";
import { ElMessageBox } from "element-plus";
import { useDownload } from "@/hooks/useDownload";
import { useDictLabel } from "@/hooks/useDict";
import CustomTag from "@/components/CustomTag/index.vue";
import { useAuthButtons } from "@/hooks/useAuthButtons";

const { BUTTONS } = useAuthButtons();

const router = useRouter();
const proTable = ref<ProTableInstance>();
const dictionaryStore = useDicStore();
const contactorList = ref([]); // 下单触点

onMounted(() => {
  // 先获取字典
  dictionaryStore.getQuantityDicList(); // 数量单位
  dictionaryStore.getTimeDicList(); // 时长单位
  dictionaryStore.getOrderType(); // 订单类型
  dictionaryStore.getOrderStatus(); // 订单状态
  dictionaryStore.getAuditStatus(); // 审核状态
  dictionaryStore.getOrderPayDicList(); // 订单-支付方式
  // 获取渠道列表-渲染下单触点的下拉框
  getChannelList().then((res: any) => {
    res.data.forEach((item: any = {}) => {
      contactorList.value.push({ value: item.code, label: item.name });
    });
  });
  proTable.value?.getTableList();
});
// 激活时获取列表（新增、编辑更新列表）
onActivated(() => {
  // proTable.value?.getTableList();
});
const orderTypeDic: any = computed(() => dictionaryStore.orderTypeDic);
const orderStatusDic: any = computed(() => dictionaryStore.orderStatusDic);
const auditStatusDic: any = computed(() => dictionaryStore.auditStatusDic);
const orderPayWayDic: any = computed(() => dictionaryStore.orderPayWayDic);

// 表格配置项
const columns: ColumnProps[] = [
  // { type: "selection", fixed: "left", width: 50 },
  {
    prop: "parentOrderNo",
    label: "订单编号",
    width: 180,
    search: { el: "input" },
    render: ({ row }) => {
      return (
        <el-button
          type="text"
          onClick={() => {
            router.push({
              path: "/order/ecologyOrder/detail",
              query: {
                orderId: row.parentOrderNo,
                auditTag: 0
              }
            });
          }}
        >
          {row.parentOrderNo}
        </el-button>
      );
    }
  },
  {
    prop: "subOrderNo",
    label: "子订单编号",
    width: 180,
    search: { el: "input" },
    render: ({ row }) => {
      return (
        <el-button
          type="text"
          onClick={() => {
            router.push({
              path: "/order/ecologyOrder/detail",
              query: {
                subOrderId: row.id
              }
            });
          }}
        >
          {row.subOrderNo}
        </el-button>
      );
    }
  },
  { prop: "customerAccount", label: "客户账号", width: 110, search: { el: "input" } },
  { prop: "purchasePhone", label: "手机号码", width: 120, search: { el: "input" } },
  {
    prop: "channelName",
    label: "下单触点",
    width: 125,
    isFilterEnum: false, // enum 只作为搜索项数据，不改变当前单元格,
    enum: contactorList,
    search: { el: "select", key: "channelCode" }
  },
  { prop: "orderType", label: "订单类型", width: 110, enum: orderTypeDic, sortable: true, search: { el: "select" } },
  {
    prop: "status",
    label: "订单状态",
    width: 105,
    enum: orderStatusDic,
    search: { el: "select", key: "statusList", props: { multiple: true, "collapse-tags": true, placeholder: "请选择（可多选）" } }
  },
  { prop: "payType", label: "支付方式", width: 90, enum: orderPayWayDic, search: { el: "select" } },
  { prop: "cbProductId", label: "话费订购支付编码", width: 145, search: { el: "input" } },
  { prop: "productName", label: "产品名称", width: 170, search: { el: "input" } },
  { prop: "skuName", label: "规格名称", width: 240, search: { el: "input" } },
  { prop: "payFee", label: "订单总金额（元）", width: 145 },
  { prop: "productProviderName", label: "产品服务商", width: 100 },
  {
    prop: "subscribeTime",
    label: "订购时间",
    sortable: true,
    width: 170,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "unsubscribeTime",
    label: "退订时间",
    isShow: false,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "effectTime",
    label: "生效时间",
    sortable: true,
    width: 170,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "endTime",
    label: "到期时间",
    sortable: true,
    width: 170,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "finishTime",
    label: "订购完成时间",
    isShow: false,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "closeTime",
    label: "订购失败时间",
    isShow: false,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  {
    prop: "creatorName",
    label: "后台创建人",
    width: 100,
    // tag: true,
    render: scope => {
      return scope.row.channelName === "一体化支撑平台" ? scope.row.creatorName : "-";
    }
  },
  { prop: "auditStatus", label: "审核状态", enum: auditStatusDic, width: 100, search: { el: "select" } },
  { prop: "auditor", label: "审核人", isShow: false, search: { el: "input" } },
  {
    prop: "auditTime",
    label: "审核时间",
    isShow: false,
    search: { el: "date-picker", props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" } }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 100 }
];

const getTableList = (params: any = {}) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  // 订购时间
  newParams.subscribeTime && (newParams.subscribeTimeOfStart = newParams.subscribeTime[0]);
  newParams.subscribeTime && (newParams.subscribeTimeOfEnd = newParams.subscribeTime[1]);
  delete newParams.subscribeTime;
  // 退订时间
  newParams.unsubscribeTime && (newParams.unsubscribeTimeOfStart = newParams.unsubscribeTime[0]);
  newParams.unsubscribeTime && (newParams.unsubscribeTimeOfEnd = newParams.unsubscribeTime[1]);
  delete newParams.unsubscribeTime;
  // 生效时间
  newParams.effectTime && (newParams.effectTimeOfStart = newParams.effectTime[0]);
  newParams.effectTime && (newParams.effectTimeOfEnd = newParams.effectTime[1]);
  delete newParams.effectTime;
  // 到期时间
  newParams.endTime && (newParams.endTimeOfStart = newParams.endTime[0]);
  newParams.endTime && (newParams.endTimeOfEnd = newParams.endTime[1]);
  delete newParams.endTime;
  // 订购完成时间
  newParams.finishTime && (newParams.finishTimeOfStart = newParams.finishTime[0]);
  newParams.finishTime && (newParams.finishTimeOfEnd = newParams.finishTime[1]);
  delete newParams.finishTime;
  // 订购失败时间
  newParams.closeTime && (newParams.closeTimeOfStart = newParams.closeTime[0]);
  newParams.closeTime && (newParams.closeTimeOfEnd = newParams.closeTime[1]);
  delete newParams.closeTime;
  // 审核时间
  newParams.auditTime && (newParams.auditTimeOfStart = newParams.auditTime[0]);
  newParams.auditTime && (newParams.auditTimeOfEnd = newParams.auditTime[1]);
  delete newParams.auditTime;
  // 订单类型（1-商城订单 2-生态触点订单 3-政企中台订单）
  newParams.multiPoint = 2;
  return getOrderList(newParams);
};

const verify = (row: { parentOrderNo: string }) => {
  router.push({
    path: "/order/ecologyOrder/detail",
    query: {
      orderId: row.parentOrderNo,
      auditTag: 1
    }
  });
};

// 导出订单列表
const downloadFile = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  // 订购时间
  newParams.subscribeTime && (newParams.subscribeTimeOfStart = newParams.subscribeTime[0]);
  newParams.subscribeTime && (newParams.subscribeTimeOfEnd = newParams.subscribeTime[1]);
  delete newParams.subscribeTime;
  // 退订时间
  newParams.unsubscribeTime && (newParams.unsubscribeTimeOfStart = newParams.unsubscribeTime[0]);
  newParams.unsubscribeTime && (newParams.unsubscribeTimeOfEnd = newParams.unsubscribeTime[1]);
  delete newParams.unsubscribeTime;
  // 生效时间
  newParams.effectTime && (newParams.effectTimeOfStart = newParams.effectTime[0]);
  newParams.effectTime && (newParams.effectTimeOfEnd = newParams.effectTime[1]);
  delete newParams.effectTime;
  // 到期时间
  newParams.endTime && (newParams.endTimeOfStart = newParams.endTime[0]);
  newParams.endTime && (newParams.endTimeOfEnd = newParams.endTime[1]);
  delete newParams.endTime;
  // 订购完成时间
  newParams.finishTime && (newParams.finishTimeOfStart = newParams.finishTime[0]);
  newParams.finishTime && (newParams.finishTimeOfEnd = newParams.finishTime[1]);
  delete newParams.finishTime;
  // 订购失败时间
  newParams.closeTime && (newParams.closeTimeOfStart = newParams.closeTime[0]);
  newParams.closeTime && (newParams.closeTimeOfEnd = newParams.closeTime[1]);
  delete newParams.closeTime;
  // 审核时间
  newParams.auditTime && (newParams.auditTimeOfStart = newParams.closeTime[0]);
  newParams.auditTime && (newParams.auditTimeOfEnd = newParams.closeTime[1]);
  delete newParams.auditTime;
  // 订单类型（1-商城订单 2-生态触点订单 3-政企中台订单）
  newParams.multiPoint = 2;
  newParams.size = 10000;

  ElMessageBox.confirm("确认导出生态触点订单?", "提示", { type: "warning" }).then(() =>
    useDownload(queryOrderListExcel, newParams)
  );
};
</script>
