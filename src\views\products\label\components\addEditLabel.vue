<!--
 * @Author: <EMAIL>
 * @Date: 2023-09-08 10:11:24
 * @LastEditors: linzewei4270
 * @LastEditTime: 2023-12-19 11:25:01
 * @Description: file content
-->
<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="editTypeChinese[params.type] + '标签'" top="0" width="30%">
      <el-form ref="ruleFormRef" :model="labelForm" :rules="rules" label-width="120px" status-icon label-position="top">
        <el-form-item label="标签名称" prop="name">
          <el-input class="label-name" v-model="labelForm.name" show-word-limit maxlength="15"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { editTypeChinese, editType } from "@/enums/dialogTypeEnum";
import { computed, ref, reactive, watch } from "vue";
import type { FormInstance } from "element-plus";
import { addTag, updateTag } from "@/api/modules/label";
import { ElMessage } from "element-plus";
const props = defineProps({
  visible: {
    type: Boolean, //类型
    default: false //默认值
  },
  params: {
    type: Object,
    default: () => {}
  }
});
const labelForm = ref({ name: "" });
const ruleFormRef = ref();
watch(
  () => props.params,
  () => {
    labelForm.value.name = props.params.name;
  }
);
const typeValidatePass = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("标签名称不能为空"));
  } else if (value.length > 15) {
    callback(new Error("标签名称不能超过15个字符"));
  } else {
    callback();
  }
};
const rules = reactive({
  name: [{ validator: typeValidatePass, trigger: "blur", required: true }]
});
let emits = defineEmits(["update:visible", "confirm", "cancel"]);

const dialogVisible = computed({
  get: () => {
    return props.visible;
  },
  set: value => {
    emits("update:visible", value);
  }
});
const onCancel = () => {
  emits("cancel");
  dialogVisible.value = false;
};
const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(valid => {
    if (valid) {
      const apiObject = {
        [editType["add"]]: addTag,
        [editType["edit"]]: updateTag
      };
      const params = {
        id: props.params.id,
        name: labelForm.value.name,
        typeId: props.params.typeId
      };
      apiObject[props.params.type as 1 | 2](params).then((res: any) => {
        ElMessage.success(res.msg);
        emits("confirm");
        dialogVisible.value = false;
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
</script>
<style lang="scss" scoped></style>
