<template>
  <div class="table-box">
    <ProTable ref="proTable" title="用量明细详情" :columns="columns" :tool-button="false" :request-api="getTableList">
      <template #changeType="{ row }">
        {{ useDictLabel(dsk_balance_change_type, row?.changeType) || "--" }}
      </template>

      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="usageDetail">
import { ref } from "vue";
import { cloneDeep } from "lodash";
import { useRoute } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { getDskBalanceRecord, exportDskBalance } from "@/api/modules/billManagement";

const { dsk_balance_change_type } = useDict("dsk_balance_change_type");
const { BUTTONS } = useAuthButtons();

const proTable = ref<ProTableInstance>();

const route = useRoute();
const { id = "" } = route.query || { id: "" };

const getTableList = (params: any) => {
  if (!BUTTONS.value?.query) {
    return Promise.resolve();
  }
  let newParams = cloneDeep(params);
  delete newParams.happenTime;
  return getDskBalanceRecord({ ...newParams, userId: id });
};

const downloadFile = async () => {
  let newParams = cloneDeep(proTable.value?.searchParam);
  delete newParams.happenTime;
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() =>
    useDownload(exportDskBalance, { ...newParams, userId: id })
  );
};

const columns: ColumnProps<any>[] = [
  { prop: "account", label: "客户账号", minWidth: 140 },
  {
    prop: "happenTime",
    label: "核时交易时间",
    search: {
      el: "date-picker",
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" },
      transform: (value: any) => {
        console.log(value);
        return {
          happenTimeStart: value[0],
          happenTimeEnd: value[1]
        };
      }
    },
    minWidth: 140
  },
  { prop: "effectTimeEnd", label: "有效期至", minWidth: 140 },
  {
    prop: "changeType",
    label: "类型",
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {dsk_balance_change_type.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "changeQuantity", label: "核时" },
  { prop: "skuPrice", label: "核时单价（元/核时）", minWidth: 160 },
  { prop: "subOrderNo", label: "关联订单号", minWidth: 140 },
  { prop: "orderCity", label: "订单号对应地市", minWidth: 140 },
  { prop: "jobBillId", label: "明细账单ID", minWidth: 140 },
  { prop: "settleTime", label: "支付时间", minWidth: 100 },
  { prop: "balanceQuantity", label: "用户核时余额", minWidth: 150 }
];
</script>
