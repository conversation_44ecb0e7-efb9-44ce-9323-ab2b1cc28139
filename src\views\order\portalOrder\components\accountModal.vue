<template>
  <el-dialog
    :append-to-body="true"
    :destroy-on-close="true"
    class="custom-modal"
    v-model="dialogVisible"
    width="1080"
    :before-close="handleClose"
    title="选择客户"
  >
    <div class="table-box">
      <pro-table
        ref="proTable"
        title="客户列表"
        :columns="columns"
        :request-api="getTableList"
        :request-auto="false"
        :tool-button="false"
      >
        <template #userSource="{ row }"> {{ useDictLabel(dict_user_source, row?.userSource) }}</template>
      </pro-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button :disabled="!proTable?.selectedList.length > 0" @click="sureCallback" type="primary"> 选择 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx" name="stepOne">
import { ref, nextTick } from "vue";
import { ElMessage } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { getPortalUserList } from "@/api/modules/system";
import { userSource, userStatus, verifyLevel } from "@/utils/serviceDict";
import { System } from "@/api/interface/system";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";

const { dict_user_source } = useDict("dict_user_source");

let emits = defineEmits(["selectedList"]);
const proTable = ref<ProTableInstance>();
const dialogVisible = ref(false);

const getTableList = (params: any = {}) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.status = 1;
  newParams.enterpriseCertificateStatue = 3;
  return getPortalUserList(newParams);
};
// 表格配置项
const columns: ColumnProps<System.PortalUserParams>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "account", label: "昊算账号", width: 100, search: { el: "input" } },
  { prop: "phone", label: "手机号码", search: { el: "input" } },
  { prop: "email", label: "用户邮箱", search: { el: "input" } },
  { prop: "userSource", label: "客户来源", width: 100 },
  { prop: "areaName", label: "所在城市" },
  // { prop: "verifyLevel", label: "认证级别", width: 100, enum: verifyLevel },
  { prop: "enterpriseCertificateName", label: "企业名称" },
  {
    prop: "status",
    label: "状态",
    width: 90,
    enum: userStatus,
    render: scope => {
      return (
        <CustomTag
          type={scope.row.status === 1 ? "success" : ""}
          status={String(scope.row.status)}
          label={scope.row.status === 1 ? "启用" : "停用"}
        ></CustomTag>
      );
    }
  }
];
const handleClose = () => {
  dialogVisible.value = false;
};
const sureCallback = () => {
  if (proTable.value?.selectedList.length > 1) {
    ElMessage.error("只能选择1个客户");
  } else {
    if (proTable.value?.selectedList.length === 0) return;
    emits("selectedList", proTable.value?.selectedList);
    dialogVisible.value = false;
  }
};
const showDialog = () => {
  dialogVisible.value = true;
  nextTick(() => {
    proTable.value?.getTableList();
  });
};
defineExpose({
  showDialog
});
</script>

<style scoped lang="scss">
:deep(.el-scrollbar) {
  min-height: 120px;
}
.el-form {
  width: 90%;
  .text-center {
    text-align: center;
  }
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
</style>
