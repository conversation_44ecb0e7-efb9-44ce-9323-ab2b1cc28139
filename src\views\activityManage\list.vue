<template>
  <div>
    <div class="table-box">
      <ProTable ref="proTable" :request-api="getTableList" :columns="columns" :tool-button="false">
        <template #tableHeader>
          <el-button plain :icon="Plus" @click="handleCreate" v-auth="'activityAdd'"> 新增活动 </el-button>
        </template>

        <template #operation="{ row }">
          <el-tooltip content="预览" placement="top">
            <i v-auth="'activityPrview'" class="iconfont2 icon-lineicon_eye_open opera-icon" @click="handlePreview(row)"></i>
          </el-tooltip>
          <el-tooltip
            content="编辑"
            placement="top"
            v-if="row.status === activeStatus.ING || row.status === activeStatus.UN_KNOW || row.status === activeStatus.UN_START"
          >
            <i v-auth="'activityEdit'" class="iconfont2 icon-a-lineicon_edit opera-icon" @click="handleEdit(row)"></i>
          </el-tooltip>
          <el-tooltip content="查看" placement="top">
            <i v-auth="'activityView'" class="iconfont2 opera-icon icon-chakan" @click="handleView(row)"></i>
          </el-tooltip>
          <el-tooltip
            content="删除"
            placement="top"
            v-if="row.status === activeStatus.UN_KNOW || row.status === activeStatus.UN_START"
          >
            <i v-auth="'activityDelete'" class="iconfont2 opera-icon icon-lajitong" @click="handelDelete(row)"></i>
          </el-tooltip>
          <el-tooltip content="终止" placement="top" v-if="row.status === activeStatus.ING">
            <i class="iconfont2 opera-icon icon-tingyong" @click="handleStar(row)" v-auth="'activityShop'"></i>
          </el-tooltip>
        </template>
      </ProTable>
    </div>
    <Preview ref="previewRef" :row-data="rowData" />
  </div>
</template>

<script setup lang="tsx">
import { Plus } from "@element-plus/icons-vue";
import ProTable from "@/components/ProTable/index.vue";
import Preview from "./comps/preview.vue";
import { ref } from "vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useRouter } from "vue-router";
import { activityH5 } from "@/api/interface/activityH5";
import { queryActivityH5Page, queryMarketingDelete, queryMarketingStar } from "@/api/modules/activityH5";
import { Action, ElMessage, ElMessageBox } from "element-plus";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { activeStatus } from "@/enums/activeEnum.ts";

const { BUTTONS } = useAuthButtons();
const { marketing_status } = useDict("marketing_status");

const router = useRouter();

const previewRef = ref();

const proTable = ref<ProTableInstance>();

const rowData = ref({});

const columns: ColumnProps[] = [
  // { type: "selection" },
  {
    prop: "name",
    label: "活动名称",
    search: { el: "input" }
  },
  {
    prop: "status",
    label: "状态",
    render: (scope: any) => `${useDictLabel(marketing_status.value, scope.row.status)}`,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {marketing_status.value.map(item => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  {
    prop: "time",
    width: 300,
    label: "活动时间",
    search: {
      el: "date-picker",
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD HH:mm:ss" },
      transform: (value: any) => {
        return {
          startTime: value[0],
          endTime: value[1]
        };
      }
    },
    render: (scope: any) => `${scope.row.startTime} - ${scope.row.endTime}`
  },
  {
    prop: "creatorName",
    label: "创建人员"
  },
  {
    prop: "createTime",
    label: "创建时间"
  },
  { prop: "operation", label: "操作", fixed: "right", width: 200 }
];

const getTableList = async (params: activityH5.activitySearchParams) => {
  Reflect.deleteProperty(params, "time");

  return Reflect.has(BUTTONS.value, "activityList") ? await queryActivityH5Page(params) : null;
};
/* 新增 */
const handleCreate = () => {
  router.push({
    path: "/operation/activityManage/create"
  });
};

const handleEdit = item => {
  router.push({
    path: "/operation/activityManage/edit",
    query: {
      marketingId: item.marketingId,
      pageType: "edit"
    }
  });
};

const handleView = item => {
  router.push({
    path: "/operation/activityManage/detail",
    query: {
      marketingId: item.marketingId,
      pageType: "view"
    }
  });
};

/* 启用停止 */
const handleStar = (rowData: activityH5.activityParamsItem) => {
  ElMessageBox.alert(`确定终止<${rowData.name}>  ?`, "温馨提示", {
    confirmButtonText: "确定",
    showCancelButton: true,
    callback: (action: Action) => {
      if (action === "confirm") {
        queryMarketingStar({ marketingId: rowData.marketingId })
          .then(result => {
            if (result.code === 200) {
              ElMessage.success("终止成功");
              proTable.value?.getTableList();
            }
          })
          .catch(err => {});
      }
    }
  });
};

/* 删除 */
const handelDelete = (rowData: activityH5.activityParamsItem) => {
  ElMessageBox.alert(`确定删除<${rowData.name}> ?`, "温馨提示", {
    confirmButtonText: "确定",
    showCancelButton: true,
    callback: (action: Action) => {
      if (action === "confirm") {
        queryMarketingDelete({ marketingId: rowData.marketingId })
          .then(result => {
            proTable.value?.getTableList();
            ElMessage.success("删除成功");
          })
          .catch(err => {
            console.log(err);
          });
      }
    }
  });
};
const handlePreview = item => {
  rowData.value = item;
  previewRef.value.handleOpen();
};

proTable.value?.getTableList();
</script>
