<template>
  <el-dialog v-model="dialogVisible" title="个人信息" width="500px" class="user-dialog">
    <el-descriptions :column="1" border>
      <el-descriptions-item label-class-name="user-label" label-align="right" label="账号">
        {{ userInfo?.account || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="user-label" label="用户姓名" label-align="right">
        {{ userInfo?.userName || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="user-label" label="手机号码" label-align="right">
        {{ userInfo?.phone || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="user-label" label="用户邮箱" label-align="right">
        {{ userInfo?.email || "-" }}
      </el-descriptions-item>
      <el-descriptions-item label-class-name="user-label" label-align="right" label="账号类型">
        <el-tag> {{ userInfo?.userType == 1 ? "内部用户" : "合作商用户" || "-" }}</el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
// import { userType } from "@/utils/serviceDict";

const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);
const dialogVisible = ref(false);
const openDialog = () => {
  dialogVisible.value = true;
};

defineExpose({ openDialog });
</script>

<style lang="scss">
.user-label {
  width: 120px;
}
</style>
