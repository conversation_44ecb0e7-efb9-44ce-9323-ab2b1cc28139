<template>
  <div class="card" style="padding-left: 32px">
    <div class="infos">
      <div class="header-title">企业基础信息</div>
      <el-form ref="formRef" :model="examineForm" label-width="120px" class="demo-ruleForm" :label-position="'left'">
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">合作商名称： </span>
              <span class="info-text">{{ rowData?.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">合作协议名称： </span>
              <span class="info-text">{{ rowData?.agreementName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="info-label">有效期： </span>
              <span class="info-text">{{ rowData?.validStartTime }} ~ {{ rowData?.validEndTime }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件：" prop="_productDocs">
              <uploadFiles
                is-private
                v-model:file-list="rowData._productDocs"
                width="135px"
                height="135px"
                :file-type="'.doc,.docx,.pdf'"
                :limit="10"
                :file-size="10"
                :show-file-list="true"
                :disabled="true"
              >
                <template #tip>
                  <div class="upload-tips">上传的文件格式要求word、pdf，不超过10M。</div>
                </template>
              </uploadFiles>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="info-item">
              <span class="info-label">是否关联旧合作协议： </span>
              <span class="info-text">{{ rowData?.isRelated == 1 ? "是" : "否" }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="info-item">
              <span class="info-label agreement-label">关联旧合作协议： </span>
              <!-- 表格展示 -->
              <div class="table-box agreement-box">
                <el-table :data="rowData.relateAgreement" size="default">
                  <el-table-column prop="agreementName" label="合作协议名称" width="300" />
                  <el-table-column prop="validTime" label="有效期" width="300">
                    <template #default="scope">
                      <span>{{ scope.row.validStartTime ? `${scope.row.validStartTime} ~ ${scope.row.validEndTime}` : "" }}</span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="header-title mt10">审核信息</div>

        <el-form-item label="审核意见" prop="auditRemark" :rules="[{ required: validRequired, message: '请输入驳回意见' }]">
          <el-input
            maxlength="500"
            placeholder="驳回时必须填写审核意见，通过默认审核意见为同意"
            show-word-limit
            :rows="5"
            v-model="examineForm.auditRemark"
            type="textarea"
            autocomplete="off"
            :disabled="!isReview"
          />
        </el-form-item>
        <div class="button" v-if="isReview">
          <el-button type="primary" @click="handleTask('pass')">通过</el-button>
          <el-button type="primary" @click="handleTask('noPass')">驳回</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts" name="agreementReview">
import { reactive, ref, computed, onMounted, nextTick } from "vue";
import type { Action, FormInstance, FormRules } from "element-plus";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import uploadFiles from "@/components/Upload/Files.vue";
import { getAgreementAuditDetail, auditProviderAgreement } from "@/api/modules/partner";
import { Partner } from "@/api/interface/partner";
import { operateType } from "../type";
import { closeTabItem } from "@/utils/closeTab";

const route = useRoute();
const router = useRouter();
const { type, id } = route.query;
const isReview = (type as string) === `${operateType.review}`;

const formRef = ref<FormInstance>();

const examineForm = reactive({
  name: "",
  agreementName: "",
  validStartTime: "",
  validEndTime: "",
  _productDocs: [],
  relateAgreement: [],
  auditRemark: ""
});
const rowData = ref<any>({ _productDocs: [] });

const getDetails = () => {
  getAgreementAuditDetail({ id: id as string }).then(resp => {
    const details: any = resp.data;
    rowData.value = resp.data;
    const agreementUrl = JSON.parse(details.agreementUrl);
    const relateAgreement = JSON.parse(details.relateAgreement);
    rowData.value._productDocs = agreementUrl.map((item: Partner.AgreementUrlType) => {
      return { ...item, name: item.originalName, url: item.link };
    });

    rowData.value.relateAgreement = relateAgreement;

    if (!isReview) {
      examineForm.auditRemark = details.auditMsg;
    }
  });
};

onMounted(() => {
  getDetails();
});

const validType = ref<string>("pass");

const validRequired = computed(() => {
  return validType.value === "noPass";
});

const handleTask = (index: string) => {
  validType.value = index;
  let tips = "";
  if (index === "pass") {
    tips = "是否确认审核通过";
  } else {
    tips = "是否确认驳回审核";
  }

  nextTick(() => {
    formRef.value?.validate().then(valid => {
      if (valid) {
        ElMessageBox.alert(tips, "操作提示", {
          confirmButtonText: "确定",
          callback: (action: Action) => {
            if (action === "confirm") {
              const auditRemark = examineForm.auditRemark === "" ? "同意" : examineForm.auditRemark;
              const toPass = index === "pass";
              auditProviderAgreement({
                id: id as string,
                msg: toPass ? auditRemark : examineForm.auditRemark,
                pass: toPass ? "1" : "0"
              }).then(res => {
                if (res.code == 200) {
                  ElMessage({
                    message: toPass ? "审核通过成功!" : "审核不通过完成!",
                    type: "success"
                  });
                  closeTabItem(route);
                  router.push("/verification/partnerReview");
                } else {
                  ElMessage({
                    message: toPass ? "审核通过失败!" : "审核不通过失败!",
                    type: "error"
                  });
                }
              });
            }
          }
        });
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.header-title {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  font-weight: 400;
  &::before {
    display: block;
    width: 3px;
    height: 14px;
    margin-right: 8px;
    content: "";
    background-color: var(--el-color-primary);
    border-radius: 1px;
  }
}
.infos {
  margin-left: 10px;
  .info-item {
    margin-right: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    .info-label {
      display: inline-block;
      margin-right: 10px;
      color: #73787f;
      text-align: left;
    }
    .info-text {
      line-height: 22px;
      color: #3a3a3d;
    }
  }
}
.agreement-label {
  float: left;
}
.agreement-box {
  display: inline-block;
  width: 600px;
}
</style>
