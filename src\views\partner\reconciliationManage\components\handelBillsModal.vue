<template>
  <el-dialog v-model="dialogVisible" :title="modalTitle" width="488" :before-close="handleClose" :destroy-on-close="true">
    <div class="content">
      <div class="content-text" v-if="modalAction === 'cancel'">是否确认取消处理该对账结果？</div>
      <template v-else>
        <el-form ref="applyRef" :model="applyForm" label-width="100px">
          <el-form-item
            label="处理意见："
            prop="handleOpinion"
            :rules="[
              { trigger: 'blur', required: true, message: '请输入处理意见' },
              { max: 500, message: '最多输入500个字符', trigger: 'blur' }
            ]"
          >
            <el-input v-model="applyForm.handleOpinion" type="text" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-form>
      </template>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> {{ modalAction === "cancel" ? "确定" : "确认平账" }} </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, unref, computed } from "vue";
import { confirmHandleStatus, cancelHandleStatus } from "@/api/modules/reconciliationManage";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";

type MODALACTION = "confirm" | "cancel";

const dialogVisible = ref(false);
const modalAction = ref("confirm");
const applyRef = ref<FormInstance>();

const applyForm = ref<any>({
  handleOpinion: ""
});

const billId = ref("");
const emit = defineEmits(["onSubmitHandel"]);

const modalTitle = computed(() => {
  switch (unref(modalAction)) {
    case "confirm":
      return "确认处理";
    case "cancel":
      return "取消处理";
    default:
      return "确认处理";
  }
});

const handleOpen = (action: MODALACTION, id: string) => {
  modalAction.value = action;
  dialogVisible.value = true;
  billId.value = id;
};

const handleClose = () => {
  dialogVisible.value = false;
  if (modalAction.value == "confirm") {
    applyRef.value?.resetFields();
  }
};

const handleSubmit = () => {
  if (modalAction.value === "confirm") {
    applyRef.value
      ?.validate()
      .then(() => {
        confirmHandleStatus({
          id: billId.value,
          handleOpinion: applyForm.value.handleOpinion
        }).then((res: any) => {
          if (res.code === 200) {
            ElMessage.success("确认处理处理成功!");
            emit("onSubmitHandel");
            handleClose();
          } else {
            ElMessage.error("确认处理处理失败!");
          }
        });
      })
      .catch((err: string) => {
        console.log(err);
      });
  } else {
    cancelHandleStatus({
      id: billId.value
    }).then((res: any) => {
      if (res.code === 200) {
        ElMessage.success("取消处理成功!");
        emit("onSubmitHandel");
        handleClose();
      } else {
        ElMessage.error("取消处理失败!");
      }
    });
  }
};

defineExpose({
  handleOpen,
  handleClose
});
</script>

<style scoped lang="scss">
.content {
  .content-text {
    color: #3a3a3d;
  }
}
</style>
