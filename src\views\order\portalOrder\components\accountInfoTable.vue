<!-- 手动开通 -->
<template>
  <div class="info-box card">
    <div class="box-title">
      <div class="title">账号信息</div>
    </div>
    <ProTable
      title="合作平台账号管理列表"
      :has-padding="false"
      :pagination="false"
      :data="tableData"
      :tool-button="false"
      ref="proTable"
      :columns="columns"
      :request-auto="false"
    >
      <template #isMainAccount> 主账号 </template>
      <template #platform="{ row }">
        {{ useDictLabel(sys_platform_account, row?.platform, "--") }}
      </template>
      <template #operation="{ row }">
        <!-- <el-button @click="handleBind(row)">绑定</el-button> -->
        <template v-if="!row.platformAccount">
          <span v-auth="'handleBind'">
            <el-tooltip content="绑定" placement="top">
              <i class="iconfont2 opera-icon icon-zhuanhuan" @click="handleBind(row)"></i>
            </el-tooltip>
          </span>
        </template>
        <span v-auth="'handleunBind'" v-else>
          <el-tooltip content="解绑" placement="top">
            <i class="iconfont2 opera-icon icon-chexiao" @click="handleUnBind(row, operateTypeConfig.UNBIND)"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>

    <PlatformDrawer ref="drawerRef" :columns="platformDrawerColumns" :search-params="searchParams">
      <template #search>
        <div class="platform-search-box">
          <label class="search-label"> 合作平台 :</label>
          <el-select v-model="searchParams.platform" placeholder="请选择合作平台" clearable @change="handlePlatform">
            <el-option v-for="item in sys_platform_account" :key="item.itemKey" :label="item.itemValue" :value="item.itemKey" />
          </el-select>
        </div>
      </template>
    </PlatformDrawer>
    <BindDialog ref="bindDialogRef" @update="onBindUpdate"></BindDialog>
  </div>
</template>

<script setup lang="tsx">
import { operateTypeConfig } from "@/views/partner/partnerAccountManage/config";

import ProTable from "@/components/ProTable/index.vue";

import { ref, onMounted, watch, nextTick, computed, reactive } from "vue";
import { ProTableInstance } from "@/components/ProTable/interface";
import { useDict, useDictLabel } from "@/hooks/useDict";
import PlatformDrawer from "./platformDrawer.vue";
import BindDialog from "@/views/partner/partnerAccountManage/components/bindDialog.vue";
import { getPlatformAccountList } from "@/api/modules/platform";

const { sys_platform_account } = useDict("sys_platform_account");

const props = defineProps({
  phone: {
    type: String,
    default: ""
  },
  customerAccount: {
    type: String,
    default: ""
  },
  parentOrderNo: {
    type: String,
    default: ""
  },
  isManualOrder: {
    type: Boolean,
    default: false
  }
});

const searchParams = reactive({
  platform: "1",
  platformAccount: null,
  isMainAccount: null
});

const drawerRef = ref<{ handleOpen: () => void; handleClose: () => void; searchTable: () => void }>();

const bindDialogRef = ref<InstanceType<BindDialog>>();

const platformDrawerColumns = [
  {
    prop: "platform",
    label: "合作平台",
    render: ({ row }) => {
      return <>{useDictLabel(sys_platform_account.value, row?.platform)}</>;
    }
  },
  {
    prop: "platformAccount",
    label: "合作平台账号",
    search: { el: "input" }
  },
  {
    prop: "isMainAccount",
    label: "是否为主账号",
    enum: [
      {
        value: 1,
        label: "是"
      },
      {
        value: 0,
        label: "否"
      }
    ],
    search: { el: "select" }
  },
  {
    prop: "mainAccount",
    label: "主账号",
    width: 180
  },
  {
    prop: "accountStatus",
    label: "平台账号状态",
    width: 120,
    render: ({ row }) => {
      return <>{row.accountStatus === 1 ? "有效" : "无效"}</>;
    }
  },
  {
    prop: "isBind",
    label: "是否已绑定",
    enum: [
      {
        value: 1,
        label: "是"
      },
      {
        value: 0,
        label: "否"
      }
    ],
    render: ({ row }) => {
      return <>{row.userAccount ? "是" : "否"}</>;
    },
    width: 110
  },
  {
    prop: "subOrderNo",
    label: "关联订单号",

    width: 180
  },
  {
    prop: "bindTime",
    label: "绑定/解绑时间",
    width: 180
  },
  {
    prop: "operation",
    label: "操作",
    fixed: "right",
    render: ({ row }) => {
      return (
        <span v-auth={"handleBind"}>
          <el-tooltip content="绑定" placement="top">
            <i class="iconfont2 opera-icon icon-zhuanhuan" onClick={() => handleBindAccount(row, operateTypeConfig.BIND)}></i>
          </el-tooltip>
        </span>
      );
    }
  }
];

const onBindUpdate = () => {
  drawerRef.value?.handleClose();
  bindDialogRef.value.close();

  nextTick(() => {
    getPlatformAccountList({
      subOrderNo: props.parentOrderNo,
      userAccount: props.customerAccount,
      phoneList: [props.phone]
    }).then(res => {
      const data = res.data.records;
      if (data.length > 0) {
        data[0]["phone"] = props.phone;
        data[0]["isMainAccount"] = "主账号";
        tableData.value = data;
      } else {
        const data = [
          {
            isMainAccount: "主账号",
            phone: props.phone,
            userAccount: props.customerAccount,
            platform: null,
            platformAccount: null
          }
        ];
        tableData.value = data;
      }
    });
  });
};

const columns = ref([
  { prop: "isMainAccount", label: "账号类型" },
  { prop: "phone", label: "账号" },
  { prop: "userAccount", label: "客户账号" },
  { prop: "platform", label: "合作平台" },
  { prop: "platformAccount", label: "合作平台账号" },
  { prop: "operation", label: "操作" }
]);
const proTable = ref<ProTableInstance>();

const tableData = ref<any[]>([]);

const handleBind = (row: Order.orderAccountListItem) => {
  drawerRef.value?.handleOpen();
  nextTick(() => {
    searchParams.isMainAccount = row.isMainAccount;
    searchParams.platformAccount = row.platformAccount;
  });
};

const handleBindAccount = (row: any, type: string) => {
  bindDialogRef.value!.show({
    type,
    id: row.id,
    userAccount: props.customerAccount,
    subOrderNo: props.parentOrderNo,
    disabled: true
  });
};

const handleUnBind = (row: Order.orderAccountListItem, type: string) => {
  bindDialogRef.value!.show({
    type,
    id: row.id,
    userAccount: props.customerAccount,
    subOrderNo: props.parentOrderNo,
    disabled: true
  });
};

const handlePlatform = (value: any) => {
  searchParams.platform = value;
  drawerRef.value?.searchTable();
};

watch([() => props.phone, () => props.customerAccount], nVal => {
  nextTick(() => {
    getPlatformAccountList({
      subOrderNo: props.parentOrderNo,
      userAccount: props.customerAccount,
      phoneList: [props.phone]
    }).then(res => {
      let data = res.data.records;
      if (data.length > 0) {
        data[0]["phone"] = nVal[0];
        data[0]["isMainAccount"] = "主账号";
        tableData.value = data;
      } else {
        data = [
          {
            isMainAccount: "主账号",
            phone: nVal[0],
            userAccount: nVal[1],
            platform: null,
            platformAccount: null
          }
        ];
        tableData.value = data;
      }
    });
  });
});

onMounted(() => {
  nextTick(() => {
    if (props.isManualOrder) {
      getPlatformAccountList({
        subOrderNo: props.parentOrderNo
      }).then(res => {
        let data = res.data.records;

        if (data.length > 0) {
          data.forEach(el => {
            el.phone = props.phone;
          });
        } else {
          data = [
            {
              isMainAccount: "主账号",
              phone: props.phone,
              userAccount: props.customerAccount,
              platform: null,
              platformAccount: null
            }
          ];
        }
        tableData.value = data;
      });
      columns.value = columns.value.splice(5, columns.value.length - 1);
    }
  });
});

// watch(
//   () => props.phone,
//   val => {
//     getPlatformAccount({ mainPhone: val, platform: 1 })
//       .then(result => {
//         tableData.value = result.data;
//       })
//       .catch(err => {});
//   }
// );
</script>

<style scoped lang="scss">
@import "../order.module.scss";
.platform-search-box {
  display: flex;
  align-items: center;
  padding: 18px;
  margin-bottom: 10px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  .search-label {
    width: 85px;
    margin-right: 12px;
    font-size: 14px;
    color: #3a3a3d;
  }
}
</style>
