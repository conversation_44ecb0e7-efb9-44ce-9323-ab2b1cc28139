<template>
  <div v-if="dialogVisible">
    <el-dialog v-model="dialogVisible" :title="dialogProps.title" top="0" width="37%">
      <el-form ref="ruleFormRef" :model="labelForm" :rules="rules" label-width="120px">
        <el-form-item label="服务商名称" prop="name">
          <el-input v-model="labelForm.name" maxlength="20" show-word-limit clearable></el-input>
        </el-form-item>
        <el-form-item label="服务商编码">
          <el-input v-model="labelForm.code" maxlength="20" show-word-limit clearable></el-input>
        </el-form-item>
        <el-form-item label="自动开通产品" prop="needPurchaseProcess">
          <el-radio-group v-model="labelForm.needPurchaseProcess">
            <el-radio :label="true">支持</el-radio>
            <el-radio :label="false">不支持</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onCancel" size="large">取消</el-button>
          <el-button type="primary" @click="saveData(ruleFormRef)" size="large">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";
import { providerUpdate, providerSave } from "@/api/modules/product";

let emits = defineEmits(["update"]);

interface DialogProps {
  show: boolean;
  title: string;
  row: Object;
  type: string;
}
const dialogVisible = ref(false);
const dialogProps = ref<DialogProps>({
  title: "",
  show: true,
  row: {},
  type: ""
});
const ruleFormRef = ref();
const labelForm = ref<any>({
  name: "",
  code: "",
  needPurchaseProcess: null
});
const onCancel = () => {
  dialogVisible.value = false;
};

const rules = reactive({
  name: [{ trigger: "blur", required: true, message: "名称不能为空" }],
  needPurchaseProcess: [{ required: true, message: "请选择", trigger: "change" }]
});

const saveData = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(data => {
    if (data) {
      const apiHandle = {
        add: providerSave,
        edit: providerUpdate
      };
      let str = dialogProps.value.type === "add" ? "新增" : "修改";
      // 编辑加上id
      if (dialogProps.value.type === "edit") {
        labelForm.value.id = dialogProps.value.row.id;
      }
      apiHandle[dialogProps.value.type](labelForm.value).then((res: any = {}) => {
        if (res.code === 200) {
          ElMessage.success(`${str}服务商成功`);
          dialogVisible.value = false;
          emits("update");
        }
      });
    } else {
      console.log("提交失败!");
      return false;
    }
  });
};
// 接收父组件传过来的参数
const acceptParams = (params: DialogProps) => {
  labelForm.value = {
    name: "",
    code: "",
    needPurchaseProcess: ""
  };
  dialogProps.value = params;
  dialogVisible.value = params.show;
  if (params.type === "edit") {
    labelForm.value.name = params.row.name;
    labelForm.value.code = params.row.code;
    labelForm.value.needPurchaseProcess = params.row.needPurchaseProcess;
  }
};
defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.el-form {
  .el-form-item--default {
    margin-bottom: 24px;
  }
}
</style>
