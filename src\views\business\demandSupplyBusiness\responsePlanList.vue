<template>
  <div class="table-box">
    <div class="require-name">
      需求名称：<span class="name">{{ route.query.demandName }}</span>
    </div>
    <ProTable
      ref="proTableRef"
      title="企业需求"
      :request-auto="true"
      :columns="columns"
      :request-api="getTableList"
      :tool-button="false"
    >
      <!-- 采纳状态 -->
      <template #acceptStatus="{ row }">
        <CustomTag
          :type="acceptStatusTypeMap[row.acceptStatus]"
          :status="row.acceptStatus"
          :label="useDictLabel(demandAcceptStatus, row.acceptStatus) || '--'"
        ></CustomTag>
      </template>
      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'view'" @click="openDetailDialog(scope.row)">
          <el-tooltip content="查看" placement="top">
            <i class="iconfont2 opera-icon icon-chakan"></i>
          </el-tooltip>
        </span>
      </template>
    </ProTable>
    <detail-dialog ref="detailDialogRef"></detail-dialog>
  </div>
</template>
<script setup lang="tsx" name="demandSupplyBusinessResponsePlanList">
import { ref, computed, onBeforeMount } from "vue";
import { useRoute } from "vue-router";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import CustomTag from "@/components/CustomTag/index.vue";
import DetailDialog from "./components/detailDialog.vue";
import { useDicStore } from "@/stores/modules/dictionaty";
import { getBusinessSchemeList } from "@/api/modules/requireRelease";
import { useDictLabel } from "@/hooks/useDict";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { acceptStatusTypeMap, TabType } from "./type";

const route = useRoute();
const dictionaryStore = useDicStore();
const { BUTTONS } = useAuthButtons();

// 采纳状态
const demandAcceptStatus: any = computed(() => dictionaryStore.demandAcceptStatus);

const proTableRef = ref<ProTableInstance>();
const columns: ColumnProps<any>[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "solutionName", label: "方案名称", minWidth: 90, search: { el: "input" } },
  {
    prop: "acceptStatus",
    label: "采纳状态",
    minWidth: 90,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {demandAcceptStatus.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "solutionCompanyName", label: "供方企业名称", minWidth: 110 },
  { prop: "solutionCompanyAddress", label: "供方企业地址", minWidth: 110 },
  { prop: "solutionContactName", label: "供方联系人", minWidth: 100 },
  { prop: "solutionPhone", label: "供方联系电话", minWidth: 110 },
  { prop: "createTime", label: "提交时间", minWidth: 110, sortable: true },
  { prop: "acceptTime", label: "采纳时间", minWidth: 110, sortable: true },
  { prop: "operation", label: "操作", fixed: "right", width: 110 }
];

const getTableList = (params: any) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.demandId = route.query.id;
  newParams.auditStatus = "pass";
  return getBusinessSchemeList(newParams);
};

const detailDialogRef = ref();
const openDetailDialog = (row: any) => {
  detailDialogRef.value?.handleOpen("需求应答信息", TabType.SERVICE, row);
};

onBeforeMount(() => {
  dictionaryStore.demandAcceptStatus.length === 0 && dictionaryStore.getDemandAcceptStatus();
});
</script>
<style scoped lang="scss">
.require-name {
  padding-left: 5px;
  margin: 5px 0 10px;
  font-size: 14px;
  .name {
    color: #3a3a3d;
  }
}
</style>
