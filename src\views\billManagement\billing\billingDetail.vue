<template>
  <div class="table-box">
    <ProTable ref="proTable" title="核时明细账单" :columns="columns" :tool-button="false" :request-api="getTableList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>
      </template>

      <template #changeType="{ row }">
        {{
          console.log(
            "useDictLabel(dsk_balance_change_type, row?.changeType)",
            useDictLabel(dsk_balance_change_type, row?.changeType),
            typeof useDictLabel(dsk_balance_change_type, row?.changeType),
            useDictLabel(dsk_balance_change_type, row?.changeType) || "--"
          )
        }}
        {{ useDictLabel(dsk_balance_change_type, row?.changeType) || "--" }}
      </template>
      <template #jobType="{ row }">
        {{ useDictLabel(dsk_job_type, row?.jobType) || "--" }}
      </template>
      <template #payStatus="{ row }">
        <CustomTag
          :type="statusTypeMap[row.payStatus]"
          :status="row.payStatus"
          :label="useDictLabel(dsk_pay_status, row.payStatus) || '--'"
        ></CustomTag>
      </template>
      <template #jobStatus="{ row }">
        {{ useDictLabel(job_status, row.jobStatus) || "--" }}
      </template>

      <template #operation="{ row }">
        <el-button type="primary" link :disabled="row.payStatus === UN_CLEARED" @click="onRelated(row)"> 关联订单 </el-button>
        <!-- <div class="operation-btn">
          <auth-button :row-data="row" :auth-list="authList"></auth-button>
        </div> -->
      </template>
    </ProTable>
    <related-modal ref="relatedRef"></related-modal>
  </div>
</template>
<script lang="tsx" setup name="billingDetail">
import { ref } from "vue";
import { cloneDeep } from "lodash";
// import { useRoute } from "vue-router";
import { ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { getDskJobBill, queryGetCluster, exportDskJobBill } from "@/api/modules/billManagement";
// import type { IAuthButtonItem } from "@/components/AuthButton/typings";
import relatedModal from "./relatedModal.vue";
import CustomTag from "@/components/CustomTag/index.vue";
import { statusTypeMap } from "./type";
import { checkMax12Months } from "@/utils/eleValidate";

const { dsk_balance_change_type, dsk_pay_status, dsk_job_type, job_status } = useDict(
  "dsk_balance_change_type",
  "dsk_pay_status",
  "dsk_job_type",
  "job_status"
);
const { BUTTONS } = useAuthButtons();
const proTable = ref<ProTableInstance>();
const EXPIRE = "3"; //过期
// const route = useRoute();
// const { userId = "" } = route.query || { userId: "" };

const relatedRef = ref<InstanceType<typeof relatedModal> | null>(null);
const UN_CLEARED = "3"; // 未结清

const getTableList = (params: any) => {
  if (!BUTTONS.value?.query) {
    return Promise.resolve();
  }
  const newParams = cloneDeep(params);
  delete newParams.happenTime;
  return getDskJobBill(newParams);
};

const downloadFile = async () => {
  const newParams = cloneDeep(proTable.value?.searchParam);
  delete newParams.happenTime;
  ElMessageBox.confirm("确认导出列表数据？", "提示", { type: "warning" }).then(() => useDownload(exportDskJobBill, newParams));
};

// const authList: IAuthButtonItem[] = [
//   {
//     contentName: "关联订单",
//     iconClass: "Related",
//     itemClick: scope => onRelated(scope),
//     authName: "relatedOrders"
//   }
// ];
const onRelated = (item: any) => {
  relatedRef.value?.acceptParams({
    title: "关联订单",
    id: item.id,
    payStatus: item?.payStatus
  });
};

const columns: ColumnProps<any>[] = [
  { prop: "id", label: "明细账单ID", minWidth: 120 },
  {
    prop: "happenTime",
    label: "核时交易时间",
    search: {
      el: "date-picker",
      props: {
        type: "monthrange",
        valueFormat: "YYYY-MM",
        rules: [{ trigger: "blur", validator: checkMax12Months }]
      },
      transform: (value: any) => {
        return {
          happenTimeStart: value[0],
          happenTimeEnd: value[1]
        };
      }
    },
    minWidth: 180
  },
  { prop: "account", label: "客户账号", search: { el: "input", order: 3 }, minWidth: 120 },
  { prop: "phone", label: "手机号码", search: { el: "input", order: 2 }, minWidth: 120 },
  {
    prop: "changeType",
    label: "类型",
    search: {
      el: "select",
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {dsk_balance_change_type.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    minWidth: 120
  },
  { prop: "relativeId", label: "作业号/订单号", minWidth: 150 },
  {
    prop: "clusterId",
    label: "集群",
    search: { el: "select" },
    enum: () => {
      return queryGetCluster();
    },
    fieldNames: { label: "displayName", value: "aiClusterId" },
    minWidth: 150
  },
  {
    prop: "jobNodes",
    label: "节点数",
    minWidth: 120,
    render: ({ row }) => {
      return <span>{row.changeType === EXPIRE ? "--" : row.jobNodes ?? "--"}</span>;
    }
  },
  {
    prop: "jobType",
    label: "作业类型",
    search: {
      el: "select",
      order: 7,
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {dsk_job_type.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    minWidth: 120
  },
  { prop: "jobStatus", label: "作业状态", minWidth: 120 },
  { prop: "settlementSecond", label: "本期运行时间（秒）", minWidth: 160 },
  {
    prop: "jobStartTime",
    label: "作业开始时间",
    minWidth: 180,
    render: ({ row }) => {
      return row.jobStartTime !== "1970-01-01 08:00:00" ? row.jobStartTime ?? "--" : "--";
    }
  },
  {
    prop: "jobEndTime",
    label: "作业结束时间",
    minWidth: 180,
    render: ({ row }) => {
      return row.jobEndTime !== "1970-01-01 08:00:00" ? row.jobEndTime ?? "--" : "--";
    }
  },
  { prop: "settlementStartTime", label: "本期开始时间", minWidth: 180 },
  { prop: "settlementEndTime", label: "本期到期时间", minWidth: 180 },
  { prop: "instanceUnitPrice", label: "作业实例单价", minWidth: 120 },
  { prop: "changeQuantity", label: "消耗核时量", minWidth: 120 },
  { prop: "billAmount", label: "本期结算金额（元）", minWidth: 160 },
  {
    prop: "payStatus",
    label: "支付状态",
    search: {
      el: "select",
      order: 6,
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请输入">
            {dsk_pay_status.value?.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    minWidth: 110
  },
  { prop: "operation", label: "操作", fixed: "right", width: 100 }
];
</script>
