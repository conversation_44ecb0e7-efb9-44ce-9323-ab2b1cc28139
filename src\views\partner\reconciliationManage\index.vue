<template>
  <div class="table-box">
    <pro-table
      ref="proTable"
      title="对账管理列表"
      :columns="columns"
      :tool-button="false"
      :init-param="initParam"
      :request-api="getTableList"
      :request-auto="false"
      :is-clear-selection="true"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button v-auth="'select'" plain @click="selectAll">
          <i class="iconfont2 icon-chaxunquanxuan"></i>
          查询结果全选
        </el-button>

        <el-button v-auth="'export'" plain @click="downloadFile">
          <i class="iconfont2 icon-a-lineicon_share"></i>
          导出
        </el-button>

        <el-button v-auth="'create'" plain :disabled="!scope.isSelected" @click="createBill()">
          <i class="iconfont2 icon-shengchengzhangdan"></i>
          生成账单
        </el-button>
      </template>
      <template #status="{ row }">
        {{ useDictLabel(ord_order_status, row?.status) || "--" }}
      </template>
      <template #orderType="{ row }">
        {{ useDictLabel(pay_req_type, row?.orderType) || "--" }}
      </template>
      <template #payMethod="{ row }">
        {{ useDictLabel(pay_method_type, row?.payMethod) || "--" }}
      </template>
      <template #billStatus="{ row }">
        {{ useDictLabel(bill_status_type, row?.billStatus) || "--" }}
      </template>
      <template #handleStatus="{ row }">
        {{ useDictLabel(handle_status_type, row?.handleStatus) || "--" }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <span v-auth="'confirm'">
          <el-tooltip v-if="scope.row.handleStatus == handleStatusType.unprocessed" content="确认处理" placement="top">
            <i class="iconfont2 icon-querenchuli1 opera-icon" @click="onHandle('confirm', scope.row.id)"></i>
          </el-tooltip>
        </span>
        <span v-auth="'cancel'">
          <el-tooltip v-if="scope.row.handleStatus == handleStatusType.processed" content="取消处理" placement="top">
            <i class="iconfont2 icon-quxiaochuli1 opera-icon" @click="onHandle('cancel', scope.row.id)"></i>
          </el-tooltip>
        </span>
      </template>
    </pro-table>

    <handel-bills-modal ref="handelBillsRef" @on-submit-handel="handleSubmit"></handel-bills-modal>
    <generate-bills-modal ref="generateBillsRef" @on-submit-bill="handleSubmit"></generate-bills-modal>
    <partnerModal ref="partnerRef" @selected-partner="getPartnerInfo"></partnerModal>
  </div>
</template>

<script setup lang="tsx" name="reconciliationManage">
import { ref, reactive, onMounted, computed } from "vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { System } from "@/api/interface/system";
import { useDownload } from "@/hooks/useDownload";
import { useAuthButtons } from "@/hooks/useAuthButtons";
import { exportReconciliation, getReconciliationList } from "@/api/modules/reconciliationManage";
import { ElMessageBox, ElMessage } from "element-plus";
import generateBillsModal from "./components/generateBillsModal.vue";
import handelBillsModal from "./components/handelBillsModal.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
import partnerModal from "@/views/partner/agreementManagement/partnerModal.vue";

enum handleStatusType {
  unprocessed = "1",
  processed = "2"
}
const {
  order_cooperate_type,
  bill_status_type,
  handle_status_type,
  yes_no_type,
  pay_method_type,
  ord_order_status,
  pay_req_type
} = useDict(
  "order_cooperate_type",
  "bill_status_type",
  "handle_status_type",
  "yes_no_type",
  "pay_method_type",
  "ord_order_status",
  "pay_req_type"
);

const { BUTTONS } = useAuthButtons();
const proTable = ref<ProTableInstance>();

const handelBillsRef = ref();
const generateBillsRef = ref();
const initParam = reactive({
  cooperateType: "Z",
  providerId: ""
});
const selectParName = ref<string>();
const partnerInfo = ref<any>();
const partnerRef = ref();
const searchParam = ref();

const getTableList = async (params: System.PortalUserParams) => {
  if (!BUTTONS.value?.query) {
    return;
  }
  const cooperateType = proTable.value?.searchParam.cooperateType || "Z";
  if (cooperateType === "P" && !partnerInfo.value?.providerId) {
    ElMessage.info("请选择合作商！");
    return;
  }
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.cooperateType = cooperateType;
  if (newParams.orderDate?.length > 0) {
    newParams.orderTimeOfStart = newParams.orderDate[0];
    newParams.orderTimeOfEnd = newParams.orderDate[1];
    newParams.orderDate = null;
  }
  if (cooperateType === "P") {
    newParams.providerId = partnerInfo.value?.providerId;
  }
  searchParam.value = newParams;
  return getReconciliationList(newParams);
};

const downloadFile = () => {
  let params = JSON.parse(JSON.stringify(searchParam.value));
  const values = params;
  values.size = 1000;
  values.current = 1;
  let newParams = JSON.parse(JSON.stringify(values));
  ElMessageBox.confirm("确认导出对账列表?", "提示", { type: "warning" }).then(() => useDownload(exportReconciliation, newParams));
};

onMounted(() => {
  proTable.value?.getTableList();
});
const isDisabled = computed(() => {
  const hasBillDate = proTable.value?.searchParam.billDateFlag == "1";
  if (hasBillDate) {
    proTable.value?.resetParams(["billDate", "parentOrderNo"]);
  }
  return hasBillDate;
});
const columns: ColumnProps<any>[] = [
  {
    type: "selection",
    fixed: "left",
    width: 50,
    selectable: (row: any) => {
      return !row.billDate;
    }
  },
  {
    prop: "cooperateType",
    label: "账单类型",
    isShow: false,
    search: {
      render: (scope: any) => {
        return (
          <div style="display: flex">
            <div>
              <el-select model-value={scope.modelValue}>
                {order_cooperate_type.value.map((item: { itemKey: string; itemValue: string }) => (
                  <el-option
                    label={item.itemValue}
                    value={item.itemKey}
                    key={item.itemKey}
                    onClick={() => {
                      scope.searchParam.cooperateType = item.itemKey;
                      scope.modelValue = item.itemKey;
                    }}
                  />
                ))}
              </el-select>
            </div>
            <div style={`margin-left: 10px;display: ${scope.searchParam.cooperateType == "P" ? "inline-block" : "none"}`}>
              <el-input
                model-value={selectParName.value}
                clearable
                placeholder="请选择合作商"
                onClick={() => {
                  partnerRef.value.handleOpen(partnerInfo.value);
                }}
                onClear={() => {
                  selectParName.value = "";
                  partnerInfo.value = null;
                }}
                rules={[
                  {
                    require: true,
                    message: "请选择合作商",
                    trigger: "blur"
                  }
                ]}
              ></el-input>
            </div>
          </div>
        );
      },
      defaultValue: "Z"
    }
  },
  { prop: "name", label: "合作商", minWidth: 100 },
  {
    prop: "billDateFlag",
    label: "账单日期是否为空",
    align: "left",
    minWidth: 100,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {yes_no_type.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    },
    isShow: false
  },
  {
    prop: "billDate",
    label: "账单日期",
    search: {
      el: "date-picker",
      props: {
        type: "month",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM",
        disabled: isDisabled
      }
    },
    minWidth: 100
  },
  {
    prop: "parentOrderNo",
    label: "订单号",
    minWidth: 190,
    search: { el: "input" }
  },
  {
    prop: "orderDate",
    label: "订单时间",
    align: "left",
    minWidth: 180,
    search: {
      el: "date-picker",
      props: {
        type: "datetimerange",
        "range-separator": "-",
        format: "YYYY-MM-DD HH:mm:ss",
        valueFormat: "YYYY-MM-DD HH:mm:ss"
      }
    }
  },
  { prop: "productName", label: "产品名称", minWidth: 150 },
  { prop: "prePayFee", label: "总金额（元）", minWidth: 120 },
  { prop: "payFee", label: "实付（元）", minWidth: 120 },
  { prop: "discountFee", label: "折扣（元）", minWidth: 120 },
  { prop: "commissionFee", label: "手续费（元）", minWidth: 120 },
  {
    prop: "unicomDividendProportion",
    label: "分成比例",
    minWidth: 100,
    render: (scope: any) => {
      return (
        <span>
          {scope.row.unicomDividendProportion} : {scope.row.partnerDividendProportion}
        </span>
      );
    }
  },
  { prop: "unicomAccountFee", label: "平台金额（元）", minWidth: 140 },
  { prop: "providerAccountFee", label: "合作方金额（元）", minWidth: 140 },
  {
    prop: "status",
    label: "订单状态",
    minWidth: 100
  },
  {
    prop: "reconciliationDate",
    label: "账单生成时间",
    align: "left",
    minWidth: 180
  },
  { prop: "tradeTime", label: "交易时间", minWidth: 180 },
  {
    prop: "orderType",
    label: "订单请求类型",
    minWidth: 150
  },
  {
    prop: "payMethod",
    label: "支付方式",
    align: "left",
    minWidth: 100,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {pay_method_type.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "totalFee", label: "账单总金额（元）", minWidth: 140 },
  { prop: "realFee", label: "账单实付款（元）", minWidth: 140 },
  { prop: "couponFee", label: "账单抵扣金额（元）", minWidth: 160 },
  {
    prop: "billStatus",
    label: "比对结果",
    align: "left",
    minWidth: 100,
    search: {
      render: (scope: any) => {
        return (
          <el-select multiple model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {bill_status_type.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      },
      key: "billStatusTypeList"
    }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 110 },
  {
    prop: "handleStatus",
    label: "平账处理结果",
    align: "left",
    minWidth: 150,
    search: {
      render: (scope: any) => {
        return (
          <el-select model-value={scope.modelValue} clearable filterable placeholder="请选择">
            {handle_status_type.value.map((item: { itemKey: string; itemValue: string }) => (
              <el-option label={item.itemValue} value={item.itemKey} />
            ))}
          </el-select>
        );
      }
    }
  },
  { prop: "handleOpinion", label: "处理意见", minWidth: 100 }
];

const isValueEqual = (array: any, property: string, value: string | number) => {
  return array?.some((item: any) => item[property] === value);
};

const createBill = async () => {
  const selectedList = proTable.value?.selectedList;
  const ids = selectedList?.map(item => item.providerId);
  const flag = new Set(ids).size === 1;
  const isZiYing = isValueEqual(selectedList, "cooperateType", "Z");
  // 校验勾选的合作商是否为同一个，或者全部都是自营的，相同则弹出【生成账单】确认框
  if (!(flag || isZiYing)) {
    // 如果勾选的列表合作商不为同一个，提示："生成的账单只允许选择一个合作商"
    ElMessage.info("生成的账单只允许选择一个合作商！");
    return;
  }
  const selectedListIds = proTable.value?.selectedListIds;
  generateBillsRef.value.handleOpen("create", selectedListIds);
};

const selectAll = async () => {
  if (!proTable.value?.tableData?.length) {
    ElMessage.info("查询结果无数据！");
    return;
  }
  if (!proTable.value?.searchParam.billDateFlag) {
    ElMessage.info("查询结果含有对账日期不为空的数据！");
    return;
  }
  generateBillsRef.value.handleOpen("all", searchParam.value);
};

const onHandle = (type = "confirm", id: string) => {
  handelBillsRef.value.handleOpen(type, id);
};

const getPartnerInfo = (values: any) => {
  partnerInfo.value = values;
  selectParName.value = values.name;
};
const handleSubmit = () => {
  proTable.value?.getTableList();
  proTable.value?.clearSelection();
};
</script>

<style lang="scss" scoped>
.table-box {
  width: 100%;
}
</style>
