<template>
  <div class="table-box">
    <ProTable
      :request-auto="false"
      ref="proTable"
      :pagination="false"
      :columns="columns"
      :request-api="getTableList"
      :tool-button="false"
    >
      <template #operation="{ row }">
        <auth-button :row-data="row" :auth-list="authList"></auth-button>
      </template>
      <template #tableHeader>
        <el-button v-auth="'userPlatformBack'" plain @click="goBack"> 返回 </el-button>
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx">
import type { CoreSystem } from "@/api/interface/coreSystem";
import {
  userPlatformView,
  userPlatformDefault,
  userPlatformSave,
  userPlatformDefaultSave
} from "@/api/modules/coreSystem/userPlatform";
import ProTable from "@/components/ProTable/index.vue";
import type { Action } from "element-plus";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { onMounted, ref } from "vue";
import { IAuthButtonItem } from "@/components/AuthButton/typings";
// import { useAuthButtons } from "@/hooks/useAuthButtons";
import { useRoute, useRouter } from "vue-router";
// import { de } from "element-plus/es/locale";
import CustomTag from "@/components/CustomTag/index.vue";
import { useDict, useDictLabel } from "@/hooks/useDict";
import { ElMessage, ElMessageBox } from "element-plus";
const route = useRoute();
const router = useRouter();
// const { BUTTONS } = useAuthButtons();

// const platformList: CoreSystem.PlatformListItem[] = [];
const userId = (route.query.id as string) || "";
const pageType = route.query.pageType || "default";
const { platform_status_type } = useDict("platform_status_type");

const authList: IAuthButtonItem<CoreSystem.userPlatformItemDetail>[] = [
  {
    contentName: "开通",
    iconClass: "Start",
    itemClick: scope => handleOpen(scope),
    show: scope => scope.status === 1,
    authName: "userPlatformOpen"
  },
  {
    contentName: "停用",
    iconClass: "Stop",
    itemClick: scope => handleStop(scope),
    show: scope => scope.status === 2,
    authName: "userPlatformStop"
  }
];

const proTable = ref<ProTableInstance>();

const columns: ColumnProps[] = [
  { prop: "displayName", label: "集群名称（资源池）" },
  {
    prop: "status",
    label: "开通状态",
    render: scope => {
      return (
        <CustomTag
          type={scope.row.status === 2 ? "success" : ""}
          status={String(scope.row.status)}
          label={useDictLabel(platform_status_type.value, scope.row.status)}
        ></CustomTag>
      );
    }
  },
  { prop: "operation", label: "操作", fixed: "right" }
];
const getTableList = async () => {
  if (pageType === "default") {
    return await userPlatformDefault();
  } else if (pageType === "view") {
    return await userPlatformView(userId);
  }
};

const handleOpen = (scope: CoreSystem.userPlatformItemDetail) => {
  const params = { platformCode: scope.platformCode as string, status: 2, userId };
  submit(params, "open");
};

const handleStop = (scope: CoreSystem.userPlatformItemDetail) => {
  ElMessageBox.alert("请您确认是否停用权限？", "提示", {
    confirmButtonText: "确定",
    callback: (action: Action) => {
      if (action === "confirm") {
        const params = { platformCode: scope.platformCode as string, status: 1, userId };
        submit(params, "stop");
      }
    }
  });
};

const submit = (data: CoreSystem.userPlatformItemDetail, operateType: string) => {
  const tips = operateType === "open" ? "开通成功" : "停用成功";
  console.log(data);
  if (pageType === "default") {
    userPlatformDefaultSave(data)
      .then(res => {
        if (res.code === 200) {
          ElMessage.success(tips);
          proTable.value?.getTableList();
        }
      })
      .catch((err: any) => {
        console.log(err);
      });
  } else if (pageType === "view") {
    userPlatformSave(data)
      .then(res => {
        if (res.code === 200) {
          ElMessage.success(tips);
          proTable.value?.getTableList();
        }
      })
      .catch((err: any) => {
        console.log(err);
      });
  }
};

const goBack = () => {
  router.back();
};

onMounted(() => {
  proTable.value?.getTableList();
});
</script>

<style scoped lang="scss">
.btn-box {
  display: flex;
  justify-content: flex-end;
}
</style>
